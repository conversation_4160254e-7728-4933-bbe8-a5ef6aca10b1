<?php

use App\Models\BlogPost;
use App\Models\BlogPostCategory;
use App\Models\BlogPostTag;
use App\Models\Comment;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('index displays published blog posts', function () {
    $posts = BlogPost::factory()->count(3)->create(['is_published' => true]);
    $response = $this->get(route('blog.index'));
    $response->assertStatus(200)
        ->assertViewIs('blog-post.index')
        ->assertViewHas('posts');
});

test('show displays a single blog post', function () {
    $post = BlogPost::factory()->create(['is_published' => true]);
    $response = $this->get(route('blog.show', $post->slug));
    $response->assertStatus(200)
        ->assertViewIs('blog-post.show')
        ->assertViewHas('post');
});

test('viewCategory displays posts for a category', function () {
    $category = BlogPostCategory::factory()->create();
    $posts = BlogPost::factory()->count(3)->create(['blog_post_category_id' => $category->id, 'is_published' => true]);
    $response = $this->get(route('blog.category', $category->slug));
    $response->assertStatus(200)
        ->assertViewIs('blog-post.category')
        ->assertViewHas('posts');
});

test('viewTag displays posts for a tag', function () {
    $tag = BlogPostTag::factory()->create();
    $posts = BlogPost::factory()->count(3)->create();
    $posts->each(function ($post) use ($tag) {
        $post->tags()->attach($tag);
    });
    $response = $this->get(route('blog.tag', $tag->slug));
    $response->assertStatus(200)
        ->assertViewIs('blog-post.tag')
        ->assertViewHas('posts');
});

test('storeComment creates a new comment for authenticated user', function () {
    $user = User::factory()->create();
    $post = BlogPost::factory()->create();
    $response = $this->actingAs($user)
        ->post(route('comments.store', $post), [
            'content' => 'Test comment',
        ]);
    $response->assertRedirect()
        ->assertSessionHas('success');
    $this->assertDatabaseHas('comments', [
        'content' => 'Test comment',
        'blog_post_id' => $post->id,
        'user_id' => $user->id,
        'guest_name' => null,
        'guest_email' => null,
    ]);
});

test('storeComment creates a new guest comment', function () {
    $post = BlogPost::factory()->create();
    $response = $this->post(route('comments.store', $post), [
        'content' => 'Test guest comment',
        'guest_name' => 'John Doe',
        'guest_email' => '<EMAIL>',
    ]);
    $response->assertRedirect()
        ->assertSessionHas('success');
    $this->assertDatabaseHas('comments', [
        'content' => 'Test guest comment',
        'blog_post_id' => $post->id,
        'user_id' => null,
        'guest_name' => 'John Doe',
        'guest_email' => '<EMAIL>',
    ]);
});

test('guest comment requires name and email', function () {
    $post = BlogPost::factory()->create();
    
    // Test missing name
    $response = $this->post(route('comments.store', $post), [
        'content' => 'Test comment',
        'guest_email' => '<EMAIL>',
    ]);
    $response->assertSessionHasErrors('guest_name');
    
    // Test missing email
    $response = $this->post(route('comments.store', $post), [
        'content' => 'Test comment',
        'guest_name' => 'John Doe',
    ]);
    $response->assertSessionHasErrors('guest_email');
    
    // Test invalid email
    $response = $this->post(route('comments.store', $post), [
        'content' => 'Test comment',
        'guest_name' => 'John Doe',
        'guest_email' => 'invalid-email',
    ]);
    $response->assertSessionHasErrors('guest_email');
});

test('updateComment updates an existing comment', function () {
    $user = User::factory()->create();
    $post = BlogPost::factory()->create();
    $comment = Comment::factory()->create(['user_id' => $user->id, 'blog_post_id' => $post->id]);
    $response = $this->actingAs($user)
        ->patch(route('comments.update', $comment), [
            'content' => 'Updated comment',
        ]);
    $response->assertRedirect()
        ->assertSessionHas('success');
    $this->assertDatabaseHas('comments', [
        'id' => $comment->id,
        'content' => 'Updated comment',
    ]);
});

test('deleteComment deletes a comment', function () {
    $user = User::factory()->create();
    $post = BlogPost::factory()->create();
    $comment = Comment::factory()->create(['user_id' => $user->id, 'blog_post_id' => $post->id]);
    $response = $this->actingAs($user)
        ->delete(route('comments.delete', $comment));
    $response->assertRedirect()
        ->assertSessionHas('success');
    $this->assertDatabaseMissing('comments', ['id' => $comment->id]);
});

test('manageTags updates tags for a blog post', function () {
    $user = User::factory()->create();
    $post = BlogPost::factory()->create();
    $tags = BlogPostTag::factory()->count(3)->create();
    $tagIds = $tags->pluck('id')->toArray();
    $response = $this->actingAs($user)
        ->post(route('blog.tags.manage', $post), [
            'tags' => $tagIds,
        ]);
    $response->assertRedirect()
        ->assertSessionHas('success');
    $this->assertEquals($tagIds, $post->fresh()->tags->pluck('id')->toArray());
}); 