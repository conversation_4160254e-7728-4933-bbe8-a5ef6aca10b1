@php
    $schema = [
        "@context" => "https://schema.org",
        "@type" => "Organization",
        "name" => get_setting('schema_organization_name', 'PinCode Directory'),
        "url" => get_setting('schema_organization_url', url('/')),
        "logo" =>  uploads_url(get_setting('site_logo')),
        "contactPoint" => [
            "@type" => "ContactPoint",
            "contactType" => 'Customer Service',
            "telephone" => get_setting('contact_phone'),
            "email" => get_setting('contact_email')
        ],
        "sameAs" => [
            get_setting('facebook_link'),
            get_setting('twitter_link'),
            get_setting('linkedin_link'),
            get_setting('instagram_link'),
            get_setting('youtube_link')
        ]
    ];
@endphp

<script type="application/ld+json">
    {!! json_encode($schema, JSON_PRETTY_PRINT) !!}
</script>
