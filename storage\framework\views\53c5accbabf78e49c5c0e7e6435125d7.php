<?php if(isset($landingPage['faq']) && $landingPage['faq']['active']): ?>
        <section class="py-20 bg-bg-light dark:bg-bg-dark">
            <div class="max-w-4xl mx-auto px-4">
                <div class="text-center mb-12" data-aos="fade-up">
                    <span
                        class="inline-block px-4 py-1 rounded-full bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark text-xs font-semibold tracking-wider uppercase mb-3 animate-pulse"><?php echo e($landingPage['faq']['content']['badge_text'] ?? 'FAQ'); ?></span>
                    <h2
                        class="text-3xl md:text-4xl font-extrabold mb-4 text-text-primary-light dark:text-text-primary-dark">
                        <?php echo e($landingPage['faq']['content']['heading'] ?? 'Frequently Asked Questions'); ?>

                    </h2>
                </div>
                <div x-data="{ open: null }" class="space-y-4">
                    <?php $__currentLoopData = $landingPage['faq']['content']['faqs'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i => $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg dark:shadow-slate-700/50 p-5 border border-border-light dark:border-border-dark"
                            data-aos="fade-up" data-aos-delay="<?php echo e($i * 100); ?>">
                            <button @click="open === <?php echo e($i + 1); ?> ? open = null : open = <?php echo e($i + 1); ?>"
                                class="flex justify-between items-center w-full text-left font-semibold text-primary-light dark:text-primary-dark text-lg focus:outline-none hover:text-accent-light dark:hover:text-accent-dark transition-colors">
                                <?php echo e($faq['question'] ?? ''); ?>

                                <i :class="open === <?php echo e($i + 1); ?> ? 'fa-solid fa-chevron-up' : 'fa-solid fa-chevron-down'"
                                    class="text-primary-light dark:text-primary-dark"></i>
                            </button>
                            <div x-show="open === <?php echo e($i + 1); ?>" x-transition
                                class="mt-3 text-text-secondary-light dark:text-text-secondary-dark">
                                <?php echo e($faq['answer'] ?? ''); ?>

                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </section>
    <?php endif; ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/home/<USER>/faq-section.blade.php ENDPATH**/ ?>