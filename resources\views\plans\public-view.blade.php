@extends('layouts.app')

@section('title', 'Subscription Plans')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="text-center mb-12">
            <h1 class="text-3xl font-bold text-text-primary-light dark:text-text-primary-dark mb-4">Choose Your Plan</h1>
            <p class="text-lg text-text-secondary-light dark:text-text-secondary-dark">Select the plan that best fits your needs</p>
        </div>

        <div class="grid md:grid-cols-3 gap-8 max-w-7xl mx-auto">
            @foreach ($plans as $plan)
                <div
                    class="bg-white dark:bg-bg-dark rounded-lg shadow-lg overflow-hidden border border-border-light dark:border-border-dark {{ auth()->check() && (int)auth()->user()->getCurrentPlan() === (int)$plan->id ? 'ring-2 ring-primary-light dark:ring-primary-dark' : '' }}">
                    <div class="p-6">
                        <h2 class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark mb-4">{{ $plan->name }}</h2>
                        <div class="text-4xl font-bold text-text-primary-light dark:text-text-primary-dark mb-4">
                            ₹{{ number_format($plan->price, 2) }}
                            <span class="text-lg font-normal text-text-secondary-light dark:text-text-secondary-dark">/month</span>
                        </div>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark mb-6">{{ $plan->description }}</p>

                        <ul class="space-y-3 mb-8">
                            @foreach ($plan->features as $feature)
                                <li class="flex items-center">
                                    <svg class="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-text-primary-light dark:text-text-primary-dark">{{ $feature }}</span>
                                </li>
                            @endforeach
                        </ul>

                        @auth
                            @if ((int)auth()->user()->getCurrentPlan() === (int)$plan->id)
                                <button disabled
                                    class="block w-full bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 px-4 py-2 rounded font-medium text-center cursor-not-allowed">
                                    Current Plan
                                </button>
                            @else
                                <form action="{{ route('orders.store') }}" method="POST">
                                    @csrf
                                    <input type="hidden" name="plan_id" value="{{ $plan->id }}">
                                    <input type="hidden" name="amount" value="{{ $plan->price }}">
                                    <input type="hidden" name="request_limit" value="{{ $plan->request_limit }}">
                                    <button type="submit"
                                        class="block w-full bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-white px-4 py-2 rounded font-medium text-center transition-colors">
                                        {{ auth()->user()->getCurrentPlan() ? 'Change Plan' : 'Subscribe Now' }}
                                    </button>
                                </form>
                            @endif
                        @else
                            <a href="{{ route('register', ['redirect' => route('plans.public')]) }}"
                                class="block w-full bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-white px-4 py-2 rounded font-medium text-center transition-colors">
                                Get Started
                            </a>
                        @endauth
                    </div>
                </div>
            @endforeach
        </div>
    </div>
@endsection
