<?php

namespace Database\Seeders;

use App\Models\CurrencyRate;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CurrencyRateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rates = [
            // USD as base currency
            ['from' => 'USD', 'to' => 'INR', 'rate' => 83.50],
            ['from' => 'USD', 'to' => 'EUR', 'rate' => 0.92],
            ['from' => 'USD', 'to' => 'GBP', 'rate' => 0.79],
            
            // INR conversions
            ['from' => 'INR', 'to' => 'USD', 'rate' => 0.012],
            ['from' => 'INR', 'to' => 'EUR', 'rate' => 0.011],
            ['from' => 'INR', 'to' => 'GBP', 'rate' => 0.0095],
            
            // EUR conversions
            ['from' => 'EUR', 'to' => 'USD', 'rate' => 1.09],
            ['from' => 'EUR', 'to' => 'INR', 'rate' => 90.80],
            ['from' => 'EUR', 'to' => 'GBP', 'rate' => 0.86],
            
            // GBP conversions
            ['from' => 'GBP', 'to' => 'USD', 'rate' => 1.27],
            ['from' => 'GBP', 'to' => 'INR', 'rate' => 105.70],
            ['from' => 'GBP', 'to' => 'EUR', 'rate' => 1.16],
        ];

        foreach ($rates as $rateData) {
            CurrencyRate::setRate(
                $rateData['from'], 
                $rateData['to'], 
                $rateData['rate'], 
                CurrencyRate::SOURCE_MANUAL
            );
        }
    }
}
