<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_gateways', function (Blueprint $table) {
            $table->id();
            $table->string('name', 50)->unique()->comment('Gateway identifier: paypal, razorpay, qr_bank_transfer');
            $table->string('display_name', 100)->comment('Human-readable gateway name');
            $table->text('description')->nullable()->comment('Gateway description for users');
            $table->boolean('is_active')->default(true)->comment('Whether gateway is enabled');
            $table->boolean('is_default')->default(false)->comment('Default gateway for new payments');
            $table->json('configuration')->comment('Encrypted gateway credentials and settings');
            $table->json('supported_currencies')->comment('Array of supported currency codes');
            $table->integer('sort_order')->default(0)->comment('Display order in gateway selection');
            $table->string('webhook_url')->nullable()->comment('Webhook endpoint URL');
            $table->text('webhook_secret')->nullable()->comment('Encrypted webhook secret');
            $table->string('logo_url')->nullable()->comment('Gateway logo image URL');
            $table->timestamps();
            
            // Indexes for performance
            $table->index('is_active', 'idx_payment_gateways_active');
            $table->index('sort_order', 'idx_payment_gateways_sort_order');
            $table->index(['is_active', 'sort_order'], 'idx_payment_gateways_active_sort');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_gateways');
    }
};
