<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PinCode extends Model
{
    use HasFactory;

    protected $table = "pin_codes";

    protected $fillable = [
        'circle',
        'pincode',
        'region',
        'division',
        'name',
        'branch_type',
        'delivery_status',
        'district',
        'state',
        'contact_number',
        'latitude',
        'longitude'
    ];
    protected $withCount = ['reviews', 'likes'];

    public function district()
    {
        return $this->belongsTo(District::class, 'district', 'name');
    }

    public function state()
    {
        return $this->belongsTo(State::class, 'state', 'name');
    }

    public function postOffice()
    {
        return $this->belongsTo(PostOffice::class, 'name', 'name');
    }

    public function reviews()
    {
        return $this->hasMany(Review::class,'pincode_id')->latest();
    }

    public function likes()
    {
        return $this->hasMany(Like::class, 'pincode_id');
    }

    public function villages()
    {
        return $this->hasMany(VillagePincode::class, 'pincode', 'pincode');
    }
}