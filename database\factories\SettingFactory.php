<?php

namespace Database\Factories;

use App\Models\Setting;
use Illuminate\Database\Eloquent\Factories\Factory;

class SettingFactory extends Factory
{
    protected $model = Setting::class;

    public function definition()
    {
        return [
            'group' => 'tawk',
            'key' => $this->faker->unique()->word,
            'value' => $this->faker->word,
            'type' => 'string',
        ];
    }
} 