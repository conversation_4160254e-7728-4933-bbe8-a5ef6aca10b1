<?php

use App\Models\PinCode;
use App\Models\State;
use App\Models\District;
use App\Models\PostOffice;
use App\Models\User;
use Laravel\Sanctum\Sanctum;

test('unauthenticated user cannot access protected api endpoints', function () {
    $response = $this->getJson('/api/pincode/110001');
    
    $response->assertStatus(401);
});

test('authenticated user can get pincode details', function () {
    $user = User::factory()->create();
    Sanctum::actingAs($user);

    $state = State::factory()->create(['name' => 'Delhi']);
    $district = District::factory()->create(['name' => 'New Delhi', 'state_id' => $state->id]);
    $postOffice = PostOffice::factory()->create(['name' => 'Connaught Place Post Office']);

    $pincode = PinCode::factory()->create([
        'pincode' => '110001',
        'state' => $state->name,
        'district' => $district->name,
        'name' => $postOffice->name,
    ]);

    $response = $this->getJson("/api/pincode/{$pincode->pincode}");

    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                'pincode',
                'state',
                'district',
                'post_office'
            ]
        ]);
});

test('authenticated user can get state names', function () {
    $user = User::factory()->create();
    Sanctum::actingAs($user);

    $state = State::factory()->create(['name' => 'Delhi']);

    $response = $this->getJson("/api/pincodes/state/Delhi");

    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name'
                ]
            ]
        ]);
});

test('authenticated user can calculate distance between pincodes', function () {
    $user = User::factory()->create();
    Sanctum::actingAs($user);

    PinCode::factory()->create(['pincode' => '110001', 'latitude' => 28.6139, 'longitude' => 77.2090]);
    PinCode::factory()->create(['pincode' => '110002', 'latitude' => 28.6142, 'longitude' => 77.2124]);

    $response = $this->getJson('/api/calculate-distance-between-two-pincodes/110001/110002');

    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                'distance',
                'unit'
            ]
        ]);
});

test('authenticated user can validate pincode', function () {
    $user = User::factory()->create();
    Sanctum::actingAs($user);

    $pincode = PinCode::factory()->create(['pincode' => '110001']);

    $response = $this->getJson("/api/validate-pincode/{$pincode->pincode}");

    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                'is_valid',
                'pincode'
            ]
        ]);
});

test('authenticated user can get nearest location', function () {
    $user = User::factory()->create();
    Sanctum::actingAs($user);

    PinCode::factory()->create([
        'latitude' => 28.6139,
        'longitude' => 77.2090,
    ]);

    $response = $this->getJson('/api/get-nearest-location/28.6139/77.2090');

    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                'nearest_location',
                'distance'
            ]
        ]);
}); 