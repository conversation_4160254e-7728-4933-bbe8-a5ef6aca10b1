<?php

use App\Models\State;
use App\Models\District;
use App\Models\PinCode;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Mock storage disk for testing
    Storage::fake('public');
});

describe('State Model', function () {
    
    it('can be created with valid data', function () {
        $state = State::create([
            'name' => 'California',
            'featured_image' => 'states/california.jpg'
        ]);

        expect($state)->toBeInstanceOf(State::class)
            ->and($state->name)->toBe('California')
            ->and($state->featured_image)->toBe('states/california.jpg');
    });

    it('uses the correct table name', function () {
        $state = new State();
        expect($state->getTable())->toBe('pin_states');
    });

    it('has correct fillable attributes', function () {
        $state = new State();
        expect($state->getFillable())->toBe(['name', 'featured_image']);
    });

    it('can create state without featured image', function () {
        $state = State::create([
            'name' => 'Texas'
        ]);

        expect($state->name)->toBe('Texas')
            ->and($state->featured_image)->toBeNull();
    });
});

describe('State Relationships', function () {
    
    it('has many districts', function () {
        $state = State::factory()->create();
        $district1 = District::factory()->create(['state_id' => $state->id]);
        $district2 = District::factory()->create(['state_id' => $state->id]);

        expect($state->districts)->toHaveCount(2)
            ->and($state->districts->first())->toBeInstanceOf(District::class)
            ->and($state->districts->contains($district1))->toBeTrue()
            ->and($state->districts->contains($district2))->toBeTrue();
    });

    it('has many pincodes through name relationship', function () {
        $state = State::factory()->create(['name' => 'California']);
        $pincode1 = PinCode::factory()->create(['state' => 'California']);
        $pincode2 = PinCode::factory()->create(['state' => 'California']);
        
        // Create pincodes for different state to ensure relationship works correctly
        PinCode::factory()->create(['state' => 'Texas']);

        expect($state->pincodes)->toHaveCount(2)
            ->and($state->pincodes->first())->toBeInstanceOf(PinCode::class)
            ->and($state->pincodes->contains($pincode1))->toBeTrue()
            ->and($state->pincodes->contains($pincode2))->toBeTrue();
    });

    it('returns empty collection when no districts exist', function () {
        $state = State::factory()->create();
        
        expect($state->districts)->toHaveCount(0)
            ->and($state->districts)->toBeInstanceOf(\Illuminate\Database\Eloquent\Collection::class);
    });

    it('returns empty collection when no pincodes exist', function () {
        $state = State::factory()->create(['name' => 'Nevada']);
        
        expect($state->pincodes)->toHaveCount(0)
            ->and($state->pincodes)->toBeInstanceOf(\Illuminate\Database\Eloquent\Collection::class);
    });
});

describe('Featured Image URL Attribute', function () {
    
    it('returns storage URL when featured image exists', function () {
        Storage::disk('public')->put('states/california.jpg', 'fake-image-content');
        
        $state = State::factory()->create([
            'featured_image' => 'states/california.jpg'
        ]);

        $expectedUrl = Storage::disk('public')->url('states/california.jpg');
        expect($state->featured_image_url)->toBe($expectedUrl);
    });

    it('returns default image URL when featured image is null', function () {
        $state = State::factory()->create([
            'featured_image' => null
        ]);

        expect($state->featured_image_url)->toBe(asset('images/default-state.jpg'));
    });

    it('returns default image URL when featured image is empty string', function () {
        $state = State::factory()->create([
            'featured_image' => ''
        ]);

        expect($state->featured_image_url)->toBe(asset('images/default-state.jpg'));
    });

    it('handles different image paths correctly', function () {
        $imagePaths = [
            'images/state1.png',
            'uploads/states/state2.jpg',
            'media/state3.gif'
        ];

        foreach ($imagePaths as $path) {
            Storage::disk('public')->put($path, 'fake-content');
            
            $state = State::factory()->create([
                'featured_image' => $path
            ]);

            $expectedUrl = Storage::disk('public')->url($path);
            expect($state->featured_image_url)->toBe($expectedUrl);
        }
    });
});

describe('State Factory Integration', function () {
    
    it('works with factory', function () {
        $state = State::factory()->create();
        
        expect($state)->toBeInstanceOf(State::class)
            ->and($state->name)->toBeString()
            ->and($state->id)->toBeInt();
    });

    it('can override factory attributes', function () {
        $state = State::factory()->create([
            'name' => 'Custom State Name',
            'featured_image' => 'custom/path.jpg'
        ]);

        expect($state->name)->toBe('Custom State Name')
            ->and($state->featured_image)->toBe('custom/path.jpg');
    });

    it('can create multiple states', function () {
        $states = State::factory()->count(3)->create();
        
        expect($states)->toHaveCount(3);
        $states->each(function ($state) {
            expect($state)->toBeInstanceOf(State::class);
        });
    });
});

describe('State Model Edge Cases', function () {
    
    it('handles very long state names', function () {
        $longName = str_repeat('Very Long State Name ', 10);
        $state = State::factory()->create(['name' => $longName]);
        
        expect($state->name)->toBe($longName);
    });

    it('handles special characters in state names', function () {
        $specialName = "State with Spëcîål Cháracters & Numbers 123";
        $state = State::factory()->create(['name' => $specialName]);
        
        expect($state->name)->toBe($specialName);
    });

    it('handles special characters in image paths', function () {
        $specialPath = 'images/state-with-dashes_and_underscores (1).jpg';
        Storage::disk('public')->put($specialPath, 'content');
        
        $state = State::factory()->create(['featured_image' => $specialPath]);
        
        $expectedUrl = Storage::disk('public')->url($specialPath);
        expect($state->featured_image_url)->toBe($expectedUrl);
    });
});

describe('State Model Database Operations', function () {
    
    it('can update state attributes', function () {
        $state = State::factory()->create(['name' => 'Original Name']);
        
        $state->update([
            'name' => 'Updated Name',
            'featured_image' => 'new/image.jpg'
        ]);

        expect($state->fresh()->name)->toBe('Updated Name')
            ->and($state->fresh()->featured_image)->toBe('new/image.jpg');
    });

    it('can delete state', function () {
        $state = State::factory()->create();
        $stateId = $state->id;
        
        $state->delete();
        
        expect(State::find($stateId))->toBeNull();
    });

    it('persists relationships after reload', function () {
        $state = State::factory()->create(['name' => 'Persistent State']);
        $district = District::factory()->create(['state_id' => $state->id]);
        
        $reloadedState = State::find($state->id);
        
        expect($reloadedState->districts)->toHaveCount(1)
            ->and($reloadedState->districts->first()->id)->toBe($district->id);
    });
});