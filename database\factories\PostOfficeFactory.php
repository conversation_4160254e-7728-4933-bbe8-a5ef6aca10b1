<?php

namespace Database\Factories;

use App\Models\PostOffice;
use App\Models\District;
use Illuminate\Database\Eloquent\Factories\Factory;

class PostOfficeFactory extends Factory
{
    protected $model = PostOffice::class;

    public function definition()
    {
        return [
            'name' => $this->faker->company . ' Post Office',
            'district_id' => District::factory(),
            'pincode' => $this->faker->numberBetween(100000, 999999),
            'branch_type' => $this->faker->randomElement(['HO', 'SO', 'BO', 'PO']),
            'created_at' => now(),
            'updated_at' => now()
        ];
    }
}