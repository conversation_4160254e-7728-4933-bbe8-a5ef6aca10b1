<?php

namespace Database\Factories;

use App\Models\Card;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Card>
 */
class CardFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Card::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(3),
            'line2' => $this->faker->sentence(6),
            'line3' => $this->faker->sentence(8),
            'imageUrl' => $this->faker->imageUrl(640, 480, 'business'),
            'link' => $this->faker->url(),
            'linkText' => $this->faker->words(2, true),
        ];
    }
} 