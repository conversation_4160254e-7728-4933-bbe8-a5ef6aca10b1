<?php

namespace Database\Factories;

use App\Models\PincodeGeo;
use Illuminate\Database\Eloquent\Factories\Factory;

class PincodeGeoFactory extends Factory
{
    protected $model = PincodeGeo::class;

    public function definition()
    {
        return [
            'pincode' => $this->faker->postcode(),
            'office_name' => $this->faker->citySuffix(),
            'division' => $this->faker->word(),
            'region' => $this->faker->word(),
            'circle' => $this->faker->word(),
            'geometry' => [
                [
                    [$this->faker->longitude, $this->faker->latitude],
                    [$this->faker->longitude, $this->faker->latitude],
                    [$this->faker->longitude, $this->faker->latitude],
                ]
            ],
        ];
    }
} 