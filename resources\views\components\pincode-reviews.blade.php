@props(['reviews', 'pincode', 'hasMoreReviews' => false, 'totalReviewsCount' => 0, 'showForm' => true, 'showViewAllButton' => true])

<div class="bg-white dark:bg-bg-dark border border-border-light dark:border-border-dark rounded-xl shadow-lg p-8 mt-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-primary-light dark:text-primary-dark"
                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            User Feedback
            @if($totalReviewsCount > 0)
                <span class="text-lg text-text-secondary-light dark:text-text-secondary-dark ml-2">({{ $totalReviewsCount }})</span>
            @endif
        </h2>
        
        @if($hasMoreReviews && $showViewAllButton)
            <a href="{{ route('reviews.all', ['pincode_id' => $pincode->id]) }}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-light dark:bg-primary-dark hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                View All Reviews
            </a>
        @endif
    </div>

    @if($reviews->count() > 0)
        <div class="space-y-6 mb-8">
            @foreach($reviews as $review)
                <div class="border-b border-border-light dark:border-border-dark pb-6 last:border-b-0 last:pb-0">
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex-1">
                            <div class="flex items-center mb-2">
                                <div class="w-10 h-10 bg-primary-light/10 dark:bg-primary-dark/10 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-primary-light dark:text-primary-dark font-semibold text-sm">
                                        {{ strtoupper(substr($review->user ? $review->user->name : $review->name, 0, 1)) }}
                                    </span>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">
                                        {{ $review->user ? $review->user->name : $review->name }}
                                    </h3>
                                    <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                        {{ $review->created_at->format('F j, Y \a\t g:i A') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        @if($review->rating)
                            <div class="flex items-center ml-4">
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                        <svg class="w-5 h-5 {{ $i <= $review->rating ? 'text-accent-light dark:text-accent-dark' : 'text-text-secondary-light/40 dark:text-text-secondary-dark/40' }}" 
                                             fill="currentColor" 
                                             viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                    @endfor
                                </div>
                                <span class="ml-2 text-sm text-text-secondary-light dark:text-text-secondary-dark">{{ $review->rating }}/5</span>
                            </div>
                        @endif
                    </div>
                    
                    <div class="prose max-w-none text-text-primary-light dark:text-text-primary-dark leading-relaxed">
                        {{ $review->comment }}
                    </div>
                </div>
            @endforeach
        </div>
        
        @if($hasMoreReviews && $showViewAllButton)
            <div class="text-center mb-8">
                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-3">
                    Showing {{ $reviews->count() }} of {{ $totalReviewsCount }} reviews
                </p>
            </div>
        @endif
    @else
        <div class="text-center py-8 mb-8">
            <svg class="mx-auto h-12 w-12 text-text-secondary-light dark:text-text-secondary-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">No reviews yet</h3>
            <p class="mt-1 text-sm text-text-secondary-light dark:text-text-secondary-dark">Be the first to review this pincode!</p>
        </div>
    @endif

    @if($showForm)
        <!-- Review Form -->
        <div class="border-t border-border-light dark:border-border-dark pt-8">
            <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Share Your Experience</h3>
            
            @if(session('success'))
                <div class="mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-300 px-4 py-3 rounded-md">
                    {{ session('success') }}
                </div>
            @endif

            @if($errors->any())
                <div class="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded-md">
                    <ul class="list-disc list-inside">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            
            <form id="reviewForm" action="{{ route('reviews.store') }}" method="POST" class="space-y-4">
                @csrf
                <input type="hidden" name="pincode_id" value="{{ $pincode->id }}">
                
                @guest
                    <div>
                        <label for="name" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">Name *</label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}"
                            class="w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark"
                            required>
                    </div>
                @endguest
                
                <div>
                    <label for="rating" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">Rating *</label>
                    <div class="flex items-center space-x-1">
                        @for($i = 1; $i <= 5; $i++)
                            <button type="button" 
                                    class="rating-star w-8 h-8 text-text-secondary-light/40 dark:text-text-secondary-dark/40 hover:text-accent-light dark:hover:text-accent-dark focus:outline-none transition-colors duration-200"
                                    data-rating="{{ $i }}">
                                <svg fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                            </button>
                        @endfor
                    </div>
                    <input type="hidden" name="rating" id="rating-input" value="">
                </div>
                
                <div>
                    <label for="comment" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">Your Review *</label>
                    <textarea name="comment" id="comment" rows="4" 
                        class="w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark"
                        placeholder="Share your experience with this post office..."
                        required>{{ old('comment') }}</textarea>
                </div>
                
                <div>
                    <button type="submit" id="submitReview"
                        class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-light dark:bg-primary-dark hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark transition-colors duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                        </svg>
                        <span id="submitText">Submit Review</span>
                        <svg id="loadingSpinner" class="hidden animate-spin ml-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </button>
                </div>
            </form>
        </div>
    @endif
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Star rating functionality
    const stars = document.querySelectorAll('.rating-star');
    const ratingInput = document.getElementById('rating-input');
    let currentRating = 0;

    stars.forEach((star, index) => {
        star.addEventListener('click', function() {
            currentRating = index + 1;
            ratingInput.value = currentRating;
            updateStars();
        });

        star.addEventListener('mouseenter', function() {
            highlightStars(index + 1);
        });
    });

    document.querySelector('.flex.items-center.space-x-1').addEventListener('mouseleave', function() {
        updateStars();
    });

    function highlightStars(rating) {
        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.remove('text-text-secondary-light/40', 'dark:text-text-secondary-dark/40');
                star.classList.add('text-accent-light', 'dark:text-accent-dark');
            } else {
                star.classList.remove('text-accent-light', 'dark:text-accent-dark');
                star.classList.add('text-text-secondary-light/40', 'dark:text-text-secondary-dark/40');
            }
        });
    }

    function updateStars() {
        highlightStars(currentRating);
    }

    // Form submission with AJAX
    const reviewForm = document.getElementById('reviewForm');
    if (reviewForm) {
        reviewForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitButton = document.getElementById('submitReview');
            const submitText = document.getElementById('submitText');
            const loadingSpinner = document.getElementById('loadingSpinner');

            // Show loading state
            submitButton.disabled = true;
            submitText.textContent = 'Submitting...';
            loadingSpinner.classList.remove('hidden');

            try {
                const formData = new FormData(this);
                const response = await fetch(this.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Show success message and reload page
                    alert(data.message || 'Review submitted successfully!');
                    window.location.reload();
                } else {
                    // Show error message
                    alert(data.message || 'Error submitting review');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('An error occurred while submitting your review');
            } finally {
                // Reset button state
                submitButton.disabled = false;
                submitText.textContent = 'Submit Review';
                loadingSpinner.classList.add('hidden');
            }
        });
    }
});
</script>
@endpush
