<?php

namespace App\Services\Payment\Responses;

use App\Services\Payment\Exceptions\PaymentGatewayException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;

class ErrorResponse
{
    protected string $errorCode;
    protected string $message;
    protected string $userMessage;
    protected array $context;
    protected array $recoveryOptions;
    protected string $severity;
    protected bool $isRetryable;
    protected ?string $gatewayName;

    public function __construct(
        string $errorCode,
        string $message,
        string $userMessage = '',
        array $context = [],
        array $recoveryOptions = [],
        string $severity = 'error',
        bool $isRetryable = false,
        ?string $gatewayName = null
    ) {
        $this->errorCode = $errorCode;
        $this->message = $message;
        $this->userMessage = $userMessage ?: $message;
        $this->context = $context;
        $this->recoveryOptions = $recoveryOptions;
        $this->severity = $severity;
        $this->isRetryable = $isRetryable;
        $this->gatewayName = $gatewayName;
    }

    /**
     * Create error response from exception
     */
    public static function fromException(PaymentGatewayException $exception): self
    {
        return new self(
            $exception->getGatewayCode() ?? 'UNKNOWN_ERROR',
            $exception->getMessage(),
            $exception->getUserMessage(),
            $exception->getContext(),
            $exception->getRecoveryOptions(),
            $exception->getSeverity(),
            $exception->isRetryable(),
            $exception->getGatewayName()
        );
    }

    /**
     * Create generic error response
     */
    public static function generic(string $message, array $context = []): self
    {
        return new self(
            'GENERIC_ERROR',
            $message,
            'An unexpected error occurred. Please try again.',
            $context,
            ['Try again', 'Contact support if issue persists']
        );
    }

    /**
     * Create network error response
     */
    public static function networkError(string $gatewayName = '', array $context = []): self
    {
        return new self(
            'NETWORK_ERROR',
            'Network connectivity issue',
            'We\'re experiencing connectivity issues. Please try again in a few moments.',
            $context,
            ['Try again in a few moments', 'Check your internet connection'],
            'warning',
            true,
            $gatewayName
        );
    }

    /**
     * Create validation error response
     */
    public static function validationError(string $field, string $message, array $context = []): self
    {
        return new self(
            'VALIDATION_ERROR',
            "Validation failed for {$field}: {$message}",
            $message,
            array_merge($context, ['field' => $field]),
            ['Check the provided information', 'Correct any errors and try again'],
            'info'
        );
    }

    /**
     * Create configuration error response
     */
    public static function configurationError(string $gatewayName, array $context = []): self
    {
        return new self(
            'CONFIGURATION_ERROR',
            "Configuration error for {$gatewayName}",
            'We\'re experiencing technical difficulties. Our team has been notified.',
            $context,
            ['Contact administrator'],
            'critical',
            false,
            $gatewayName
        );
    }

    /**
     * Get error code
     */
    public function getErrorCode(): string
    {
        return $this->errorCode;
    }

    /**
     * Get technical message
     */
    public function getMessage(): string
    {
        return $this->message;
    }

    /**
     * Get user-friendly message
     */
    public function getUserMessage(): string
    {
        return $this->userMessage;
    }

    /**
     * Get context information
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Get recovery options
     */
    public function getRecoveryOptions(): array
    {
        return $this->recoveryOptions;
    }

    /**
     * Get error severity
     */
    public function getSeverity(): string
    {
        return $this->severity;
    }

    /**
     * Check if error is retryable
     */
    public function isRetryable(): bool
    {
        return $this->isRetryable;
    }

    /**
     * Get gateway name
     */
    public function getGatewayName(): ?string
    {
        return $this->gatewayName;
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'error_code' => $this->errorCode,
            'message' => $this->message,
            'user_message' => $this->userMessage,
            'context' => $this->context,
            'recovery_options' => $this->recoveryOptions,
            'severity' => $this->severity,
            'is_retryable' => $this->isRetryable,
            'gateway_name' => $this->gatewayName,
            'timestamp' => now()->toISOString()
        ];
    }

    /**
     * Convert to JSON response
     */
    public function toJsonResponse(int $statusCode = 400): JsonResponse
    {
        $data = [
            'success' => false,
            'error' => [
                'code' => $this->errorCode,
                'message' => $this->userMessage,
                'details' => $this->context,
                'recovery_options' => $this->recoveryOptions,
                'is_retryable' => $this->isRetryable
            ],
            'timestamp' => now()->toISOString()
        ];

        // Add debug information in non-production environments
        if (config('app.debug')) {
            $data['debug'] = [
                'technical_message' => $this->message,
                'severity' => $this->severity,
                'gateway' => $this->gatewayName
            ];
        }

        return response()->json($data, $statusCode);
    }

    /**
     * Convert to redirect response with error
     */
    public function toRedirectResponse(string $route = 'payment.error', array $routeParams = []): RedirectResponse
    {
        return redirect()->route($route, $routeParams)
            ->with('error', $this->userMessage)
            ->with('error_code', $this->errorCode)
            ->with('recovery_options', $this->recoveryOptions)
            ->with('is_retryable', $this->isRetryable);
    }

    /**
     * Log the error
     */
    public function log(string $channel = 'payment'): self
    {
        $logData = [
            'error_code' => $this->errorCode,
            'message' => $this->message,
            'user_message' => $this->userMessage,
            'context' => $this->context,
            'severity' => $this->severity,
            'gateway' => $this->gatewayName,
            'is_retryable' => $this->isRetryable,
            'timestamp' => now()->toISOString()
        ];

        match ($this->severity) {
            'critical' => Log::channel($channel)->critical($this->message, $logData),
            'error' => Log::channel($channel)->error($this->message, $logData),
            'warning' => Log::channel($channel)->warning($this->message, $logData),
            'info' => Log::channel($channel)->info($this->message, $logData),
            default => Log::channel($channel)->error($this->message, $logData)
        };

        return $this;
    }

    /**
     * Send notification to administrators for critical errors
     */
    public function notifyAdmins(): self
    {
        if ($this->severity === 'critical') {
            // In a real implementation, you would send notifications here
            // For example, using Laravel's notification system or external services
            Log::channel('admin-alerts')->critical('Critical payment error occurred', [
                'error_code' => $this->errorCode,
                'message' => $this->message,
                'gateway' => $this->gatewayName,
                'context' => $this->context,
                'timestamp' => now()->toISOString()
            ]);
        }

        return $this;
    }

    /**
     * Add additional context
     */
    public function withContext(array $additionalContext): self
    {
        $this->context = array_merge($this->context, $additionalContext);
        return $this;
    }

    /**
     * Add recovery option
     */
    public function addRecoveryOption(string $option): self
    {
        $this->recoveryOptions[] = $option;
        return $this;
    }

    /**
     * Set user message
     */
    public function setUserMessage(string $message): self
    {
        $this->userMessage = $message;
        return $this;
    }

    /**
     * Mark as retryable
     */
    public function markAsRetryable(bool $retryable = true): self
    {
        $this->isRetryable = $retryable;
        return $this;
    }

    /**
     * Set severity
     */
    public function setSeverity(string $severity): self
    {
        $this->severity = $severity;
        return $this;
    }
}