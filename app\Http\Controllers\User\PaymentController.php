<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Payment;
use App\Models\PaymentGateway;
use App\Models\Plan;
use App\Services\Payment\PaymentGatewayManager;
use App\Services\Payment\CurrencyConversionService;
use App\Services\Payment\PaymentValidationService;
use App\Services\Payment\PaymentCSRFService;
use App\Services\Payment\FileUploadSecurityService;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    protected PaymentGatewayManager $gatewayManager;
    protected CurrencyConversionService $currencyService;
    protected PaymentValidationService $validationService;
    protected PaymentCSRFService $csrfService;

    public function __construct(
        PaymentGatewayManager $gatewayManager, 
        CurrencyConversionService $currencyService,
        PaymentValidationService $validationService,
        PaymentCSRFService $csrfService
    ) {
        $this->gatewayManager = $gatewayManager;
        $this->currencyService = $currencyService;
        $this->validationService = $validationService;
        $this->csrfService = $csrfService;
        
        // Apply rate limiting middleware to payment operations
        $this->middleware('payment.rate.limit')->only([
            'createPayment', 'verifyPayment', 'processPayment', 'razorpayVerify'
        ]);
    }

    /**
     * Show available plans with gateway selection
     */
    public function showPlans()
    {
        $plans = Plan::where('is_active', true)->get();
        $gateways = $this->gatewayManager->getAllGatewaysConfigForFrontend();
        
        // Generate CSRF token for payment operations
        $csrfToken = $this->csrfService->generatePaymentToken(
            Auth::id(),
            'payment_creation',
            ['action' => 'show_plans']
        );
        
        return view('payment.form', compact('plans', 'gateways', 'csrfToken'));
    }

    /**
     * Generate CSRF token for payment operations
     */
    public function generateCSRFToken(Request $request)
    {
        $request->validate([
            'operation' => 'required|string|in:payment_creation,file_upload,payment_verification',
            'context' => 'sometimes|array'
        ]);

        try {
            $tokenResult = $this->csrfService->generatePaymentToken(
                Auth::id(),
                $request->operation,
                $request->context ?? []
            );

            if (!$tokenResult['success']) {
                return response()->json([
                    'success' => false,
                    'error' => $tokenResult['error']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'token' => $tokenResult['token'],
                'expires_at' => $tokenResult['expires_at']
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to generate CSRF token', [
                'user_id' => Auth::id(),
                'operation' => $request->operation,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to generate security token'
            ], 500);
        }
    }

    /**
     * Show gateway selection for an order
     */
    public function selectGateway(Order $order)
    {
        // Ensure user can only process their own orders
        if ($order->user_id !== Auth::id()) {
            abort(403);
        }

        // Only allow processing of pending orders
        if ($order->status !== Order::STATUS_PENDING) {
            return redirect()->route('orders.show', $order)
                ->with('error', 'This order cannot be processed.');
        }

        $gateways = $this->gatewayManager->getGatewaysForCurrency($order->currency ?? 'USD');
        $conversionPreview = $this->currencyService->getConversionPreview($order->amount, $order->currency ?? 'USD');

        return view('user.payment.gateway-selection', compact('order', 'gateways', 'conversionPreview'));
    }

    /**
     * Process payment for an existing order
     */
    public function process(Order $order)
    {
        // Ensure user can only process their own orders
        if ($order->user_id !== Auth::id()) {
            abort(403);
        }

        // Only allow processing of pending orders
        if ($order->status !== Order::STATUS_PENDING) {
            return redirect()->route('orders.show', $order)
                ->with('error', 'This order cannot be processed.');
        }

        return view('user.payment.process', compact('order'));
    }

    /**
     * Create a payment using the selected gateway
     */
    public function createPayment(Request $request)
    {
        // Enhanced validation with security checks
        $request->validate([
            'gateway_id' => 'required|exists:payment_gateways,id',
            'plan_id' => 'required_without:order_id|exists:plans,id',
            'order_id' => 'required_without:plan_id|exists:orders,id',
            'csrf_token' => 'required|string',
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'required|string|size:3',
        ]);

        try {
            // Validate CSRF token
            $csrfValidation = $this->csrfService->validateAndConsumeToken(
                $request->csrf_token,
                Auth::id(),
                'payment_creation',
                ['gateway_id' => $request->gateway_id]
            );

            if (!$csrfValidation['valid']) {
                Log::warning('Invalid CSRF token for payment creation', [
                    'user_id' => Auth::id(),
                    'error' => $csrfValidation['error'],
                    'ip' => $request->ip()
                ]);

                return redirect()->back()
                    ->with('error', 'Security validation failed. Please refresh and try again.');
            }

            // Validate payment request data
            $paymentData = [
                'amount' => $request->amount,
                'currency' => $request->currency,
                'gateway_id' => $request->gateway_id,
                'plan_id' => $request->plan_id ?? null,
            ];

            $requestValidation = $this->validationService->validatePaymentRequest($paymentData);
            if (!$requestValidation['valid']) {
                Log::warning('Invalid payment request data', [
                    'user_id' => Auth::id(),
                    'errors' => $requestValidation['details'],
                    'request_data' => $paymentData
                ]);

                return redirect()->back()
                    ->withErrors($requestValidation['details'])
                    ->with('error', 'Payment validation failed.');
            }
        } catch (\Exception $e) {
            Log::error('Payment validation error', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->with('error', 'Payment validation failed. Please try again.');
        }

        try {
            // If order_id is provided, use existing order
            if ($request->has('order_id')) {
                $order = Order::findOrFail($request->order_id);

                // Ensure user can only pay for their own orders
                if ($order->user_id !== Auth::id()) {
                    abort(403);
                }

                // Only allow payment for pending orders
                if ($order->status !== Order::STATUS_PENDING) {
                    return redirect()->route('orders.show', $order)
                        ->with('error', 'This order cannot be processed.');
                }

                $plan = Plan::findOrFail($order->plan_id);
            } else {
                // Create a new order
                $plan = Plan::where('is_active', true)->findOrFail($request->plan_id);

                $order = Order::create([
                    'user_id' => Auth::id(),
                    'plan_id' => $plan->id,
                    'order_number' => 'ORD-' . uniqid(),
                    'amount' => $plan->price,
                    'currency' => $request->currency ?? 'USD',
                    'request_limit' => $plan->request_limit,
                    'status' => Order::STATUS_PENDING,
                ]);
            }

            Log::info('Creating payment with gateway', [
                'order_id' => $order->id,
                'gateway_id' => $request->gateway_id,
                'amount' => $order->amount,
                'currency' => $order->currency,
            ]);

            // Create payment using the gateway manager
            $paymentResponse = $this->gatewayManager->createPayment($order, $request->gateway_id);

            if ($paymentResponse->isSuccess()) {
                // Handle different gateway responses
                $gateway = PaymentGateway::findOrFail($request->gateway_id);
                
                switch ($gateway->name) {
                    case 'razorpay':
                        return $this->handleRazorpayResponse($paymentResponse, $order);
                    
                    case 'qr_bank_transfer':
                        return $this->handleQRBankTransferResponse($paymentResponse, $order);
                    
                    case 'paypal':
                        return $this->handlePayPalResponse($paymentResponse, $order);
                    
                    default:
                        throw new PaymentGatewayException('Unsupported gateway: ' . $gateway->name);
                }
            }

            throw new PaymentGatewayException($paymentResponse->message ?? 'Payment creation failed');

        } catch (PaymentGatewayException $e) {
            Log::error('Payment gateway error', [
                'message' => $e->getMessage(),
                'gateway_code' => $e->getGatewayCode(),
                'order_id' => $order->id ?? null,
            ]);

            if (isset($order)) {
                $order->update(['status' => Order::STATUS_FAILED]);
            }

            return redirect()->route('payment.cancel.page')
                ->with('error', 'Payment initialization failed: ' . $e->getMessage());
        
        } catch (\Exception $e) {
            Log::error('Payment creation error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'order_id' => $order->id ?? null,
            ]);

            if (isset($order)) {
                $order->update(['status' => Order::STATUS_FAILED]);
            }

            return redirect()->route('payment.cancel.page')
                ->with('error', 'Payment initialization failed: ' . $e->getMessage());
        }
    }

    /**
     * Handle Razorpay payment response
     */
    private function handleRazorpayResponse($paymentResponse, Order $order)
    {
        $checkoutData = $paymentResponse->metadata;
        return view('user.payment.razorpay-checkout', compact('order', 'checkoutData', 'paymentResponse'));
    }

    /**
     * Handle QR Bank Transfer payment response
     */
    private function handleQRBankTransferResponse($paymentResponse, Order $order)
    {
        $qrData = $paymentResponse->metadata['qr_data'];
        $bankDetails = $paymentResponse->metadata['bank_details'];
        $paymentReference = $paymentResponse->metadata['payment_reference'];
        $uploadUrl = $paymentResponse->metadata['upload_url'];

        return view('user.payment.qr-bank-transfer', compact(
            'order', 'qrData', 'bankDetails', 'paymentReference', 'uploadUrl', 'paymentResponse'
        ));
    }

    /**
     * Handle PayPal payment response (for backward compatibility)
     */
    private function handlePayPalResponse($paymentResponse, Order $order)
    {
        if ($paymentResponse->checkoutUrl) {
            return redirect()->away($paymentResponse->checkoutUrl);
        }

        // If no checkout URL, show error
        return redirect()->route('payment.cancel.page')
            ->with('error', 'PayPal checkout URL not available');
    }

    /**
     * Verify payment status
     */
    public function verifyPayment(Request $request)
    {
        $request->validate([
            'payment_id' => 'required',
        ]);

        try {
            $payment = Payment::findOrFail($request->payment_id);

            // Ensure user can only verify their own payments
            if ($payment->order->user_id !== Auth::id()) {
                abort(403);
            }

            $verificationResponse = $this->gatewayManager->verifyPayment($payment);

            return response()->json([
                'success' => $verificationResponse->isSuccess(),
                'status' => $verificationResponse->status,
                'message' => $verificationResponse->message,
            ]);

        } catch (\Exception $e) {
            Log::error('Payment verification error', [
                'payment_id' => $request->payment_id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment verification failed',
            ], 500);
        }
    }

    /**
     * Get payment status
     */
    public function getPaymentStatus(Payment $payment)
    {
        // Ensure user can only check their own payments
        if ($payment->order->user_id !== Auth::id()) {
            abort(403);
        }

        try {
            $statusResponse = $this->gatewayManager->getPaymentStatus($payment);

            return response()->json([
                'success' => $statusResponse->isSuccess(),
                'status' => $statusResponse->status,
                'message' => $statusResponse->message,
                'payment_id' => $statusResponse->paymentId,
                'amount' => $statusResponse->amount,
                'currency' => $statusResponse->currency,
            ]);

        } catch (\Exception $e) {
            Log::error('Payment status check error', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get payment status',
            ], 500);
        }
    }

    /**
     * Handle successful payment (updated for multi-gateway support)
     */
    public function success(Request $request)
    {
        try {
            // Handle different gateway callbacks
            if ($request->has('order_id')) {
                // PayPal callback
                return $this->handlePayPalCallback($request);
            } elseif ($request->has('razorpay_payment_id')) {
                // Razorpay callback
                return $this->handleRazorpayCallback($request);
            } else {
                // Generic success page
                return redirect()->route('payment.success.page')
                    ->with('success', 'Payment completed successfully!');
            }

        } catch (\Exception $e) {
            Log::error('Payment success callback error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
            ]);

            return redirect()->route('payment.cancel.page')
                ->with('error', 'Payment processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Handle PayPal callback (backward compatibility)
     */
    private function handlePayPalCallback(Request $request)
    {
        $order = Order::findOrFail($request->order_id);

        // Ensure user can only process their own orders
        if ($order->user_id !== Auth::id()) {
            abort(403);
        }

        // Find the payment record
        $payment = Payment::where('order_id', $order->id)->latest()->first();

        if ($payment && $payment->gateway) {
            // Use the new gateway system to verify the payment
            $verificationResponse = $this->gatewayManager->verifyPayment($payment);

            if ($verificationResponse->isSuccess() && $verificationResponse->status === 'completed') {
                return redirect()->route('payment.success.page')
                    ->with('success', 'Payment completed successfully!');
            }
        }

        // Fallback to legacy PayPal handling if needed
        return $this->legacyPayPalSuccess($request);
    }

    /**
     * Handle Razorpay callback
     */
    private function handleRazorpayCallback(Request $request)
    {
        $request->validate([
            'razorpay_payment_id' => 'required',
            'razorpay_order_id' => 'required',
            'razorpay_signature' => 'required',
        ]);

        try {
            // Find payment by Razorpay order ID
            $payment = Payment::where('gateway_order_id', $request->razorpay_order_id)->first();

            if (!$payment) {
                throw new \Exception('Payment not found');
            }

            // Ensure user can only process their own payments
            if ($payment->order->user_id !== Auth::id()) {
                abort(403);
            }

            // Verify the payment using gateway manager
            $verificationResponse = $this->gatewayManager->verifyPayment($payment);

            if ($verificationResponse->isSuccess()) {
                return redirect()->route('payment.success.page')
                    ->with('success', 'Payment completed successfully!');
            }

            throw new \Exception($verificationResponse->message ?? 'Payment verification failed');

        } catch (\Exception $e) {
            Log::error('Razorpay callback error', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return redirect()->route('payment.cancel.page')
                ->with('error', 'Payment verification failed: ' . $e->getMessage());
        }
    }

    /**
     * Legacy PayPal success handling (for backward compatibility)
     */
    private function legacyPayPalSuccess(Request $request)
    {
        // Keep the original PayPal logic as fallback
        // This can be removed once all payments use the new system
        $order = Order::findOrFail($request->order_id);

        Log::info('Using legacy PayPal success handling', [
            'order_id' => $order->id,
            'token' => $request->token,
        ]);

        // Original PayPal capture logic would go here
        // For now, just redirect to success page
        return redirect()->route('payment.success.page')
            ->with('success', 'Payment completed successfully!');
    }

    /**
     * Handle cancelled payment
     */
    public function cancel(Request $request)
    {
        try {
            if ($request->has('order_id')) {
                $order = Order::findOrFail($request->order_id);

                // Ensure user can only process their own orders
                if ($order->user_id !== Auth::id()) {
                    abort(403);
                }

                // Update order status
                $order->update([
                    'status' => Order::STATUS_CANCELLED
                ]);

                // Update any pending payments
                Payment::where('order_id', $order->id)
                      ->where('payment_status', Payment::STATUS_PENDING)
                      ->update(['payment_status' => Payment::STATUS_FAILED]);

                Log::info('Payment cancelled', [
                    'order_id' => $order->id,
                    'user_id' => Auth::id(),
                ]);
            }

            return redirect()->route('payment.cancel.page')
                ->with('warning', 'Payment was cancelled.');

        } catch (\Exception $e) {
            Log::error('Payment cancel callback error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
            ]);

            return redirect()->route('payment.cancel.page')
                ->with('error', 'Error processing cancellation: ' . $e->getMessage());
        }
    }

    /**
     * Upload payment proof for QR bank transfer
     */
    public function uploadPaymentProof(Request $request, FileUploadSecurityService $fileSecurityService)
    {
        $request->validate([
            'payment_id' => 'required|exists:payments,id',
            'payment_proof' => 'required|file|max:5120', // 5MB max
            'csrf_token' => 'required|string'
        ]);

        try {
            $payment = Payment::findOrFail($request->payment_id);

            // Ensure user can only upload proof for their own payments
            if ($payment->order->user_id !== Auth::id()) {
                abort(403);
            }

            // Validate CSRF token
            $csrfValidation = $this->csrfService->validateAndConsumeToken(
                $request->csrf_token,
                Auth::id(),
                'file_upload',
                ['payment_id' => $payment->id]
            );

            if (!$csrfValidation['valid']) {
                Log::warning('Invalid CSRF token for file upload', [
                    'user_id' => Auth::id(),
                    'payment_id' => $payment->id,
                    'error' => $csrfValidation['error']
                ]);

                return redirect()->back()
                    ->with('error', 'Security validation failed. Please refresh and try again.');
            }

            // Validate uploaded file
            $file = $request->file('payment_proof');
            $validationResult = $fileSecurityService->validateUploadedFile($file, Auth::id());

            if (!$validationResult['valid']) {
                Log::warning('File upload validation failed', [
                    'user_id' => Auth::id(),
                    'payment_id' => $payment->id,
                    'error' => $validationResult['error'],
                    'file_name' => $file->getClientOriginalName()
                ]);

                return redirect()->back()
                    ->with('error', 'File validation failed: ' . $validationResult['error']);
            }

            // Securely store the file
            $storageResult = $fileSecurityService->securelyStoreFile($file, Auth::id(), $payment->id);

            if (!$storageResult['success']) {
                Log::error('File storage failed', [
                    'user_id' => Auth::id(),
                    'payment_id' => $payment->id,
                    'error' => $storageResult['error']
                ]);

                return redirect()->back()
                    ->with('error', 'Failed to store file securely. Please try again.');
            }

            // Create payment proof record
            $paymentProof = $payment->paymentProofs()->create([
                'file_path' => $storageResult['file_path'],
                'file_name' => $storageResult['secure_filename'],
                'file_size' => $storageResult['file_info']['size'],
                'mime_type' => $storageResult['file_info']['mime_type'],
                'verification_status' => 'pending',
                'uploaded_at' => now()
            ]);

            // Update payment status to indicate proof uploaded
            $payment->update([
                'payment_status' => Payment::STATUS_PENDING,
                'payment_proof' => $storageResult['file_path']
            ]);

            Log::info('Payment proof uploaded successfully', [
                'user_id' => Auth::id(),
                'payment_id' => $payment->id,
                'proof_id' => $paymentProof->id,
                'file_size' => $storageResult['file_info']['size']
            ]);

            return redirect()->back()
                ->with('success', 'Payment proof uploaded successfully. It will be reviewed by our team.');

        } catch (\Exception $e) {
            Log::error('Payment proof upload failed', [
                'user_id' => Auth::id(),
                'payment_id' => $request->payment_id,
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->with('error', 'Failed to upload payment proof. Please try again.');
        }
    }

    /**
     * Process a mock payment (for testing only)
     */
    // public function mockPayment(Request $request)
    // {
    //     try {
    //         $order = Order::findOrFail($request->order_id);

    //         // Ensure user can only pay for their own orders
    //         if ($order->user_id !== Auth::id()) {
    //             abort(403);
    //         }

    //         // Only allow payment for pending orders
    //         if ($order->status !== Order::STATUS_PENDING) {
    //             return redirect()->route('orders.show', $order)
    //                 ->with('error', 'This order cannot be processed.');
    //         }

    //         // Update order status
    //         $order->update([
    //             'status' => Order::STATUS_COMPLETED,
    //             'paid_at' => now(),
    //         ]);

    //         // Create a success message
    //         session()->flash('success', 'Mock payment completed successfully! Your order has been processed.');

    //         return redirect()->route('dashboard');
    //     } catch (\Exception $e) {
    //         \Log::error('Mock payment error', [
    //             'message' => $e->getMessage(),
    //             'trace' => $e->getTraceAsString()
    //         ]);

    //         return redirect()->route('payment.cancel.page')
    //             ->with('error', 'Mock payment failed: ' . $e->getMessage());
    //     }
    // }

    /**
     * Show Razorpay checkout page
     */
    public function razorpayCheckout(Order $order)
    {
        // Ensure user can only process their own orders
        if ($order->user_id !== Auth::id()) {
            abort(403);
        }

        // Only allow processing of pending orders
        if ($order->status !== Order::STATUS_PENDING) {
            return redirect()->route('user.orders.show', $order)
                ->with('error', 'This order cannot be processed.');
        }

        try {
            // Get Razorpay gateway service
            $razorpayGateway = PaymentGateway::where('name', 'razorpay')
                ->where('is_active', true)
                ->first();

            if (!$razorpayGateway) {
                return redirect()->route('user.payment.gateway-selection', $order)
                    ->with('error', 'Razorpay payment is not available at the moment.');
            }

            $gatewayService = $this->gatewayManager->getGatewayService('razorpay');
            
            // Create Razorpay order
            $paymentResponse = $gatewayService->createPayment($order);
            
            if (!$paymentResponse->success) {
                throw new PaymentGatewayException($paymentResponse->message);
            }

            // Get Razorpay configuration for frontend
            $razorpayConfig = [
                'key_id' => $razorpayGateway->configuration['key_id'] ?? '',
            ];

            $activeGateways = PaymentGateway::where('is_active', true)->get();

            return view('user.payment.razorpay-checkout', [
                'order' => $order,
                'razorpayOrder' => $paymentResponse->gatewayResponse,
                'razorpayConfig' => $razorpayConfig,
                'activeGateways' => $activeGateways
            ]);

        } catch (PaymentGatewayException $e) {
            Log::error('Razorpay checkout error', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('user.payment.gateway-selection', $order)
                ->with('error', 'Failed to initialize Razorpay payment: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Razorpay checkout unexpected error', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('user.payment.gateway-selection', $order)
                ->with('error', 'An unexpected error occurred. Please try again.');
        }
    }

    /**
     * Verify Razorpay payment
     */
    public function razorpayVerify(Request $request)
    {
        $request->validate([
            'order_id' => 'required|exists:orders,id',
            'razorpay_payment_id' => 'required|string',
            'razorpay_order_id' => 'required|string',
            'razorpay_signature' => 'required|string',
            'customer_details' => 'required|array',
            'csrf_token' => 'sometimes|string'
        ]);

        try {
            // Optional CSRF validation for Razorpay callbacks
            if ($request->has('csrf_token')) {
                $csrfValidation = $this->csrfService->validateAndConsumeToken(
                    $request->csrf_token,
                    Auth::id(),
                    'payment_verification',
                    ['order_id' => $request->order_id]
                );

                if (!$csrfValidation['valid']) {
                    Log::warning('Invalid CSRF token for Razorpay verification', [
                        'user_id' => Auth::id(),
                        'order_id' => $request->order_id,
                        'error' => $csrfValidation['error']
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Security validation failed'
                    ], 400);
                }
            }
        } catch (\Exception $e) {
            Log::error('CSRF validation error', [
                'error' => $e->getMessage(),
                'order_id' => $request->order_id
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Security validation failed'
            ], 400);
        }

        try {
            $order = Order::findOrFail($request->order_id);

            // Ensure user can only verify their own payments
            if ($order->user_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            // Get Razorpay gateway service
            $razorpayGateway = PaymentGateway::where('name', 'razorpay')->first();
            $gatewayService = $this->gatewayManager->getGatewayService('razorpay');

            // Verify payment signature
            $verificationData = [
                'razorpay_payment_id' => $request->razorpay_payment_id,
                'razorpay_order_id' => $request->razorpay_order_id,
                'razorpay_signature' => $request->razorpay_signature
            ];

            $verificationResponse = $gatewayService->verifyPayment($verificationData);

            if (!$verificationResponse->success) {
                throw new PaymentGatewayException($verificationResponse->message);
            }

            // Create or update payment record
            $payment = Payment::updateOrCreate(
                [
                    'order_id' => $order->id,
                    'gateway_payment_id' => $request->razorpay_payment_id
                ],
                [
                    'gateway_id' => $razorpayGateway->id,
                    'gateway_order_id' => $request->razorpay_order_id,
                    'amount' => $order->amount,
                    'currency' => $order->currency,
                    'payment_status' => Payment::STATUS_COMPLETED,
                    'payment_method' => 'razorpay',
                    'payment_details' => [
                        'customer_details' => $request->customer_details,
                        'razorpay_signature' => $request->razorpay_signature
                    ],
                    'paid_at' => now()
            ]);

            // Update order status
            $order->update([
                'status' => Order::STATUS_COMPLETED
            ]);

            // Activate plan for user
            if ($order->plan) {
                $this->activatePlanForUser($order->user, $order->plan);
            }

            Log::info('Razorpay payment verified successfully', [
                'order_id' => $order->id,
                'payment_id' => $payment->id,
                'razorpay_payment_id' => $request->razorpay_payment_id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment verified successfully',
                'redirect_url' => route('user.payment.success', $order->id)
            ]);

        } catch (PaymentGatewayException $e) {
            Log::error('Razorpay verification error', [
                'order_id' => $request->order_id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);

        } catch (\Exception $e) {
            Log::error('Razorpay verification unexpected error', [
                'order_id' => $request->order_id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment verification failed. Please contact support.'
            ], 500);
        }
    }

    /**
     * Handle Razorpay payment errors
     */
    public function razorpayError(Request $request)
    {
        $request->validate([
            'order_id' => 'required|exists:orders,id',
            'error' => 'required|array'
        ]);

        try {
            $order = Order::findOrFail($request->order_id);

            // Ensure user can only log errors for their own orders
            if ($order->user_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            // Create or update payment record with error
            Payment::updateOrCreate(
                [
                    'order_id' => $order->id,
                    'gateway_id' => PaymentGateway::where('name', 'razorpay')->first()->id
                ],
                [
                    'amount' => $order->amount,
                    'currency' => $order->currency,
                    'payment_status' => Payment::STATUS_FAILED,
                    'payment_method' => 'razorpay',
                    'failed_reason' => $request->error['description'] ?? 'Payment failed',
                    'payment_details' => [
                        'error' => $request->error
                    ]
                ]
            );

            // Update order status
            $order->update([
                'status' => Order::STATUS_FAILED,
                'payment_status' => Order::PAYMENT_STATUS_FAILED
            ]);

            Log::error('Razorpay payment failed', [
                'order_id' => $order->id,
                'error' => $request->error
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Error logged successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Razorpay error logging failed', [
                'order_id' => $request->order_id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to log error'
            ], 500);
        }
    }

    /**
     * Show payment success page
     */
    public function showSuccess(Order $order)
    {
        // Ensure user can only view their own order success
        if ($order->user_id !== Auth::id()) {
            abort(403);
        }

        $payment = $order->payments()->latest()->first();

        return view('user.payment.success', [
            'order' => $order,
            'payment' => $payment
        ]);
    }

    /**
     * Show payment failure page
     */
    public function failure(Order $order)
    {
        // Ensure user can only view their own order failure
        if ($order->user_id !== Auth::id()) {
            abort(403);
        }

        $payment = $order->payments()->latest()->first();
        $error = $payment->failed_reason ?? 'Payment failed for unknown reason';

        return view('user.payment.failure', [
            'order' => $order,
            'payment' => $payment,
            'error' => $error
        ]);
    }

    /**
     * Check payment status
     */
    public function checkStatus(Order $order)
    {
        // Ensure user can only check their own order status
        if ($order->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'status' => $order->payment_status,
            'order_status' => $order->status
        ]);
    }

    /**
     * Show gateway selection page
     */
    public function gatewaySelection(Order $order)
    {
        // Ensure user can only process their own orders
        if ($order->user_id !== Auth::id()) {
            abort(403);
        }

        // Only allow processing of pending orders
        if ($order->status !== Order::STATUS_PENDING) {
            return redirect()->route('user.orders.show', $order)
                ->with('error', 'This order cannot be processed.');
        }

        $activeGateways = PaymentGateway::where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('display_name')
            ->get();

        return view('user.payment.gateway-selection', [
            'order' => $order,
            'activeGateways' => $activeGateways
        ]);
    }

    /**
     * Process payment with selected gateway
     */
    public function processPayment(Request $request)
    {
        $request->validate([
            'order_id' => 'required|exists:orders,id',
            'payment_gateway' => 'required|exists:payment_gateways,id',
            'terms_accepted' => 'required|accepted',
            'csrf_token' => 'required|string'
        ]);

        try {
            // Validate CSRF token
            $csrfValidation = $this->csrfService->validateAndConsumeToken(
                $request->csrf_token,
                Auth::id(),
                'payment_creation',
                ['gateway_id' => $request->payment_gateway]
            );

            if (!$csrfValidation['valid']) {
                Log::warning('Invalid CSRF token for payment processing', [
                    'user_id' => Auth::id(),
                    'order_id' => $request->order_id,
                    'error' => $csrfValidation['error']
                ]);

                return redirect()->back()
                    ->with('error', 'Security validation failed. Please refresh and try again.');
            }

        } catch (\Exception $e) {
            Log::error('CSRF validation error', [
                'error' => $e->getMessage(),
                'order_id' => $request->order_id
            ]);

            return redirect()->back()
                ->with('error', 'Security validation failed. Please refresh and try again.');
        }

        try {
            $order = Order::findOrFail($request->order_id);

            // Ensure user can only process their own orders
            if ($order->user_id !== Auth::id()) {
                abort(403);
            }

            $gateway = PaymentGateway::findOrFail($request->payment_gateway);

            // Redirect to appropriate gateway checkout
            switch ($gateway->name) {
                case 'razorpay':
                    return redirect()->route('user.payment.razorpay.checkout', $order);
                case 'paypal':
                    return redirect()->route('payment.process', $order);
                case 'qr_bank_transfer':
                    return redirect()->route('user.payment.qr', $order);
                default:
                    return redirect()->back()
                        ->with('error', 'Selected payment method is not supported.');
            }

        } catch (\Exception $e) {
            Log::error('Payment processing error', [
                'order_id' => $request->order_id,
                'gateway_id' => $request->payment_gateway,
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->with('error', 'Failed to process payment. Please try again.');
        }
    }

    /**
     * Activate plan for user
     */
    private function activatePlanForUser($user, $plan)
    {
        // Implementation depends on your plan activation logic
        // This is a placeholder for the actual plan activation
        Log::info('Plan activated for user', [
            'user_id' => $user->id,
            'plan_id' => $plan->id
        ]);
    }

    /**
     * Show QR code payment interface
     */
    public function qrPayment(Order $order)
    {
        // Ensure user can only process their own orders
        if ($order->user_id !== Auth::id()) {
            abort(403);
        }

        // Only allow processing of pending orders
        if ($order->status !== Order::STATUS_PENDING) {
            return redirect()->route('user.orders.show', $order)
                ->with('error', 'This order cannot be processed.');
        }

        try {
            // Get QR Bank Transfer gateway service
            $qrGateway = PaymentGateway::where('name', 'qr_bank_transfer')
                ->where('is_active', true)
                ->first();

            if (!$qrGateway) {
                return redirect()->route('user.payment.gateway-selection', $order)
                    ->with('error', 'QR code payment is not available at the moment.');
            }

            $gatewayService = $this->gatewayManager->getGatewayService('qr_bank_transfer');
            
            // Create QR payment
            $paymentResponse = $gatewayService->createPayment($order);
            
            if (!$paymentResponse->success) {
                throw new PaymentGatewayException($paymentResponse->message);
            }

            // Create or get payment record
            $payment = Payment::firstOrCreate(
                [
                    'order_id' => $order->id,
                    'gateway_id' => $qrGateway->id
                ],
                [
                    'gateway_payment_id' => 'qr_' . $order->order_number,
                    'amount' => $order->amount,
                    'currency' => $order->currency,
                    'payment_status' => Payment::STATUS_PENDING,
                    'payment_method' => 'qr_bank_transfer',
                    'payment_details' => $paymentResponse->gatewayResponse
                ]
            );

            // Get bank details and QR code
            $bankDetails = $gatewayService->getBankDetails();
            $qrCode = $gatewayService->generateQRCode($order);

            return view('user.payment.qr-payment', [
                'order' => $order,
                'payment' => $payment,
                'bankDetails' => $bankDetails,
                'qrCode' => $qrCode
            ]);

        } catch (PaymentGatewayException $e) {
            Log::error('QR payment initialization error', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('user.payment.gateway-selection', $order)
                ->with('error', 'Failed to initialize QR payment: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('QR payment unexpected error', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('user.payment.gateway-selection', $order)
                ->with('error', 'An unexpected error occurred. Please try again.');
        }
    }
}
