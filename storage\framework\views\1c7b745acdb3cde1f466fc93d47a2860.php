<!-- Sidebar -->
<aside
    class="fixed inset-y-0 left-0 z-40 w-64 overflow-y-auto bg-bg-light dark:bg-bg-dark transform lg:transform-none lg:opacity-100 duration-300 lg:sticky lg:top-0 lg:left-0 lg:inset-0 shadow-xl h-screen transition-all"
    :class="{ 'translate-x-0 ease-out': sidebarOpen, '-translate-x-full ease-in': !sidebarOpen }" x-cloak
    x-transition:enter="transition ease-out duration-300" x-transition:enter-start="-translate-x-full"
    x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in duration-200"
    x-transition:leave-start="translate-x-0" x-transition:leave-end="-translate-x-full" aria-label="Main navigation">

    <!-- Header -->
    <div class="flex items-center justify-center py-6 border-b border-border-light dark:border-border-dark">
        <div class="flex items-center">
            <svg class="h-8 w-8 text-accent-light dark:text-accent-dark" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4">
                </path>
            </svg>
            <span
                class="ml-3 text-xl font-bold text-text-primary-light dark:text-text-primary-dark"><?php echo e(get_setting('site_name', 'Pincode Directory')); ?></span>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="mt-6 px-2" aria-label="Sidebar Navigation">
        <ul role="list" class="space-y-1">

            <!-- Dashboard -->
            <li>
                <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.dashboard') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                    href="<?php echo e(route('admin.dashboard')); ?>"
                    aria-current="<?php echo e(Request::routeIs('admin.dashboard') ? 'page' : 'false'); ?>">
                    <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                        </path>
                    </svg>
                    <span class="ml-3 text-sm font-medium">Dashboard</span>
                </a>
            </li>

            <!-- Content & Blog Management Section -->
            <li class="pt-4 mt-4 border-t border-border-light dark:border-border-dark" x-data="{ open: false }">
                <button type="button" @click="open = !open"
                    class="w-full flex items-center justify-between px-4 py-2 text-xs font-semibold text-accent-light dark:text-accent-dark uppercase tracking-wider focus:outline-none">
                    <span>Content & Blog</span>
                    <svg :class="{ 'transform rotate-90': open }" class="w-3 h-3 transition-transform duration-200"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
                <ul x-show="open" x-transition class="space-y-1">

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.pages*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.pages.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.pages*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Pages</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.landing-page*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.landing-page.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.landing-page*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Landing Page</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.blog.posts*') || Request::routeIs('admin.blog.index') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.blog.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.blog.posts*') || Request::routeIs('admin.blog.index') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-1m-4-4l-3 3m0 0l-3-3m3 3V3">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Blog Posts</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.blog.categories*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.blog.categories.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.blog.categories*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-1m-4-4l-3 3m0 0l-3-3m3 3V3">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Blog Categories</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.blog.tags*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.blog.tags.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.blog.tags*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Blog Tags</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.comments*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.comments.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.comments*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Comments</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.testimonials*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.testimonials.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.testimonials*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Testimonials</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.courier-dict*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.courier-dict.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.courier-dict*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Courier Dictionary</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Location Management Section -->
            <li class="pt-4 mt-4 border-t border-border-light dark:border-border-dark" x-data="{ open: false }">
                <button type="button" @click="open = !open"
                    class="w-full flex items-center justify-between px-4 py-2 text-xs font-semibold text-accent-light dark:text-accent-dark uppercase tracking-wider focus:outline-none">
                    <span>Location Management</span>
                    <svg :class="{ 'transform rotate-90': open }" class="w-3 h-3 transition-transform duration-200"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
                <ul x-show="open" x-transition class="space-y-1">

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.states*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.states.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.states*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">States</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.districts*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.districts.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.districts*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Districts</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.pincodes*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.pincodes.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.pincodes*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                </path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Pincodes</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Orders & Engagement Section -->
            <li class="pt-4 mt-4 border-t border-border-light dark:border-border-dark" x-data="{ open: false }">
                <button type="button" @click="open = !open"
                    class="w-full flex items-center justify-between px-4 py-2 text-xs font-semibold text-accent-light dark:text-accent-dark uppercase tracking-wider focus:outline-none">
                    <span>Orders & Engagement</span>
                    <svg :class="{ 'transform rotate-90': open }" class="w-3 h-3 transition-transform duration-200"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
                <ul x-show="open" x-transition class="space-y-1">

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.orders*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.orders.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.orders*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Orders</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.reviews*') || Request::routeIs('admin.admin.reviews*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.admin.reviews.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.reviews*') || Request::routeIs('admin.admin.reviews*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Reviews</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- User & Subscription Management Section -->
            <li class="pt-4 mt-4 border-t border-border-light dark:border-border-dark" x-data="{ open: false }">
                <button type="button" @click="open = !open"
                    class="w-full flex items-center justify-between px-4 py-2 text-xs font-semibold text-accent-light dark:text-accent-dark uppercase tracking-wider focus:outline-none">
                    <span>User & Subscription</span>
                    <svg :class="{ 'transform rotate-90': open }" class="w-3 h-3 transition-transform duration-200"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
                <ul x-show="open" x-transition class="space-y-1">

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.users*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.users.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.users*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Users</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.plans*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.plans.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.plans*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Plans</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.payments*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.payments.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.payments*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Payments</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.api-tokens*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.api-tokens.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.api-tokens*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">API Tokens</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- System Settings Section -->
            <li class="pt-4 mt-4 border-t border-border-light dark:border-border-dark" x-data="{ open: false }">
                <button type="button" @click="open = !open"
                    class="w-full flex items-center justify-between px-4 py-2 text-xs font-semibold text-accent-light dark:text-accent-dark uppercase tracking-wider focus:outline-none">
                    <span>System Settings</span>
                    <svg :class="{ 'transform rotate-90': open }" class="w-3 h-3 transition-transform duration-200"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
                <ul x-show="open" x-transition class="space-y-1">

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.settings*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.settings.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.settings*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                                </path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">General Settings</span>
                        </a>
                    </li>

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.mail-config*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.mail-config.index')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.mail-config*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Mail Configuration</span>
                        </a>
                    </li>

                    

                    <li>
                        <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.clear-cache*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                            href="<?php echo e(route('admin.clear-cache')); ?>"
                            aria-current="<?php echo e(Request::routeIs('admin.clear-cache*') ? 'page' : 'false'); ?>">
                            <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                </path>
                            </svg>
                            <span class="ml-3 text-sm font-medium">Clear Cache</span>
                        </a>
                    </li>
                </ul>
            </li>
        </ul>

        <!-- Account Section -->
        <div class="pt-6 mt-6 border-t border-border-light dark:border-border-dark">
            <h3
                class="px-4 mb-3 text-xs font-semibold text-accent-light dark:text-accent-dark uppercase tracking-wider">
                Account</h3>

            <a class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 <?php echo e(Request::routeIs('admin.profile*') ? 'bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm'); ?>"
                href="<?php echo e(route('admin.profile.index')); ?>"
                aria-current="<?php echo e(Request::routeIs('admin.profile*') ? 'page' : 'false'); ?>">
                <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                    </path>
                </svg>
                <span class="ml-3 text-sm font-medium">My Profile</span>
            </a>

            <form method="POST" action="<?php echo e(route('admin.logout')); ?>" class="mt-2">
                <?php echo csrf_field(); ?>
                <button type="submit"
                    class="flex w-full items-center px-4 py-3 rounded-lg text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:shadow-sm transition-all duration-200">
                    <svg class="w-5 h-5 text-accent-light dark:text-accent-dark" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1">
                        </path>
                    </svg>
                    <span class="ml-3 text-sm font-medium">Logout</span>
                </button>
            </form>
        </div>
    </nav>
</aside>
<?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/admin/layouts/sidebar.blade.php ENDPATH**/ ?>