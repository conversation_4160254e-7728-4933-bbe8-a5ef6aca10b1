<?php

use App\Models\PinCode;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

// Tests for validatePincode endpoint
test('validate pincode endpoint returns false for invalid pincode format', function () {
    $user = User::factory()->create();
    $response = $this->actingAs($user)->getJson('/api/validate-pincode/123');
    
    $response->assertStatus(200)
        ->assertJson([
            'data' => [
                'is_valid' => false,
                'pincode' => '123',
            ],
        ]);
});

test('validate pincode endpoint returns false for non-existent pincode', function () {
    $user = User::factory()->create();
    $response = $this->actingAs($user)->getJson('/api/validate-pincode/999999');
    
    $response->assertStatus(200)
        ->assertJson([
            'data' => [
                'is_valid' => false,
                'pincode' => '999999',
            ],
        ]);
});

test('validate pincode endpoint returns true for existing pincode', function () {
    $user = User::factory()->create();
    $pincode = PinCode::factory()->create(['pincode' => '123456']);

    $response = $this->actingAs($user)->getJson('/api/validate-pincode/123456');
    
    $response->assertStatus(200)
        ->assertJson([
            'data' => [
                'is_valid' => true,
                'pincode' => '123456',
            ],
        ]);
});

test('validate pincode endpoint handles alphanumeric pincode', function () {
    $user = User::factory()->create();
    $response = $this->actingAs($user)->getJson('/api/validate-pincode/abc123');
    
    $response->assertStatus(200)
        ->assertJson([
            'data' => [
                'is_valid' => false,
                'pincode' => 'abc123',
            ],
        ]);
});

// Tests for calculateDistanceBetweenTwoPincodes endpoint
test('calculate distance endpoint returns error for invalid first pincode', function () {
    $user = User::factory()->create();
    $pincode2 = PinCode::factory()->create(['pincode' => '400001']);

    $response = $this->actingAs($user)->getJson('/api/calculate-distance-between-two-pincodes/999999/400001');
    
    $response->assertStatus(422)
        ->assertJson(['error' => 'One or both pincodes are invalid']);
});

test('calculate distance endpoint returns error for invalid second pincode', function () {
    $user = User::factory()->create();
    $pincode1 = PinCode::factory()->create(['pincode' => '110001']);

    $response = $this->actingAs($user)->getJson('/api/calculate-distance-between-two-pincodes/110001/999999');
    
    $response->assertStatus(422)
        ->assertJson(['error' => 'One or both pincodes are invalid']);
});

test('calculate distance endpoint returns error for both invalid pincodes', function () {
    $user = User::factory()->create();
    $response = $this->actingAs($user)->getJson('/api/calculate-distance-between-two-pincodes/999999/888888');
    
    $response->assertStatus(422)
        ->assertJson(['error' => 'One or both pincodes are invalid']);
});

test('calculate distance endpoint successfully calculates distance between two pincodes', function () {
    $user = User::factory()->create();
    $pincode1 = PinCode::factory()->create([
        'pincode' => '110001',
        'latitude' => 28.6139,
        'longitude' => 77.2090,
    ]);
    $pincode2 = PinCode::factory()->create([
        'pincode' => '400001',
        'latitude' => 19.0760,
        'longitude' => 72.8777,
    ]);

    $response = $this->actingAs($user)->getJson('/api/calculate-distance-between-two-pincodes/110001/400001');
    
    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                'distance',
                'unit',
            ],
        ])
        ->assertJson([
            'data' => [
                'unit' => 'km',
            ],
        ]);
    
    // Verify distance is a positive number
    $responseData = $response->json('data');
    expect($responseData['distance'])->toBeGreaterThan(0);
});

test('calculate distance endpoint returns zero distance for same pincode', function () {
    $user = User::factory()->create();
    $pincode = PinCode::factory()->create([
        'pincode' => '110001',
        'latitude' => 28.6139,
        'longitude' => 77.2090,
    ]);

    $response = $this->actingAs($user)->getJson('/api/calculate-distance-between-two-pincodes/110001/110001');
    
    $response->assertStatus(200)
        ->assertJson([
            'data' => [
                'distance' => 0.0,
                'unit' => 'km',
            ],
        ]);
});

test('calculate distance endpoint handles pincodes with string coordinates', function () {
    $user = User::factory()->create();
    $pincode1 = PinCode::factory()->create([
        'pincode' => '110001',
        'latitude' => '28.6139',
        'longitude' => '77.2090',
    ]);
    $pincode2 = PinCode::factory()->create([
        'pincode' => '400001',
        'latitude' => '19.0760',
        'longitude' => '72.8777',
    ]);

    $response = $this->actingAs($user)->getJson('/api/calculate-distance-between-two-pincodes/110001/400001');
    
    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                'distance',
                'unit',
            ],
        ]);
    
    // Verify distance is calculated correctly even with string coordinates
    $responseData = $response->json('data');
    expect($responseData['distance'])->toBeGreaterThan(0);
}); 