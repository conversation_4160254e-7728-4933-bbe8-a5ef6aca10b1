<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Plan;
use App\Models\Order;
use App\Models\Payment;
use App\Models\PaymentGateway;
use App\Models\PaymentProof;
use App\Models\CurrencyRate;
use App\Models\WebhookLog;
use Illuminate\Database\Seeder;

class PaymentTestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating payment test data...');

        // Create payment gateways
        $this->createPaymentGateways();

        // Create currency rates
        $this->createCurrencyRates();

        // Create test users if they don't exist
        $users = $this->createTestUsers();

        // Create test plans
        $plans = $this->createTestPlans();

        // Create test orders and payments
        $this->createTestOrdersAndPayments($users, $plans);

        // Create webhook logs
        $this->createWebhookLogs();

        $this->command->info('Payment test data created successfully!');
    }

    /**
     * Create payment gateways
     */
    private function createPaymentGateways(): void
    {
        $this->command->info('Creating payment gateways...');

        // Razorpay Gateway
        PaymentGateway::factory()->razorpay()->active()->create([
            'sort_order' => 1,
            'is_default' => true
        ]);

        // PayPal Gateway
        PaymentGateway::factory()->paypal()->active()->create([
            'sort_order' => 2
        ]);

        // QR Bank Transfer Gateway
        PaymentGateway::factory()->qrBankTransfer()->active()->create([
            'sort_order' => 3
        ]);

        // Inactive test gateway
        PaymentGateway::factory()->razorpay()->inactive()->create([
            'display_name' => 'Razorpay (Inactive)',
            'sort_order' => 4
        ]);
    }

    /**
     * Create currency rates
     */
    private function createCurrencyRates(): void
    {
        $this->command->info('Creating currency rates...');

        // USD to other currencies
        CurrencyRate::factory()->usdToInr()->apiSource()->recent()->create();
        CurrencyRate::factory()->usdToEur()->apiSource()->recent()->create();
        CurrencyRate::factory()->usdToGbp()->apiSource()->recent()->create();

        // Reverse rates
        CurrencyRate::factory()->create([
            'from_currency' => 'INR',
            'to_currency' => 'USD',
            'rate' => 0.012048,
            'source' => 'api'
        ]);

        CurrencyRate::factory()->create([
            'from_currency' => 'EUR',
            'to_currency' => 'USD',
            'rate' => 1.176,
            'source' => 'api'
        ]);

        // Some stale rates for testing
        CurrencyRate::factory()->stale()->create([
            'from_currency' => 'USD',
            'to_currency' => 'JPY',
            'rate' => 150.25,
            'source' => 'manual'
        ]);
    }

    /**
     * Create test users
     */
    private function createTestUsers(): array
    {
        $this->command->info('Creating test users...');

        $users = [];

        // Create admin user if doesn't exist
        $admin = User::where('email', '<EMAIL>')->first();
        if (!$admin) {
            $admin = User::factory()->create([
                'name' => 'Test Admin',
                'email' => '<EMAIL>',
                'is_admin' => true
            ]);
        }
        $users[] = $admin;

        // Create regular test users
        for ($i = 1; $i <= 5; $i++) {
            $user = User::where('email', "user{$i}@test.com")->first();
            if (!$user) {
                $user = User::factory()->create([
                    'name' => "Test User {$i}",
                    'email' => "user{$i}@test.com"
                ]);
            }
            $users[] = $user;
        }

        return $users;
    }

    /**
     * Create test plans
     */
    private function createTestPlans(): array
    {
        $this->command->info('Creating test plans...');

        $plans = [];

        // Basic plan
        $plans[] = Plan::factory()->create([
            'name' => 'Basic Plan',
            'price' => 99.00,
            'currency' => 'USD',
            'request_limit' => 1000,
            'is_active' => true
        ]);

        // Premium plan
        $plans[] = Plan::factory()->create([
            'name' => 'Premium Plan',
            'price' => 199.00,
            'currency' => 'USD',
            'request_limit' => 5000,
            'is_active' => true
        ]);

        // INR plan
        $plans[] = Plan::factory()->create([
            'name' => 'India Basic',
            'price' => 999.00,
            'currency' => 'INR',
            'request_limit' => 1000,
            'is_active' => true
        ]);

        return $plans;
    }

    /**
     * Create test orders and payments
     */
    private function createTestOrdersAndPayments(array $users, array $plans): void
    {
        $this->command->info('Creating test orders and payments...');

        $gateways = PaymentGateway::all();

        foreach ($users as $user) {
            if ($user->is_admin) continue; // Skip admin user

            // Create 2-3 orders per user
            for ($i = 0; $i < rand(2, 3); $i++) {
                $plan = $plans[array_rand($plans)];
                $gateway = $gateways[array_rand($gateways->toArray())];

                $order = Order::factory()->create([
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'amount' => $plan->price,
                    'currency' => $plan->currency,
                    'status' => $this->faker->randomElement([
                        Order::STATUS_PENDING,
                        Order::STATUS_COMPLETED,
                        Order::STATUS_FAILED
                    ])
                ]);

                // Create payment for the order
                $paymentStatus = match ($order->status) {
                    Order::STATUS_COMPLETED => Payment::STATUS_COMPLETED,
                    Order::STATUS_FAILED => Payment::STATUS_FAILED,
                    default => Payment::STATUS_PENDING
                };

                $payment = Payment::factory()->create([
                    'order_id' => $order->id,
                    'gateway_id' => $gateway->id,
                    'amount' => $order->amount,
                    'currency' => $order->currency,
                    'payment_method' => $gateway->name,
                    'payment_status' => $paymentStatus,
                    'paid_at' => $paymentStatus === Payment::STATUS_COMPLETED ? now() : null
                ]);

                // Create payment proof for QR bank transfer payments
                if ($gateway->name === 'qr_bank_transfer' && $paymentStatus !== Payment::STATUS_FAILED) {
                    $proofStatus = match ($paymentStatus) {
                        Payment::STATUS_COMPLETED => PaymentProof::STATUS_APPROVED,
                        default => PaymentProof::STATUS_PENDING
                    };

                    PaymentProof::factory()->create([
                        'payment_id' => $payment->id,
                        'verification_status' => $proofStatus,
                        'verified_by' => $proofStatus === PaymentProof::STATUS_APPROVED ? $users[0]->id : null,
                        'verified_at' => $proofStatus === PaymentProof::STATUS_APPROVED ? now() : null
                    ]);
                }
            }
        }

        // Create some additional specific test scenarios
        $this->createSpecificTestScenarios($users, $plans, $gateways);
    }

    /**
     * Create specific test scenarios
     */
    private function createSpecificTestScenarios(array $users, array $plans, $gateways): void
    {
        $user = $users[1]; // Use second user
        $plan = $plans[0];
        $qrGateway = $gateways->where('name', 'qr_bank_transfer')->first();

        if ($qrGateway) {
            // Pending QR payment with proof
            $order = Order::factory()->create([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'status' => Order::STATUS_PENDING
            ]);

            $payment = Payment::factory()->create([
                'order_id' => $order->id,
                'gateway_id' => $qrGateway->id,
                'payment_method' => 'qr_bank_transfer',
                'payment_status' => Payment::STATUS_PENDING
            ]);

            PaymentProof::factory()->pending()->imageFile()->create([
                'payment_id' => $payment->id
            ]);

            // Rejected QR payment
            $rejectedOrder = Order::factory()->create([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'status' => Order::STATUS_FAILED
            ]);

            $rejectedPayment = Payment::factory()->create([
                'order_id' => $rejectedOrder->id,
                'gateway_id' => $qrGateway->id,
                'payment_method' => 'qr_bank_transfer',
                'payment_status' => Payment::STATUS_FAILED
            ]);

            PaymentProof::factory()->rejected()->create([
                'payment_id' => $rejectedPayment->id,
                'verified_by' => $users[0]->id
            ]);
        }
    }

    /**
     * Create webhook logs
     */
    private function createWebhookLogs(): void
    {
        $this->command->info('Creating webhook logs...');

        $gateways = PaymentGateway::whereIn('name', ['razorpay', 'paypal'])->get();
        $payments = Payment::whereIn('payment_method', ['razorpay', 'paypal'])->get();

        foreach ($gateways as $gateway) {
            // Create successful webhook logs
            WebhookLog::factory()->count(10)->processed()->create([
                'gateway_id' => $gateway->id,
                'payment_id' => $payments->random()->id ?? null
            ]);

            // Create failed webhook logs
            WebhookLog::factory()->count(3)->failed()->create([
                'gateway_id' => $gateway->id
            ]);

            // Create pending webhook logs
            WebhookLog::factory()->count(2)->pending()->create([
                'gateway_id' => $gateway->id
            ]);
        }

        // Create some specific webhook scenarios
        if ($razorpayGateway = $gateways->where('name', 'razorpay')->first()) {
            WebhookLog::factory()->razorpay()->signatureFailure()->create([
                'gateway_id' => $razorpayGateway->id
            ]);

            WebhookLog::factory()->razorpay()->highRetryCount()->create([
                'gateway_id' => $razorpayGateway->id
            ]);
        }
    }

    /**
     * Get faker instance
     */
    private function faker()
    {
        return \Faker\Factory::create();
    }

    private $faker;

    public function __construct()
    {
        $this->faker = $this->faker();
    }
}