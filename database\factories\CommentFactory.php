<?php

namespace Database\Factories;

use App\Models\Comment;
use App\Models\BlogPost;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class CommentFactory extends Factory
{
    protected $model = Comment::class;

    public function definition()
    {
        return [
            'content' => $this->faker->sentence(),
            // BlogPost ID must be provided by the test/seeder, or it will be null.
            'blog_post_id' => null,
            'user_id' => User::factory(),
            'guest_name' => null,
            'guest_email' => null,
            'parent_id' => null,
            'is_approved' => $this->faker->boolean(),
            'rejected_reason' => null,
            'moderated_at' => null,
            'moderated_by' => null,
        ];
    }

    /**
     * Create a guest comment
     */
    public function guest()
    {
        return $this->state(function (array $attributes) {
            return [
                'user_id' => null,
                'guest_name' => $this->faker->name(),
                'guest_email' => $this->faker->safeEmail(),
            ];
        });
    }
}