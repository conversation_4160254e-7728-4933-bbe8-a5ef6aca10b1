<?php

namespace Database\Factories;

use App\Models\ApiRequest;
use App\Models\User;
use App\Models\PersonalAccessToken;
use Illuminate\Database\Eloquent\Factories\Factory;

class ApiRequestFactory extends Factory
{
    protected $model = ApiRequest::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'personal_access_token_id' => PersonalAccessToken::factory(),
            'endpoint' => $this->faker->randomElement(['/api/pincodes', '/api/states', '/api/cities']),
            'method' => $this->faker->randomElement(['GET', 'POST', 'PUT', 'DELETE']),
            'status' => $this->faker->randomElement([200, 201, 400, 401, 403, 404, 500]),
            'ip_address' => $this->faker->ipv4,
            'request_data' => json_encode(['test' => 'data']),
            'response_data' => json_encode(['message' => 'success']),
        ];
    }
} 