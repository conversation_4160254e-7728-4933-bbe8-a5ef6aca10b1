<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PaymentGateway;
use App\Services\Payment\PaymentGatewayManager;
use App\Services\Payment\PaymentGatewayFactory;
use App\Services\Payment\CurrencyConversionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class PaymentGatewayController extends Controller
{
    protected PaymentGatewayManager $gatewayManager;
    protected CurrencyConversionService $currencyService;

    public function __construct(PaymentGatewayManager $gatewayManager, CurrencyConversionService $currencyService)
    {
        $this->gatewayManager = $gatewayManager;
        $this->currencyService = $currencyService;
    }

    /**
     * Display a listing of payment gateways.
     */
    public function index()
    {
        $gateways = PaymentGateway::ordered()->get();
        $connectionTests = $this->gatewayManager->testAllConnections();
        $supportedCurrencies = $this->currencyService->getSupportedCurrencies();

        return view('admin.payment-gateways.index', compact(
            'gateways', 'connectionTests', 'supportedCurrencies'
        ));
    }

    /**
     * Show the form for creating a new payment gateway.
     */
    public function create()
    {
        $supportedCurrencies = $this->currencyService->getSupportedCurrencies();
        $availableGateways = PaymentGatewayFactory::getAvailableGateways();
        
        return view('admin.payment-gateways.create', compact(
            'supportedCurrencies', 'availableGateways'
        ));
    }

    /**
     * Store a newly created payment gateway.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:50|unique:payment_gateways,name',
            'display_name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'configuration' => 'required|array',
            'supported_currencies' => 'required|array|min:1',
            'supported_currencies.*' => 'string|size:3',
            'sort_order' => 'integer|min:0',
            'webhook_url' => 'nullable|url|max:255',
            'webhook_secret' => 'nullable|string|max:255',
            'logo_url' => 'nullable|url|max:255',
        ]);

        try {
            // If setting as default, unset other defaults
            if ($request->boolean('is_default')) {
                PaymentGateway::where('is_default', true)->update(['is_default' => false]);
            }

            $gateway = PaymentGateway::create([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'description' => $request->description,
                'is_active' => $request->boolean('is_active', true),
                'is_default' => $request->boolean('is_default', false),
                'configuration' => $request->configuration,
                'supported_currencies' => $request->supported_currencies,
                'sort_order' => $request->integer('sort_order', 0),
                'webhook_url' => $request->webhook_url,
                'webhook_secret' => $request->webhook_secret,
                'logo_url' => $request->logo_url,
            ]);

            Log::info('Payment gateway created', [
                'gateway_id' => $gateway->id,
                'name' => $gateway->name,
                'admin_id' => auth()->id(),
            ]);

            return redirect()->route('admin.payment-gateways.index')
                ->with('success', 'Payment gateway created successfully.');

        } catch (\Exception $e) {
            Log::error('Failed to create payment gateway', [
                'error' => $e->getMessage(),
                'request_data' => $request->except(['configuration', 'webhook_secret']),
                'admin_id' => auth()->id(),
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create payment gateway: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified payment gateway.
     */
    public function show(PaymentGateway $paymentGateway)
    {
        $stats = $this->gatewayManager->getGatewayStats($paymentGateway);
        $connectionTest = $this->gatewayManager->testGatewayConnection($paymentGateway);
        $recentPayments = $paymentGateway->payments()
                                       ->with('order.user')
                                       ->latest()
                                       ->limit(10)
                                       ->get();

        return view('admin.payment-gateways.show', compact(
            'paymentGateway', 'stats', 'connectionTest', 'recentPayments'
        ));
    }

    /**
     * Show the form for editing the specified payment gateway.
     */
    public function edit(PaymentGateway $paymentGateway)
    {
        $supportedCurrencies = $this->currencyService->getSupportedCurrencies();
        $validationErrors = $paymentGateway->validateConfiguration();
        
        return view('admin.payment-gateways.edit', compact(
            'paymentGateway', 'supportedCurrencies', 'validationErrors'
        ));
    }

    /**
     * Update the specified payment gateway.
     */
    public function update(Request $request, PaymentGateway $paymentGateway)
    {
        $request->validate([
            'display_name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'configuration' => 'required|array',
            'supported_currencies' => 'required|array|min:1',
            'supported_currencies.*' => 'string|size:3',
            'sort_order' => 'integer|min:0',
            'webhook_url' => 'nullable|url|max:255',
            'webhook_secret' => 'nullable|string|max:255',
            'logo_url' => 'nullable|url|max:255',
        ]);

        try {
            // If setting as default, unset other defaults
            if ($request->boolean('is_default') && !$paymentGateway->is_default) {
                PaymentGateway::where('is_default', true)->update(['is_default' => false]);
            }

            $paymentGateway->update([
                'display_name' => $request->display_name,
                'description' => $request->description,
                'is_active' => $request->boolean('is_active'),
                'is_default' => $request->boolean('is_default'),
                'configuration' => $request->configuration,
                'supported_currencies' => $request->supported_currencies,
                'sort_order' => $request->integer('sort_order'),
                'webhook_url' => $request->webhook_url,
                'webhook_secret' => $request->webhook_secret,
                'logo_url' => $request->logo_url,
            ]);

            Log::info('Payment gateway updated', [
                'gateway_id' => $paymentGateway->id,
                'name' => $paymentGateway->name,
                'admin_id' => auth()->id(),
            ]);

            return redirect()->route('admin.payment-gateways.index')
                ->with('success', 'Payment gateway updated successfully.');

        } catch (\Exception $e) {
            Log::error('Failed to update payment gateway', [
                'gateway_id' => $paymentGateway->id,
                'error' => $e->getMessage(),
                'admin_id' => auth()->id(),
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update payment gateway: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified payment gateway.
     */
    public function destroy(PaymentGateway $paymentGateway)
    {
        try {
            // Check if gateway has payments
            $paymentCount = $paymentGateway->payments()->count();
            
            if ($paymentCount > 0) {
                return redirect()->back()
                    ->with('error', "Cannot delete gateway with {$paymentCount} existing payments.");
            }

            // If this was the default gateway, set another as default
            if ($paymentGateway->is_default) {
                $newDefault = PaymentGateway::where('is_active', true)
                                          ->where('id', '!=', $paymentGateway->id)
                                          ->orderBy('sort_order')
                                          ->first();
                
                if ($newDefault) {
                    $newDefault->setAsDefault();
                }
            }

            $gatewayName = $paymentGateway->name;
            $paymentGateway->delete();

            Log::info('Payment gateway deleted', [
                'gateway_name' => $gatewayName,
                'admin_id' => auth()->id(),
            ]);

            return redirect()->route('admin.payment-gateways.index')
                ->with('success', 'Payment gateway deleted successfully.');

        } catch (\Exception $e) {
            Log::error('Failed to delete payment gateway', [
                'gateway_id' => $paymentGateway->id,
                'error' => $e->getMessage(),
                'admin_id' => auth()->id(),
            ]);

            return redirect()->back()
                ->with('error', 'Failed to delete payment gateway: ' . $e->getMessage());
        }
    }

    /**
     * Toggle gateway status (enable/disable).
     */
    public function toggleStatus(PaymentGateway $paymentGateway)
    {
        try {
            if ($paymentGateway->is_active) {
                $result = $this->gatewayManager->disableGateway($paymentGateway);
                $message = $result ? 'Gateway disabled successfully.' : 'Failed to disable gateway.';
            } else {
                $result = $this->gatewayManager->enableGateway($paymentGateway);
                $message = $result ? 'Gateway enabled successfully.' : 'Failed to enable gateway.';
            }

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'is_active' => $paymentGateway->fresh()->is_active,
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => $message,
            ], 400);

        } catch (\Exception $e) {
            Log::error('Failed to toggle gateway status', [
                'gateway_id' => $paymentGateway->id,
                'error' => $e->getMessage(),
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle gateway status: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Set gateway as default.
     */
    public function setDefault(PaymentGateway $paymentGateway)
    {
        try {
            if (!$paymentGateway->is_active) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot set inactive gateway as default.',
                ], 400);
            }

            $paymentGateway->setAsDefault();

            Log::info('Default gateway changed', [
                'gateway_id' => $paymentGateway->id,
                'gateway_name' => $paymentGateway->name,
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Default gateway updated successfully.',
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to set default gateway', [
                'gateway_id' => $paymentGateway->id,
                'error' => $e->getMessage(),
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to set default gateway: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test gateway connection.
     */
    public function testConnection(PaymentGateway $paymentGateway)
    {
        try {
            $result = $this->gatewayManager->testGatewayConnection($paymentGateway);

            Log::info('Gateway connection tested', [
                'gateway_id' => $paymentGateway->id,
                'result' => $result,
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => $result,
                'message' => $result ? 'Connection successful.' : 'Connection failed.',
                'tested_at' => now()->toISOString(),
            ]);

        } catch (\Exception $e) {
            Log::error('Gateway connection test failed', [
                'gateway_id' => $paymentGateway->id,
                'error' => $e->getMessage(),
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update gateway sort order.
     */
    public function updateSortOrder(Request $request)
    {
        $request->validate([
            'gateways' => 'required|array',
            'gateways.*.id' => 'required|exists:payment_gateways,id',
            'gateways.*.sort_order' => 'required|integer|min:0',
        ]);

        try {
            foreach ($request->gateways as $gatewayData) {
                PaymentGateway::where('id', $gatewayData['id'])
                            ->update(['sort_order' => $gatewayData['sort_order']]);
            }

            Log::info('Gateway sort order updated', [
                'gateways_count' => count($request->gateways),
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Sort order updated successfully.',
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update gateway sort order', [
                'error' => $e->getMessage(),
                'admin_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update sort order: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get gateway configuration template.
     */
    public function getConfigTemplate(Request $request)
    {
        $request->validate([
            'gateway_name' => 'required|string|in:paypal,razorpay,qr_bank_transfer',
        ]);

        $templates = [
            'paypal' => [
                'mode' => 'sandbox',
                'sandbox_client_id' => '',
                'sandbox_client_secret' => '',
                'live_client_id' => '',
                'live_client_secret' => '',
                'currency' => 'USD',
                'payment_action' => 'Sale'
            ],
            'razorpay' => [
                'key_id' => '',
                'key_secret' => '',
                'webhook_secret' => '',
                'currency' => 'INR'
            ],
            'qr_bank_transfer' => [
                'bank_name' => '',
                'account_name' => '',
                'account_number' => '',
                'ifsc_code' => '',
                'branch_name' => '',
                'currency' => 'INR',
                'qr_code_data' => ''
            ],
        ];

        return response()->json([
            'success' => true,
            'template' => $templates[$request->gateway_name] ?? [],
        ]);
    }

    /**
     * Validate gateway configuration.
     */
    public function validateConfig(Request $request, PaymentGateway $paymentGateway)
    {
        try {
            $errors = $paymentGateway->validateConfiguration();

            return response()->json([
                'success' => empty($errors),
                'errors' => $errors,
                'message' => empty($errors) ? 'Configuration is valid.' : 'Configuration has errors.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Configuration validation failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get gateway statistics.
     */
    public function getStats(PaymentGateway $paymentGateway, Request $request)
    {
        $request->validate([
            'from' => 'nullable|date',
            'to' => 'nullable|date|after_or_equal:from',
        ]);

        try {
            $from = $request->from ? new \DateTime($request->from) : null;
            $to = $request->to ? new \DateTime($request->to) : null;

            $stats = $this->gatewayManager->getGatewayStats($paymentGateway, $from, $to);

            return response()->json([
                'success' => true,
                'stats' => $stats,
                'period' => [
                    'from' => $from?->format('Y-m-d'),
                    'to' => $to?->format('Y-m-d'),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get statistics: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export gateway configuration.
     */
    public function exportConfig(PaymentGateway $paymentGateway)
    {
        try {
            $config = [
                'name' => $paymentGateway->name,
                'display_name' => $paymentGateway->display_name,
                'description' => $paymentGateway->description,
                'supported_currencies' => $paymentGateway->supported_currencies,
                'sort_order' => $paymentGateway->sort_order,
                'webhook_url' => $paymentGateway->webhook_url,
                'logo_url' => $paymentGateway->logo_url,
                // Note: We don't export sensitive configuration data
                'exported_at' => now()->toISOString(),
                'exported_by' => auth()->user()->name,
            ];

            $filename = "gateway-{$paymentGateway->name}-config-" . now()->format('Y-m-d-H-i-s') . '.json';

            return response()->json($config)
                ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export configuration: ' . $e->getMessage(),
            ], 500);
        }
    }
}