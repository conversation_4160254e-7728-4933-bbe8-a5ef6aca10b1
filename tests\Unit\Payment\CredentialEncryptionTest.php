<?php

namespace Tests\Unit\Payment;

use App\Services\Payment\CredentialEncryptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Crypt;
use Tests\TestCase;

class CredentialEncryptionTest extends TestCase
{
    use RefreshDatabase;

    protected CredentialEncryptionService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new CredentialEncryptionService();
    }

    public function test_encrypts_credentials()
    {
        $credentials = [
            'api_key' => 'secret_api_key_123',
            'secret_key' => 'very_secret_key_456',
            'webhook_secret' => 'webhook_secret_789'
        ];

        $encrypted = $this->service->encryptCredentials($credentials);

        $this->assertIsArray($encrypted);
        $this->assertArrayHasKey('api_key', $encrypted);
        $this->assertArrayHasKey('secret_key', $encrypted);
        $this->assertArrayHasKey('webhook_secret', $encrypted);

        // Verify values are encrypted (not plain text)
        $this->assertNotEquals('secret_api_key_123', $encrypted['api_key']);
        $this->assertNotEquals('very_secret_key_456', $encrypted['secret_key']);
        $this->assertNotEquals('webhook_secret_789', $encrypted['webhook_secret']);
    }

    public function test_decrypts_credentials()
    {
        $originalCredentials = [
            'api_key' => 'secret_api_key_123',
            'secret_key' => 'very_secret_key_456',
            'webhook_secret' => 'webhook_secret_789'
        ];

        $encrypted = $this->service->encryptCredentials($originalCredentials);
        $decrypted = $this->service->decryptCredentials($encrypted);

        $this->assertEquals($originalCredentials, $decrypted);
    }

    public function test_handles_empty_credentials()
    {
        $emptyCredentials = [];
        
        $encrypted = $this->service->encryptCredentials($emptyCredentials);
        $this->assertIsArray($encrypted);
        $this->assertEmpty($encrypted);

        $decrypted = $this->service->decryptCredentials($encrypted);
        $this->assertIsArray($decrypted);
        $this->assertEmpty($decrypted);
    }

    public function test_handles_null_values()
    {
        $credentialsWithNull = [
            'api_key' => 'secret_key',
            'optional_field' => null,
            'another_key' => 'another_value'
        ];

        $encrypted = $this->service->encryptCredentials($credentialsWithNull);
        $decrypted = $this->service->decryptCredentials($encrypted);

        $this->assertEquals($credentialsWithNull, $decrypted);
        $this->assertNull($decrypted['optional_field']);
    }

    public function test_encrypts_only_sensitive_fields()
    {
        $credentials = [
            'api_key' => 'secret_key',
            'secret_key' => 'secret_value',
            'webhook_secret' => 'webhook_value',
            'public_key' => 'public_value',
            'mode' => 'test',
            'timeout' => 30
        ];

        $encrypted = $this->service->encryptCredentials($credentials);

        // Sensitive fields should be encrypted
        $this->assertNotEquals('secret_key', $encrypted['api_key']);
        $this->assertNotEquals('secret_value', $encrypted['secret_key']);
        $this->assertNotEquals('webhook_value', $encrypted['webhook_secret']);

        // Non-sensitive fields should remain as-is
        $this->assertEquals('public_value', $encrypted['public_key']);
        $this->assertEquals('test', $encrypted['mode']);
        $this->assertEquals(30, $encrypted['timeout']);
    }

    public function test_identifies_sensitive_fields()
    {
        $sensitiveFields = [
            'api_key',
            'secret_key',
            'private_key',
            'webhook_secret',
            'client_secret',
            'password',
            'token',
            'access_token',
            'refresh_token'
        ];

        foreach ($sensitiveFields as $field) {
            $this->assertTrue(
                $this->service->isSensitiveField($field),
                "Field '{$field}' should be identified as sensitive"
            );
        }

        $nonSensitiveFields = [
            'public_key',
            'client_id',
            'mode',
            'timeout',
            'endpoint',
            'version'
        ];

        foreach ($nonSensitiveFields as $field) {
            $this->assertFalse(
                $this->service->isSensitiveField($field),
                "Field '{$field}' should not be identified as sensitive"
            );
        }
    }

    public function test_handles_nested_credentials()
    {
        $nestedCredentials = [
            'razorpay' => [
                'key_id' => 'rzp_test_123',
                'key_secret' => 'secret_123',
                'webhook_secret' => 'webhook_123'
            ],
            'paypal' => [
                'client_id' => 'paypal_client_123',
                'client_secret' => 'paypal_secret_123',
                'mode' => 'sandbox'
            ]
        ];

        $encrypted = $this->service->encryptCredentials($nestedCredentials);
        $decrypted = $this->service->decryptCredentials($encrypted);

        $this->assertEquals($nestedCredentials, $decrypted);

        // Verify nested sensitive fields are encrypted
        $this->assertNotEquals('secret_123', $encrypted['razorpay']['key_secret']);
        $this->assertNotEquals('paypal_secret_123', $encrypted['paypal']['client_secret']);

        // Verify nested non-sensitive fields are not encrypted
        $this->assertEquals('rzp_test_123', $encrypted['razorpay']['key_id']);
        $this->assertEquals('sandbox', $encrypted['paypal']['mode']);
    }

    public function test_handles_encryption_errors_gracefully()
    {
        // Mock Crypt to throw an exception
        Crypt::shouldReceive('encrypt')
             ->andThrow(new \Exception('Encryption failed'));

        $credentials = ['api_key' => 'test_key'];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to encrypt credentials');

        $this->service->encryptCredentials($credentials);
    }

    public function test_handles_decryption_errors_gracefully()
    {
        $invalidEncryptedData = [
            'api_key' => 'invalid_encrypted_data'
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to decrypt credentials');

        $this->service->decryptCredentials($invalidEncryptedData);
    }

    public function test_validates_credential_structure()
    {
        $validCredentials = [
            'api_key' => 'test_key',
            'secret_key' => 'test_secret'
        ];

        $this->assertTrue($this->service->validateCredentialStructure($validCredentials));

        $invalidCredentials = 'not_an_array';
        $this->assertFalse($this->service->validateCredentialStructure($invalidCredentials));
    }

    public function test_masks_sensitive_data_for_logging()
    {
        $credentials = [
            'api_key' => 'secret_api_key_123456789',
            'secret_key' => 'very_secret_key_987654321',
            'client_id' => 'public_client_id_123',
            'mode' => 'test'
        ];

        $masked = $this->service->maskSensitiveData($credentials);

        // Sensitive fields should be masked
        $this->assertEquals('secret_a***********789', $masked['api_key']);
        $this->assertEquals('very_se***********321', $masked['secret_key']);

        // Non-sensitive fields should remain unchanged
        $this->assertEquals('public_client_id_123', $masked['client_id']);
        $this->assertEquals('test', $masked['mode']);
    }

    public function test_generates_encryption_key_hash()
    {
        $credentials = ['api_key' => 'test_key'];
        
        $hash1 = $this->service->generateCredentialHash($credentials);
        $hash2 = $this->service->generateCredentialHash($credentials);

        // Same credentials should produce same hash
        $this->assertEquals($hash1, $hash2);

        // Different credentials should produce different hash
        $differentCredentials = ['api_key' => 'different_key'];
        $hash3 = $this->service->generateCredentialHash($differentCredentials);
        $this->assertNotEquals($hash1, $hash3);
    }

    public function test_rotates_encryption_keys()
    {
        $credentials = [
            'api_key' => 'test_key',
            'secret_key' => 'test_secret'
        ];

        $encrypted1 = $this->service->encryptCredentials($credentials);
        
        // Simulate key rotation
        $this->service->rotateEncryptionKey();
        
        $encrypted2 = $this->service->encryptCredentials($credentials);

        // Encrypted values should be different after key rotation
        $this->assertNotEquals($encrypted1['api_key'], $encrypted2['api_key']);

        // But both should decrypt to the same original values
        $decrypted1 = $this->service->decryptCredentials($encrypted1);
        $decrypted2 = $this->service->decryptCredentials($encrypted2);

        $this->assertEquals($credentials, $decrypted1);
        $this->assertEquals($credentials, $decrypted2);
    }

    public function test_audit_logs_encryption_operations()
    {
        $credentials = ['api_key' => 'test_key'];

        // Enable audit logging
        $this->service->enableAuditLogging();

        $this->service->encryptCredentials($credentials);
        $auditLogs = $this->service->getAuditLogs();

        $this->assertNotEmpty($auditLogs);
        $this->assertStringContainsString('encrypt', $auditLogs[0]['operation']);
    }

    public function test_validates_encryption_integrity()
    {
        $credentials = [
            'api_key' => 'test_key',
            'secret_key' => 'test_secret'
        ];

        $encrypted = $this->service->encryptCredentials($credentials);
        
        // Verify integrity
        $this->assertTrue($this->service->verifyEncryptionIntegrity($encrypted));

        // Tamper with encrypted data
        $encrypted['api_key'] = 'tampered_data';
        
        // Integrity check should fail
        $this->assertFalse($this->service->verifyEncryptionIntegrity($encrypted));
    }

    public function test_bulk_encryption_operations()
    {
        $multipleCredentials = [
            'gateway1' => [
                'api_key' => 'key1',
                'secret_key' => 'secret1'
            ],
            'gateway2' => [
                'api_key' => 'key2',
                'secret_key' => 'secret2'
            ]
        ];

        $encrypted = $this->service->bulkEncryptCredentials($multipleCredentials);
        $decrypted = $this->service->bulkDecryptCredentials($encrypted);

        $this->assertEquals($multipleCredentials, $decrypted);
    }

    public function test_encryption_performance()
    {
        $largeCredentials = [];
        for ($i = 0; $i < 100; $i++) {
            $largeCredentials["key_{$i}"] = "secret_value_{$i}";
        }

        $startTime = microtime(true);
        $encrypted = $this->service->encryptCredentials($largeCredentials);
        $encryptTime = microtime(true) - $startTime;

        $startTime = microtime(true);
        $decrypted = $this->service->decryptCredentials($encrypted);
        $decryptTime = microtime(true) - $startTime;

        // Encryption/decryption should complete within reasonable time (1 second)
        $this->assertLessThan(1.0, $encryptTime, 'Encryption took too long');
        $this->assertLessThan(1.0, $decryptTime, 'Decryption took too long');

        $this->assertEquals($largeCredentials, $decrypted);
    }
}