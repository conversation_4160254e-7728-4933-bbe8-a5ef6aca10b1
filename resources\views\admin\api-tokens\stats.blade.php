{{-- <x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-text-primary-light dark:text-text-primary-dark leading-tight">
                {{ __('API Token Statistics') }}
            </h2>
            <a href="{{ route('admin.api-tokens.index') }}" class="inline-flex items-center px-4 py-2 bg-bg-secondary-light dark:bg-bg-secondary-dark border border-border-light dark:border-border-dark rounded-md font-semibold text-xs text-text-primary-light dark:text-text-primary-dark uppercase tracking-widest hover:bg-bg-secondary-light-hover dark:hover:bg-bg-secondary-dark-hover focus:outline-none focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 disabled:opacity-25 transition">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                {{ __('Back to List') }}
            </a>
        </div>
    </x-slot> --}}

<div class="bg-bg-light dark:bg-bg-dark rounded-lg shadow-md overflow-hidden mb-6 border border-border-light dark:border-border-dark">
    <div class="px-6 py-4 bg-primary-light dark:bg-primary-dark text-white">
        <h3 class="text-lg font-semibold">API Token Statistics</h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Total Tokens -->
            <div class="bg-bg-secondary-light dark:bg-bg-secondary-dark p-4 rounded-lg border border-border-light dark:border-border-dark">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-primary-light/10 dark:bg-primary-dark/10">
                        <svg class="h-6 w-6 text-primary-light dark:text-primary-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Total Tokens</h3>
                        <p class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">{{ $stats['total_tokens'] }}</p>
                    </div>
                </div>
            </div>

            <!-- Active Tokens -->
            <div class="bg-bg-secondary-light dark:bg-bg-secondary-dark p-4 rounded-lg border border-border-light dark:border-border-dark">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-500/10 dark:bg-green-500/20">
                        <svg class="h-6 w-6 text-green-500 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Active Tokens</h3>
                        <p class="text-lg font-semibold text-green-600 dark:text-green-400">{{ $stats['active_tokens'] }}</p>
                    </div>
                </div>
            </div>

            <!-- Expired Tokens -->
            <div class="bg-bg-secondary-light dark:bg-bg-secondary-dark p-4 rounded-lg border border-border-light dark:border-border-dark">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-500/10 dark:bg-red-500/20">
                        <svg class="h-6 w-6 text-red-500 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Expired Tokens</h3>
                        <p class="text-lg font-semibold text-red-600 dark:text-red-400">{{ $stats['expired_tokens'] }}</p>
                    </div>
                </div>
            </div>

            <!-- Successful Requests -->
            <div class="bg-bg-secondary-light dark:bg-bg-secondary-dark p-4 rounded-lg border border-border-light dark:border-border-dark">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-500/10 dark:bg-green-500/20">
                        <svg class="h-6 w-6 text-green-500 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Successful Requests</h3>
                        <p class="text-lg font-semibold text-green-600 dark:text-green-400">{{ $stats['successful_requests'] }}</p>
                    </div>
                </div>
            </div>

            <!-- Failed Requests -->
            <div class="bg-bg-secondary-light dark:bg-bg-secondary-dark p-4 rounded-lg border border-border-light dark:border-border-dark">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-500/10 dark:bg-red-500/20">
                        <svg class="h-6 w-6 text-red-500 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Failed Requests</h3>
                        <p class="text-lg font-semibold text-red-600 dark:text-red-400">{{ $stats['failed_requests'] }}</p>
                    </div>
                </div>
            </div>

            <!-- Users with Tokens -->
            <div class="bg-bg-secondary-light dark:bg-bg-secondary-dark p-4 rounded-lg border border-border-light dark:border-border-dark">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-primary-light/10 dark:bg-primary-dark/10">
                        <svg class="h-6 w-6 text-primary-light dark:text-primary-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Users with Tokens</h3>
                        <p class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">{{ $stats['users_with_tokens'] }}</p>
                    </div>
                </div>
            </div>

            <!-- Total API Requests -->
            <div class="bg-bg-secondary-light dark:bg-bg-secondary-dark p-4 rounded-lg border border-border-light dark:border-border-dark">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-primary-light/10 dark:bg-primary-dark/10">
                        <svg class="h-6 w-6 text-primary-light dark:text-primary-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Total API Requests</h3>
                        <p class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">{{ $stats['total_requests'] }}</p>
                    </div>
                </div>
            </div>

            <!-- Average Requests per Token -->
            <div class="bg-bg-secondary-light dark:bg-bg-secondary-dark p-4 rounded-lg border border-border-light dark:border-border-dark">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-primary-light/10 dark:bg-primary-dark/10">
                        <svg class="h-6 w-6 text-primary-light dark:text-primary-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Avg Requests per Token</h3>
                        <p class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">{{ $stats['avg_requests_per_token'] }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- </x-app-layout> --}}