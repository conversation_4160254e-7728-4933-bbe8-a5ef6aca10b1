@extends('admin.layouts.admin')

@section('title', 'Orders')

@section('page-title', 'Orders')

@section('content')
    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">All Orders</h2>
            </div>

            @if (session('success'))
                <div
                    class="mb-4 bg-green-100 dark:bg-green-900/20 border-l-4 border-green-500 dark:border-green-700 text-green-700 dark:text-green-200 p-4">
                    {{ session('success') }}
                </div>
            @endif

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-border-light dark:divide-border-dark hidden md:table">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Order Number</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">User</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider hidden md:table-cell">Plan</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider hidden md:table-cell">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider hidden md:table-cell">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                        @forelse($orders as $order)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-text-primary-light dark:text-text-primary-dark">{{ $order->order_number }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">{{ $order->user->name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark hidden md:table-cell">{{ $order->plan_id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark hidden md:table-cell">₹{{ number_format($order->amount, 2) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $order->status === 'completed' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' : ($order->status === 'cancelled' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400' : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400') }}">{{ ucfirst($order->status) }}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark hidden md:table-cell">{{ $order->created_at->format('M d, Y') }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{{ route('admin.orders.show', $order) }}" class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light mr-3">View</a>
                                    <form action="{{ route('admin.orders.destroy', $order) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to delete this order?')">Delete</button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="px-6 py-4 text-center text-text-secondary-light dark:text-text-secondary-dark">No orders found.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
                <!-- Mobile Card View -->
                <div class="md:hidden">
                    @forelse($orders as $order)
                        <div class="mb-4 p-4 bg-white dark:bg-bg-dark rounded-lg shadow border border-border-light dark:border-border-dark">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-semibold text-text-primary-light dark:text-text-primary-dark">Order #{{ $order->order_number }}</span>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $order->status === 'completed' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' : ($order->status === 'cancelled' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400' : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400') }}">{{ ucfirst($order->status) }}</span>
                            </div>
                            <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1"><span class="font-medium">User:</span> {{ $order->user->name }}</div>
                            <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1"><span class="font-medium">Plan:</span> {{ $order->plan_id }}</div>
                            <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1"><span class="font-medium">Amount:</span> ₹{{ number_format($order->amount, 2) }}</div>
                            <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-2"><span class="font-medium">Date:</span> {{ $order->created_at->format('M d, Y') }}</div>
                            <div class="flex space-x-4">
                                <a href="{{ route('admin.orders.show', $order) }}" class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light">View</a>
                                <form action="{{ route('admin.orders.destroy', $order) }}" method="POST" class="inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to delete this order?')">Delete</button>
                                </form>
                            </div>
                        </div>
                    @empty
                        <div class="p-4 text-center text-text-secondary-light dark:text-text-secondary-dark">No orders found.</div>
                    @endforelse
                </div>
            </div>

            <div class="mt-4">
                {{ $orders->links() }}
            </div>
        </div>
    </div>
@endsection
