<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="light">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="User dashboard for managing orders and account">
    <title>@yield('title', 'User Dashboard')</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    {{-- <link rel="icon" href="{{ uploads_url(get_setting('site_favicon')) }}"> --}}
    
    <!-- Dark mode script - must be before CSS loads -->
    <script>
        (function() {
            try {
                var theme = localStorage.getItem('theme');
                if (theme === 'dark' || (!theme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                    document.documentElement.classList.add('dark');
                    document.documentElement.classList.remove('light');
                    document.documentElement.setAttribute('data-theme', 'dark');
                } else {
                    document.documentElement.classList.remove('dark');
                    document.documentElement.classList.add('light');
                    document.documentElement.setAttribute('data-theme', 'light');
                }
            } catch (e) {}
        })();
    </script>

    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @stack('styles')
</head>

<body class="bg-bg-light dark:bg-bg-dark min-h-screen font-sans text-text-primary-light dark:text-text-primary-dark">
    <div x-data="{ sidebarOpen: window.innerWidth >= 1024 ? true : false }" x-init="window.addEventListener('resize', () => { if(window.innerWidth >= 1024) sidebarOpen = true; })" class="min-h-screen flex relative">
        <!-- Mobile sidebar backdrop overlay -->
        <div x-show="sidebarOpen"
            class="fixed inset-0 bg-gray-900/50 dark:bg-black/70 z-40 lg:hidden transition-opacity overflow-hidden"
            x-cloak 
            x-transition:enter="transition ease-out duration-300" 
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100" 
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100" 
            x-transition:leave-end="opacity-0" 
            @click="sidebarOpen = false"
            aria-hidden="true"></div>

        <!-- Sidebar -->
        <aside class="fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-bg-dark shadow-lg dark:shadow-gray-900/30 transform transition-transform duration-300 ease-in-out lg:static lg:inset-0 lg:translate-x-0 border-r border-border-light dark:border-border-dark"
               :class="window.innerWidth >= 1024 ? 'translate-x-0' : (!sidebarOpen ? '-translate-x-full' : 'translate-x-0')">

            @include('layouts.partials.user-sidebar')
        </aside>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col min-h-screen lg:pl-0">
            <!-- Top Navigation -->
            <header class="bg-white dark:bg-bg-dark shadow-sm dark:shadow-gray-900/20 z-30 sticky top-0 relative border-b border-border-light dark:border-border-dark">
                <div class="px-4 sm:px-6 lg:px-8 py-4">
                    <div class="flex items-center justify-between">
                        <!-- Mobile menu button -->
                        <button @click="sidebarOpen = !sidebarOpen" 
                                type="button" 
                                class="lg:hidden text-text-secondary-light dark:text-text-secondary-dark hover:text-text-primary-light dark:hover:text-text-primary-dark focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark p-2 rounded-md relative z-50 transition-colors duration-200">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>

                        <!-- Desktop view page title -->
                        <h1 class="hidden sm:block text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">@yield('page-title', 'Dashboard')</h1>

                        <!-- Right side actions -->
                        <div class="flex items-center space-x-4">
                            <!-- Theme Toggle Button -->
                            <button onclick="toggleTheme()" 
                                class="p-1 rounded-md text-text-secondary-light dark:text-text-secondary-dark hover:text-primary-light dark:hover:text-primary-dark focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark transition-colors duration-200">
                                <span class="dark:hidden">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                                    </svg>
                                </span>
                                <span class="hidden dark:inline">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                                    </svg>
                                </span>
                            </button>
                            
                            <!-- User dropdown -->
                            <div x-data="{ dropdownOpen: false }" class="relative z-40">
                                <button @click="dropdownOpen = !dropdownOpen"
                                    class="flex items-center space-x-2 rounded-full focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark"
                                    id="user-menu-button" aria-expanded="false" aria-haspopup="true">
                                    <div
                                        class="h-8 w-8 rounded-full bg-primary-light dark:bg-primary-dark flex items-center justify-center text-white shadow-md transition-all hover:bg-primary-dark dark:hover:bg-primary-light">
                                        {{ substr(auth()->user()->name ?? 'U', 0, 1) }}
                                    </div>
                                    <span
                                        class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark hidden sm:inline-block">{{ auth()->user()->name ?? 'User' }}</span>
                                    <svg class="hidden sm:inline-block h-5 w-5 text-text-secondary-light dark:text-text-secondary-dark" fill="currentColor"
                                        viewBox="0 0 20 20" aria-hidden="true">
                                        <path fill-rule="evenodd"
                                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </button>
                                <!-- Dropdown menu -->
                                <div x-show="dropdownOpen" @click.away="dropdownOpen = false"
                                    x-transition:enter="transition ease-out duration-100"
                                    x-transition:enter-start="transform opacity-0 scale-95"
                                    x-transition:enter-end="transform opacity-100 scale-100"
                                    x-transition:leave="transition ease-in duration-75"
                                    x-transition:leave-start="transform opacity-100 scale-100"
                                    x-transition:leave-end="transform opacity-0 scale-95"
                                    class="absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white dark:bg-bg-dark border border-border-light dark:border-border-dark focus:outline-none z-50"
                                    role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1">
                                    <!-- User dropdown items -->
                                    <a href="{{ route('profile.edit') }}"
                                        class="flex items-center px-4 py-2 text-sm text-text-primary-light dark:text-text-primary-dark hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200">
                                        <svg class="mr-3 h-4 w-4 text-text-secondary-light dark:text-text-secondary-dark" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                                            </path>
                                        </svg>
                                        Your Profile
                                    </a>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit"
                                            class="flex w-full items-center px-4 py-2 text-sm text-text-primary-light dark:text-text-primary-dark hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200">
                                            <svg class="mr-3 h-4 w-4 text-text-secondary-light dark:text-text-secondary-dark" fill="none"
                                                stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1">
                                                </path>
                                            </svg>
                                            Logout
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mobile view page title -->
                <div class="px-4 py-2 sm:hidden border-t border-border-light dark:border-border-dark">
                    <h1 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark truncate">@yield('title', 'Dashboard')</h1>
                </div>
            </header>

            <!-- Page Content -->
            <main id="main-content" class="flex-1 overflow-x-hidden overflow-y-auto bg-bg-light dark:bg-bg-dark pt-4 sm:pt-6 relative z-10">
                <div class="container mx-auto px-3 sm:px-6 lg:px-8 pb-6 sm:pb-8">
                    @if (session('success'))
                        <div x-data="{ show: true }" x-show="show" x-init="setTimeout(() => show = false, 5000)"
                            class="mb-6 bg-green-50 dark:bg-green-900/20 border-l-4 border-green-500 text-green-700 dark:text-green-300 p-4 rounded-md shadow-md dark:shadow-gray-900/10">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-2" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>{{ session('success') }}</span>
                            </div>
                        </div>
                    @endif

                    @if (session('error'))
                        <div x-data="{ show: true }" x-show="show" x-init="setTimeout(() => show = false, 5000)"
                            class="mb-6 bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 text-red-700 dark:text-red-300 p-4 rounded-md shadow-md dark:shadow-gray-900/10">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 text-red-500 dark:text-red-400 mr-2" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                <span>{{ session('error') }}</span>
                            </div>
                        </div>
                    @endif

                    <!-- Content wrapper with card style -->
                    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm dark:shadow-gray-900/10 p-6 border border-border-light dark:border-border-dark">
                        @yield('content')
                    </div>
                </div>
            </main>

            <!-- Footer -->
            <footer class="bg-white dark:bg-bg-dark py-4 px-6 border-t border-border-light dark:border-border-dark relative z-10">
                <div class="container mx-auto flex flex-col md:flex-row justify-between items-center">
                    <div class="text-center md:text-left mb-2 md:mb-0">
                        <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">&copy; {{ date('Y') }} {{ config('app.name') }}. All
                            rights reserved.</p>
                    </div>
                    <div class="flex space-x-4">
                        <a href="#" class="text-text-secondary-light dark:text-text-secondary-dark hover:text-primary-light dark:hover:text-primary-dark text-sm transition-colors duration-200">Privacy Policy</a>
                        <a href="#" class="text-text-secondary-light dark:text-text-secondary-dark hover:text-primary-light dark:hover:text-primary-dark text-sm transition-colors duration-200">Terms of Service</a>
                        <a href="#" class="text-text-secondary-light dark:text-text-secondary-dark hover:text-primary-light dark:hover:text-primary-dark text-sm transition-colors duration-200">Help Center</a>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    @stack('scripts')
    
    <!-- Theme Toggle Script -->
    <script>
        // Theme toggle functionality
        function setThemeIcon(isDark) {
            // This function is intentionally left empty as we're using SVG icons
            // that automatically show/hide based on dark mode class
        }

        function toggleTheme() {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark');
            if (isDark) {
                html.classList.remove('dark');
                html.classList.add('light');
                html.setAttribute('data-theme', 'light');
                localStorage.setItem('theme', 'light');
            } else {
                html.classList.add('dark');
                html.classList.remove('light');
                html.setAttribute('data-theme', 'dark');
                localStorage.setItem('theme', 'dark');
            }
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial state based on class
            const isDark = document.documentElement.classList.contains('dark');
        });
    </script>
</body>

</html>