<?php

namespace Tests\Feature\Services;

use App\Services\MailConfigService;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

// uses(TestCase::class); // This is already defined in tests/Pest.php

// Helper to set environment variables for a test
function setEnvConfig(array $config)
{
    foreach ($config as $key => $value) {
        putenv("{$key}={$value}");
    }
}

// Helper to reset env variables
function resetEnvConfig(array $config)
{
    foreach ($config as $key => $value) {
        putenv($key);
    }
}

beforeEach(function () {
    $this->mailConfigService = new MailConfigService();
    // Keep a copy of the original mail config
    $this->originalConfig = Config::get('mail');
});

afterEach(function () {
    // Restore original config
    Config::set('mail', $this->originalConfig);
    // Clear any mocked facades
    \Mockery::close();
});

it('gets the current mail configuration from environment variables', function () {
    $testEnv = function ($key, $default = null) {
        $values = [
            'MAIL_MAILER' => 'smtp',
            'MAIL_HOST' => 'test.host.com',
            'MAIL_PORT' => '1234',
            'MAIL_USERNAME' => 'testuser',
            'MAIL_PASSWORD' => 'testpass',
            'MAIL_ENCRYPTION' => 'ssl',
            'MAIL_FROM_ADDRESS' => '<EMAIL>',
            'MAIL_FROM_NAME' => 'Test Name',
        ];
        return $values[$key] ?? $default;
    };

    $config = $this->mailConfigService->getCurrentConfig($testEnv);

    expect($config['mail_driver'])->toBe('smtp');
    expect($config['mail_host'])->toBe('test.host.com');
    expect($config['mail_port'])->toBe('1234');
    expect($config['mail_username'])->toBe('testuser');
    expect($config['mail_password'])->toBe('testpass');
    expect($config['mail_encryption'])->toBe('ssl');
    expect($config['mail_from_address'])->toBe('<EMAIL>');
    expect($config['mail_from_name'])->toBe('Test Name');
});

it('loads mail configuration and sets it at runtime', function () {
    $testEnv = function ($key, $default = null) {
        $values = [
            'MAIL_MAILER' => 'smtp',
            'MAIL_HOST' => 'runtime.host.com',
            'MAIL_PORT' => 5678,
            'MAIL_USERNAME' => 'runtimeuser',
            'MAIL_PASSWORD' => 'runtimepass',
            'MAIL_ENCRYPTION' => 'tls',
            'MAIL_FROM_ADDRESS' => '<EMAIL>',
            'MAIL_FROM_NAME' => 'Runtime Name',
        ];
        return $values[$key] ?? $default;
    };
    
    $this->mailConfigService->loadMailConfig($testEnv);

    $runtimeConfig = Config::get('mail');

    expect($runtimeConfig['default'])->toBe('smtp');
    expect($runtimeConfig['mailers']['smtp']['host'])->toBe('runtime.host.com');
    expect($runtimeConfig['mailers']['smtp']['port'])->toBe(5678);
    expect($runtimeConfig['mailers']['smtp']['username'])->toBe('runtimeuser');
    expect($runtimeConfig['mailers']['smtp']['password'])->toBe('runtimepass');
    expect($runtimeConfig['from']['address'])->toBe('<EMAIL>');
    expect($runtimeConfig['from']['name'])->toBe('Runtime Name');
});

it('updates mail configuration in the .env file', function () {
    $newData = [
        'mail_host' => 'new.host.com',
        'mail_port' => '9999',
        'mail_from_name' => 'New "Test" Name'
    ];

    $originalEnv = "MAIL_HOST=old.host.com\nMAIL_PORT=1234\nMAIL_FROM_NAME=\"Old Name\"";
    $expectedEnv = "MAIL_HOST=new.host.com\nMAIL_PORT=9999\nMAIL_FROM_NAME=\"New \\\"Test\\\" Name\"";

    File::shouldReceive('get')->once()->with(base_path('.env'))->andReturn($originalEnv);
    File::shouldReceive('put')->once()->with(base_path('.env'), $expectedEnv);

    $result = $this->mailConfigService->updateMailConfig($newData);

    expect($result)->toBeTrue();
});

it('handles exceptions when updating the .env file', function () {
    Log::shouldReceive('error')->once();
    File::shouldReceive('get')->andThrow(new \Exception('Failed to read file'));

    $result = $this->mailConfigService->updateMailConfig(['mail_host' => 'any.host']);
    
    expect($result)->toBe(false);
});

it('verifies a complete and valid mail configuration', function () {
    $this->instance(MailConfigService::class, \Mockery::mock(MailConfigService::class, function ($mock) {
        $mock->makePartial()->shouldReceive('getCurrentConfig')->andReturn([
            'mail_driver' => 'smtp',
            'mail_host' => 'test.host',
            'mail_port' => '587',
            'mail_from_address' => '<EMAIL>',
            'mail_from_name' => 'Test',
            'mail_username' => 'user',
            'mail_password' => 'pass',
        ]);
    }));

    $service = app(MailConfigService::class);
    expect($service->verifyMailConfig())->toBeTrue();
});

it('fails verification if required settings are missing', function () {
    Log::shouldReceive('warning')->once()->with('Missing required mail configuration: mail_host');

    $this->instance(MailConfigService::class, \Mockery::mock(MailConfigService::class, function ($mock) {
        $mock->makePartial()->shouldReceive('getCurrentConfig')->andReturn([
            'mail_driver' => 'smtp',
            'mail_host' => '', // Missing
            'mail_port' => '587',
            'mail_from_address' => '<EMAIL>',
            'mail_from_name' => 'Test',
        ]);
    }));

    $service = app(MailConfigService::class);
    expect($service->verifyMailConfig())->toBeFalse();
});

it('fails verification if smtp credentials are missing', function () {
    Log::shouldReceive('warning')->once()->with('SMTP credentials are required but not configured');

    $this->instance(MailConfigService::class, \Mockery::mock(MailConfigService::class, function ($mock) {
        $mock->makePartial()->shouldReceive('getCurrentConfig')->andReturn([
            'mail_driver' => 'smtp',
            'mail_host' => 'test.host',
            'mail_port' => '587',
            'mail_from_address' => '<EMAIL>',
            'mail_from_name' => 'Test',
            'mail_username' => '', // Missing
            'mail_password' => '', // Missing
        ]);
    }));

    $service = app(MailConfigService::class);
    expect($service->verifyMailConfig())->toBeFalse();
}); 