<?php

namespace Tests\Feature\Services;

use App\Models\LandingPageContent;
use App\Models\LandingPageSection;
use App\Services\LandingPageService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

beforeEach(function () {
    $this->landingPageService = new LandingPageService();
});

it('gets all active landing page sections ordered by sort_order', function () {
    LandingPageSection::factory()->create(['sort_order' => 2, 'is_active' => true]);
    LandingPageSection::factory()->create(['sort_order' => 1, 'is_active' => true]);
    LandingPageSection::factory()->create(['is_active' => false]);

    $sections = $this->landingPageService->getAllSections();

    expect($sections)->toHaveCount(2)
        ->and($sections->first()->sort_order)->toBe(1)
        ->and($sections->last()->sort_order)->toBe(2);
});

it('caches the result of all sections', function () {
    Cache::shouldReceive('remember')
        ->once()
        ->with('landing_page_sections', 3600, \Closure::class)
        ->andReturn(collect());

    $this->landingPageService->getAllSections();
});

it('gets a specific active section by slug', function () {
    $section = LandingPageSection::factory()->create(['slug' => 'hero', 'is_active' => true]);
    LandingPageSection::factory()->create(['slug' => 'about', 'is_active' => false]);

    $result = $this->landingPageService->getSection('hero');
    $inactiveResult = $this->landingPageService->getSection('about');
    $nonExistentResult = $this->landingPageService->getSection('contact');

    expect($result->id)->toBe($section->id)
        ->and($inactiveResult)->toBeNull()
        ->and($nonExistentResult)->toBeNull();
});

it('caches the result of a single section', function () {
    $slug = 'hero-section';
    Cache::shouldReceive('remember')
        ->once()
        ->with("landing_page_section_{$slug}", 3600, \Closure::class)
        ->andReturn(null);

    $this->landingPageService->getSection($slug);
});

it('formats section content correctly', function () {
    $section = LandingPageSection::factory()->create();
    LandingPageContent::factory()->for($section, 'section')->create(['key' => 'title', 'value' => 'Welcome', 'type' => 'text']);
    LandingPageContent::factory()->for($section, 'section')->create(['key' => 'features', 'value' => json_encode([['item' => 'a'], ['item' => 'b']]), 'type' => 'repeater']);
    LandingPageContent::factory()->for($section, 'section')->create(['key' => 'banner', 'value' => 'image.jpg', 'type' => 'image']);

    // The service's is_array check for images won't be hit, but this reflects how data is stored.
    $section->load('contents');
    $formattedContent = $this->landingPageService->formatSectionContent($section);

    expect($formattedContent)->toEqual([
        'title' => 'Welcome',
        'features' => [['item' => 'a'], ['item' => 'b']],
        'banner' => 'image.jpg',
    ]);
});

it('gets all landing page data formatted for display', function () {
    // Active section
    $activeSection = LandingPageSection::factory()->create(['slug' => 'active-section', 'is_active' => true]);
    $activeSection->contents()->create(LandingPageContent::factory()->make(['key' => 'title', 'value' => 'Active Title'])->toArray());

    // Inactive section
    $inactiveSection = LandingPageSection::factory()->create(['slug' => 'inactive-section', 'is_active' => false]);
    $inactiveSection->contents()->create(LandingPageContent::factory()->make(['key' => 'title', 'value' => 'Inactive Title'])->toArray());

    $pageData = $this->landingPageService->getLandingPageData();

    expect($pageData)->toHaveKeys(['active-section', 'inactive-section'])
        ->and($pageData['active-section']['active'])->toBe(true)
        ->and($pageData['active-section']['content']['title'])->toBe('Active Title')
        ->and($pageData['inactive-section']['active'])->toBe(false)
        ->and($pageData['inactive-section']['content']['title'])->toBe('Inactive Title');
});


it('clears the landing page cache', function () {
    $section1 = LandingPageSection::factory()->create(['slug' => 'section-one']);
    $section2 = LandingPageSection::factory()->create(['slug' => 'section-two']);

    Cache::shouldReceive('forget')->with('landing_page_sections')->once();
    Cache::shouldReceive('forget')->with("landing_page_section_{$section1->slug}")->once();
    Cache::shouldReceive('forget')->with("landing_page_section_{$section2->slug}")->once();

    $this->landingPageService->clearCache();

    // This is just to satisfy mockery expectations.
    expect(true)->toBeTrue();
});

it('handles json decoding failure gracefully in formatSectionContent', function () {
    $section = LandingPageSection::factory()->create();
    LandingPageContent::factory()->for($section, 'section')->create([
        'key' => 'features',
        'value' => 'not a valid json',
        'type' => 'repeater'
    ]);

    $formattedContent = $this->landingPageService->formatSectionContent($section->load('contents'));

    // The model accessor returns null for invalid JSON, and the service now passes that through.
    expect($formattedContent['features'])->toBeNull();
});

it('handles empty image array gracefully in formatSectionContent', function () {
    // We test this with an in-memory model to ensure we can test the 'is_array' check,
    // bypassing potential issues with database type casting for this specific edge case.
    $testContent = new LandingPageContent([
        'key' => 'banner',
        'type' => 'image',
        'value' => [], // Set value to an array
    ]);

    $testSection = new LandingPageSection();
    $testSection->setRelation('contents', collect([$testContent]));

    $formattedContent = $this->landingPageService->formatSectionContent($testSection);

    expect($formattedContent['banner'])->toBeNull();
});

it('handles different content types in getLandingPageData', function () {
    $section = LandingPageSection::factory()->create(['slug' => 'mixed-content']);
    LandingPageContent::factory()->for($section, 'section')->create(['key' => 'banner', 'value' => 'image.png', 'type' => 'image']);
    LandingPageContent::factory()->for($section, 'section')->create(['key' => 'features', 'value' => json_encode([['item' => 'x'], ['item' => 'y']]), 'type' => 'repeater']);

    $pageData = $this->landingPageService->getLandingPageData();
    $content = $pageData['mixed-content']['content'];

    expect($content['banner'])->toBe('image.png')
        ->and($content['features'])->toEqual([['item' => 'x'], ['item' => 'y']]);
}); 