<?php $__env->startSection('title', 'Edit Blog Post'); ?>

<?php $__env->startSection('styles'); ?>
    <style>
        .ql-editor {
            min-height: 400px;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="px-4 w-full max-w-7xl mx-auto">
        <div class="mb-6">
            <h1 class="text-3xl font-bold text-text-primary-light dark:text-text-primary-dark">Edit Blog Post</h1>
            <ol class="flex flex-wrap list-none rounded mb-4 bg-transparent">
                <li class="text-sm leading-normal text-text-secondary-light dark:text-text-secondary-dark">
                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light transition-colors">Dashboard</a>
                </li>
                <li class="text-sm text-text-secondary-light dark:text-text-secondary-dark mx-2">/</li>
                <li class="text-sm leading-normal text-text-secondary-light dark:text-text-secondary-dark">
                    <a href="<?php echo e(route('admin.blog.posts.index')); ?>" class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light transition-colors">Blog Posts</a>
                </li>
                <li class="text-sm text-text-secondary-light dark:text-text-secondary-dark mx-2">/</li>
                <li class="text-sm font-semibold text-text-primary-light dark:text-text-primary-dark">Edit</li>
            </ol>
        </div>

        <div class="bg-white dark:bg-bg-dark rounded-xl shadow-lg mb-6 overflow-hidden border border-border-light dark:border-border-dark">
            <div class="flex items-center px-6 py-4 border-b border-border-light dark:border-border-dark bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-3 text-primary-light dark:text-primary-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                <span class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">Edit Blog Post</span>
            </div>
            <div class="p-6">
                <form action="<?php echo e(route('admin.blog.posts.update', $post)); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>

                    <div class="flex flex-col md:flex-row md:space-x-6">
                        <div class="md:w-2/3">
                            <div class="mb-6">
                                <label for="title" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                                    Title <span class="text-red-500">*</span>
                                </label>
                                <input type="text"
                                    class="w-full rounded-lg border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="title" name="title" value="<?php echo e(old('title', $post->title)); ?>" required>
                                <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-6">
                                <label for="content" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                                    Content <span class="text-red-500">*</span>
                                </label>
                                <div id="quill-editor" class="w-full rounded-lg border-border-light dark:border-border-dark bg-white dark:bg-bg-dark <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"></div>
                                <input type="hidden" name="content" id="content-input" value="<?php echo e(old('content', $post->content)); ?>">
                                <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-6">
                                <label for="excerpt" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                                    Excerpt
                                </label>
                                <textarea
                                    class="w-full rounded-lg border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors <?php $__errorArgs = ['excerpt'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="excerpt" name="excerpt" rows="3"><?php echo e(old('excerpt', $post->excerpt)); ?></textarea>
                                <p class="mt-2 text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                    A short summary of the post (optional). If left empty, an excerpt will be generated from the content.
                                </p>
                                <?php $__errorArgs = ['excerpt'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-6">
                                <label for="featured_image" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                                    Featured Image
                                </label>
                                <div class="mt-1">
                                    <div class="w-full">
                                        <label class="flex flex-col items-center px-4 py-6 bg-white dark:bg-bg-dark rounded-lg border-2 border-dashed border-border-light dark:border-border-dark cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                            <div id="image-preview-container" class="mb-3 w-full max-w-xs <?php echo e($post->featured_image ? '' : 'hidden'); ?>">
                                                <img id="image-preview" class="max-w-full h-auto rounded-lg shadow-md" src="<?php echo e($post->featured_image ? uploads_url($post->featured_image) : ''); ?>" alt="Image preview">
                                            </div>
                                            <svg xmlns="http://www.w3.org/2000/svg" id="upload-icon" class="h-12 w-12 text-text-secondary-light dark:text-text-secondary-dark <?php echo e($post->featured_image ? 'hidden' : ''); ?>"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                            <span id="upload-text" class="mt-2 text-base leading-normal text-text-secondary-light dark:text-text-secondary-dark"><?php echo e($post->featured_image ? 'Change image' : 'Select image'); ?></span>
                                            <input type="file" class="hidden" name="featured_image" id="featured_image"
                                                accept="image/*">
                                        </label>
                                        <button type="button" id="remove-image" class="mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 transition-colors <?php echo e($post->featured_image ? '' : 'hidden'); ?>">
                                            Remove image
                                        </button>
                                    </div>
                                </div>
                                <p class="mt-2 text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                    Recommended image size: 1200×630 pixels (16:9 ratio)
                                </p>
                                <?php $__errorArgs = ['featured_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="md:w-1/3">
                            <!-- Publishing Settings Card -->
                            <div class="bg-white dark:bg-bg-dark rounded-xl shadow-sm border border-border-light dark:border-border-dark mb-6 overflow-hidden">
                                <div class="px-4 py-3 border-b border-border-light dark:border-border-dark bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 font-medium text-text-primary-light dark:text-text-primary-dark">
                                    Publishing
                                </div>
                                <div class="p-4">
                                    <div class="flex items-center mb-4">
                                        <label class="inline-flex items-center cursor-pointer">
                                            <input type="checkbox" id="is_published" name="is_published"
                                                <?php echo e(old('is_published', $post->is_published) ? 'checked' : ''); ?>

                                                class="rounded border-border-light dark:border-border-dark text-primary-light dark:text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors">
                                            <span class="ml-2 text-sm text-text-primary-light dark:text-text-primary-dark">Published</span>
                                        </label>
                                    </div>
                                    <div class="flex items-center mb-4">
                                        <label class="inline-flex items-center cursor-pointer">
                                            <input type="checkbox" id="featured" name="featured"
                                                <?php echo e(old('featured', $post->featured) ? 'checked' : ''); ?>

                                                class="rounded border-border-light dark:border-border-dark text-primary-light dark:text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors">
                                            <span class="ml-2 text-sm text-text-primary-light dark:text-text-primary-dark">Featured post</span>
                                        </label>
                                    </div>
                                    <div class="flex flex-col gap-2">
                                        <button type="submit"
                                            class="w-full bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-white font-medium py-2.5 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-2 dark:focus:ring-offset-bg-dark">
                                            Update Post
                                        </button>
                                        <a href="<?php echo e(route('admin.blog.posts.index')); ?>"
                                            class="w-full text-center border border-border-light dark:border-border-dark bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-text-primary-light dark:text-text-primary-dark font-medium py-2.5 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-400 focus:ring-offset-2 dark:focus:ring-offset-bg-dark">
                                            Cancel
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Category Card -->
                            <div class="bg-white dark:bg-bg-dark rounded-xl shadow-sm border border-border-light dark:border-border-dark mb-6 overflow-hidden">
                                <div class="px-4 py-3 border-b border-border-light dark:border-border-dark bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 font-medium text-text-primary-light dark:text-text-primary-dark">
                                    Category
                                </div>
                                <div class="p-4">
                                    <select
                                        class="w-full rounded-lg border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors <?php $__errorArgs = ['blog_post_category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        name="blog_post_category_id" required>
                                        <option value="">Select Category</option>
                                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($category->id); ?>"
                                                <?php echo e(old('blog_post_category_id', $post->blog_post_category_id) == $category->id ? 'selected' : ''); ?>>
                                                <?php echo e($category->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['blog_post_category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    <div class="mt-3">
                                        <a href="<?php echo e(route('admin.blog.categories.create')); ?>"
                                            class="text-sm inline-flex items-center px-3 py-1.5 border border-primary-light dark:border-primary-dark text-primary-light dark:text-primary-dark rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                                            target="_blank">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none"
                                                viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M12 4v16m8-8H4" />
                                            </svg>
                                            Add New Category
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Tags Card -->
                            <div class="bg-white dark:bg-bg-dark rounded-xl shadow-sm border border-border-light dark:border-border-dark mb-6 overflow-hidden">
                                <div class="px-4 py-3 border-b border-border-light dark:border-border-dark bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 font-medium text-text-primary-light dark:text-text-primary-dark">
                                    Tags
                                </div>
                                <div class="p-4">
                                    <select
                                        class="w-full rounded-lg border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors tags-select <?php $__errorArgs = ['tags'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        name="tags[]" multiple>
                                        <?php $__currentLoopData = $tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($tag->id); ?>"
                                                <?php echo e(is_array(old('tags', $post->tags->pluck('id')->toArray())) && in_array($tag->id, old('tags', $post->tags->pluck('id')->toArray())) ? 'selected' : ''); ?>>
                                                <?php echo e($tag->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['tags'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    <div class="mt-3">
                                        <a href="<?php echo e(route('admin.blog.tags.create')); ?>"
                                            class="text-sm inline-flex items-center px-3 py-1.5 border border-primary-light dark:border-primary-dark text-primary-light dark:text-primary-dark rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                                            target="_blank">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none"
                                                viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M12 4v16m8-8H4" />
                                            </svg>
                                            Add New Tag
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- SEO Card -->
                            <div class="bg-white dark:bg-bg-dark rounded-xl shadow-sm border border-border-light dark:border-border-dark mb-6 overflow-hidden">
                                <div class="px-4 py-3 border-b border-border-light dark:border-border-dark bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 font-medium text-text-primary-light dark:text-text-primary-dark">
                                    SEO
                                </div>
                                <div class="p-4">
                                    <div class="mb-4">
                                        <label for="meta_title" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                                            Meta Title
                                        </label>
                                        <input type="text"
                                            class="w-full rounded-lg border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors <?php $__errorArgs = ['meta_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            id="meta_title" name="meta_title"
                                            value="<?php echo e(old('meta_title', $post->meta_title)); ?>">
                                        <p class="mt-2 text-xs text-text-secondary-light dark:text-text-secondary-dark">Leave empty to use post title</p>
                                        <?php $__errorArgs = ['meta_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="mb-4">
                                        <label for="meta_description" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                                            Meta Description
                                        </label>
                                        <textarea
                                            class="w-full rounded-lg border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors <?php $__errorArgs = ['meta_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            id="meta_description" name="meta_description" rows="3"><?php echo e(old('meta_description', $post->meta_description)); ?></textarea>
                                        <p class="mt-2 text-xs text-text-secondary-light dark:text-text-secondary-dark">Leave empty to use post excerpt</p>
                                        <?php $__errorArgs = ['meta_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="mb-4">
                                        <label for="meta_keywords" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                                            Meta Keywords
                                        </label>
                                        <input type="text"
                                            class="w-full rounded-lg border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors <?php $__errorArgs = ['meta_keywords'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            id="meta_keywords" name="meta_keywords"
                                            value="<?php echo e(old('meta_keywords', is_array($post->meta_keywords) ? implode(', ', $post->meta_keywords) : $post->meta_keywords)); ?>">
                                        <p class="mt-2 text-xs text-text-secondary-light dark:text-text-secondary-dark">Separate keywords with commas</p>
                                        <?php $__errorArgs = ['meta_keywords'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <style>
        /* Custom styles for multi-select */
        .tags-select {
            min-height: 100px;
        }
        .tags-select option {
            padding: 0.5rem;
            margin: 0.25rem 0;
        }
        .tags-select option:checked {
            background-color: #e5e7eb;
        }
        .dark .tags-select option:checked {
            background-color: #374151;
        }
        /* Quill editor styles */
        .ql-editor {
            min-height: 400px;
            font-family: inherit;
            font-size: 1rem;
            line-height: 1.5;
            color: rgb(17 24 39);
        }
        .dark .ql-editor {
            color: rgb(229 231 235);
        }
        .ql-toolbar {
            border-top-left-radius: 0.5rem;
            border-top-right-radius: 0.5rem;
            background-color: #f9fafb;
            border-color: #e5e7eb;
        }
        .dark .ql-toolbar {
            background-color: #1f2937;
            border-color: #374151;
        }
        .ql-container {
            border-bottom-left-radius: 0.5rem;
            border-bottom-right-radius: 0.5rem;
            font-family: inherit;
            border-color: #e5e7eb;
        }
        .dark .ql-container {
            border-color: #374151;
        }
        .ql-editor img {
            max-width: 100%;
            height: auto;
            border-radius: 0.375rem;
        }
        .ql-editor.ql-blank::before {
            font-style: italic;
            color: #6b7280;
        }
        .dark .ql-editor.ql-blank::before {
            color: #9ca3af;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <script src="https://unpkg.com/quill-image-resize-module@3.0.0/image-resize.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Quill editor with image resize module
            const quill = new Quill('#quill-editor', {
                theme: 'snow',
                modules: {
                    toolbar: [
                        ['bold', 'italic', 'underline', 'strike'],
                        ['blockquote', 'code-block'],
                        [{ 'header': 1 }, { 'header': 2 }],
                        [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                        [{ 'script': 'sub' }, { 'script': 'super' }],
                        [{ 'indent': '-1' }, { 'indent': '+1' }],
                        [{ 'direction': 'rtl' }],
                        [{ 'size': ['small', false, 'large', 'huge'] }],
                        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'font': [] }],
                        [{ 'align': [] }],
                        ['clean'],
                        ['link', 'image', 'table']
                    ],
                    imageResize: {
                        displaySize: true
                    }
                },
                placeholder: 'Write your content here...'
            });

            // Set initial content
            const contentInput = document.getElementById('content-input');
            if (contentInput.value) {
                quill.clipboard.dangerouslyPasteHTML(contentInput.value);
            }

            // Update hidden input with Quill content when content changes
            quill.on('text-change', function() {
                contentInput.value = quill.root.innerHTML;
            });

            // Featured image preview functionality
            const featuredImageInput = document.getElementById('featured_image');
            const imagePreviewContainer = document.getElementById('image-preview-container');
            const imagePreview = document.getElementById('image-preview');
            const uploadIcon = document.getElementById('upload-icon');
            const uploadText = document.getElementById('upload-text');
            const removeImageBtn = document.getElementById('remove-image');

            featuredImageInput.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    // Validate file type
                    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                    if (!validTypes.includes(file.type)) {
                        alert('Please select a valid image file (JPEG, PNG, GIF, WEBP)');
                        this.value = '';
                        return;
                    }

                    // Validate file size (max 2MB)
                    if (file.size > 2 * 1024 * 1024) {
                        alert('Image size must be less than 2MB');
                        this.value = '';
                        return;
                    }

                    // Show preview
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        imagePreview.src = e.target.result;
                        imagePreviewContainer.classList.remove('hidden');
                        uploadIcon.classList.add('hidden');
                        uploadText.textContent = 'Change image';
                        removeImageBtn.classList.remove('hidden');
                    };
                    reader.readAsDataURL(file);
                }
            });

            // Remove image functionality
            removeImageBtn.addEventListener('click', function(e) {
                e.preventDefault();
                featuredImageInput.value = '';
                imagePreviewContainer.classList.add('hidden');
                uploadIcon.classList.remove('hidden');
                uploadText.textContent = 'Select image';
                removeImageBtn.classList.add('hidden');
            });

            // Enhanced image upload handler for Quill editor
            const toolbar = quill.getModule('toolbar');
            toolbar.addHandler('image', function() {
                const input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', 'image/*');
                input.click();

                input.onchange = function() {
                    const file = input.files[0];
                    if (!file) return;

                    // Validate file type
                    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                    if (!validTypes.includes(file.type)) {
                        alert('Please select a valid image file (JPEG, PNG, GIF, WEBP)');
                        return;
                    }

                    // Validate file size (max 2MB)
                    if (file.size > 2 * 1024 * 1024) {
                        alert('File size must be less than 2MB');
                        return;
                    }

                    // Show loading indicator
                    const range = quill.getSelection(true);
                    const loadingText = 'Uploading image...';
                    quill.insertText(range.index, loadingText, 'italic', true);
                    quill.setSelection(range.index + loadingText.length);

                    // Create progress indicator
                    const progressBar = document.createElement('div');
                    progressBar.className = 'upload-progress';
                    progressBar.style.cssText = `
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background: rgba(0, 0, 0, 0.7);
                        color: white;
                        padding: 10px 20px;
                        border-radius: 4px;
                        z-index: 1000;
                    `;
                    progressBar.textContent = 'Uploading...';
                    document.body.appendChild(progressBar);

                    const formData = new FormData();
                    formData.append('file', file);
                    formData.append('_token', '<?php echo e(csrf_token()); ?>');

                    fetch('<?php echo e(route("admin.blog.upload-image")); ?>', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Remove loading text and progress bar
                        quill.deleteText(range.index, loadingText.length);
                        document.body.removeChild(progressBar);
                        
                        if (data.location) {
                            // Insert the image at the current position
                            quill.insertEmbed(range.index, 'image', data.location);
                            // Move cursor after the image
                            quill.setSelection(range.index + 1);
                            
                            // Update the hidden input with the new content
                            contentInput.value = quill.root.innerHTML;
                        } else {
                            throw new Error(data.message || 'Upload failed');
                        }
                    })
                    .catch(error => {
                        // Remove loading text and progress bar
                        quill.deleteText(range.index, loadingText.length);
                        document.body.removeChild(progressBar);
                        
                        console.error('Error:', error);
                        alert('Upload failed: ' + error.message);
                    });
                };
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/admin/blog/posts/blog-edit.blade.php ENDPATH**/ ?>