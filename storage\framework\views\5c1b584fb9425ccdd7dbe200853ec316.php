<?php if(isset($landingPage['hero']) && $landingPage['hero']['active']): ?>
    <section id="hero-animated"
        class="relative overflow-hidden h-auto min-h-screen sm:h-screen flex items-start sm:items-center justify-center bg-bg-light dark:bg-bg-dark transition-colors duration-300 pt-16 sm:pt-0">
        <!-- Enhanced Parallax, Morphing Blobs, and Interactive BG -->
        <div class="absolute inset-0 z-0 pointer-events-none">
            <div class="parallax-bg" id="parallaxBg">
                <!-- Enhanced floating elements with better animations -->
                <div class="floating-elements">
                    <div class="floating-element text-6xl animate-bounce-slow" style="animation-delay: 0s;">📍</div>
                    <div class="floating-element text-5xl animate-pulse-slow" style="left: 15%; animation-delay: -2s;">🏢
                    </div>
                    <div class="floating-element text-4xl animate-wiggle" style="left: 35%; animation-delay: -4s;">📮
                    </div>
                    <div class="floating-element text-6xl animate-spin-slow" style="left: 65%; animation-delay: -6s;">🌏
                    </div>
                    <div class="floating-element text-5xl animate-float-gentle"
                        style="left: 85%; animation-delay: -8s;">📊</div>
                    <div class="floating-element text-3xl animate-bounce-slow"
                        style="left: 25%; top: 70%; animation-delay: -10s;">🚚</div>
                    <div class="floating-element text-4xl animate-pulse-slow"
                        style="left: 75%; top: 20%; animation-delay: -12s;">📬</div>
                </div>
                <!-- Enhanced morphing blobs with theme colors -->
                <div class="morphing-blob enhanced-blob w-96 h-96 top-10 left-10 bg-primary-light/30 dark:bg-primary-dark/30"
                    style="animation-delay: 0s;"></div>
                <div class="morphing-blob enhanced-blob w-80 h-80 top-1/2 right-10 bg-accent-light/25 dark:bg-accent-dark/25"
                    style="animation-delay: -2s;"></div>
                <div class="morphing-blob enhanced-blob w-72 h-72 bottom-10 left-1/3 bg-primary-dark/20 dark:bg-primary-light/20"
                    style="animation-delay: -4s;"></div>
                <!-- Additional smaller blobs for more dynamic effect -->
                <div class="morphing-blob enhanced-blob w-48 h-48 top-1/4 left-1/2 bg-accent-dark/15 dark:bg-accent-light/15"
                    style="animation-delay: -6s;"></div>
                <div class="morphing-blob enhanced-blob w-64 h-64 bottom-1/4 right-1/4 bg-primary-light/20 dark:bg-primary-dark/20"
                    style="animation-delay: -8s;"></div>
            </div>
            <div class="absolute inset-0 interactive-bg dark:bg-bg-dark/80" id="interactiveBg"></div>
            <!-- Particle system -->
            <div class="absolute inset-0 particle-system" id="particleSystem"></div>
        </div>
        <div
            class="relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex flex-col md:flex-row items-start md:items-center justify-center gap-12">
            <div
                class="flex-1 hero-texts flex flex-col items-center md:items-start justify-center text-center md:text-left">
                <span
                    class="inline-block px-6 py-2 rounded-full text-xs font-bold tracking-wider uppercase mb-6 hero-badge glass-effect enhanced-glow bg-gradient-to-r from-primary-light to-accent-light dark:from-primary-dark dark:to-accent-dark text-white shadow-lg animate-pulse-glow">
                    <i class="fa-solid fa-trophy mr-2"></i><?php echo e($landingPage['hero']['content']['badge_text'] ?? "India's #1 Postal Directory"); ?>

                </span>
                <h1 class="text-5xl md:text-7xl font-black tracking-tight mb-8 leading-tight hero-title">
                    <?php echo $landingPage['hero']['content']['heading'] ??
                        '<span class="block mb-2 text-text-primary-light dark:text-text-primary-dark animate-slide-in-left">Discover</span><span class="enhanced-gradient-text typing-animation block animate-slide-in-right">Every Pincode</span><span class="block text-text-primary-light dark:text-text-primary-dark animate-slide-in-left">in India</span>'; ?>

                </h1>
                <p
                    class="text-xl md:text-2xl text-white/80 mb-10 max-w-2xl hero-subtitle mx-auto md:mx-0 leading-relaxed animate-fade-in-up">
                    <?php echo e($landingPage['hero']['content']['subheading'] ?? 'Instantly search, verify, and explore 167,000+ post offices with with rich, verified data and interactive tools.'); ?>

                </p>
                <div class="flex flex-col sm:flex-row gap-6 hero-ctas justify-center md:justify-start">


                    <a href="<?php echo e($landingPage['hero']['content']['cta_link'] ?? '#search-section'); ?>"
                        class="group px-10 py-4 border-2 rounded-xl enhanced-shadow transition-all duration-500 text-xl font-bold flex items-center gap-3 bg-accent-light dark:bg-accent-dark text-text-primary-light dark:text-text-primary-light border-accent-light dark:border-accent-dark hover:bg-accent-light/90 dark:hover:bg-accent-dark/90 transform hover:scale-105 mx-auto w-fit">
                        <span><?php echo e($landingPage['hero']['content']['cta_text'] ?? 'Start Searching'); ?></span>
                        <i
                            class="fa-solid fa-magnifying-glass group-hover:rotate-12 transition-transform duration-300"></i>
                    </a>

                    <a href="<?php echo e($landingPage['hero']['content']['second_cta_link'] ?? '#features'); ?>"
                        class="group px-10 py-4 text-white rounded-xl shadow-2xl transition-all duration-500 text-xl font-bold flex items-center gap-3 enhanced-glow bg-gradient-to-r from-primary-light to-primary-dark hover:from-accent-light hover:to-primary-dark transform hover:scale-105 hover:-translate-y-1">
                        <span><?php echo e($landingPage['hero']['content']['second_cta_text'] ?? 'See Features'); ?></span>
                        <i class="fa-solid fa-star group-hover:rotate-180 transition-transform duration-500"></i>
                    </a>

                </div>
            </div>
            <div class="flex-1 flex justify-center items-center min-h-[400px]">
                <div class="relative w-full max-w-lg">
                    <!-- Enhanced image container with multiple animations -->
                    <div class="relative animate-float-complex">
                        <img src="<?php echo e(uploads_url($landingPage['hero']['content']['hero_image'] ?? 'images/india-map.webp')); ?>"
                            alt="India Map"
                            class="rounded-3xl shadow-2xl border-4 border-gradient-to-r from-primary-light to-accent-light dark:from-primary-dark dark:to-accent-dark bg-white dark:bg-bg-dark transform transition-all duration-700 hover:scale-105 hover:rotate-1" />
                        <!-- Animated border glow -->
                        <div
                            class="absolute inset-0 rounded-3xl border-4 border-gradient-to-r from-primary-light/50 to-accent-light/50 dark:from-primary-dark/50 dark:to-accent-dark/50 animate-pulse-border">
                        </div>
                    </div>
                    <!-- Enhanced verification badge -->
                    <div
                        class="absolute bottom-6 right-6 rounded-2xl shadow-2xl p-5 flex items-center gap-3 animate-slide-in-bottom bg-white/95 dark:bg-bg-dark/95 backdrop-blur-md border border-border-light/50 dark:border-border-dark/50">
                        <i class="fa-solid fa-circle-check text-green-500 text-2xl animate-bounce-gentle"></i>
                        <div>
                            <span
                                class="font-bold text-text-primary-light dark:text-text-primary-dark block"><?php echo e($landingPage['hero']['content']['verified_by_1'] ??
                                    "Verified
                                                                by"); ?></span>
                            <span
                                class="font-semibold text-primary-light dark:text-primary-dark"><?php echo e($landingPage['hero']['content']['verified_by_2'] ?? 'India Post'); ?></span>
                        </div>
                    </div>
                    <!-- Floating stats badges -->
                    <div
                        class="absolute -top-4 -left-2 bg-gradient-to-r from-accent-light to-accent-dark text-white px-4 py-2 rounded-full shadow-lg animate-bounce-slow">
                        <span
                            class="font-bold text-sm"><?php echo e($landingPage['hero']['content']['floating_txt_1'] ?? '167K+ Post Offices'); ?></span>
                    </div>
                    <div class="absolute -bottom-4 -left-2 bg-gradient-to-r from-primary-light to-primary-dark text-white px-4 py-2 rounded-full shadow-lg animate-bounce-slow"
                        style="animation-delay: -1s;">
                        <span
                            class="font-bold text-sm"><?php echo e($landingPage['hero']['content']['floating_txt_2'] ?? '28 States & 8 UTs'); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php endif; ?>
<?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/home/<USER>/hero-section.blade.php ENDPATH**/ ?>