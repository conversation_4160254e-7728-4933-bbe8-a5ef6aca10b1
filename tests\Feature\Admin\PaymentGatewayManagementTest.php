<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Models\PaymentGateway;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PaymentGatewayManagementTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected PaymentGateway $gateway;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create(['is_admin' => true]);
        
        $this->gateway = PaymentGateway::factory()->create([
            'name' => 'razorpay',
            'display_name' => 'Razorpay',
            'is_active' => true,
            'configuration' => [
                'key_id' => 'rzp_test_1234567890',
                'key_secret' => 'test_secret_key',
                'webhook_secret' => 'test_webhook_secret'
            ],
            'supported_currencies' => ['INR', 'USD']
        ]);
    }

    public function test_admin_can_view_payment_gateways_index()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payment-gateways.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.payment-gateways.index');
        $response->assertViewHas('gateways');
        $response->assertSee($this->gateway->display_name);
    }

    public function test_admin_can_create_payment_gateway()
    {
        $this->actingAs($this->admin);

        $gatewayData = [
            'name' => 'paypal',
            'display_name' => 'PayPal',
            'description' => 'Pay with PayPal',
            'is_active' => true,
            'supported_currencies' => ['USD', 'EUR'],
            'configuration' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
                'mode' => 'sandbox'
            ]
        ];

        $response = $this->post(route('admin.payment-gateways.store'), $gatewayData);

        $response->assertRedirect(route('admin.payment-gateways.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('payment_gateways', [
            'name' => 'paypal',
            'display_name' => 'PayPal',
            'is_active' => true
        ]);
    }

    public function test_admin_can_view_gateway_configuration()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payment-gateways.configure', $this->gateway));

        $response->assertStatus(200);
        $response->assertViewIs('admin.payment-gateways.configure');
        $response->assertViewHas('gateway');
        $response->assertSee($this->gateway->display_name);
    }

    public function test_admin_can_update_gateway_configuration()
    {
        $this->actingAs($this->admin);

        $updateData = [
            'display_name' => 'Razorpay Updated',
            'description' => 'Updated description',
            'is_active' => false,
            'supported_currencies' => ['INR'],
            'configuration' => [
                'key_id' => 'rzp_test_updated',
                'key_secret' => 'updated_secret',
                'webhook_secret' => 'updated_webhook_secret'
            ]
        ];

        $response = $this->put(route('admin.payment-gateways.update', $this->gateway), $updateData);

        $response->assertRedirect(route('admin.payment-gateways.index'));
        $response->assertSessionHas('success');

        $this->gateway->refresh();
        $this->assertEquals('Razorpay Updated', $this->gateway->display_name);
        $this->assertEquals('Updated description', $this->gateway->description);
        $this->assertFalse($this->gateway->is_active);
    }

    public function test_admin_can_toggle_gateway_status()
    {
        $this->actingAs($this->admin);

        $response = $this->post(route('admin.payment-gateways.toggle-status', $this->gateway));

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->gateway->refresh();
        $this->assertFalse($this->gateway->is_active);

        // Toggle again
        $response = $this->post(route('admin.payment-gateways.toggle-status', $this->gateway));

        $this->gateway->refresh();
        $this->assertTrue($this->gateway->is_active);
    }

    public function test_admin_can_test_gateway_connection()
    {
        $this->actingAs($this->admin);

        $response = $this->post(route('admin.payment-gateways.test', $this->gateway));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message'
        ]);
    }

    public function test_admin_can_test_all_gateway_connections()
    {
        // Create multiple gateways
        PaymentGateway::factory()->count(2)->create(['is_active' => true]);

        $this->actingAs($this->admin);

        $response = $this->post(route('admin.payment-gateways.test-all'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'results'
        ]);
    }

    public function test_admin_can_set_default_gateway()
    {
        $this->actingAs($this->admin);

        $response = $this->post(route('admin.payment-gateways.set-default', $this->gateway));

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->gateway->refresh();
        $this->assertTrue($this->gateway->is_default);

        // Verify other gateways are not default
        $otherGateways = PaymentGateway::where('id', '!=', $this->gateway->id)->get();
        foreach ($otherGateways as $gateway) {
            $this->assertFalse($gateway->is_default);
        }
    }

    public function test_admin_can_delete_gateway()
    {
        $this->actingAs($this->admin);

        $response = $this->delete(route('admin.payment-gateways.destroy', $this->gateway));

        $response->assertRedirect(route('admin.payment-gateways.index'));
        $response->assertSessionHas('success');

        $this->assertSoftDeleted('payment_gateways', [
            'id' => $this->gateway->id
        ]);
    }

    public function test_admin_can_perform_bulk_actions()
    {
        $gateways = PaymentGateway::factory()->count(3)->create();

        $this->actingAs($this->admin);

        $response = $this->post(route('admin.payment-gateways.bulk-action'), [
            'action' => 'disable',
            'gateway_ids' => $gateways->pluck('id')->toArray()
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        foreach ($gateways as $gateway) {
            $gateway->refresh();
            $this->assertFalse($gateway->is_active);
        }
    }

    public function test_admin_can_get_config_fields_for_gateway_type()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payment-gateways.config-fields', [
            'gateway_type' => 'razorpay'
        ]));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'fields' => [
                '*' => [
                    'name',
                    'type',
                    'label',
                    'required'
                ]
            ]
        ]);
    }

    public function test_admin_validation_for_gateway_creation()
    {
        $this->actingAs($this->admin);

        // Test with missing required fields
        $response = $this->post(route('admin.payment-gateways.store'), [
            // Missing required fields
        ]);

        $response->assertSessionHasErrors(['name', 'display_name']);
    }

    public function test_admin_validation_for_duplicate_gateway_name()
    {
        $this->actingAs($this->admin);

        $response = $this->post(route('admin.payment-gateways.store'), [
            'name' => $this->gateway->name, // Duplicate name
            'display_name' => 'Another Gateway',
            'is_active' => true,
            'supported_currencies' => ['USD'],
            'configuration' => []
        ]);

        $response->assertSessionHasErrors('name');
    }

    public function test_admin_validation_for_invalid_configuration()
    {
        $this->actingAs($this->admin);

        $response = $this->put(route('admin.payment-gateways.update', $this->gateway), [
            'display_name' => 'Updated Gateway',
            'configuration' => [
                'key_id' => '', // Empty required field
                'key_secret' => 'test_secret'
            ]
        ]);

        $response->assertSessionHasErrors();
    }

    public function test_admin_can_view_gateway_statistics()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payment-gateways.index'));

        $response->assertStatus(200);
        // In a real implementation, you'd verify statistics are displayed
        $response->assertViewHas('gateways');
    }

    public function test_admin_can_export_gateway_configuration()
    {
        $this->actingAs($this->admin);

        // This would be a feature to export gateway configs
        $response = $this->get(route('admin.payment-gateways.export', $this->gateway));

        // This route doesn't exist yet, but would be part of export functionality
        // $response->assertStatus(200);
        // $response->assertHeader('Content-Type', 'application/json');
    }

    public function test_admin_can_import_gateway_configuration()
    {
        $this->actingAs($this->admin);

        $configData = [
            'gateways' => [
                [
                    'name' => 'stripe',
                    'display_name' => 'Stripe',
                    'configuration' => [
                        'public_key' => 'pk_test_123',
                        'secret_key' => 'sk_test_123'
                    ]
                ]
            ]
        ];

        // This would be a feature to import gateway configs
        $response = $this->post(route('admin.payment-gateways.import'), $configData);

        // This route doesn't exist yet, but would be part of import functionality
        // $response->assertRedirect();
        // $response->assertSessionHas('success');
    }

    public function test_admin_gateway_configuration_encryption()
    {
        $this->actingAs($this->admin);

        $sensitiveConfig = [
            'display_name' => 'Test Gateway',
            'configuration' => [
                'api_key' => 'sensitive_api_key',
                'secret' => 'very_secret_value'
            ]
        ];

        $response = $this->put(route('admin.payment-gateways.update', $this->gateway), $sensitiveConfig);

        $response->assertRedirect();

        // Verify sensitive data is encrypted in database
        $this->gateway->refresh();
        $config = $this->gateway->configuration;
        
        // In a real implementation, you'd verify the data is encrypted
        $this->assertIsArray($config);
        $this->assertArrayHasKey('api_key', $config);
    }

    public function test_admin_can_view_gateway_logs()
    {
        $this->actingAs($this->admin);

        // This would show logs related to the gateway
        $response = $this->get(route('admin.payment-gateways.logs', $this->gateway));

        // This route doesn't exist yet, but would be part of logging functionality
        // $response->assertStatus(200);
        // $response->assertViewIs('admin.payment-gateways.logs');
    }

    public function test_non_admin_cannot_access_gateway_management()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $response = $this->get(route('admin.payment-gateways.index'));
        $response->assertStatus(403);

        $response = $this->post(route('admin.payment-gateways.store'), []);
        $response->assertStatus(403);

        $response = $this->put(route('admin.payment-gateways.update', $this->gateway), []);
        $response->assertStatus(403);
    }

    public function test_guest_cannot_access_gateway_management()
    {
        $response = $this->get(route('admin.payment-gateways.index'));
        $response->assertRedirect(route('admin.login'));

        $response = $this->post(route('admin.payment-gateways.store'), []);
        $response->assertRedirect(route('admin.login'));
    }

    public function test_admin_gateway_sorting_and_ordering()
    {
        $gateway1 = PaymentGateway::factory()->create(['sort_order' => 1]);
        $gateway2 = PaymentGateway::factory()->create(['sort_order' => 2]);
        $gateway3 = PaymentGateway::factory()->create(['sort_order' => 3]);

        $this->actingAs($this->admin);

        $response = $this->post(route('admin.payment-gateways.reorder'), [
            'gateway_order' => [
                $gateway3->id,
                $gateway1->id,
                $gateway2->id
            ]
        ]);

        // This route doesn't exist yet, but would be part of ordering functionality
        // $response->assertRedirect();
        // $response->assertSessionHas('success');
    }

    public function test_admin_can_clone_gateway_configuration()
    {
        $this->actingAs($this->admin);

        $response = $this->post(route('admin.payment-gateways.clone', $this->gateway), [
            'new_name' => 'razorpay_clone',
            'new_display_name' => 'Razorpay Clone'
        ]);

        // This route doesn't exist yet, but would be part of cloning functionality
        // $response->assertRedirect();
        // $response->assertSessionHas('success');
        
        // Verify new gateway was created
        // $this->assertDatabaseHas('payment_gateways', [
        //     'name' => 'razorpay_clone',
        //     'display_name' => 'Razorpay Clone'
        // ]);
    }
}