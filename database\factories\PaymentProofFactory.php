<?php

namespace Database\Factories;

use App\Models\Payment;
use App\Models\PaymentProof;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PaymentProof>
 */
class PaymentProofFactory extends Factory
{
    protected $model = PaymentProof::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $fileTypes = ['jpg', 'jpeg', 'png', 'pdf'];
        $fileType = $this->faker->randomElement($fileTypes);
        $fileName = 'payment_proof_' . $this->faker->uuid() . '.' . $fileType;
        
        return [
            'payment_id' => Payment::factory(),
            'file_path' => 'payment_proofs/' . now()->format('Y/m') . '/user_' . $this->faker->numberBetween(1, 1000) . '/' . $fileName,
            'file_name' => $fileName,
            'file_size' => $this->faker->numberBetween(50000, 2000000), // 50KB to 2MB
            'mime_type' => $this->getMimeType($fileType),
            'verification_status' => $this->faker->randomElement([
                PaymentProof::STATUS_PENDING,
                PaymentProof::STATUS_APPROVED,
                PaymentProof::STATUS_REJECTED
            ]),
            'verification_notes' => $this->faker->optional(0.6)->sentence(),
            'verified_by' => $this->faker->optional(0.7)->randomElement([1, 2, 3]), // Admin user IDs
            'verified_at' => $this->faker->optional(0.7)->dateTimeBetween('-30 days', 'now'),
        ];
    }

    /**
     * Create a pending payment proof
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'verification_status' => PaymentProof::STATUS_PENDING,
            'verification_notes' => null,
            'verified_by' => null,
            'verified_at' => null,
        ]);
    }

    /**
     * Create an approved payment proof
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'verification_status' => PaymentProof::STATUS_APPROVED,
            'verification_notes' => $this->faker->randomElement([
                'Payment verified successfully',
                'Valid payment proof received',
                'Amount matches order total',
                'Bank transfer confirmed'
            ]),
            'verified_by' => $this->faker->numberBetween(1, 5),
            'verified_at' => $this->faker->dateTimeBetween('-7 days', 'now'),
        ]);
    }

    /**
     * Create a rejected payment proof
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'verification_status' => PaymentProof::STATUS_REJECTED,
            'verification_notes' => $this->faker->randomElement([
                'Invalid payment proof - amount mismatch',
                'Unclear screenshot, please resubmit',
                'Payment not found in bank records',
                'Duplicate payment submission',
                'Insufficient payment amount'
            ]),
            'rejection_reason' => $this->faker->randomElement([
                'invalid_proof',
                'insufficient_amount',
                'duplicate_payment',
                'other'
            ]),
            'verified_by' => $this->faker->numberBetween(1, 5),
            'verified_at' => $this->faker->dateTimeBetween('-7 days', 'now'),
        ]);
    }

    /**
     * Create a payment proof for a specific payment
     */
    public function forPayment(Payment $payment): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_id' => $payment->id,
        ]);
    }

    /**
     * Create a payment proof with image file
     */
    public function imageFile(): static
    {
        $imageTypes = ['jpg', 'jpeg', 'png'];
        $fileType = $this->faker->randomElement($imageTypes);
        $fileName = 'payment_screenshot_' . $this->faker->uuid() . '.' . $fileType;

        return $this->state(fn (array $attributes) => [
            'file_name' => $fileName,
            'file_path' => 'payment_proofs/' . now()->format('Y/m') . '/user_' . $this->faker->numberBetween(1, 1000) . '/' . $fileName,
            'mime_type' => $this->getMimeType($fileType),
            'file_size' => $this->faker->numberBetween(100000, 1500000), // 100KB to 1.5MB for images
        ]);
    }

    /**
     * Create a payment proof with PDF file
     */
    public function pdfFile(): static
    {
        $fileName = 'payment_receipt_' . $this->faker->uuid() . '.pdf';

        return $this->state(fn (array $attributes) => [
            'file_name' => $fileName,
            'file_path' => 'payment_proofs/' . now()->format('Y/m') . '/user_' . $this->faker->numberBetween(1, 1000) . '/' . $fileName,
            'mime_type' => 'application/pdf',
            'file_size' => $this->faker->numberBetween(200000, 3000000), // 200KB to 3MB for PDFs
        ]);
    }

    /**
     * Create a recently uploaded payment proof
     */
    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween('-3 days', 'now'),
            'verification_status' => PaymentProof::STATUS_PENDING,
            'verified_at' => null,
            'verified_by' => null,
        ]);
    }

    /**
     * Create an old payment proof
     */
    public function old(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween('-90 days', '-30 days'),
            'verification_status' => $this->faker->randomElement([
                PaymentProof::STATUS_APPROVED,
                PaymentProof::STATUS_REJECTED
            ]),
            'verified_at' => $this->faker->dateTimeBetween('-90 days', '-30 days'),
            'verified_by' => $this->faker->numberBetween(1, 5),
        ]);
    }

    /**
     * Create a large file payment proof
     */
    public function largeFile(): static
    {
        return $this->state(fn (array $attributes) => [
            'file_size' => $this->faker->numberBetween(4000000, 5000000), // 4MB to 5MB
        ]);
    }

    /**
     * Create a small file payment proof
     */
    public function smallFile(): static
    {
        return $this->state(fn (array $attributes) => [
            'file_size' => $this->faker->numberBetween(10000, 100000), // 10KB to 100KB
        ]);
    }

    /**
     * Create a payment proof verified by specific admin
     */
    public function verifiedBy(User $admin): static
    {
        return $this->state(fn (array $attributes) => [
            'verified_by' => $admin->id,
            'verified_at' => $this->faker->dateTimeBetween('-7 days', 'now'),
            'verification_status' => $this->faker->randomElement([
                PaymentProof::STATUS_APPROVED,
                PaymentProof::STATUS_REJECTED
            ]),
        ]);
    }

    /**
     * Create a payment proof with custom notes
     */
    public function withNotes(string $notes): static
    {
        return $this->state(fn (array $attributes) => [
            'verification_notes' => $notes,
        ]);
    }

    /**
     * Get MIME type for file extension
     */
    private function getMimeType(string $extension): string
    {
        return match (strtolower($extension)) {
            'jpg', 'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'pdf' => 'application/pdf',
            default => 'application/octet-stream'
        };
    }
}