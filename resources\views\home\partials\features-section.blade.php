@if(isset($landingPage['features']) && $landingPage['features']['active'])
        <!-- Features Section -->
        <section id="features"
            class="py-20 bg-gradient-to-br from-primary-light/10 to-accent-light/10 dark:from-primary-dark/10 dark:to-accent-dark/10 transition-colors duration-300">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div
                    class="text-center max-w-3xl mx-auto mb-16 dark:from-accent-dark to-accent-light rounded-2xl p-8 shadow-lg">
                    <span
                        class="inline-block px-3 py-1 rounded-full bg-primary-light/20 text-primary-light dark:bg-primary-dark/20 dark:text-primary-dark text-xs font-semibold tracking-wider uppercase mb-3">{{ $landingPage['features']['content']['badge_text'] ?? 'Features' }}</span>
                    <h2
                        class="text-3xl md:text-4xl font-extrabold mb-4 text-text-primary-light dark:text-text-primary-dark">
                        {{ $landingPage['features']['content']['heading'] ?? 'Everything you need for postal code information' }}
                    </h2>
                    <p class="text-lg text-text-secondary-light dark:text-text-secondary-dark">{{ $landingPage['features']['content']['subheading'] ?? 'Our comprehensive pincode directory provides accurate and up-to-date information for all your needs.' }}</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                    @foreach ($landingPage['features']['content']['features'] ?? [] as $feature)
                        <div
                            class="bg-white dark:bg-bg-dark rounded-xl shadow-lg p-6 card-hover transition-colors duration-300 border border-border-light dark:border-border-dark">
                            <div
                                class="w-12 h-12 bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark rounded-xl flex items-center justify-center mb-5">
                                {!! $feature['icon'] ?? '' !!}
                            </div>
                            <h3 class="text-xl font-bold mb-3 text-text-primary-light dark:text-text-primary-dark">
                                {{ $feature['title'] ?? '' }}</h3>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark mb-4">
                                {{ $feature['description'] ?? '' }}</p>
                            <a href="#"
                                class="text-primary-light dark:text-primary-dark hover:text-accent-light dark:hover:text-accent-dark font-medium inline-flex items-center transition-colors duration-200">
                                {{ $landingPage['features']['content']['learn_more_text'] ?? 'Learn more' }}
                                <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif