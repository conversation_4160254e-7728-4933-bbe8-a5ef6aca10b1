#!/usr/bin/env python3
import mysql.connector
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("migration.log"),
        logging.StreamHandler()
    ]
)

# Database connection configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'pincodes-new'
}

def connect_to_db():
    """Establish database connection"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        logging.info("Database connection established successfully")
        return conn
    except mysql.connector.Error as err:
        logging.error(f"Error connecting to database: {err}")
        raise

def migrate_users():
    """Migrate data from users2 to users table"""
    conn = connect_to_db()
    cursor = conn.cursor(dictionary=True)
    
    try:
        # Get total count for logging purposes
        cursor.execute("SELECT COUNT(*) as count FROM users2")
        total_users = cursor.fetchone()['count']
        logging.info(f"Starting migration of {total_users} users from users2 to users")
        
        # Fetch all users from users2
        cursor.execute("SELECT * FROM users2")
        users2_data = cursor.fetchall()
        
        # Set up counters
        successful = 0
        failed = 0
        
        for user in users2_data:
            try:
                # Map fields from users2 to users
                # For name, concatenate firstname and lastname if available
                name = user['firstname'] or ''
                if user['lastname']:
                    name += ' ' + user['lastname'] if name else user['lastname']
                
                # If no name is available, use username
                if not name:
                    name = user['username']
                
                # Map status from integer to string
                status_value = 'active' if user['status'] == 1 else 'banned'
                
                # Map email_verified_at based on ev value
                email_verified_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S') if user['ev'] == 1 else None
                
                # Insert into users table
                insert_query = """
                INSERT INTO users (
                    id, name, email, email_verified_at, password, role, status, 
                    remember_token, created_at, updated_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                ) ON DUPLICATE KEY UPDATE
                    name = VALUES(name),
                    email_verified_at = VALUES(email_verified_at),
                    password = VALUES(password),
                    role = VALUES(role),
                    status = VALUES(status),
                    remember_token = VALUES(remember_token),
                    updated_at = VALUES(updated_at)
                """
                
                cursor.execute(insert_query, (
                    user['id'],
                    name,
                    user['email'],
                    email_verified_at,
                    user['password'],
                    user['role'],
                    status_value,
                    user['remember_token'],
                    user['created_at'],
                    user['updated_at']
                ))
                
                successful += 1
                if successful % 100 == 0:  # Log progress every 100 records
                    logging.info(f"Processed {successful} users")
                
            except Exception as e:
                failed += 1
                logging.error(f"Error migrating user ID {user['id']}: {str(e)}")
                
        # Commit the changes
        conn.commit()
        logging.info(f"Migration completed: {successful} users migrated successfully, {failed} failed")
        
    except Exception as e:
        conn.rollback()
        logging.error(f"Migration failed: {str(e)}")
        raise
    finally:
        cursor.close()
        conn.close()
        logging.info("Database connection closed")

def main():
    try:
        start_time = datetime.now()
        logging.info(f"Starting migration at {start_time}")
        
        migrate_users()
        
        end_time = datetime.now()
        duration = end_time - start_time
        logging.info(f"Migration completed at {end_time}. Total duration: {duration}")
        
    except Exception as e:
        logging.error(f"Migration script failed: {str(e)}")

if __name__ == "__main__":
    main()