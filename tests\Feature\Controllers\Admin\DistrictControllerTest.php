<?php

use App\Models\District;
use App\Models\State;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create an admin user for testing
    $this->admin = User::factory()->create([
        'role' => User::ROLE_ADMIN,
        'status' => User::STATUS_ACTIVE,
    ]);
    
    // Create some test states
    $this->states = State::factory()->count(3)->create();
    
    // Create some test districts
    $this->districts = District::factory()->count(3)->create([
        'state_id' => $this->states->first()->id
    ]);
    
    // Setup fake storage
    Storage::fake('public');
});

test('admin can view districts index page', function () {
    $response = $this->actingAs($this->admin)
        ->get(route('admin.districts.index'));
    
    $response->assertStatus(200);
    $response->assertViewIs('admin.districts.index');
    $response->assertViewHas('districts');

    // Get the districts from the view data
    $viewDistricts = $response->viewData('districts');
    
    // Verify that the view has districts
    $this->assertNotEmpty($viewDistricts, 'No districts found in the view data');
    $this->assertGreaterThanOrEqual(1, $viewDistricts->count(), 'Expected at least one district in the view data');
    
    // Check that the first district has the expected attributes
    $firstDistrict = $viewDistricts->first();
    $this->assertNotNull($firstDistrict);
    $this->assertArrayHasKey('id', $firstDistrict->toArray());
    $this->assertArrayHasKey('name', $firstDistrict->toArray());
    $this->assertArrayHasKey('state_id', $firstDistrict->toArray());
});

test('admin can access district create page', function () {
    $response = $this->actingAs($this->admin)
        ->get(route('admin.districts.create'));
    
    $response->assertStatus(200);
    $response->assertViewIs('admin.districts.create');
    $response->assertViewHas('states');
});

test('admin can store a new district', function () {
    $districtName = 'New Test District';
    $state = $this->states->first();
    
    $response = $this->actingAs($this->admin)
        ->post(route('admin.districts.store'), [
            'name' => $districtName,
            'state_id' => $state->id,
        ]);
    
    $response->assertRedirect(route('admin.districts.index'));
    $response->assertSessionHas('success', 'District created successfully.');
    
    $this->assertDatabaseHas('pin_districts', [
        'name' => $districtName,
        'state_id' => $state->id,
    ]);
});

test('admin cannot store a district with invalid state_id', function () {
    $response = $this->actingAs($this->admin)
        ->post(route('admin.districts.store'), [
            'name' => 'Test District',
            'state_id' => 99999, // Non-existent state ID
        ]);
    
    $response->assertSessionHasErrors('state_id');
});

test('admin can access district edit page', function () {
    $district = $this->districts->first();
    
    $response = $this->actingAs($this->admin)
        ->get(route('admin.districts.edit', $district));
    
    $response->assertStatus(200);
    $response->assertViewIs('admin.districts.edit');
    $response->assertViewHas('district');
    $response->assertViewHas('states');
    $response->assertSee($district->name);
});

test('admin can update a district', function () {
    $district = $this->districts->first();
    $newName = 'Updated District Name';
    $newState = $this->states->last();
    
    $response = $this->actingAs($this->admin)
        ->put(route('admin.districts.update', $district), [
            'name' => $newName,
            'state_id' => $newState->id,
        ]);
    
    $response->assertRedirect(route('admin.districts.index'));
    $response->assertSessionHas('success', 'District updated successfully.');
    
    $this->assertDatabaseHas('pin_districts', [
        'id' => $district->id,
        'name' => $newName,
        'state_id' => $newState->id,
    ]);
});

test('admin can delete a district', function () {
    $district = $this->districts->first();
    
    $response = $this->actingAs($this->admin)
        ->delete(route('admin.districts.destroy', $district));
    
    $response->assertRedirect(route('admin.districts.index'));
    $response->assertSessionHas('success', 'District deleted successfully.');
    
    $this->assertDatabaseMissing('pin_districts', [
        'id' => $district->id,
    ]);
});

test('non-admin cannot access districts index page', function () {
    $regularUser = User::factory()->create([
        'role' => User::ROLE_USER,
        'status' => User::STATUS_ACTIVE,
    ]);
    
    $response = $this->actingAs($regularUser)
        ->get(route('admin.districts.index'));
    
    $response->assertStatus(403);
});

test('validation fails with missing required fields', function () {
    $response = $this->actingAs($this->admin)
        ->from(route('admin.districts.create'))
        ->post(route('admin.districts.store'), []);
    
    $response->assertRedirect();
    $response->assertSessionHasErrors(['name', 'state_id']);
});

test('validation fails with too long district name', function () {
    $response = $this->actingAs($this->admin)
        ->from(route('admin.districts.create'))
        ->post(route('admin.districts.store'), [
            'name' => str_repeat('a', 256), // 256 characters, max is 255
            'state_id' => $this->states->first()->id,
        ]);
    
    $response->assertRedirect();
    $response->assertSessionHasErrors('name');
});

test('districts are ordered by name', function () {
    // Create districts with specific names to test ordering
    District::factory()->create(['name' => 'Zebra District']);
    District::factory()->create(['name' => 'Alpha District']);
    District::factory()->create(['name' => 'Beta District']);
    
    $response = $this->actingAs($this->admin)
        ->get(route('admin.districts.index'));
    
    $response->assertStatus(200);
    
    $districts = $response->viewData('districts');
    $districtNames = $districts->pluck('name')->toArray();
    
    // Check if districts are ordered alphabetically
    $sortedNames = $districtNames;
    sort($sortedNames);
    
    $this->assertEquals($sortedNames, $districtNames, 'Districts should be ordered by name');
});

test('districts are paginated', function () {
    // Create more than 10 districts to test pagination
    District::factory()->count(15)->create();
    
    $response = $this->actingAs($this->admin)
        ->get(route('admin.districts.index'));
    
    $response->assertStatus(200);
    
    $districts = $response->viewData('districts');
    
    // Check if pagination is working (default is 10 per page)
    $this->assertLessThanOrEqual(10, $districts->count(), 'Should have at most 10 districts per page');
    $this->assertGreaterThan(10, $districts->total(), 'Should have more than 10 districts total');
});

test('district has state relationship', function () {
    $district = $this->districts->first();
    
    $response = $this->actingAs($this->admin)
        ->get(route('admin.districts.index'));
    
    $response->assertStatus(200);
    
    $districts = $response->viewData('districts');
    $firstDistrict = $districts->first();
    
    // Check if the state relationship is loaded
    $this->assertNotNull($firstDistrict->state);
    $this->assertEquals($district->state->id, $firstDistrict->state->id);
    $this->assertEquals($district->state->name, $firstDistrict->state->name);
}); 