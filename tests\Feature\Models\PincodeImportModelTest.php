<?php

use App\Models\PincodeImport;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);


// # Run all pincode import tests
// php artisan test --filter="PincodeImport"

// # Run specific test files
// php artisan test tests/Feature/Admin/PincodeImportControllerTest.php
// php artisan test tests/Feature/Models/PincodeImportModelTest.php

// # Run with coverage
// php artisan test --coverage --filter="PincodeImport"


describe('PincodeImport Model', function () {
    
    describe('Factory', function () {
        it('creates a valid import record', function () {
            $import = PincodeImport::factory()->create();
            
            expect($import)->toBeInstanceOf(PincodeImport::class);
            expect($import->user_id)->toBeInt();
            expect($import->filename)->toContain('.csv');
            expect($import->file_path)->toContain('imports/');
            expect($import->status)->toBeIn(['processing', 'completed', 'failed']);
        });

        it('creates processing import', function () {
            $import = PincodeImport::factory()->processing()->create();
            
            expect($import->status)->toBe('processing');
            expect($import->has_errors)->toBe(false);
            expect($import->error_details)->toBeNull();
        });

        it('creates completed import', function () {
            $import = PincodeImport::factory()->completed()->create();
            
            expect($import->status)->toBe('completed');
            expect($import->has_errors)->toBe(false);
            expect($import->error_details)->toBeNull();
            expect($import->failed_records)->toBe(0);
        });

        it('creates failed import', function () {
            $import = PincodeImport::factory()->failed()->create();
            
            expect($import->status)->toBe('failed');
            expect($import->has_errors)->toBe(true);
            expect($import->successful_records)->toBe(0);
        });

        it('creates completed import with errors', function () {
            $import = PincodeImport::factory()->completedWithErrors()->create();
            
            expect($import->status)->toBe('completed');
            expect($import->has_errors)->toBe(true);
            expect($import->failed_records)->toBeGreaterThan(0);
        });
    });

    describe('Relationships', function () {
        it('belongs to a user', function () {
            $user = User::factory()->create();
            $import = PincodeImport::factory()->create(['user_id' => $user->id]);
            
            expect($import->user)->toBeInstanceOf(User::class);
            expect($import->user->id)->toBe($user->id);
        });
    });

    describe('Attributes', function () {
        it('casts error_details to array', function () {
            $errorData = [
                ['row' => 1, 'error' => 'Invalid pincode format'],
                ['row' => 2, 'error' => 'Missing required field']
            ];
            
            $import = PincodeImport::factory()->create([
                'error_details' => $errorData
            ]);
            
            expect($import->error_details)->toBeArray();
            expect($import->error_details)->toHaveCount(2);
            expect($import->error_details[0]['error'])->toBe('Invalid pincode format');
        });

        it('casts boolean fields correctly', function () {
            $import = PincodeImport::factory()->create([
                'has_header' => true,
                'update_existing' => false,
                'has_errors' => true
            ]);
            
            expect($import->has_header)->toBe(true);
            expect($import->update_existing)->toBe(false);
            expect($import->has_errors)->toBe(true);
        });
    });

    describe('Scopes', function () {
        it('filters by status', function () {
            PincodeImport::factory()->processing()->create();
            PincodeImport::factory()->completed()->create();
            PincodeImport::factory()->failed()->create();
            
            $processingImports = PincodeImport::where('status', 'processing')->get();
            $completedImports = PincodeImport::where('status', 'completed')->get();
            $failedImports = PincodeImport::where('status', 'failed')->get();
            
            expect($processingImports)->toHaveCount(1);
            expect($completedImports)->toHaveCount(1);
            expect($failedImports)->toHaveCount(1);
        });

        it('filters imports with errors', function () {
            PincodeImport::factory()->completed()->create(); // No errors
            PincodeImport::factory()->completedWithErrors()->create(); // Has errors
            PincodeImport::factory()->failed()->create(); // Has errors
            
            $importsWithErrors = PincodeImport::where('has_errors', true)->get();
            
            expect($importsWithErrors)->toHaveCount(2);
        });
    });

    describe('Methods', function () {
        it('calculates success rate correctly', function () {
            $import = PincodeImport::factory()->create([
                'total_records' => 100,
                'successful_records' => 80,
                'failed_records' => 20
            ]);
            
            // Assuming there's a method to calculate success rate
            $successRate = ($import->successful_records / $import->total_records) * 100;
            
            expect($successRate)->toBe(80.0);
        });

        it('determines if import is active', function () {
            $processingImport = PincodeImport::factory()->processing()->create();
            $queuedImport = PincodeImport::factory()->create(['status' => 'queued']);
            $completedImport = PincodeImport::factory()->completed()->create();
            
            $activeStatuses = ['processing', 'queued'];
            
            expect(in_array($processingImport->status, $activeStatuses))->toBe(true);
            expect(in_array($queuedImport->status, $activeStatuses))->toBe(true);
            expect(in_array($completedImport->status, $activeStatuses))->toBe(false);
        });
    });

    describe('Validation', function () {
        it('requires user_id', function () {
            expect(function () {
                PincodeImport::factory()->create(['user_id' => null]);
            })->toThrow(\Illuminate\Database\QueryException::class);
        });

        it('requires filename', function () {
            expect(function () {
                PincodeImport::factory()->create(['filename' => null]);
            })->toThrow(\Illuminate\Database\QueryException::class);
        });

        it('requires status', function () {
            expect(function () {
                PincodeImport::factory()->create(['status' => null]);
            })->toThrow(\Illuminate\Database\QueryException::class);
        });

        it('validates status values', function () {
            $validStatuses = ['processing', 'completed', 'failed', 'queued'];
            
            foreach ($validStatuses as $status) {
                $import = PincodeImport::factory()->create(['status' => $status]);
                expect($import->status)->toBe($status);
            }
        });
    });

    describe('Error Details Management', function () {
        it('adds error details correctly', function () {
            $import = PincodeImport::factory()->create([
                'error_details' => null,
                'has_errors' => false
            ]);
            
            $error = ['row' => 1, 'error' => 'Test error'];
            
            // Simulate adding error details
            $currentErrors = $import->error_details ?? [];
            $currentErrors[] = $error;
            
            $import->update([
                'error_details' => $currentErrors,
                'has_errors' => true,
                'failed_records' => count($currentErrors)
            ]);
            
            $import->refresh();
            
            expect($import->has_errors)->toBe(true);
            expect($import->error_details)->toHaveCount(1);
            expect($import->error_details[0]['error'])->toBe('Test error');
            expect($import->failed_records)->toBe(1);
        });

        it('limits error details to prevent memory issues', function () {
            $import = PincodeImport::factory()->create([
                'error_details' => null,
                'has_errors' => false
            ]);
            
            // Simulate adding many errors
            $errors = [];
            for ($i = 1; $i <= 300; $i++) {
                $errors[] = ['row' => $i, 'error' => "Error {$i}"];
            }
            
            // Limit to 250 errors
            $limitedErrors = array_slice($errors, 0, 250);
            
            $import->update([
                'error_details' => $limitedErrors,
                'has_errors' => true,
                'failed_records' => count($errors) // Total failed, not limited
            ]);
            
            $import->refresh();
            
            expect($import->error_details)->toHaveCount(250);
            expect($import->failed_records)->toBe(300);
        });
    });

    describe('File Management', function () {
        it('generates unique file paths', function () {
            $import1 = PincodeImport::factory()->create();
            $import2 = PincodeImport::factory()->create();
            
            expect($import1->file_path)->not->toBe($import2->file_path);
        });

        it('validates file path format', function () {
            $import = PincodeImport::factory()->create();
            
            expect($import->file_path)->toContain('imports/');
            expect($import->file_path)->toContain('.csv');
        });
    });

    describe('Progress Tracking', function () {
        it('tracks import progress correctly', function () {
            $import = PincodeImport::factory()->create([
                'total_records' => 100,
                'successful_records' => 50,
                'failed_records' => 10
            ]);
            
            $processedRecords = $import->successful_records + $import->failed_records;
            $remainingRecords = $import->total_records - $processedRecords;
            
            expect($processedRecords)->toBe(60);
            expect($remainingRecords)->toBe(40);
        });

        it('determines completion status', function () {
            $incompleteImport = PincodeImport::factory()->create([
                'total_records' => 100,
                'successful_records' => 50,
                'failed_records' => 10
            ]);
            
            $completeImport = PincodeImport::factory()->create([
                'total_records' => 100,
                'successful_records' => 90,
                'failed_records' => 10
            ]);
            
            $incompleteProcessed = $incompleteImport->successful_records + $incompleteImport->failed_records;
            $completeProcessed = $completeImport->successful_records + $completeImport->failed_records;
            
            expect($incompleteProcessed)->toBeLessThan($incompleteImport->total_records);
            expect($completeProcessed)->toBe($completeImport->total_records);
        });
    });

    describe('Data Integrity', function () {
        it('maintains data consistency', function () {
            $import = PincodeImport::factory()->create([
                'total_records' => 100,
                'successful_records' => 80,
                'failed_records' => 20
            ]);
            
            $totalProcessed = $import->successful_records + $import->failed_records;
            
            expect($totalProcessed)->toBeLessThanOrEqual($import->total_records);
            expect($import->successful_records)->toBeGreaterThanOrEqual(0);
            expect($import->failed_records)->toBeGreaterThanOrEqual(0);
        });

        it('handles zero records correctly', function () {
            $import = PincodeImport::factory()->create([
                'total_records' => 0,
                'successful_records' => 0,
                'failed_records' => 0
            ]);
            
            expect($import->total_records)->toBe(0);
            expect($import->successful_records)->toBe(0);
            expect($import->failed_records)->toBe(0);
        });
    });
}); 