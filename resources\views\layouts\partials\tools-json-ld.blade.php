@if(isset($tool))
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  "name": "{{ $tool->name }}",
  "description": "{{ $tool->meta_description }}",
  "applicationCategory": "WebApplication",
  "operatingSystem": "All",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "USD",
    "availability": "https://schema.org/InStock"
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.8",
    "ratingCount": "{{ $tool->reviews_count ?? 245 }}"
  },
  "author": {
    "@type": "Organization",
    "name": "Pincodes India",
    "url": "{{ config('app.url') }}"
  },
  "potentialAction": {
    "@type": "UseAction",
    "target": "{{ url()->current() }}"
  },
  "screenshot": {
    "@type": "ImageObject",
    "url": "{{ asset('assets/images/tools/' . $tool->image_path) }}",
    "width": "1366",
    "height": "768",
    "caption": "Screenshot of {{ $tool->name }}"
  },
  "keywords": "{{ $tool->meta_keywords }}",
  "datePublished": "{{ $tool->created_at->format('Y-m-d') }}",
  "dateModified": "{{ $tool->updated_at->format('Y-m-d') }}"
}
</script>
@endif 