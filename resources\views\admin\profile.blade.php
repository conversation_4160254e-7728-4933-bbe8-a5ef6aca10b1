@extends('admin.layouts.admin')

@section('title', 'Admin Profile')
@section('page-title', 'Your Profile')

@section('content')
<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Profile Information -->
    <div class="md:col-span-2">
        <div class="bg-bg-light dark:bg-bg-dark shadow-md rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-border-light dark:border-border-dark">
                <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">Profile Information</h2>
                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark mt-1">Update your account's profile information and email address.</p>
            </div>

            <form action="{{ route('admin.profile.update') }}" method="POST" class="p-6">
                @csrf
                @method('PUT')
                <div class="space-y-4">
                    <div>
                        <label for="name" class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Name</label>
                        <input type="text" name="name" id="name" value="{{ old('name', auth()->user()->name) }}" required
                            class="w-full border-border-light dark:border-border-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 @error('name') border-accent-dark dark:border-accent-light @enderror bg-bg-light dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                        @error('name')
                            <p class="mt-1 text-sm text-accent-dark dark:text-accent-light">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Email Address</label>
                        <input type="email" name="email" id="email" value="{{ old('email', auth()->user()->email) }}" required
                            class="w-full border-border-light dark:border-border-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 @error('email') border-accent-dark dark:border-accent-light @enderror bg-bg-light dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                        @error('email')
                            <p class="mt-1 text-sm text-accent-dark dark:text-accent-light">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="mt-6">
                    <button type="submit" class="bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-text-primary-dark dark:text-text-primary-light px-4 py-2 rounded-md text-sm font-medium">
                        Save
                    </button>
                </div>
            </form>
        </div>

        <!-- Update Password -->
        <div class="bg-bg-light dark:bg-bg-dark shadow-md rounded-lg overflow-hidden mt-6">
            <div class="px-6 py-4 border-b border-border-light dark:border-border-dark">
                <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">Update Password</h2>
                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark mt-1">Ensure your account is using a long, random password to stay secure.</p>
            </div>

            <form action="{{ route('admin.profile.password') }}" method="POST" class="p-6">
                @csrf
                @method('PUT')
                <div class="space-y-4">
                    <div>
                        <label for="current_password" class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Current Password</label>
                        <input type="password" name="current_password" id="current_password" required
                            class="w-full border-border-light dark:border-border-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 @error('current_password') border-accent-dark dark:border-accent-light @enderror bg-bg-light dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                        @error('current_password')
                            <p class="mt-1 text-sm text-accent-dark dark:text-accent-light">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="new_password" class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">New Password</label>
                        <input type="password" name="new_password" id="new_password" required
                            class="w-full border-border-light dark:border-border-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 @error('new_password') border-accent-dark dark:border-accent-light @enderror bg-bg-light dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                        @error('new_password')
                            <p class="mt-1 text-sm text-accent-dark dark:text-accent-light">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="new_password_confirmation" class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Confirm Password</label>
                        <input type="password" name="new_password_confirmation" id="new_password_confirmation" required
                            class="w-full border-border-light dark:border-border-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-bg-light dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                    </div>
                </div>

                <div class="mt-6">
                    <button type="submit" class="bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-text-primary-dark dark:text-text-primary-light px-4 py-2 rounded-md text-sm font-medium">
                        Update Password
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Profile Summary -->
    <div>
        <div class="bg-bg-light dark:bg-bg-dark shadow-md rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-border-light dark:border-border-dark">
                <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">Profile Summary</h2>
            </div>

            <div class="p-6">
                <div class="flex flex-col items-center">
                    <div class="h-24 w-24 rounded-full bg-border-light dark:bg-border-dark flex items-center justify-center text-text-secondary-light dark:text-text-secondary-dark text-3xl mb-4">
                        {{ substr(auth()->user()->name ?? 'A', 0, 1) }}
                    </div>
                    <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">{{ auth()->user()->name }}</h3>
                    <p class="text-text-secondary-light dark:text-text-secondary-dark">{{ auth()->user()->email }}</p>
                    
                    <div class="mt-4 w-full">
                        <div class="flex justify-between py-3 border-b border-border-light dark:border-border-dark">
                            <span class="text-text-secondary-light dark:text-text-secondary-dark">Role</span>
                            <span class="font-medium text-text-primary-light dark:text-text-primary-dark">{{ ucfirst(auth()->user()->role ?? 'Admin') }}</span>
                        </div>
                        <div class="flex justify-between py-3 border-b border-border-light dark:border-border-dark">
                            <span class="text-text-secondary-light dark:text-text-secondary-dark">Joined</span>
                            <span class="font-medium text-text-primary-light dark:text-text-primary-dark">{{ auth()->user()->created_at->format('M d, Y') }}</span>
                        </div>
                        {{-- <div class="flex justify-between py-3 border-b border-border-light dark:border-border-dark">
                            <span class="text-text-secondary-light dark:text-text-secondary-dark">Last Login</span>
                            <span class="font-medium text-text-primary-light dark:text-text-primary-dark">{{ auth()->user()->last_login_at ? auth()->user()->last_login_at->format('M d, Y H:i') : 'Never' }}</span>
                        </div> --}}
                        <div class="flex justify-between py-3">
                            <span class="text-text-secondary-light dark:text-text-secondary-dark">Status</span>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ auth()->user()->status === 'active' ? 'bg-accent-light/10 dark:bg-accent-dark/10 text-accent-light dark:text-accent-dark' : 'bg-accent-dark/10 dark:bg-accent-light/10 text-accent-dark dark:text-accent-light' }}">
                                {{ ucfirst(auth()->user()->status ?? 'Active') }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Two-Factor Authentication -->
        <div class="bg-bg-light dark:bg-bg-dark shadow-md rounded-lg overflow-hidden mt-6">
            <div class="px-6 py-4 border-b border-border-light dark:border-border-dark">
                <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">Two-Factor Authentication</h2>
                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark mt-1">Add additional security to your account using two-factor authentication.</p>
            </div>

            <div class="p-6">
                @if(auth()->user()->two_factor_enabled ?? false)
                    <div class="mb-4">
                        <div class="flex items-center">
                            <div class="bg-accent-light/10 dark:bg-accent-dark/10 p-2 rounded-full">
                                <svg class="h-6 w-6 text-accent-light dark:text-accent-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Two-factor authentication is enabled</h3>
                                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Your account is protected with two-factor authentication.</p>
                            </div>
                        </div>
                    </div>

                    <form action="{{ route('admin.two-factor.disable') }}" method="POST">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="bg-accent-dark dark:bg-accent-light hover:bg-accent-light dark:hover:bg-accent-dark text-text-primary-light dark:text-text-primary-dark px-4 py-2 rounded-md text-sm font-medium">
                            Disable Two-Factor Authentication
                        </button>
                    </form>
                @else
                    <div class="mb-4">
                        <div class="flex items-center">
                            <div class="bg-accent-dark/10 dark:bg-accent-light/10 p-2 rounded-full">
                                <svg class="h-6 w-6 text-accent-dark dark:text-accent-light" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Two-factor authentication is not enabled</h3>
                                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Add an extra layer of security to your account.</p>
                            </div>
                        </div>
                    </div>

                    <form action="{{ route('admin.profile.2fa.enable') }}" method="POST">
                        @csrf
                        <button type="submit" class="bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-text-primary-dark dark:text-text-primary-light px-4 py-2 rounded-md text-sm font-medium">
                            Enable Two-Factor Authentication
                        </button>
                    </form>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
