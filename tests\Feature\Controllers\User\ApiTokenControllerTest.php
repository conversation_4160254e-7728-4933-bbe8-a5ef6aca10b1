<?php

use App\Models\User;
use App\Models\PersonalAccessToken;
use App\Models\Order;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = User::factory()->create([
        'role' => User::ROLE_USER,
        'status' => User::STATUS_ACTIVE
    ]);
});

test('user can view their api tokens', function () {
    $token = PersonalAccessToken::factory()->create([
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);

    $response = $this->actingAs($this->user)
        ->get(route('user.api-tokens.index'));

    $response->assertStatus(200)
        ->assertViewIs('user.api-tokens.index')
        ->assertViewHas('tokens', function ($tokens) use ($token) {
            return $tokens->count() === 1 && $tokens->first()->id === $token->id;
        });
});

test('user can view create token page', function () {
    $response = $this->actingAs($this->user)
        ->get(route('user.api-tokens.create'));

    $response->assertStatus(200)
        ->assertViewIs('user.api-tokens.create');
});

test('user cannot create token without active subscription', function () {
    $response = $this->actingAs($this->user)
        ->withHeaders(['X-Requested-With' => 'XMLHttpRequest'])
        ->post(route('tokens.store'), [
            'name' => 'Test Token'
        ]);

    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'errors' => [
                'subscription' => ['You need an active subscription to create API tokens.']
            ]
        ]);

    $this->assertDatabaseMissing('personal_access_tokens', [
        'name' => 'Test Token',
        'tokenable_id' => $this->user->id
    ]);
});

test('user can create token with active subscription', function () {
    // Create an active subscription
    Order::factory()->create([
        'user_id' => $this->user->id,
        'status' => Order::STATUS_COMPLETED,
        'paid_at' => now()
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['X-Requested-With' => 'XMLHttpRequest'])
        ->post(route('tokens.store'), [
            'name' => 'Test Token'
        ]);

    $response->assertStatus(200)
        ->assertJson([
            'success' => true
        ])
        ->assertJsonStructure([
            'success',
            'token',
            'expires_at'
        ]);

    $this->assertDatabaseHas('personal_access_tokens', [
        'name' => 'Test Token',
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);
});

test('user cannot create more than maximum allowed tokens', function () {
    // Create an active subscription
    Order::factory()->create([
        'user_id' => $this->user->id,
        'status' => Order::STATUS_COMPLETED,
        'paid_at' => now()
    ]);

    // Create maximum allowed tokens
    PersonalAccessToken::factory()->count(User::TOKEN_MAX_PER_USER)->create([
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['X-Requested-With' => 'XMLHttpRequest'])
        ->post(route('tokens.store'), [
            'name' => 'Extra Token'
        ]);

    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'errors' => [
                'token_limit' => ['You have reached the maximum number of allowed tokens.']
            ]
        ]);

    $this->assertDatabaseMissing('personal_access_tokens', [
        'name' => 'Extra Token',
        'tokenable_id' => $this->user->id
    ]);
});

test('user can delete their token', function () {
    $token = PersonalAccessToken::factory()->create([
        'name' => 'Test Token',
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['X-Requested-With' => 'XMLHttpRequest'])
        ->delete(route('tokens.destroy', $token->id));

    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'API token deleted successfully.'
        ]);

    $this->assertDatabaseMissing('personal_access_tokens', [
        'id' => $token->id
    ]);
});

test('user cannot delete another users token', function () {
    $otherUser = User::factory()->create();
    $token = PersonalAccessToken::factory()->create([
        'tokenable_id' => $otherUser->id,
        'tokenable_type' => User::class
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['X-Requested-With' => 'XMLHttpRequest'])
        ->delete(route('tokens.destroy', $token->id));

    $response->assertStatus(404);
    $this->assertDatabaseHas('personal_access_tokens', [
        'id' => $token->id
    ]);
});

test('user can revoke all their tokens', function () {
    // Create multiple tokens
    PersonalAccessToken::factory()->count(3)->create([
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['X-Requested-With' => 'XMLHttpRequest'])
        ->post(route('tokens.revoke-all'));

    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'All tokens have been revoked.'
        ]);

    $this->assertDatabaseMissing('personal_access_tokens', [
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);
});

test('user can regenerate their token', function () {
    $token = PersonalAccessToken::factory()->create([
        'name' => 'Test Token',
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['X-Requested-With' => 'XMLHttpRequest'])
        ->post(route('tokens.regenerate', $token->id));

    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'token_regenerated' => true
        ])
        ->assertJsonStructure([
            'success',
            'token',
            'token_regenerated'
        ]);

    // Old token should be deleted
    $this->assertDatabaseMissing('personal_access_tokens', [
        'id' => $token->id
    ]);

    // New token should be created
    $this->assertDatabaseHas('personal_access_tokens', [
        'name' => 'Test Token',
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);
}); 