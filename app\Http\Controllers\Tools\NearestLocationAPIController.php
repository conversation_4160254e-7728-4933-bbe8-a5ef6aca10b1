<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\PinCode;

class NearestLocationAPIController extends Controller
{
    // /get-nearest-location/{latitude}/{longitude}


    public function findNearestLocation($latitude, $longitude)
    {
        $userLat = $latitude;
        $userLng = $longitude;

        // Define the bounding box for limiting the number of records
        $distance = 10; // Distance in kilometers
        $latRange = $distance / 111; // 1 degree latitude ~ 111 km
        $lngRange = $distance / (111 * cos(deg2rad($userLat)));

        // Using the Haversine formula to calculate distances
        $nearestLocation = PinCode::select(DB::raw("*, 
            (6371 * acos(cos(radians($userLat)) 
            * cos(radians(latitude)) 
            * cos(radians(longitude) - radians($userLng)) 
            + sin(radians($userLat)) 
            * sin(radians(latitude)))) AS distance"))
            ->whereBetween('latitude', [$userLat - $latRange, $userLat + $latRange])
            ->whereBetween('longitude', [$userLng - $lngRange, $userLng + $lngRange])
            ->orderBy('distance')
            ->first();

        return response()->json([
            'post_office' => ucfirst($nearestLocation->name),
            'pincode' => $nearestLocation->pincode,
            'district' => ucfirst($nearestLocation->district),
            'state' => ucfirst($nearestLocation->state),
            'distance' => $nearestLocation->distance,
        ]);
    }
}