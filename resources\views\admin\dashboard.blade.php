@extends('admin.layouts.admin')

@section('title', 'Admin Dashboard')
@section('page-title', 'Dashboard')

@section('content')
<div x-data="dashboardData()" x-init="fetchStats()">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-bg-light dark:bg-bg-dark rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-primary-light/10 dark:bg-primary-dark/10">
                    <svg class="h-8 w-8 text-primary-light dark:text-primary-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="text-text-secondary-light dark:text-text-secondary-dark text-sm">Total Users</h2>
                    <div class="flex items-center">
                        <div x-show="loading" class="animate-pulse h-8 w-16 bg-border-light dark:bg-border-dark rounded"></div>
                        <p x-show="!loading" class="text-2xl font-semibold text-text-primary-light dark:text-text-primary-dark" x-text="stats.users"></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-bg-light dark:bg-bg-dark rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-accent-light/10 dark:bg-accent-dark/10">
                    <svg class="h-8 w-8 text-accent-light dark:text-accent-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="text-text-secondary-light dark:text-text-secondary-dark text-sm">Monthly Revenue</h2>
                    <div class="flex items-center">
                        <div x-show="loading" class="animate-pulse h-8 w-24 bg-border-light dark:bg-border-dark rounded"></div>
                        <p x-show="!loading" class="text-2xl font-semibold text-text-primary-light dark:text-text-primary-dark" x-text="'$' + stats.revenue"></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-bg-light dark:bg-bg-dark rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-primary-dark/10 dark:bg-primary-light/10">
                    <svg class="h-8 w-8 text-primary-dark dark:text-primary-light" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="text-text-secondary-light dark:text-text-secondary-dark text-sm">Active Subscriptions</h2>
                    <div class="flex items-center">
                        <div x-show="loading" class="animate-pulse h-8 w-16 bg-border-light dark:bg-border-dark rounded"></div>
                        <p x-show="!loading" class="text-2xl font-semibold text-text-primary-light dark:text-text-primary-dark" x-text="stats.subscriptions"></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-bg-light dark:bg-bg-dark rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-accent-dark/10 dark:bg-accent-light/10">
                    <svg class="h-8 w-8 text-accent-dark dark:text-accent-light" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="text-text-secondary-light dark:text-text-secondary-dark text-sm">Page Views</h2>
                    <div class="flex items-center">
                        <div x-show="loading" class="animate-pulse h-8 w-20 bg-border-light dark:bg-border-dark rounded"></div>
                        <p x-show="!loading" class="text-2xl font-semibold text-text-primary-light dark:text-text-primary-dark" x-text="stats.pageViews"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity & Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Recent Activity -->
        <div class="bg-bg-light dark:bg-bg-dark rounded-lg shadow-md">
            <div class="border-b border-border-light dark:border-border-dark px-6 py-4">
                <h2 class="font-semibold text-lg text-text-primary-light dark:text-text-primary-dark">Recent Activity</h2>
            </div>
            <div class="p-6" x-show="loading">
                <div class="animate-pulse space-y-4">
                    <div class="flex items-center space-x-4">
                        <div class="rounded-full bg-border-light dark:bg-border-dark h-10 w-10"></div>
                        <div class="flex-1 space-y-2">
                            <div class="h-4 bg-border-light dark:bg-border-dark rounded w-3/4"></div>
                            <div class="h-4 bg-border-light dark:bg-border-dark rounded w-1/2"></div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="rounded-full bg-border-light dark:bg-border-dark h-10 w-10"></div>
                        <div class="flex-1 space-y-2">
                            <div class="h-4 bg-border-light dark:bg-border-dark rounded w-3/4"></div>
                            <div class="h-4 bg-border-light dark:bg-border-dark rounded w-1/2"></div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="rounded-full bg-border-light dark:bg-border-dark h-10 w-10"></div>
                        <div class="flex-1 space-y-2">
                            <div class="h-4 bg-border-light dark:bg-border-dark rounded w-3/4"></div>
                            <div class="h-4 bg-border-light dark:bg-border-dark rounded w-1/2"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="divide-y divide-border-light dark:divide-border-dark" x-show="!loading">
                <template x-for="(activity, index) in activities" :key="index">
                    <div class="px-6 py-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-10 w-10 rounded-full bg-border-light dark:bg-border-dark flex items-center justify-center text-text-secondary-light dark:text-text-secondary-dark" x-text="activity.user.substring(0, 1)"></div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark" x-text="activity.action"></div>
                                <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                    <span x-text="activity.user"></span>
                                    <span class="mx-1">•</span>
                                    <span x-text="activity.time"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <div class="px-6 py-4 text-center" x-show="activities.length === 0">
                    <p class="text-text-secondary-light dark:text-text-secondary-dark">No recent activity found.</p>
                </div>
            </div>
            <div class="bg-bg-light dark:bg-bg-dark border-t border-border-light dark:border-border-dark px-6 py-3">
                {{-- <a href="{{ route('admin.activity') }}" class="text-sm font-medium text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light">View all activity</a> --}}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-bg-light dark:bg-bg-dark rounded-lg shadow-md">
            <div class="border-b border-border-light dark:border-border-dark px-6 py-4">
                <h2 class="font-semibold text-lg text-text-primary-light dark:text-text-primary-dark">Quick Actions</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 gap-4">
                    <a href="{{ route('admin.users.create') }}" class="flex flex-col items-center p-4 bg-bg-light dark:bg-bg-dark rounded-lg hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 transition duration-150">
                        <svg class="h-8 w-8 text-primary-light dark:text-primary-dark mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 0 18 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                        </svg>
                        <span class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Add User</span>
                    </a>

                    <a href="{{ route('admin.settings.index') }}" class="flex flex-col items-center p-4 bg-bg-light dark:bg-bg-dark rounded-lg hover:bg-accent-light/10 dark:hover:bg-accent-dark/10 transition duration-150">
                        <svg class="h-8 w-8 text-accent-light dark:text-accent-dark mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Settings</span>
                    </a>

                    <a href="{{ route('admin.pincodes.create') }}" class="flex flex-col items-center p-4 bg-bg-light dark:bg-bg-dark rounded-lg hover:bg-primary-dark/10 dark:hover:bg-primary-light/10 transition duration-150">
                        <svg class="h-8 w-8 text-primary-dark dark:text-primary-light mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Add Pincode</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Health -->
    <div class="bg-bg-light dark:bg-bg-dark rounded-lg shadow-md">
        <div class="border-b border-border-light dark:border-border-dark px-6 py-4">
            <h2 class="font-semibold text-lg text-text-primary-light dark:text-text-primary-dark">System Health</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-bg-light dark:bg-bg-dark p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-2">PHP Version</h3>
                    <p class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark" x-text="stats.system.php_version"></p>
                </div>
                <div class="bg-bg-light dark:bg-bg-dark p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-2">Laravel Version</h3>
                    <p class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark" x-text="stats.system.laravel_version"></p>
                </div>
                <div class="bg-bg-light dark:bg-bg-dark p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-2">Server Memory</h3>
                    <div class="space-y-1">
                        <div class="flex justify-between text-sm">
                            <span class="text-text-secondary-light dark:text-text-secondary-dark">Used:</span>
                            <span class="font-medium text-text-primary-light dark:text-text-primary-dark" x-text="stats.system.memory.used"></span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-text-secondary-light dark:text-text-secondary-dark">Total:</span>
                            <span class="font-medium text-text-primary-light dark:text-text-primary-dark" x-text="stats.system.memory.total"></span>
                        </div>
                        <div class="w-full bg-border-light dark:bg-border-dark rounded-full h-2.5">
                            <div class="bg-primary-light dark:bg-primary-dark h-2.5 rounded-full" :style="`width: ${stats.system.memory.percentage}%`"></div>
                        </div>
                    </div>
                </div>
                <div class="bg-bg-light dark:bg-bg-dark p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-2">Disk Usage</h3>
                    <div class="space-y-1">
                        <div class="flex justify-between text-sm">
                            <span class="text-text-secondary-light dark:text-text-secondary-dark">Used:</span>
                            <span class="font-medium text-text-primary-light dark:text-text-primary-dark" x-text="stats.system.disk.used"></span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-text-secondary-light dark:text-text-secondary-dark">Total:</span>
                            <span class="font-medium text-text-primary-light dark:text-text-primary-dark" x-text="stats.system.disk.total"></span>
                        </div>
                        <div class="w-full bg-border-light dark:bg-border-dark rounded-full h-2.5">
                            <div class="bg-accent-light dark:bg-accent-dark h-2.5 rounded-full" :style="`width: ${stats.system.disk.percentage}%`"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    function dashboardData() {
        return {
            loading: true,
            stats: {
                users: 0,
                revenue: 0,
                subscriptions: 0,
                pageViews: 0,
                system: {
                    php_version: '',
                    laravel_version: '',
                    memory: { used: '0', total: '0', percentage: 0 },
                    disk: { used: '0', total: '0', percentage: 0 }
                }
            },
            activities: [],
            async fetchStats() {
                try {
                    const response = await fetch('/admin/dashboard/stats', {
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        }
                    });
                    if (!response.ok) throw new Error('Network response was not ok');
                    const data = await response.json();
                    this.stats = data.stats || this.stats;
                    this.activities = data.activities || [];
                } catch (error) {
                    console.error('Error fetching stats:', error);
                } finally {
                    this.loading = false;
                }
            }
        }
    }
</script>
@endpush
@endsection