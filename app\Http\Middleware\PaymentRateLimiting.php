<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class PaymentRateLimiting
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json(['error' => 'Unauthenticated.'], 401);
        }

        // Different rate limits for different payment operations
        $operation = $this->getPaymentOperation($request);
        $limits = $this->getOperationLimits($operation);
        
        $key = "payment:{$operation}:user:{$user->id}";
        
        if (RateLimiter::tooManyAttempts($key, $limits['max_attempts'])) {
            Log::warning('Payment rate limit exceeded', [
                'user_id' => $user->id,
                'operation' => $operation,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);
            
            return response()->json([
                'error' => 'Too many payment attempts',
                'message' => $limits['message'],
                'retry_after' => RateLimiter::availableIn($key)
            ], 429);
        }

        RateLimiter::hit($key, $limits['decay_minutes'] * 60);

        return $next($request);
    }

    /**
     * Determine the payment operation from the request
     *
     * @param Request $request
     * @return string
     */
    private function getPaymentOperation(Request $request): string
    {
        $route = $request->route();
        
        if (!$route) {
            return 'general';
        }

        $routeName = $route->getName();
        $method = $request->method();
        
        // Map routes to operations
        if (str_contains($routeName, 'payment.create') || $method === 'POST') {
            return 'create';
        }
        
        if (str_contains($routeName, 'payment.upload-proof')) {
            return 'upload_proof';
        }
        
        if (str_contains($routeName, 'payment.verify')) {
            return 'verify';
        }
        
        if (str_contains($routeName, 'payment.status')) {
            return 'status_check';
        }
        
        return 'general';
    }

    /**
     * Get rate limit configuration for different operations
     *
     * @param string $operation
     * @return array
     */
    private function getOperationLimits(string $operation): array
    {
        $limits = [
            'create' => [
                'max_attempts' => 5,
                'decay_minutes' => 60,
                'message' => 'Too many payment creation attempts. Please wait before trying again.'
            ],
            'upload_proof' => [
                'max_attempts' => 10,
                'decay_minutes' => 60,
                'message' => 'Too many file upload attempts. Please wait before uploading again.'
            ],
            'verify' => [
                'max_attempts' => 20,
                'decay_minutes' => 60,
                'message' => 'Too many payment verification attempts. Please wait before checking again.'
            ],
            'status_check' => [
                'max_attempts' => 30,
                'decay_minutes' => 60,
                'message' => 'Too many status check attempts. Please wait before checking again.'
            ],
            'general' => [
                'max_attempts' => 15,
                'decay_minutes' => 60,
                'message' => 'Too many payment-related requests. Please wait before trying again.'
            ]
        ];

        return $limits[$operation] ?? $limits['general'];
    }
}