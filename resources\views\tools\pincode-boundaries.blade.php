﻿@extends('layouts.app')

@section('content')
<div class="flex flex-col md:flex-row min-h-[80vh] bg-bg-light dark:bg-bg-dark">
    <!-- Sidebar -->
    <aside class="md:w-1/3 w-full bg-white dark:bg-slate-800 p-6 border-b md:border-b-0 md:border-r border-border-light dark:border-border-dark flex flex-col gap-6 sticky top-16 h-fit z-10">
        <div>
            <h2 class="text-2xl font-bold mb-4 text-text-primary-light dark:text-text-primary-dark">Pincode Directory</h2>
            <p class="mb-4 text-text-secondary-light dark:text-text-secondary-dark text-sm">
                Explore India's most comprehensive pincode directory. Find accurate postal codes, post office details, and pincode boundaries for every region, city, and village. Perfect for address validation, logistics, and local discovery.
            </p>
            <!-- Search Form -->
            <label for="pincodeSearch" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">Search Pincode</label>
            <div class="relative">
                <input type="text" id="pincodeSearch" class="block w-full rounded-lg border border-border-light dark:border-border-dark bg-bg-light dark:bg-slate-700 text-text-primary-light dark:text-text-primary-dark focus:ring-primary-light focus:border-primary-light dark:focus:ring-primary-dark dark:focus:border-primary-dark transition" placeholder="Enter pincode...">
                <div class="loading absolute right-2 top-2.5 hidden">
                    <svg class="animate-spin h-5 w-5 text-primary-light dark:text-primary-dark" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                    </svg>
                </div>
            </div>
        </div>
        <!-- Search Results -->
        <div id="searchResults" class="overflow-y-auto max-h-72 space-y-2"></div>
        <!-- Selected Pincode Info -->
        <div id="pincodeInfo" class="hidden">
            <div class="bg-bg-light dark:bg-slate-700 rounded-lg shadow p-4">
                <h5 class="text-lg font-semibold mb-2 text-primary-light dark:text-primary-dark">Pincode Details</h5>
                <div id="pincodeDetails"></div>
            </div>
        </div>
        <div id="postOfficeList" class="mt-4"></div>
        <div id="pincodeResultDiv" class="mt-4"></div>
    </aside>
    <!-- Map Section -->
    <main class="flex-1 bg-slate-50 dark:bg-bg-dark p-4 flex flex-col">
        <div class="flex flex-col h-full">
            <h4 class="text-xl font-semibold mb-2 text-text-primary-light dark:text-text-primary-dark">Map View</h4>
            <div id="map" class="rounded-lg shadow border border-border-light dark:border-border-dark flex-1 min-h-[400px] z-0"></div>
            <div class="mt-2 flex items-center justify-between">
                <small class="text-text-secondary-light dark:text-text-secondary-dark">
                    Zoom in to see pincode boundaries. Click on boundaries for details.
                </small>
                <div id="mouse-coords" class="text-xs text-text-secondary-light dark:text-text-secondary-dark"></div>
            </div>
        </div>
        <!-- FAQ Section -->
        <section class="mt-8">
            <h5 class="text-lg font-bold mb-4 text-text-primary-light dark:text-text-primary-dark">Frequently Asked Questions</h5>
            <div class="space-y-4">
                <div class="bg-white dark:bg-slate-800 rounded-lg shadow p-4">
                    <h6 class="font-semibold text-text-primary-light dark:text-text-primary-dark">What is a pincode?</h6>
                    <p class="text-text-secondary-light dark:text-text-secondary-dark text-sm mt-1">A pincode (Postal Index Number) is a unique code assigned to a specific geographic area in India to simplify mail delivery and sorting.</p>
                </div>
                <div class="bg-white dark:bg-slate-800 rounded-lg shadow p-4">
                    <h6 class="font-semibold text-text-primary-light dark:text-text-primary-dark">How can I find the pincode for my area?</h6>
                    <p class="text-text-secondary-light dark:text-text-secondary-dark text-sm mt-1">You can search for your area or post office using the search box above, or browse the map to find pincode boundaries and details.</p>
                </div>
                <div class="bg-white dark:bg-slate-800 rounded-lg shadow p-4">
                    <h6 class="font-semibold text-text-primary-light dark:text-text-primary-dark">Can I use this directory for address validation?</h6>
                    <p class="text-text-secondary-light dark:text-text-secondary-dark text-sm mt-1">Yes, this directory provides accurate and up-to-date pincode and post office information, making it ideal for address validation and logistics.</p>
                </div>
                <div class="bg-white dark:bg-slate-800 rounded-lg shadow p-4">
                    <h6 class="font-semibold text-text-primary-light dark:text-text-primary-dark">What information is available for each pincode?</h6>
                    <p class="text-text-secondary-light dark:text-text-secondary-dark text-sm mt-1">For each pincode, you can view the post office name, division, region, circle, and the geographic boundary on the map.</p>
                </div>
            </div>
        </section>

        <!-- Reviews Section -->
        <section class="py-8">
            <div class="container max-w-6xl mx-auto px-4">
                <x-tool-reviews
                    :reviews="$reviews"
                    :tool="$tool"
                    :hasMoreReviews="$hasMoreReviews ?? false"
                    :totalReviewsCount="$totalReviewsCount ?? 0"
                />
            </div>
        </section>
    </main>
</div>
@endsection

@push('styles')
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        #map { min-height: 400px; height: 500px; width: 100%; }
        .leaflet-container { font-family: inherit; }
    </style>
@endpush

@push('scripts')
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Multiple base layers
        const baseLayers = {
            "OpenStreetMap": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', { attribution: '© OpenStreetMap contributors' }),
            "CartoDB Dark": L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', { attribution: '© CartoDB' }),
            "Esri Satellite": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', { attribution: '© Esri' })
        };
        const map = L.map('map', { layers: [baseLayers["OpenStreetMap"]] }).setView([20.5937, 78.9629], 5);
        L.control.layers(baseLayers).addTo(map);
        L.control.scale().addTo(map);
        map.zoomControl.setPosition('topright');

        // Show mouse coordinates
        map.on('mousemove', function(e) {
            document.getElementById('mouse-coords').innerText = `Lat: ${e.latlng.lat.toFixed(5)}, Lng: ${e.latlng.lng.toFixed(5)}`;
        });

        // Layer group for pincodes
        const pincodeLayer = L.layerGroup().addTo(map);
        let currentPincodeLayer = null;
        let centroidMarker = null;

        // Highlight on hover
        function highlightFeature(e) {
            var layer = e.target;
            layer.setStyle({
                weight: 5,
                color: '#6366f1',
                fillOpacity: 0.4
            });
            layer.bringToFront();
        }
        function resetHighlight(e) {
            if (currentPincodeLayer && currentPincodeLayer.resetStyle) {
                currentPincodeLayer.resetStyle(e.target);
            }
        }
        function onEachFeature(feature, layer) {
            layer.on({
                mouseover: highlightFeature,
                mouseout: resetHighlight
            });
        }

        // Utility: Get centroid of polygon
        function getCentroid(coords) {
            let pts = coords[0];
            let x = 0, y = 0, n = pts.length;
            pts.forEach(pt => { x += pt[1]; y += pt[0]; });
            return [x / n, y / n];
        }

        // Search functionality
        let searchTimeout;
        const searchInput = document.getElementById('pincodeSearch');
        const searchResults = document.getElementById('searchResults');
        const loading = document.querySelector('.loading');
        const pincodeInfo = document.getElementById('pincodeInfo');
        const pincodeDetails = document.getElementById('pincodeDetails');

        function searchPincodes(query) {
            if (query.length < 3) {
                searchResults.innerHTML = '';
                return;
            }
            loading.classList.remove('hidden');
            fetch(`/pincode-geo/search?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    loading.classList.add('hidden');
                    displaySearchResults(data);
                })
                .catch(error => {
                    loading.classList.add('hidden');
                    console.error('Search error:', error);
                });
        }

        function displaySearchResults(results) {
            if (results.length === 0) {
                searchResults.innerHTML = '<div class="bg-blue-50 text-blue-700 rounded p-2 text-center">No results found</div>';
                return;
            }
            let html = '<div class="flex flex-col gap-2">';
            results.forEach(result => {
                html += `
                    <a href="#" class="block p-3 rounded-lg border border-theme-primary dark:border-gray-700 bg-theme-card dark:bg-gray-800 shadow-sm hover:bg-primary-50 dark:hover:bg-primary-900 transition pincode-result"
                       data-pincode="${result.pincode}">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-primary-700 dark:text-primary-400">${result.pincode}</span>
                            <span class="text-xs text-theme-muted">${result.circle}</span>
                        </div>
                        <div class="text-sm text-theme-primary dark:text-gray-200">${result.office_name}</div>
                        <div class="text-xs text-theme-muted">${result.division}</div>
                    </a>
                `;
            });
            html += '</div>';
            searchResults.innerHTML = html;
            document.querySelectorAll('.pincode-result').forEach(element => {
                element.addEventListener('click', function(e) {
                    e.preventDefault();
                    const pincode = this.dataset.pincode;
                    loadPincodeDetails(pincode);
                });
            });
        }

        function loadPincodeDetails(pincode) {
            fetch(`/pincode-geo/${pincode}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Pincode not found');
                        return;
                    }
                    // Display pincode info
                    const props = data.properties;
                    pincodeDetails.innerHTML = `
                        <table class="w-full text-sm">
                            <tr><th class="text-left pr-2">Pincode:</th><td>${props.pincode}</td></tr>
                            <tr><th class="text-left pr-2">Office:</th><td>${props.office_name}</td></tr>
                            <tr><th class="text-left pr-2">Division:</th><td>${props.division}</td></tr>
                            <tr><th class="text-left pr-2">Region:</th><td>${props.region || 'N/A'}</td></tr>
                            <tr><th class="text-left pr-2">Circle:</th><td>${props.circle}</td></tr>
                        </table>
                    `;
                    pincodeInfo.classList.remove('hidden');
                    // Clear previous pincode layer and centroid marker
                    if (currentPincodeLayer) map.removeLayer(currentPincodeLayer);
                    if (centroidMarker) map.removeLayer(centroidMarker);
                    // Add pincode boundary to map with highlight events
                    currentPincodeLayer = L.geoJSON(data, {
                        style: {
                            color: '#f43f5e',
                            weight: 3,
                            fillOpacity: 0.2
                        },
                        onEachFeature: onEachFeature
                    }).addTo(map);
                    // Fit map to boundary
                    map.fitBounds(currentPincodeLayer.getBounds());
                    // Add centroid marker if polygon
                    if (data.geometry && data.geometry.type === 'Polygon') {
                        const centroid = getCentroid(data.geometry.coordinates);
                        centroidMarker = L.marker(centroid, { title: 'Centroid' }).addTo(map);
                    }
                    // After rendering pincode details, fetch post offices:
                    fetch(`/pincode-geo/post-offices?pincode=${encodeURIComponent(pincode)}`)
                        .then(response => response.json())
                        .then(postOffices => {
                            renderPostOfficeList(postOffices);
                        });
                })
                .catch(error => {
                    console.error('Error loading pincode details:', error);
                });
        }

        function renderPostOfficeList(postOffices) {
            const container = document.getElementById('postOfficeList');
            if (!Array.isArray(postOffices) || postOffices.length === 0) {
                container.innerHTML = '<div class="text-theme-muted text-sm">No post offices found for this pincode.</div>';
                return;
            }
            let html = `
                <div class="mt-2">
                    <div class="flex items-center justify-between mb-2">
                        <h6 class="font-semibold text-theme-primary dark:text-gray-200">Associated Post Offices</h6>
                        <button id="togglePostOfficeList" type="button" class="text-xs px-2 py-1 rounded bg-gray-200 dark:bg-gray-700 text-theme-primary dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none">
                            Minimize
                        </button>
                    </div>
                    <div id="postOfficeListContent">
                        <ul class="space-y-2">
            `;
            postOffices.forEach(office => {
                html += `
                    <li class="bg-theme-card dark:bg-gray-800 rounded shadow p-3">
                        <div class="font-medium">
                            <a href="${office.url}" target="_blank" rel="noopener" class="text-primary-700 dark:text-primary-400 hover:underline">${capitalizeFirst(office.name)}</a>
                        </div>
                        <div class="text-xs text-theme-muted">${office.branch_type} &mdash; ${office.delivery_status}</div>
                        <div class="text-xs text-theme-muted">${capitalizeFirst(office.district)}, ${capitalizeFirst(office.state)}</div>
                        ${office.contact_number ? `<div class="text-xs text-theme-muted">Contact: ${office.contact_number}</div>` : ''}
                    </li>
                `;
            });
            html += '</ul></div></div>';
            container.innerHTML = html;

            // Add toggle functionality
            const toggleBtn = document.getElementById('togglePostOfficeList');
            const contentDiv = document.getElementById('postOfficeListContent');
            let minimized = false;
            toggleBtn.addEventListener('click', function() {
                minimized = !minimized;
                if (minimized) {
                    contentDiv.style.display = 'none';
                    toggleBtn.textContent = 'Maximize';
                } else {
                    contentDiv.style.display = '';
                    toggleBtn.textContent = 'Minimize';
                }
            });
        }

        function loadPincodesByBounds() {
            const zoom = map.getZoom();
            if (zoom < 8) {
                pincodeLayer.clearLayers();
                return;
            }
            const bounds = map.getBounds();
            const params = new URLSearchParams({
                north: bounds.getNorth(),
                south: bounds.getSouth(),
                east: bounds.getEast(),
                west: bounds.getWest()
            });
            fetch(`/pincode-geo/bounds?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error loading pincodes:', data.error);
                        return;
                    }
                    pincodeLayer.clearLayers();
                    L.geoJSON(data, {
                        style: {
                            color: '#2563eb',
                            weight: 1,
                            fillOpacity: 0.1
                        },
                        onEachFeature: function(feature, layer) {
                            const props = feature.properties;
                            layer.bindPopup(`
                                <div>
                                    <strong>${props.pincode}</strong><br>
                                    <strong>Office:</strong> ${props.office_name}<br>
                                    <strong>Division:</strong> ${props.division}<br>
                                    <strong>Circle:</strong> ${props.circle}
                                </div>
                            `);
                        }
                    }).addTo(pincodeLayer);
                })
                .catch(error => {
                    console.error('Error loading pincodes:', error);
                });
        }

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            if (query.length === 0) {
                searchResults.innerHTML = '';
                return;
            }
            searchTimeout = setTimeout(() => {
                searchPincodes(query);
            }, 300);
        });

        map.on('moveend zoomend', loadPincodesByBounds);
        if (map.getZoom() >= 8) {
            loadPincodesByBounds();
        }

        function capitalizeFirst(str) {
            if (!str) return '';
            return str.charAt(0).toUpperCase() + str.slice(1);
        }

        // --- Add: Marker on map click to show lat/lng ---
        let clickMarker = null;
        // Add: container for displaying nearest pincode info
        let pincodeResultDiv = document.getElementById('pincodeResultDiv');
        // Add: layer for displaying boundary
        let clickedBoundaryLayer = null;
        map.on('click', function(e) {
            const lat = e.latlng.lat;
            const lng = e.latlng.lng;
            // Display coordinates in the #mouse-coords div
            document.getElementById('mouse-coords').innerText = `Clicked at: Lat: ${lat.toFixed(5)}, Lng: ${lng.toFixed(5)}`;
            // If marker exists, move it; else, create new
            if (clickMarker) {
                clickMarker.setLatLng(e.latlng);
            } else {
                clickMarker = L.marker(e.latlng, {title: 'Clicked Location'}).addTo(map);
            }
            // Show loading state
            pincodeResultDiv.innerHTML = '<span class="text-theme-muted">Searching for nearest pincode...</span>';
            // Fetch nearest pincode from backend
            fetch(`/pincode-geo/nearest?lat=${lat}&lng=${lng}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        pincodeResultDiv.innerHTML = `<span class='text-red-600'>${data.error}</span>`;
                        // Remove previous boundary if any
                        if (clickedBoundaryLayer) {
                            map.removeLayer(clickedBoundaryLayer);
                            clickedBoundaryLayer = null;
                        }
                        return;
                    }
                    pincodeResultDiv.innerHTML = `
                        <div class="text-theme-primary dark:text-gray-100 font-semibold mb-1">Nearest Pincode: <span class="text-primary-700 dark:text-primary-400">${data.pincode}</span></div>
                        <div class="text-sm text-theme-secondary dark:text-gray-300">Post Office: <span class="font-medium">${data.name || data.office_name || '-'}</span></div>
                        <div class="text-xs text-theme-muted">District: ${data.district}, State: ${data.state}</div>
                        <div class="text-xs text-theme-muted">Distance: ${data.distance ? data.distance.toFixed(2) + ' km' : '-'}</div>
                    `;
                    // Remove previous boundary if any
                    if (clickedBoundaryLayer) {
                        map.removeLayer(clickedBoundaryLayer);
                        clickedBoundaryLayer = null;
                    }
                    // Add boundary if present
                    if (data.boundary && data.boundary.geometry) {
                        clickedBoundaryLayer = L.geoJSON(data.boundary, {
                            style: {
                                color: '#f43f5e',
                                weight: 3,
                                fillOpacity: 0.2
                            }
                        }).addTo(map);
                        // Fit map to boundary
                        if (clickedBoundaryLayer.getBounds && clickedBoundaryLayer.getBounds().isValid()) {
                            map.fitBounds(clickedBoundaryLayer.getBounds());
                        }
                    }
                })
                .catch(err => {
                    pincodeResultDiv.innerHTML = '<span class="text-red-600">Error fetching pincode.</span>';
                    if (clickedBoundaryLayer) {
                        map.removeLayer(clickedBoundaryLayer);
                        clickedBoundaryLayer = null;
                    }
                });
        });
    </script>
@endpush