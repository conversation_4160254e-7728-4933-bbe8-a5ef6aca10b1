<?php

namespace Tests\Mocks;

use App\Models\Order;
use App\Services\Payment\PaymentGatewayServiceInterface;
use App\Services\Payment\Responses\PaymentResponse;
use App\Services\Payment\Responses\WebhookResponse;
use App\Services\Payment\Responses\RefundResponse;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use Illuminate\Http\Request;

class MockQRBankTransferService implements PaymentGatewayServiceInterface
{
    private array $callLog = [];
    private bool $shouldFail = false;
    private string $failureReason = 'Mock failure';
    private array $mockBankDetails = [
        'bank_name' => 'Mock Bank',
        'account_name' => 'Mock Company Ltd',
        'account_number' => '**********',
        'ifsc_code' => 'MOCK0001234',
        'branch_name' => 'Mock Branch',
        'upi_id' => 'mockcompany@mockbank'
    ];

    public function createPayment(Order $order): PaymentResponse
    {
        $this->logCall('createPayment', ['order_id' => $order->id]);

        if ($this->shouldFail) {
            return PaymentResponse::error($this->failureReason, 'QR_CREATION_FAILED');
        }

        $mockPaymentId = 'qr_mock_' . uniqid();
        $paymentReference = 'QR' . strtoupper(substr($order->order_number, -8)) . rand(1000, 9999);

        // Generate mock QR data
        $qrData = $this->generateMockQRData($order, $paymentReference);

        return PaymentResponse::success([
            'payment_id' => $mockPaymentId,
            'status' => 'pending',
            'amount' => $order->amount,
            'currency' => $order->currency,
            'metadata' => [
                'qr_data' => $qrData,
                'bank_details' => $this->mockBankDetails,
                'payment_reference' => $paymentReference,
                'upload_url' => '/payment/qr/upload-proof',
                'instructions' => $this->getMockInstructions($order),
                'expires_at' => now()->addHours(24)
            ]
        ]);
    }

    public function verifyPayment(string $paymentId): PaymentResponse
    {
        $this->logCall('verifyPayment', ['payment_id' => $paymentId]);

        if ($this->shouldFail) {
            return PaymentResponse::error($this->failureReason, 'VERIFICATION_FAILED');
        }

        // Mock verification based on payment ID pattern
        if (str_contains($paymentId, 'approved')) {
            return PaymentResponse::success([
                'payment_id' => $paymentId,
                'status' => 'completed',
                'verified_at' => now(),
                'verification_method' => 'admin_approval'
            ]);
        }

        if (str_contains($paymentId, 'rejected')) {
            return PaymentResponse::error('Payment proof rejected by admin', 'PROOF_REJECTED');
        }

        // Default to pending
        return PaymentResponse::success([
            'payment_id' => $paymentId,
            'status' => 'pending',
            'message' => 'Payment proof pending admin verification'
        ]);
    }

    public function handleWebhook(Request $request): WebhookResponse
    {
        $this->logCall('handleWebhook', ['request' => $request->all()]);

        // QR Bank Transfer doesn't support webhooks
        throw new PaymentGatewayException('Webhooks not supported for QR Bank Transfer');
    }

    public function refundPayment(string $paymentId, float $amount): RefundResponse
    {
        $this->logCall('refundPayment', [
            'payment_id' => $paymentId,
            'amount' => $amount
        ]);

        // QR Bank Transfer doesn't support automatic refunds
        throw new PaymentGatewayException('Refunds not supported for QR Bank Transfer');
    }

    public function getPaymentStatus(string $paymentId): PaymentResponse
    {
        $this->logCall('getPaymentStatus', ['payment_id' => $paymentId]);

        if ($this->shouldFail) {
            return PaymentResponse::error($this->failureReason, 'STATUS_CHECK_FAILED');
        }

        // Mock status based on payment ID pattern
        if (str_contains($paymentId, 'completed')) {
            return PaymentResponse::success([
                'payment_id' => $paymentId,
                'status' => 'completed',
                'verified_at' => now()->subHours(1),
                'verification_method' => 'admin_approval'
            ]);
        }

        if (str_contains($paymentId, 'failed')) {
            return PaymentResponse::error('Payment verification failed', 'VERIFICATION_FAILED');
        }

        return PaymentResponse::success([
            'payment_id' => $paymentId,
            'status' => 'pending',
            'message' => 'Awaiting payment proof upload and verification'
        ]);
    }

    // Mock-specific methods for testing

    public function setShouldFail(bool $shouldFail, string $reason = 'Mock failure'): void
    {
        $this->shouldFail = $shouldFail;
        $this->failureReason = $reason;
    }

    public function getCallLog(): array
    {
        return $this->callLog;
    }

    public function getCallCount(string $method = null): int
    {
        if ($method === null) {
            return count($this->callLog);
        }

        return count(array_filter($this->callLog, function ($call) use ($method) {
            return $call['method'] === $method;
        }));
    }

    public function wasMethodCalled(string $method): bool
    {
        return $this->getCallCount($method) > 0;
    }

    public function getLastCall(string $method = null): ?array
    {
        $calls = $method ? array_filter($this->callLog, function ($call) use ($method) {
            return $call['method'] === $method;
        }) : $this->callLog;

        return empty($calls) ? null : end($calls);
    }

    public function clearCallLog(): void
    {
        $this->callLog = [];
    }

    public function setBankDetails(array $bankDetails): void
    {
        $this->mockBankDetails = array_merge($this->mockBankDetails, $bankDetails);
    }

    public function getBankDetails(): array
    {
        return $this->mockBankDetails;
    }

    public function generateMockQRData(Order $order, string $reference): string
    {
        // Generate mock UPI QR code data
        $upiString = sprintf(
            'upi://pay?pa=%s&pn=%s&am=%.2f&cu=%s&tn=%s',
            $this->mockBankDetails['upi_id'],
            urlencode($this->mockBankDetails['account_name']),
            $order->amount,
            $order->currency,
            urlencode("Payment for {$order->order_number} - Ref: {$reference}")
        );

        return $upiString;
    }

    public function getMockInstructions(Order $order): array
    {
        return [
            'steps' => [
                '1. Scan the QR code using any UPI app (Google Pay, PhonePe, Paytm, etc.)',
                '2. Verify the payment amount: ' . $order->currency . ' ' . number_format($order->amount, 2),
                '3. Complete the payment using your preferred UPI app',
                '4. Take a screenshot of the successful payment confirmation',
                '5. Upload the payment proof using the form below',
                '6. Wait for admin verification (usually within 24 hours)'
            ],
            'important_notes' => [
                'Payment amount must match exactly: ' . $order->currency . ' ' . number_format($order->amount, 2),
                'Include the reference number in payment description: ' . $order->order_number,
                'Keep the payment receipt for your records',
                'Upload clear, readable screenshots only'
            ],
            'support_info' => [
                'email' => '<EMAIL>',
                'phone' => '******-567-8900',
                'hours' => 'Monday to Friday, 9 AM to 6 PM'
            ]
        ];
    }

    public function simulateProofUpload(string $paymentId, string $fileName = 'payment_proof.jpg'): array
    {
        $this->logCall('simulateProofUpload', [
            'payment_id' => $paymentId,
            'file_name' => $fileName
        ]);

        return [
            'payment_id' => $paymentId,
            'file_name' => $fileName,
            'file_size' => rand(100000, 500000), // Random size between 100KB and 500KB
            'mime_type' => 'image/jpeg',
            'uploaded_at' => now(),
            'verification_status' => 'pending'
        ];
    }

    public function simulateAdminApproval(string $paymentId, string $adminNotes = 'Payment verified successfully'): array
    {
        $this->logCall('simulateAdminApproval', [
            'payment_id' => $paymentId,
            'admin_notes' => $adminNotes
        ]);

        return [
            'payment_id' => $paymentId,
            'status' => 'completed',
            'admin_notes' => $adminNotes,
            'verified_at' => now(),
            'verified_by' => 'admin_mock_' . rand(1, 100)
        ];
    }

    public function simulateAdminRejection(string $paymentId, string $reason = 'Invalid payment proof'): array
    {
        $this->logCall('simulateAdminRejection', [
            'payment_id' => $paymentId,
            'reason' => $reason
        ]);

        return [
            'payment_id' => $paymentId,
            'status' => 'failed',
            'rejection_reason' => $reason,
            'verified_at' => now(),
            'verified_by' => 'admin_mock_' . rand(1, 100)
        ];
    }

    public function simulateInvalidAmount(): void
    {
        $this->setShouldFail(true, 'Amount below minimum allowed for bank transfer');
    }

    public function simulateUnsupportedCurrency(): void
    {
        $this->setShouldFail(true, 'Currency not supported by QR Bank Transfer gateway');
    }

    public function simulateConfigurationError(): void
    {
        $this->setShouldFail(true, 'Missing required bank transfer configuration');
    }

    public function reset(): void
    {
        $this->callLog = [];
        $this->shouldFail = false;
        $this->failureReason = 'Mock failure';
        $this->mockBankDetails = [
            'bank_name' => 'Mock Bank',
            'account_name' => 'Mock Company Ltd',
            'account_number' => '**********',
            'ifsc_code' => 'MOCK0001234',
            'branch_name' => 'Mock Branch',
            'upi_id' => 'mockcompany@mockbank'
        ];
    }

    private function logCall(string $method, array $parameters = []): void
    {
        $this->callLog[] = [
            'method' => $method,
            'parameters' => $parameters,
            'timestamp' => now(),
            'call_id' => uniqid()
        ];
    }
}