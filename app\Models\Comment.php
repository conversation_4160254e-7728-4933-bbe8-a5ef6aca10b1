<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Comment extends Model
{
    use HasFactory;

    protected $fillable = [
        'content',
        'blog_post_id',
        'user_id',
        'guest_name',
        'guest_email',
        'parent_id',
        'is_approved',
        'rejected_reason',
        'moderated_at',
        'moderated_by'
    ];

    // Default values for new comments
    protected $attributes = [
        'is_approved' => false,
    ];

    protected $casts = [
        'is_approved' => 'boolean',
        'moderated_at' => 'datetime',
    ];

    // Relationship with BlogPost
    public function blogPost()
    {
        return $this->belongsTo(BlogPost::class, 'blog_post_id');
    }

    // Alias for blogPost relationship to maintain compatibility
    public function post()
    {
        return $this->belongsTo(BlogPost::class, 'blog_post_id');
    }

    // Relationship with User (Commenter)
    public function author()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    // Get the commenter name (either from user or guest)
    public function getCommenterNameAttribute()
    {
        return $this->user ? $this->user->name : $this->guest_name;
    }

    // Get the commenter email (either from user or guest)
    public function getCommenterEmailAttribute()
    {
        return $this->user ? $this->user->email : $this->guest_email;
    }

    // Check if this is a guest comment
    public function isGuestComment()
    {
        return is_null($this->user_id);
    }

    // Relationship for nested comments
    public function parent()
    {
        return $this->belongsTo(Comment::class, 'parent_id');
    }

    public function replies()
    {
        return $this->hasMany(Comment::class, 'parent_id');
    }

    // Relationship with moderator (admin who approved/rejected)
    public function moderator()
    {
        return $this->belongsTo(User::class, 'moderated_by');
    }

    // Scopes for filtering comments by status
    public function scopeApproved(Builder $query): Builder
    {
        return $query->where('is_approved', true);
    }

    public function scopePending(Builder $query): Builder
    {
        return $query->where('is_approved', false)
            ->whereNull('rejected_reason');
    }

    public function scopeRejected(Builder $query): Builder
    {
        return $query->where('is_approved', false)
            ->whereNotNull('rejected_reason');
    }
}