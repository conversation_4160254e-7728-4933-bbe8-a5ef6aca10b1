<?php

use App\Models\Order;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use function Pest\Laravel\{get, post, delete, actingAs};

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->admin = User::factory()->create(['role' => 'admin']);
});

it('can display orders list', function () {
    // Create some orders
    $orders = Order::factory()->count(3)->create();

    $response = $this->actingAs($this->admin)
        ->get(route('admin.orders.index'));

    $response->assertStatus(200)
        ->assertViewIs('admin.orders.index')
        ->assertViewHas('orders')
        ->assertSee($orders[0]->id)
        ->assertSee($orders[1]->id)
        ->assertSee($orders[2]->id);
});

it('paginates orders list', function () {
    // Create more orders than the pagination limit
    $orders = Order::factory()->count(15)->create();

    // Test first page
    $response = $this->actingAs($this->admin)
        ->get(route('admin.orders.index'));

    $response->assertStatus(200)
        ->assertViewIs('admin.orders.index')
        ->assertViewHas('orders');

    // Get the paginated orders from the view
    $paginatedOrders = $response->viewData('orders');
    
    // Assert that we have the correct number of items per page
    expect($paginatedOrders->count())->toBe(10);
    
    // Assert that the first 10 orders are on the first page
    for ($i = 0; $i < 10; $i++) {
        expect($paginatedOrders->contains($orders[$i]))->toBeTrue();
    }

    // Assert that orders beyond the first page are not visible
    for ($i = 10; $i < 15; $i++) {
        expect($paginatedOrders->contains($orders[$i]))->toBeFalse();
    }

    // Test second page
    $response = $this->actingAs($this->admin)
        ->get(route('admin.orders.index', ['page' => 2]));

    $response->assertStatus(200)
        ->assertViewIs('admin.orders.index')
        ->assertViewHas('orders');

    // Get the paginated orders from the second page
    $paginatedOrders = $response->viewData('orders');
    
    // Assert that we have the remaining orders on the second page
    expect($paginatedOrders->count())->toBe(5);
    
    // Assert that the remaining orders are on the second page
    for ($i = 10; $i < 15; $i++) {
        expect($paginatedOrders->contains($orders[$i]))->toBeTrue();
    }
});

it('can display single order details', function () {
    $order = Order::factory()->create();

    $response = $this->actingAs($this->admin)
        ->get(route('admin.orders.show', $order));

    $response->assertStatus(200)
        ->assertViewIs('admin.orders.show')
        ->assertViewHas('order')
        ->assertSee($order->id);
});

it('can update order status', function () {
    $order = Order::factory()->create(['status' => 'pending']);

    $response = $this->actingAs($this->admin)
        ->patch(route('admin.orders.update', $order), [
            'status' => 'completed',
            'notes' => 'Order completed successfully'
        ]);

    $response->assertRedirect(route('admin.orders.show', $order))
        ->assertSessionHas('success', 'Order updated successfully.');

    $this->assertDatabaseHas('orders', [
        'id' => $order->id,
        'status' => 'completed',
        'notes' => 'Order completed successfully'
    ]);
});

it('validates order status update', function () {
    $order = Order::factory()->create();

    $response = $this->actingAs($this->admin)
        ->patch(route('admin.orders.update', $order), [
            'status' => 'invalid_status',
            'notes' => 'Some notes'
        ]);

    $response->assertSessionHasErrors('status');
});

it('validates notes length', function () {
    $order = Order::factory()->create();

    $response = $this->actingAs($this->admin)
        ->patch(route('admin.orders.update', $order), [
            'status' => 'completed',
            'notes' => str_repeat('a', 501) // Exceeds 500 characters
        ]);

    $response->assertSessionHasErrors('notes');
});

it('can delete an order', function () {
    $order = Order::factory()->create();

    $response = $this->actingAs($this->admin)
        ->delete(route('admin.orders.destroy', $order));

    $response->assertRedirect(route('admin.orders.index'))
        ->assertSessionHas('success', 'Order deleted successfully.');

    $this->assertDatabaseMissing('orders', ['id' => $order->id]);
});

it('prevents non-admin users from accessing orders list', function () {
    $user = User::factory()->create(['role' => 'user']);

    $response = $this->actingAs($user)
        ->get(route('admin.orders.index'));

    $response->assertStatus(403);
});

it('prevents non-admin users from viewing order details', function () {
    $user = User::factory()->create(['role' => 'user']);
    $order = Order::factory()->create();

    $response = $this->actingAs($user)
        ->get(route('admin.orders.show', $order));

    $response->assertStatus(403);
});

it('prevents non-admin users from updating orders', function () {
    $user = User::factory()->create(['role' => 'user']);
    $order = Order::factory()->create();

    $response = $this->actingAs($user)
        ->patch(route('admin.orders.update', $order), [
            'status' => 'completed',
            'notes' => 'Some notes'
        ]);

    $response->assertStatus(403);
});

it('prevents non-admin users from deleting orders', function () {
    $user = User::factory()->create(['role' => 'user']);
    $order = Order::factory()->create();

    $response = $this->actingAs($user)
        ->delete(route('admin.orders.destroy', $order));

    $response->assertStatus(403);
});

it('requires authentication to access orders', function () {
    $response = $this->get(route('admin.orders.index'));
    $response->assertRedirect(route('admin.login'));

    $order = Order::factory()->create();
    
    $response = $this->get(route('admin.orders.show', $order));
    $response->assertRedirect(route('admin.login'));

    $response = $this->patch(route('admin.orders.update', $order), [
        'status' => 'completed',
        'notes' => 'Some notes'
    ]);
    $response->assertRedirect(route('admin.login'));

    $response = $this->delete(route('admin.orders.destroy', $order));
    $response->assertRedirect(route('admin.login'));
});