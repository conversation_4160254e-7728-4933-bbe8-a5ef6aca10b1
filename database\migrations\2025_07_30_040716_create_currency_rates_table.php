<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currency_rates', function (Blueprint $table) {
            $table->id();
            $table->string('from_currency', 3)->comment('Source currency code (ISO 4217)');
            $table->string('to_currency', 3)->comment('Target currency code (ISO 4217)');
            $table->decimal('rate', 12, 6)->comment('Exchange rate from source to target');
            $table->string('source', 50)->default('manual')->comment('Rate source: manual, api, etc.');
            $table->timestamps();
            
            // Unique constraint for currency pairs
            $table->unique(['from_currency', 'to_currency'], 'unique_currency_pair');
            
            // Indexes for performance
            $table->index(['from_currency', 'to_currency'], 'idx_currency_rates_currencies');
            $table->index('updated_at', 'idx_currency_rates_updated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currency_rates');
    }
};
