<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('likes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pincode_id')->constrained('pin_codes')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->index(['pincode_id', 'ip_address']);

            // Prevent multiple likes from same IP for same pincode
            $table->unique(['pincode_id', 'ip_address']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('likes');
    }
};