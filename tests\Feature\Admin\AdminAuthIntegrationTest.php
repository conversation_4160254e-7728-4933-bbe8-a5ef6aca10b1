<?php

use App\Models\User;
use Illuminate\Support\Facades\Hash;

beforeEach(function () {
    $this->admin = User::factory()->create([
        'role' => 'admin',
        'status' => 'active',
        'password' => Hash::make('password123')
    ]);
    
    $this->regularUser = User::factory()->create([
        'role' => 'user',
        'status' => 'active'
    ]);
    
    $this->inactiveAdmin = User::factory()->create([
        'role' => 'admin',
        'status' => 'inactive'
    ]);
});

describe('Admin Authentication Integration', function () {
    
    describe('Login Process', function () {
        it('allows admin to login with valid credentials', function () {
            $response = $this->post(route('admin.login.post'), [
                'email' => $this->admin->email,
                'password' => 'password123'
            ]);

            $response->assertRedirect(route('admin.dashboard'));
            $this->assertAuthenticatedAs($this->admin);
        });

        it('redirects to dashboard after successful login', function () {
            $response = $this->post(route('admin.login.post'), [
                'email' => $this->admin->email,
                'password' => 'password123'
            ]);

            $response->assertRedirect(route('admin.dashboard'));
            $response->assertSessionHasNoErrors();
        });

        it('rejects regular user login attempt', function () {
            $response = $this->post(route('admin.login.post'), [
                'email' => $this->regularUser->email,
                'password' => 'password123'
            ]);

            $response->assertRedirect(route('admin.login'));
            $response->assertSessionHas('error', 'You do not have admin privileges.');
            $this->assertGuest();
        });

        it('rejects inactive admin login attempt', function () {
            $response = $this->post(route('admin.login.post'), [
                'email' => $this->inactiveAdmin->email,
                'password' => 'password123'
            ]);

            $response->assertRedirect(route('admin.login'));
            $response->assertSessionHas('error', 'Your account is inactive. Please contact the administrator.');
            $this->assertGuest();
        });

        it('validates required fields', function () {
            $response = $this->post(route('admin.login.post'), []);

            $response->assertSessionHasErrors(['email', 'password']);
        });

        it('validates email format', function () {
            $response = $this->post(route('admin.login.post'), [
                'email' => 'invalid-email',
                'password' => 'password123'
            ]);

            $response->assertSessionHasErrors(['email']);
        });
    });

    describe('Logout Process', function () {
        it('logs out admin successfully', function () {
            $this->actingAs($this->admin);

            $response = $this->post(route('admin.logout'));

            $response->assertRedirect(route('admin.login'));
            $this->assertGuest();
        });

        it('clears session data on logout', function () {
            $this->actingAs($this->admin);
            session(['test_data' => 'value']);

            $response = $this->post(route('admin.logout'));

            $response->assertRedirect(route('admin.login'));
            $this->assertNull(session('test_data'));
        });
    });

    describe('Authentication Middleware', function () {
        it('redirects unauthenticated users to login', function () {
            $response = $this->get(route('admin.dashboard'));

            $response->assertRedirect(route('admin.login'));
        });

        it('allows authenticated admin to access dashboard', function () {
            $this->actingAs($this->admin);

            $response = $this->get(route('admin.dashboard'));

            $response->assertStatus(200);
        });

        it('prevents regular user from accessing admin routes', function () {
            $this->actingAs($this->regularUser);

            $response = $this->get(route('admin.dashboard'));

            $response->assertStatus(403);
        });
    });

    describe('Session Management', function () {
        it('regenerates session on login', function () {
            $oldSessionId = session()->getId();

            $response = $this->post(route('admin.login.post'), [
                'email' => $this->admin->email,
                'password' => 'password123'
            ]);

            $newSessionId = session()->getId();
            expect($newSessionId)->not->toBe($oldSessionId);
        });

        it('maintains session across admin requests', function () {
            $this->actingAs($this->admin);
            session(['admin_test' => 'persistent']);

            $response = $this->get(route('admin.dashboard'));
            
            expect(session('admin_test'))->toBe('persistent');
        });
    });
});