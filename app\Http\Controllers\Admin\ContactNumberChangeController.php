<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContactNumberChange;
use App\Models\PinCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class ContactNumberChangeController extends Controller
{
    /**
     * Display a listing of contact number change requests.
     */
    public function index(Request $request)
    {
        $query = ContactNumberChange::with(['pincode', 'user'])
            ->latest();

        // Add search functionality
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->whereHas('pincode', function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('pincode', 'like', "%{$search}%");
                })
                ->orWhere('new_number', 'like', "%{$search}%")
                ->orWhere('old_number', 'like', "%{$search}%");
            });
        }

        // Add status filter
        if ($request->has('status') && in_array($request->status, ['pending', 'approved', 'rejected'])) {
            $query->where('status', $request->status);
        }

        $changes = $query->paginate(20)->withQueryString();

        return view('admin.contact-number-changes.index', compact('changes'));
    }

    /**
     * Show the form for reviewing a specific change request.
     */
    public function show(ContactNumberChange $change)
    {
        $change->load(['pincode', 'user']);
        
        // Add related changes for the same pincode
        $relatedChanges = ContactNumberChange::where('pincode_id', $change->pincode_id)
            ->where('id', '!=', $change->id)
            ->latest()
            ->take(5)
            ->get();

        return view('admin.contact-number-changes.show', compact('change', 'relatedChanges'));
    }

    /**
     * Update the status of a contact number change request.
     */
    public function update(Request $request, ContactNumberChange $change)
    {
        $validated = $request->validate([
            'status' => ['required', Rule::in(['approved', 'rejected'])],
            'admin_notes' => 'required|string|max:500'
        ]);

        try {
            DB::beginTransaction();

            // Update the change request
            $change->update([
                'status' => $validated['status'],
                'admin_notes' => $validated['admin_notes'],
                'reviewed_at' => now(),
                'reviewed_by' => auth()->id()
            ]);

            // If approved, update the actual contact number
            if ($validated['status'] === 'approved') {
                $pincode = PinCode::findOrFail($change->pincode_id);
                
                // Log the change for audit purposes
                Log::info('Contact number updated', [
                    'pincode_id' => $pincode->id,
                    'old_number' => $pincode->contact_number,
                    'new_number' => $change->new_number,
                    'changed_by' => auth()->id(),
                    'change_request_id' => $change->id
                ]);

                $pincode->contact_number = $change->new_number;
                $pincode->save();
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Contact number change request ' . $validated['status'] . ' successfully.'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update contact number change request', [
                'error' => $e->getMessage(),
                'change_id' => $change->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update contact number change request.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get statistics for the dashboard.
     */
    public function getStats()
    {
        // dd('getStats hit');
        $stats = [
            'total' => ContactNumberChange::count(),
            'pending' => ContactNumberChange::where('status', 'pending')->count(),
            'approved' => ContactNumberChange::where('status', 'approved')->count(),
            'rejected' => ContactNumberChange::where('status', 'rejected')->count(),
            'today' => ContactNumberChange::whereDate('created_at', today())->count(),
            'this_week' => ContactNumberChange::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'this_month' => ContactNumberChange::whereMonth('created_at', now()->month)->count(),
        ];

        return response()->json($stats);
    }
} 