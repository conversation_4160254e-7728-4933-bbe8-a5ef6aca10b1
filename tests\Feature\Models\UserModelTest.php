<?php

use App\Models\User;
use App\Models\Order;
use App\Models\ApiRequest;
use App\Models\Review;
use App\Models\Plan;
use App\Models\PersonalAccessToken;
use Lara<PERSON>\Sanctum\NewAccessToken;
use Illuminate\Support\Facades\Hash;

test('user factory creates a valid user', function () {
    $user = User::factory()->create();
    
    expect($user)->toBeInstanceOf(User::class)
        ->and($user->name)->not->toBeEmpty()
        ->and($user->email)->not->toBeEmpty()
        ->and($user->email_verified_at)->not->toBeNull();
    
    // Check role and status if they exist
    if (isset($user->role)) {
        expect($user->role)->toBe(User::ROLE_USER);
    }
    
    if (isset($user->status)) {
        expect($user->status)->toBe(User::STATUS_ACTIVE);
    }
});

test('user can be created with custom attributes', function () {
    $user = User::factory()->create([
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'role' => User::ROLE_ADMIN,
        'status' => User::STATUS_INACTIVE,
    ]);
    
    expect($user->name)->toBe('Test User')
        ->and($user->email)->toBe('<EMAIL>')
        ->and($user->role)->toBe(User::ROLE_ADMIN)
        ->and($user->status)->toBe(User::STATUS_INACTIVE);
});

test('user factory can create unverified user', function () {
    $user = User::factory()->unverified()->create();
    
    expect($user->email_verified_at)->toBeNull();
});

test('fillable attributes are correctly defined', function () {
    $fillable = (new User())->getFillable();
    
    expect($fillable)->toContain('name')
        ->toContain('email')
        ->toContain('password')
        ->toContain('role')
        ->toContain('status')
        ->toContain('plan_id');
});

test('hidden attributes are correctly defined', function () {
    $hidden = (new User())->getHidden();
    
    expect($hidden)->toContain('password')
        ->toContain('remember_token');
});

test('casts are correctly defined', function () {
    $casts = (new User())->getCasts();
    
    expect($casts)->toHaveKey('email_verified_at')
        ->toHaveKey('password');
    
    expect($casts['email_verified_at'])->toBe('datetime');
    expect($casts['password'])->toBe('hashed');
});

test('isAdmin returns true for admin users', function () {
    $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);
    $user = User::factory()->create(['role' => User::ROLE_USER]);
    
    expect($admin->isAdmin())->toBeTrue()
        ->and($user->isAdmin())->toBeFalse();
});

test('isActive returns true for active users', function () {
    $active = User::factory()->create(['status' => User::STATUS_ACTIVE]);
    $inactive = User::factory()->create(['status' => User::STATUS_INACTIVE]);
    
    expect($active->isActive())->toBeTrue()
        ->and($inactive->isActive())->toBeFalse();
});

test('user has orders relationship', function () {
        $user = User::factory()->create();
        $order = Order::factory()->create(['user_id' => $user->id]);
        
        expect($user->orders)->toHaveCount(1)
            ->and($user->orders->first()->id)->toBe($order->id);
});

test('user has apiRequests relationship', function () {
        $user = User::factory()->create();
        $apiRequest = ApiRequest::factory()->create(['user_id' => $user->id]);
        
        expect($user->apiRequests)->toHaveCount(1)
            ->and($user->apiRequests->first()->id)->toBe($apiRequest->id);
});

test('user has reviews relationship', function () {
        $user = User::factory()->create();
        $review = Review::factory()->create(['user_id' => $user->id]);
        
        expect($user->reviews)->toHaveCount(1)
            ->and($user->reviews->first()->id)->toBe($review->id);
});

test('getCurrentPlan returns plan_id from latest completed order', function () {
        $user = User::factory()->create();
        $plan = Plan::factory()->create();
        
        // Create an older completed order
        Order::factory()->create([
            'user_id' => $user->id,
            'plan_id' => 'OldPlan',
            'status' => Order::STATUS_COMPLETED,
            'paid_at' => now()->subDays(10),
        ]);
        
        // Create a newer completed order
        Order::factory()->create([
            'user_id' => $user->id,
        'plan_id' => (string) $plan->id,
            'status' => Order::STATUS_COMPLETED,
            'paid_at' => now()->subDays(5),
        ]);
        
    expect($user->getCurrentPlan())->toBe((string) $plan->id);
});

test('getCurrentPlan returns Free when no completed orders exist', function () {
        $user = User::factory()->create();
        
        // Create a pending order (not completed)
        Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_PENDING,
        ]);
        
        expect($user->getCurrentPlan())->toBe('Free');
});

test('getTotalRequests returns count of api requests', function () {
        $user = User::factory()->create();
        
        // Create 3 API requests
        ApiRequest::factory()->count(3)->create(['user_id' => $user->id]);
        
        expect($user->getTotalRequests())->toBe(3);
});

test('getRemainingRequests calculates correctly with active order', function () {
        $user = User::factory()->create();
        
        // Create a completed order with request limit
        Order::factory()->create([
            'user_id' => $user->id,
            'request_limit' => 100,
            'status' => Order::STATUS_COMPLETED,
            'paid_at' => now()->subDays(5),
        ]);
        
        // Create 30 API requests
        ApiRequest::factory()->count(30)->create(['user_id' => $user->id]);
        
        expect($user->getRemainingRequests())->toBe(70); // 100 - 30
});

test('getRemainingRequests defaults to 100 for free plan', function () {
        $user = User::factory()->create();
        
        // Create 30 API requests without any order
        ApiRequest::factory()->count(30)->create(['user_id' => $user->id]);
        
        expect($user->getRemainingRequests())->toBe(70); // 100 - 30
});

test('getRemainingRequests returns 0 when limit is exceeded', function () {
        $user = User::factory()->create();
        
        // Create a completed order with request limit
        Order::factory()->create([
            'user_id' => $user->id,
            'request_limit' => 50,
            'status' => Order::STATUS_COMPLETED,
            'paid_at' => now()->subDays(5),
        ]);
        
        // Create 60 API requests (exceeding the limit)
        ApiRequest::factory()->count(60)->create(['user_id' => $user->id]);
        
        expect($user->getRemainingRequests())->toBe(0);
});

test('hasActiveSubscription returns true for recent completed orders', function () {
        $user = User::factory()->create();
        
        // Create a recent completed order
        Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_COMPLETED,
            'paid_at' => now()->subDays(15),
        ]);
        
        expect($user->hasActiveSubscription())->toBeTrue();
});

test('hasActiveSubscription returns false for old completed orders', function () {
        $user = User::factory()->create();
        
        // Create an old completed order
        Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_COMPLETED,
            'paid_at' => now()->subDays(40),
        ]);
        
        expect($user->hasActiveSubscription())->toBeFalse();
});

test('getActiveOrder returns the latest completed order', function () {
        $user = User::factory()->create();
        
        // Create an older completed order
        $oldOrder = Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_COMPLETED,
            'paid_at' => now()->subDays(20),
        ]);
        
        // Create a newer completed order
        $newOrder = Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_COMPLETED,
            'paid_at' => now()->subDays(10),
        ]);
        
        // Create a pending order (should be ignored)
        Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_PENDING,
        ]);
        
        expect($user->getActiveOrder()->id)->toBe($newOrder->id);
});

test('canCreateToken returns true when below token limit', function () {
    $user = User::factory()->create();
    
    // Create fewer tokens than the limit
    PersonalAccessToken::factory()->count(2)->create([
        'tokenable_id' => $user->id,
        'tokenable_type' => User::class
    ]);
    
    expect($user->canCreateToken())->toBeTrue();
});

test('canCreateToken returns false when at token limit', function () {
    $user = User::factory()->create();
    
    // Create tokens up to the limit
    PersonalAccessToken::factory()->count(5)->create([
        'tokenable_id' => $user->id,
        'tokenable_type' => User::class
    ]);
    
    expect($user->canCreateToken())->toBeFalse();
});

test('getActiveTokensCount returns count of non-expired tokens', function () {
    $user = User::factory()->create();
    
    // Create recent tokens (within expiry period)
    PersonalAccessToken::factory()->count(3)->create([
        'tokenable_id' => $user->id,
        'tokenable_type' => User::class,
        'created_at' => now()->subDays(100)
    ]);
    
    // Create old tokens (expired)
    PersonalAccessToken::factory()->count(2)->create([
        'tokenable_id' => $user->id,
        'tokenable_type' => User::class,
        'created_at' => now()->subDays(400)
    ]);
    
    expect($user->getActiveTokensCount())->toBe(3);
});

test('createApiToken creates token with correct expiration', function () {
    $user = User::factory()->create();
    
    $token = $user->createApiToken('Test Token');
    
    expect($token)->toBeInstanceOf(NewAccessToken::class);
    
    // Check that the token was created in the database
    $tokenRecord = PersonalAccessToken::where('tokenable_id', $user->id)
        ->where('name', 'Test Token')
        ->first();
    
    expect($tokenRecord)->not->toBeNull();
});

test('user has plan relationship', function () {
        $plan = Plan::factory()->create();
        $user = User::factory()->create(['plan_id' => $plan->id]);
        
        expect($user->plan)->toBeInstanceOf(Plan::class)
            ->and($user->plan->id)->toBe($plan->id);
});

test('password is hashed when set', function () {
    $user = User::factory()->create([
        'password' => 'password123'
    ]);
    
    // Verify the password is hashed
    expect(Hash::check('password123', $user->password))->toBeTrue();
});

test('user can be soft deleted and restored', function () {
    $user = User::factory()->create();
    $userId = $user->id;
    
    $user->delete();
    expect(User::find($userId))->toBeNull();
    expect(User::withTrashed()->find($userId))->not->toBeNull();
    
    $user->restore();
    expect(User::find($userId))->not->toBeNull();
});

test('user constants are correctly defined', function () {
    expect(User::ROLE_ADMIN)->toBe('admin')
        ->and(User::ROLE_USER)->toBe('user')
        ->and(User::STATUS_ACTIVE)->toBe('active')
        ->and(User::STATUS_INACTIVE)->toBe('inactive')
        ->and(User::TOKEN_MAX_PER_USER)->toBe(5)
        ->and(User::TOKEN_EXPIRY_DAYS)->toBe(365);
});