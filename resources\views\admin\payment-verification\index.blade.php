@extends('admin.layouts.app')

@section('title', 'Payment Verification')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <div class="d-flex">
                        <div class="dropdown me-2">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="mdi mdi-filter"></i> Filter
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="?status=pending">Pending Only</a></li>
                                <li><a class="dropdown-item" href="?status=approved">Approved</a></li>
                                <li><a class="dropdown-item" href="?status=rejected">Rejected</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="?">All Payments</a></li>
                            </ul>
                        </div>
                        <button type="button" class="btn btn-success me-2" id="bulk-approve-btn" disabled>
                            <i class="mdi mdi-check-all"></i> Bulk Approve
                        </button>
                        <button type="button" class="btn btn-danger" id="bulk-reject-btn" disabled>
                            <i class="mdi mdi-close-thick"></i> Bulk Reject
                        </button>
                    </div>
                </div>
                <h4 class="page-title">Payment Verification</h4>
            </div>
        </div>
    </div>

    <!-- Verification Statistics -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-muted mb-3 lh-1 d-block text-truncate">Pending Verification</span>
                            <h4 class="mb-3">
                                <span class="counter-value" data-target="{{ $stats['pending'] ?? 0 }}">0</span>
                            </h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-warning">
                                <span class="avatar-title bg-warning rounded-circle">
                                    <i class="mdi mdi-clock-outline font-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-muted mb-3 lh-1 d-block text-truncate">Approved Today</span>
                            <h4 class="mb-3">
                                <span class="counter-value" data-target="{{ $stats['approved_today'] ?? 0 }}">0</span>
                            </h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-success">
                                <span class="avatar-title bg-success rounded-circle">
                                    <i class="mdi mdi-check-circle font-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-muted mb-3 lh-1 d-block text-truncate">Rejected Today</span>
                            <h4 class="mb-3">
                                <span class="counter-value" data-target="{{ $stats['rejected_today'] ?? 0 }}">0</span>
                            </h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-danger">
                                <span class="avatar-title bg-danger rounded-circle">
                                    <i class="mdi mdi-close-circle font-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-muted mb-3 lh-1 d-block text-truncate">Avg. Processing Time</span>
                            <h4 class="mb-3">
                                <span class="counter-value" data-target="{{ $stats['avg_processing_hours'] ?? 0 }}">0</span>h
                            </h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-info">
                                <span class="avatar-title bg-info rounded-circle">
                                    <i class="mdi mdi-timer-outline font-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Verification Queue -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="header-title">Payment Verification Queue</h4>
                        <div class="d-flex align-items-center">
                            <div class="form-check me-3">
                                <input class="form-check-input" type="checkbox" id="select-all">
                                <label class="form-check-label" for="select-all">
                                    Select All
                                </label>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    Sort by
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="?sort=created_at&order=desc">Newest First</a></li>
                                    <li><a class="dropdown-item" href="?sort=created_at&order=asc">Oldest First</a></li>
                                    <li><a class="dropdown-item" href="?sort=amount&order=desc">Highest Amount</a></li>
                                    <li><a class="dropdown-item" href="?sort=amount&order=asc">Lowest Amount</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 20px;">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="customCheck1">
                                        </div>
                                    </th>
                                    <th>Payment Details</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Payment Proof</th>
                                    <th>Submitted</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($pendingPayments as $payment)
                                <tr data-payment-id="{{ $payment->id }}">
                                    <td>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input payment-checkbox" 
                                                   value="{{ $payment->id }}" 
                                                   {{ $payment->latestPaymentProof?->verification_status === 'pending' ? '' : 'disabled' }}>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0 me-3">
                                                <div class="avatar-xs">
                                                    <span class="avatar-title rounded-circle bg-primary">
                                                        <i class="mdi mdi-qrcode"></i>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-0">{{ $payment->order->order_number }}</h6>
                                                <small class="text-muted">{{ $payment->order->plan->name ?? 'N/A' }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <h6 class="mb-0">{{ $payment->order->user->name }}</h6>
                                            <small class="text-muted">{{ $payment->order->user->email }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-medium">{{ $payment->currency }} {{ number_format($payment->amount, 2) }}</span>
                                    </td>
                                    <td>
                                        @if($payment->latestPaymentProof)
                                        <div class="d-flex align-items-center">
                                            <button type="button" class="btn btn-sm btn-outline-primary me-2" 
                                                    onclick="viewProof({{ $payment->latestPaymentProof->id }})">
                                                <i class="mdi mdi-eye"></i> View
                                            </button>
                                            <div>
                                                <div class="small">{{ $payment->latestPaymentProof->file_name }}</div>
                                                <div class="small text-muted">{{ $payment->latestPaymentProof->getFormattedFileSize() }}</div>
                                            </div>
                                        </div>
                                        @else
                                        <span class="text-muted">No proof uploaded</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ $payment->latestPaymentProof?->created_at->diffForHumans() ?? 'N/A' }}</span>
                                    </td>
                                    <td>
                                        @php
                                            $status = $payment->latestPaymentProof?->verification_status ?? 'no_proof';
                                            $statusColors = [
                                                'pending' => 'warning',
                                                'approved' => 'success',
                                                'rejected' => 'danger',
                                                'no_proof' => 'secondary'
                                            ];
                                        @endphp
                                        <span class="badge bg-{{ $statusColors[$status] }}">
                                            {{ ucfirst(str_replace('_', ' ', $status)) }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            @if($payment->latestPaymentProof && $payment->latestPaymentProof->verification_status === 'pending')
                                            <button type="button" class="btn btn-outline-success" 
                                                    onclick="approvePayment({{ $payment->id }})" title="Approve">
                                                <i class="mdi mdi-check"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="rejectPayment({{ $payment->id }})" title="Reject">
                                                <i class="mdi mdi-close"></i>
                                            </button>
                                            @endif
                                            <button type="button" class="btn btn-outline-info" 
                                                    onclick="viewPaymentDetails({{ $payment->id }})" title="View Details">
                                                <i class="mdi mdi-information"></i>
                                            </button>
                                            <div class="dropdown">
                                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="mdi mdi-dots-horizontal"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="addNotes({{ $payment->id }})">Add Notes</a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="viewHistory({{ $payment->id }})">View History</a></li>
                                                    @if($payment->latestPaymentProof)
                                                    <li><a class="dropdown-item" href="{{ route('admin.payment-verification.download-proof', $payment->latestPaymentProof) }}" target="_blank">Download Proof</a></li>
                                                    @endif
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="mdi mdi-file-document-outline font-48 text-muted mb-3"></i>
                                            <h5 class="text-muted">No Payments Found</h5>
                                            <p class="text-muted">No payments require verification at this time.</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    @if($pendingPayments->hasPages())
                    <div class="d-flex justify-content-center mt-3">
                        {{ $pendingPayments->links() }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Proof Modal -->
<div class="modal fade" id="proofModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Payment Proof</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="proof-content">
                    <!-- Content will be loaded dynamically -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" id="approve-from-modal">
                    <i class="mdi mdi-check me-1"></i> Approve
                </button>
                <button type="button" class="btn btn-danger" id="reject-from-modal">
                    <i class="mdi mdi-close me-1"></i> Reject
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Approval/Rejection Modal -->
<div class="modal fade" id="actionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="action-modal-title">Approve Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="action-form">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea class="form-control" name="notes" rows="3" 
                                  placeholder="Add any notes about this verification..."></textarea>
                    </div>
                    <div id="rejection-reason" style="display: none;">
                        <div class="mb-3">
                            <label class="form-label">Rejection Reason *</label>
                            <select class="form-select" name="rejection_reason">
                                <option value="">Select reason</option>
                                <option value="invalid_proof">Invalid payment proof</option>
                                <option value="amount_mismatch">Amount mismatch</option>
                                <option value="duplicate_payment">Duplicate payment</option>
                                <option value="insufficient_details">Insufficient details</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="action-submit-btn">Confirm</button>
                </div>
            </form>
        </div>
    </div>
</div>@endsec
tion

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentPaymentId = null;
    let currentAction = null;

    // Select all functionality
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.payment-checkbox:not(:disabled)');
        checkboxes.forEach(cb => cb.checked = this.checked);
        updateBulkButtons();
    });

    // Individual checkbox change
    document.querySelectorAll('.payment-checkbox').forEach(cb => {
        cb.addEventListener('change', updateBulkButtons);
    });

    // Bulk approve
    document.getElementById('bulk-approve-btn').addEventListener('click', function() {
        const selectedPayments = getSelectedPayments();
        if (selectedPayments.length === 0) return;
        
        if (confirm(`Are you sure you want to approve ${selectedPayments.length} payment(s)?`)) {
            bulkAction('approve', selectedPayments);
        }
    });

    // Bulk reject
    document.getElementById('bulk-reject-btn').addEventListener('click', function() {
        const selectedPayments = getSelectedPayments();
        if (selectedPayments.length === 0) return;
        
        showActionModal('reject', selectedPayments, true);
    });

    // Action form submission
    document.getElementById('action-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const notes = formData.get('notes');
        const rejectionReason = formData.get('rejection_reason');
        
        if (currentAction === 'reject' && !rejectionReason) {
            showAlert('error', 'Please select a rejection reason');
            return;
        }
        
        const data = {
            action: currentAction,
            notes: notes,
            rejection_reason: rejectionReason
        };
        
        if (Array.isArray(currentPaymentId)) {
            // Bulk action
            data.payment_ids = currentPaymentId;
            bulkActionSubmit(data);
        } else {
            // Single action
            singleActionSubmit(currentPaymentId, data);
        }
    });

    // Counter animation
    document.querySelectorAll('.counter-value').forEach(function(counter) {
        const target = parseInt(counter.getAttribute('data-target'));
        const increment = target / 200;
        let current = 0;
        
        const timer = setInterval(function() {
            current += increment;
            if (current >= target) {
                counter.textContent = target.toLocaleString();
                clearInterval(timer);
            } else {
                counter.textContent = Math.ceil(current).toLocaleString();
            }
        }, 10);
    });
});

function updateBulkButtons() {
    const selectedCount = document.querySelectorAll('.payment-checkbox:checked').length;
    const approveBtn = document.getElementById('bulk-approve-btn');
    const rejectBtn = document.getElementById('bulk-reject-btn');
    
    approveBtn.disabled = selectedCount === 0;
    rejectBtn.disabled = selectedCount === 0;
    
    if (selectedCount > 0) {
        approveBtn.innerHTML = `<i class="mdi mdi-check-all"></i> Approve (${selectedCount})`;
        rejectBtn.innerHTML = `<i class="mdi mdi-close-thick"></i> Reject (${selectedCount})`;
    } else {
        approveBtn.innerHTML = '<i class="mdi mdi-check-all"></i> Bulk Approve';
        rejectBtn.innerHTML = '<i class="mdi mdi-close-thick"></i> Bulk Reject';
    }
}

function getSelectedPayments() {
    return Array.from(document.querySelectorAll('.payment-checkbox:checked'))
        .map(cb => parseInt(cb.value));
}

function viewProof(proofId) {
    const modal = new bootstrap.Modal(document.getElementById('proofModal'));
    const content = document.getElementById('proof-content');
    
    // Show loading
    content.innerHTML = '<div class="text-center p-4"><div class="spinner-border" role="status"></div></div>';
    modal.show();

    fetch(`{{ route('admin.payment-verification.view-proof') }}/${proofId}`)
        .then(response => response.text())
        .then(html => {
            content.innerHTML = html;
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">Failed to load payment proof</div>';
        });
}

function approvePayment(paymentId) {
    showActionModal('approve', paymentId);
}

function rejectPayment(paymentId) {
    showActionModal('reject', paymentId);
}

function showActionModal(action, paymentId, isBulk = false) {
    currentAction = action;
    currentPaymentId = paymentId;
    
    const modal = new bootstrap.Modal(document.getElementById('actionModal'));
    const title = document.getElementById('action-modal-title');
    const submitBtn = document.getElementById('action-submit-btn');
    const rejectionReason = document.getElementById('rejection-reason');
    
    if (action === 'approve') {
        title.textContent = isBulk ? `Approve ${paymentId.length} Payments` : 'Approve Payment';
        submitBtn.className = 'btn btn-success';
        submitBtn.innerHTML = '<i class="mdi mdi-check me-1"></i> Approve';
        rejectionReason.style.display = 'none';
    } else {
        title.textContent = isBulk ? `Reject ${paymentId.length} Payments` : 'Reject Payment';
        submitBtn.className = 'btn btn-danger';
        submitBtn.innerHTML = '<i class="mdi mdi-close me-1"></i> Reject';
        rejectionReason.style.display = 'block';
    }
    
    // Reset form
    document.getElementById('action-form').reset();
    
    modal.show();
}

function singleActionSubmit(paymentId, data) {
    const submitBtn = document.getElementById('action-submit-btn');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Processing...';
    submitBtn.disabled = true;
    
    const url = data.action === 'approve' 
        ? `{{ route('admin.payment-verification.approve', '') }}/${paymentId}`
        : `{{ route('admin.payment-verification.reject', '') }}/${paymentId}`;
    
    fetch(url, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('success', result.message);
            bootstrap.Modal.getInstance(document.getElementById('actionModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', result.message);
        }
    })
    .catch(error => {
        showAlert('error', 'Failed to process payment verification');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function bulkActionSubmit(data) {
    const submitBtn = document.getElementById('action-submit-btn');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Processing...';
    submitBtn.disabled = true;
    
    fetch(`{{ route('admin.payment-verification.bulk-action') }}`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('success', result.message);
            bootstrap.Modal.getInstance(document.getElementById('actionModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', result.message);
        }
    })
    .catch(error => {
        showAlert('error', 'Failed to process bulk action');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function viewPaymentDetails(paymentId) {
    window.open(`{{ route('admin.payments.show', '') }}/${paymentId}`, '_blank');
}

function addNotes(paymentId) {
    // Implementation for adding notes
    const notes = prompt('Add notes for this payment:');
    if (notes) {
        fetch(`{{ route('admin.payment-verification.add-notes') }}/${paymentId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ notes: notes })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showAlert('success', 'Notes added successfully');
            } else {
                showAlert('error', result.message);
            }
        })
        .catch(error => {
            showAlert('error', 'Failed to add notes');
        });
    }
}

function viewHistory(paymentId) {
    window.open(`{{ route('admin.payment-verification.history', '') }}/${paymentId}`, '_blank');
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
@endpush