<?php

namespace Database\Seeders;

use App\Models\PaymentGateway;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PaymentGatewaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $gateways = [
            [
                'name' => 'paypal',
                'display_name' => 'PayPal',
                'description' => 'Pay securely with your PayPal account or credit card',
                'is_active' => true,
                'is_default' => true,
                'configuration' => [
                    'mode' => 'sandbox',
                    'sandbox_client_id' => '',
                    'sandbox_client_secret' => '',
                    'live_client_id' => '',
                    'live_client_secret' => '',
                    'currency' => 'USD',
                    'payment_action' => 'Sale'
                ],
                'supported_currencies' => ['USD', 'EUR', 'GBP', 'INR'],
                'sort_order' => 1,
                'webhook_url' => null,
                'webhook_secret' => null,
                'logo_url' => '/images/gateways/paypal-logo.png',
            ],
            [
                'name' => 'razorpay',
                'display_name' => 'Razorpay',
                'description' => 'Pay with UPI, Cards, Net Banking, and Wallets',
                'is_active' => false,
                'is_default' => false,
                'configuration' => [
                    'key_id' => '',
                    'key_secret' => '',
                    'webhook_secret' => '',
                    'currency' => 'INR'
                ],
                'supported_currencies' => ['INR'],
                'sort_order' => 2,
                'webhook_url' => '/webhooks/razorpay',
                'webhook_secret' => null,
                'logo_url' => '/images/gateways/razorpay-logo.png',
            ],
            [
                'name' => 'qr_bank_transfer',
                'display_name' => 'Bank Transfer (QR)',
                'description' => 'Pay via bank transfer using QR code or manual transfer',
                'is_active' => false,
                'is_default' => false,
                'configuration' => [
                    'bank_name' => '',
                    'account_name' => '',
                    'account_number' => '',
                    'ifsc_code' => '',
                    'branch_name' => '',
                    'currency' => 'INR',
                    'qr_code_data' => ''
                ],
                'supported_currencies' => ['INR', 'USD'],
                'sort_order' => 3,
                'webhook_url' => null,
                'webhook_secret' => null,
                'logo_url' => '/images/gateways/bank-transfer-logo.png',
            ],
        ];

        foreach ($gateways as $gatewayData) {
            PaymentGateway::updateOrCreate(
                ['name' => $gatewayData['name']],
                $gatewayData
            );
        }
    }
}
