@extends('admin.layouts.app')

@section('title', 'Payment Verification Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.payment-verification.index') }}">Payment Verification</a></li>
                        <li class="breadcrumb-item active">Verification Details</li>
                    </ol>
                </div>
                <h4 class="page-title">Payment Verification Details</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Payment Information -->
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Payment Information</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Order Number:</label>
                                <p class="mb-0">{{ $payment->order->order_number }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Plan:</label>
                                <p class="mb-0">{{ $payment->order->plan->name ?? 'N/A' }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Amount:</label>
                                <p class="mb-0">{{ $payment->currency }} {{ number_format($payment->amount, 2) }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Payment Method:</label>
                                <p class="mb-0">
                                    <span class="badge bg-info">QR Bank Transfer</span>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Payment Status:</label>
                                <p class="mb-0">
                                    @php
                                        $statusColors = [
                                            'pending' => 'warning',
                                            'completed' => 'success',
                                            'failed' => 'danger'
                                        ];
                                    @endphp
                                    <span class="badge bg-{{ $statusColors[$payment->payment_status] ?? 'secondary' }}">
                                        {{ ucfirst($payment->payment_status) }}
                                    </span>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Created:</label>
                                <p class="mb-0">{{ $payment->created_at->format('M d, Y H:i') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Customer Information</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Name:</label>
                                <p class="mb-0">{{ $payment->order->user->name }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Email:</label>
                                <p class="mb-0">{{ $payment->order->user->email }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Phone:</label>
                                <p class="mb-0">{{ $payment->order->user->phone ?? 'N/A' }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Customer Since:</label>
                                <p class="mb-0">{{ $payment->order->user->created_at->format('M d, Y') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Proof -->
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Payment Proof</h4>
                </div>
                <div class="card-body">
                    @if($payment->paymentProofs->isNotEmpty())
                        @foreach($payment->paymentProofs as $proof)
                        <div class="border rounded p-3 mb-3">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0 me-3">
                                            <div class="avatar-sm">
                                                <span class="avatar-title rounded-circle bg-primary">
                                                    <i class="mdi mdi-file-image"></i>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">{{ $proof->original_filename }}</h6>
                                            <p class="text-muted mb-0">
                                                Size: {{ $proof->getFormattedFileSize() }} | 
                                                Uploaded: {{ $proof->created_at->diffForHumans() }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="viewProofImage('{{ Storage::disk('payment_proofs')->url($proof->file_path) }}')">
                                            <i class="mdi mdi-eye"></i> View
                                        </button>
                                        <a href="{{ route('admin.payment-verification.download-proof', $proof) }}" 
                                           class="btn btn-sm btn-outline-secondary" target="_blank">
                                            <i class="mdi mdi-download"></i> Download
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            @if($proof->admin_notes)
                            <div class="mt-3 pt-3 border-top">
                                <label class="form-label fw-bold">Admin Notes:</label>
                                <p class="mb-0">{{ $proof->admin_notes }}</p>
                            </div>
                            @endif
                        </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="mdi mdi-file-outline font-48 text-muted mb-3"></i>
                            <h5 class="text-muted">No Payment Proof</h5>
                            <p class="text-muted">No payment proof has been uploaded for this payment.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Verification Actions -->
            @if($payment->payment_status === 'pending' && $payment->paymentProofs->where('verification_status', 'pending')->isNotEmpty())
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Verification Actions</h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success" onclick="approvePayment()">
                            <i class="mdi mdi-check me-1"></i> Approve Payment
                        </button>
                        <button type="button" class="btn btn-danger" onclick="rejectPayment()">
                            <i class="mdi mdi-close me-1"></i> Reject Payment
                        </button>
                    </div>
                </div>
            </div>
            @endif

            <!-- Verification Status -->
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Verification Status</h4>
                </div>
                <div class="card-body">
                    @php
                        $latestProof = $payment->paymentProofs->first();
                    @endphp
                    
                    @if($latestProof)
                        <div class="mb-3">
                            <label class="form-label fw-bold">Current Status:</label>
                            <p class="mb-0">
                                @php
                                    $statusColors = [
                                        'pending' => 'warning',
                                        'approved' => 'success',
                                        'rejected' => 'danger'
                                    ];
                                @endphp
                                <span class="badge bg-{{ $statusColors[$latestProof->verification_status] ?? 'secondary' }}">
                                    {{ ucfirst($latestProof->verification_status) }}
                                </span>
                            </p>
                        </div>

                        @if($latestProof->verified_at)
                        <div class="mb-3">
                            <label class="form-label fw-bold">Verified At:</label>
                            <p class="mb-0">{{ $latestProof->verified_at->format('M d, Y H:i') }}</p>
                        </div>
                        @endif

                        @if($latestProof->verified_by)
                        <div class="mb-3">
                            <label class="form-label fw-bold">Verified By:</label>
                            <p class="mb-0">{{ $latestProof->verifiedBy->name ?? 'N/A' }}</p>
                        </div>
                        @endif

                        @if($latestProof->rejection_reason)
                        <div class="mb-3">
                            <label class="form-label fw-bold">Rejection Reason:</label>
                            <p class="mb-0">
                                <span class="badge bg-danger">{{ ucfirst(str_replace('_', ' ', $latestProof->rejection_reason)) }}</span>
                            </p>
                        </div>
                        @endif
                    @else
                        <div class="text-center">
                            <i class="mdi mdi-clock-outline font-24 text-muted mb-2"></i>
                            <p class="text-muted mb-0">Awaiting payment proof upload</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Quick Stats</h4>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="mb-2">
                                <h4 class="mb-0">{{ $payment->order->user->payments()->count() }}</h4>
                                <p class="text-muted mb-0">Total Payments</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-2">
                                <h4 class="mb-0">{{ $payment->order->user->payments()->where('payment_status', 'completed')->count() }}</h4>
                                <p class="text-muted mb-0">Successful</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Viewer Modal -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Payment Proof</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="proof-image" src="" alt="Payment Proof" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<!-- Action Modal -->
<div class="modal fade" id="actionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="action-modal-title">Approve Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="action-form">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Admin Notes</label>
                        <textarea class="form-control" name="admin_notes" rows="3" 
                                  placeholder="Add any notes about this verification..."></textarea>
                    </div>
                    <div id="rejection-reason" style="display: none;">
                        <div class="mb-3">
                            <label class="form-label">Rejection Reason *</label>
                            <select class="form-select" name="rejection_reason">
                                <option value="">Select reason</option>
                                <option value="invalid_proof">Invalid payment proof</option>
                                <option value="insufficient_amount">Insufficient amount</option>
                                <option value="duplicate_payment">Duplicate payment</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="action-submit-btn">Confirm</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let currentAction = null;

function viewProofImage(imageUrl) {
    document.getElementById('proof-image').src = imageUrl;
    new bootstrap.Modal(document.getElementById('imageModal')).show();
}

function approvePayment() {
    showActionModal('approve');
}

function rejectPayment() {
    showActionModal('reject');
}

function showActionModal(action) {
    currentAction = action;
    
    const modal = new bootstrap.Modal(document.getElementById('actionModal'));
    const title = document.getElementById('action-modal-title');
    const submitBtn = document.getElementById('action-submit-btn');
    const rejectionReason = document.getElementById('rejection-reason');
    
    if (action === 'approve') {
        title.textContent = 'Approve Payment';
        submitBtn.className = 'btn btn-success';
        submitBtn.innerHTML = '<i class="mdi mdi-check me-1"></i> Approve';
        rejectionReason.style.display = 'none';
    } else {
        title.textContent = 'Reject Payment';
        submitBtn.className = 'btn btn-danger';
        submitBtn.innerHTML = '<i class="mdi mdi-close me-1"></i> Reject';
        rejectionReason.style.display = 'block';
    }
    
    // Reset form
    document.getElementById('action-form').reset();
    
    modal.show();
}

document.getElementById('action-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const adminNotes = formData.get('admin_notes');
    const rejectionReason = formData.get('rejection_reason');
    
    if (currentAction === 'reject' && !rejectionReason) {
        alert('Please select a rejection reason');
        return;
    }
    
    const submitBtn = document.getElementById('action-submit-btn');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Processing...';
    submitBtn.disabled = true;
    
    const url = currentAction === 'approve' 
        ? '{{ route("admin.payment-verification.approve", $payment) }}'
        : '{{ route("admin.payment-verification.reject", $payment) }}';
    
    const data = {
        admin_notes: adminNotes,
        rejection_reason: rejectionReason
    };
    
    fetch(url, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('Payment verification completed successfully');
            bootstrap.Modal.getInstance(document.getElementById('actionModal')).hide();
            location.reload();
        } else {
            alert(result.message || 'Failed to process verification');
        }
    })
    .catch(error => {
        alert('Failed to process payment verification');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});
</script>
@endpush