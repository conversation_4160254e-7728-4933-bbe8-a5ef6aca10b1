@if(isset($landingPage['search']) && $landingPage['search']['active'])
<section id="search-section"
        class="py-20 bg-gradient-to-br from-primary-light/10 via-accent-light/5 to-primary-dark/10 dark:from-primary-dark/10 dark:via-accent-dark/5 dark:to-primary-light/10 transition-colors duration-300 relative overflow-hidden">
        <!-- Animated background elements -->
        <div class="absolute inset-0 pointer-events-none">
            <div class="absolute top-10 left-10 w-32 h-32 bg-primary-light/10 dark:bg-primary-dark/10 rounded-full animate-pulse-slow"></div>
            <div class="absolute bottom-10 right-10 w-24 h-24 bg-accent-light/10 dark:bg-accent-dark/10 rounded-full animate-bounce-slow"></div>
            <div class="absolute top-1/2 left-1/4 w-16 h-16 bg-primary-dark/10 dark:bg-primary-light/10 rounded-full animate-float-gentle"></div>
        </div>

        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="max-w-3xl mx-auto">
                <div class="text-center mb-12 animate-fade-in-up">
                    <span class="inline-block px-4 py-2 rounded-full bg-gradient-to-r from-primary-light/20 to-accent-light/20 dark:from-primary-dark/20 dark:to-accent-dark/20 text-primary-light dark:text-primary-dark text-sm font-semibold tracking-wider uppercase mb-4 backdrop-blur-sm border border-primary-light/30 dark:border-primary-dark/30">
                        <i class="fa-solid fa-search mr-2"></i>{{ $landingPage['search']['content']['badge_text'] ?? 'Search Tool' }}
                    </span>
                    <h2 class="text-4xl md:text-5xl font-black mb-6 text-primary-light dark:text-primary-dark">
                        {{ $landingPage['search']['content']['heading'] ?? 'Find Any Pincode In India' }}
                    </h2>
                    <p class="text-xl text-text-secondary-light dark:text-text-secondary-dark leading-relaxed">
                        {{ $landingPage['search']['content']['subheading'] ?? 'Enter a pincode, locality, district, or state to find detailed information' }}
                    </p>
                </div>

                <div
                    class="bg-white/80 dark:bg-bg-dark/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 md:p-10 border border-border-light/50 dark:border-border-dark/50 transition-all duration-500 hover:shadow-3xl hover:scale-[1.02] animate-slide-in-bottom">
                    <form id="searchForm" class="relative">
                        <div class="flex flex-col md:flex-row gap-4">
                            <div class="flex-1">
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg class="h-5 w-5 text-text-secondary-light dark:text-text-secondary-dark"
                                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                            stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </div>
                                    <input type="text" name="query" id="search"
                                        class="block w-full pl-10 pr-3 py-4 border border-border-light dark:border-border-dark rounded-lg bg-bg-light dark:bg-bg-dark focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark transition-all duration-200 placeholder-text-secondary-light dark:placeholder-text-secondary-dark text-text-primary-light dark:text-text-primary-dark"
                                        placeholder="{{ $landingPage['search']['content']['search_placeholder'] ?? 'Enter pincode or locality name' }}">
                                </div>
                            </div>
                            <div>
                                <button type="submit"
                                    class="bg-primary-light hover:bg-primary-light/90 dark:bg-primary-dark dark:hover:bg-primary-dark/90 text-white w-full md:w-auto py-4 px-8 rounded-lg font-medium flex items-center justify-center transition-all duration-200 shadow-md hover:shadow-lg">
                                    <span>{{ $landingPage['search']['content']['search_button_text'] ?? 'Search' }}</span>
                                    <svg class="ml-2 w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <div class="mt-6 flex items-center justify-center md:justify-start gap-6">
                            <div class="flex items-center">
                                <input type="radio" id="type-pincode" name="type" value="pincode" checked=""
                                    class="h-4 w-4 text-primary-light dark:text-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark border-border-light dark:border-border-dark bg-bg-light dark:bg-bg-dark">
                                <label for="type-pincode"
                                    class="ml-2 text-sm text-text-secondary-light dark:text-text-secondary-dark font-medium">Pincode</label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" id="type-name" name="type" value="name"
                                    class="h-4 w-4 text-primary-light dark:text-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark border-border-light dark:border-border-dark bg-bg-light dark:bg-bg-dark">
                                <label for="type-name"
                                    class="ml-2 text-sm text-text-secondary-light dark:text-text-secondary-dark font-medium">Post
                                    Office</label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" id="type-district" name="type" value="district"
                                    class="h-4 w-4 text-primary-light dark:text-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark border-border-light dark:border-border-dark bg-bg-light dark:bg-bg-dark">
                                <label for="type-district"
                                    class="ml-2 text-sm text-text-secondary-light dark:text-text-secondary-dark font-medium">District</label>
                            </div>
                        </div>
                    </form>

                    <div class="mt-8 pt-6 border-t border-border-light dark:border-border-dark">
                        <div class="text-center">
                            <h3
                                class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider mb-3">
                                {{ $landingPage['search']['content']['popular_searches_heading'] ?? 'Popular Searches' }}
                            </h3>
                            <div class="flex flex-wrap justify-center gap-2">
                                @foreach($landingPage['search']['content']['popular_searches'] ?? [
                                    ['name' => 'Delhi', 'link' => '/search?query=delhi&type=name'],
                                    ['name' => 'Mumbai', 'link' => '/search?query=mumbai&type=name'],
                                    ['name' => 'Bangalore', 'link' => '/search?query=bangalore&type=name'],
                                    ['name' => 'Hyderabad', 'link' => '/search?query=hyderabad&type=name'],
                                    ['name' => 'Chennai', 'link' => '/search?query=chennai&type=name'],
                                    ['name' => 'Kolkata', 'link' => '/search?query=kolkata&type=name']
                                ] as $search)
                                    <a href="{{ $search['link'] }}"
                                        class="px-3 py-1 bg-bg-light dark:bg-bg-dark hover:bg-accent-light/10 dark:hover:bg-accent-dark/10 hover:text-accent-light dark:hover:text-accent-dark rounded-full text-sm text-text-secondary-light dark:text-text-secondary-dark transition-all duration-200 border border-border-light dark:border-border-dark hover:border-accent-light dark:hover:border-accent-dark">{{ $search['name'] }}</a>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    @endif