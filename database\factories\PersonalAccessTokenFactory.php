<?php

namespace Database\Factories;

use App\Models\PersonalAccessToken;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class PersonalAccessTokenFactory extends Factory
{
    protected $model = PersonalAccessToken::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->words(3, true),
            'token' => Str::random(64),
            'abilities' => ['*'],
            'last_used_at' => $this->faker->optional()->dateTimeThisMonth(),
            'expires_at' => now()->addDays(User::TOKEN_EXPIRY_DAYS),
            'tokenable_id' => User::factory(),
            'tokenable_type' => User::class,
        ];
    }
} 