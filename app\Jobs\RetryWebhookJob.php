<?php

namespace App\Jobs;

use App\Models\WebhookLog;
use App\Services\Payment\WebhookRetryService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class RetryWebhookJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $webhookLogId;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 1; // We handle retries in the service

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 120;

    /**
     * Create a new job instance.
     */
    public function __construct(int $webhookLogId)
    {
        $this->webhookLogId = $webhookLogId;
    }

    /**
     * Execute the job.
     */
    public function handle(WebhookRetryService $retryService): void
    {
        try {
            $webhookLog = WebhookLog::find($this->webhookLogId);
            
            if (!$webhookLog) {
                Log::warning('Webhook log not found for retry', [
                    'webhook_log_id' => $this->webhookLogId
                ]);
                return;
            }

            Log::info('Processing webhook retry job', [
                'webhook_log_id' => $this->webhookLogId,
                'retry_count' => $webhookLog->retry_count,
                'gateway' => $webhookLog->gateway->name ?? 'unknown'
            ]);

            $retryService->processWebhookWithRetry($webhookLog);

        } catch (\Exception $e) {
            Log::error('Webhook retry job failed', [
                'webhook_log_id' => $this->webhookLogId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Don't retry the job itself, let the service handle webhook retries
            $this->fail($e);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Webhook retry job finally failed', [
            'webhook_log_id' => $this->webhookLogId,
            'error' => $exception->getMessage()
        ]);
    }
}