<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddGuestFieldsToCommentsTable extends Migration
{
    public function up()
    {
        Schema::table('comments', function (Blueprint $table) {
            // Make user_id nullable to allow guest comments
            $table->foreignId('user_id')->nullable()->change();
            
            // Add guest fields
            $table->string('guest_name')->nullable()->after('user_id');
            $table->string('guest_email')->nullable()->after('guest_name');
        });
    }

    public function down()
    {
        Schema::table('comments', function (Blueprint $table) {
            // Remove guest fields
            $table->dropColumn(['guest_name', 'guest_email']);
            
            // Make user_id required again (this might fail if there are guest comments)
            $table->foreignId('user_id')->nullable(false)->change();
        });
    }
}