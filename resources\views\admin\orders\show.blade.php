@extends('admin.layouts.admin')

@section('title', 'Order Details')

@section('page-title', 'Order Details')

@section('content')
<div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">Order #{{ $order->order_number }}</h2>
            <a href="{{ route('admin.orders.index') }}" class="bg-gray-500 dark:bg-gray-700 text-white px-4 py-2 rounded-lg hover:bg-gray-600 dark:hover:bg-gray-600 transition-colors">
                Back to List
            </a>
        </div>

        @if(session('success'))
            <div class="mb-4 bg-green-100 dark:bg-green-900/20 border-l-4 border-green-500 dark:border-green-700 text-green-700 dark:text-green-200 p-4">
                {{ session('success') }}
            </div>
        @endif

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div>
                    <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">Order Information</h3>
                    <dl class="mt-2 space-y-2">
                        <div class="flex justify-between">
                            <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Order Number:</dt>
                            <dd class="text-sm text-text-primary-light dark:text-text-primary-dark">{{ $order->order_number }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Plan:</dt>
                            <dd class="text-sm text-text-primary-light dark:text-text-primary-dark">{{ $order->plan_id }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Amount:</dt>
                            <dd class="text-sm text-text-primary-light dark:text-text-primary-dark">₹{{ number_format($order->amount, 2) }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Request Limit:</dt>
                            <dd class="text-sm text-text-primary-light dark:text-text-primary-dark">{{ number_format($order->request_limit) }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Created Date:</dt>
                            <dd class="text-sm text-text-primary-light dark:text-text-primary-dark">{{ $order->created_at->format('M d, Y H:i:s') }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Paid Date:</dt>
                            <dd class="text-sm text-text-primary-light dark:text-text-primary-dark">{{ $order->paid_at ? $order->paid_at->format('M d, Y H:i:s') : 'Not paid' }}</dd>
                        </div>
                    </dl>
                </div>

                <div>
                    <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">Customer Information</h3>
                    <dl class="mt-2 space-y-2">
                        <div class="flex justify-between">
                            <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Name:</dt>
                            <dd class="text-sm text-text-primary-light dark:text-text-primary-dark">{{ $order->user->name }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Email:</dt>
                            <dd class="text-sm text-text-primary-light dark:text-text-primary-dark">{{ $order->user->email }}</dd>
                        </div>
                    </dl>
                </div>
            </div>

            <div>
                <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark mb-4">Update Order</h3>
                <form action="{{ route('admin.orders.update', $order) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="space-y-4">
                        <div>
                            <label for="status" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Status</label>
                            <select name="status" id="status" class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark sm:text-sm">
                                <option value="pending" {{ $order->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="completed" {{ $order->status === 'completed' ? 'selected' : '' }}>Completed</option>
                                <option value="cancelled" {{ $order->status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                            </select>
                        </div>

                        <div>
                            <label for="notes" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Notes</label>
                            <textarea name="notes" id="notes" rows="4" class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark sm:text-sm">{{ old('notes', $order->notes) }}</textarea>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" class="inline-flex justify-center rounded-md border border-transparent bg-primary-light dark:bg-primary-dark px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-2 dark:focus:ring-offset-bg-dark">
                                Update Order
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection