/* ===================================
   CUSTOM THEME CSS VARIABLES
   ===================================
   This file contains all theme variables for easy customization.
   Change these values to update the entire website design.
*/

:root {
  --primary-50: #ffffff;
  --primary-100: #ffffff;
  --primary-200: #ffffff;
  --primary-300: #fcfaff;
  --primary-400: #c09cf7;
  --primary-500: #530fb8;
  --primary-600: #28075a;
  --primary-700: #000000;
  --primary-800: #000000;
  --primary-900: #000000;
  --primary-950: #000000;
  --secondary-50: #ffffff;
  --secondary-100: #ffffff;
  --secondary-200: #ffffff;
  --secondary-300: #fffcfa;
  --secondary-400: #f7c09c;
  --secondary-500: #b8530f;
  --secondary-600: #5a2807;
  --secondary-700: #000000;
  --secondary-800: #000000;
  --secondary-900: #000000;
  --secondary-950: #000000;
  --accent-50: #ffffff;
  --accent-100: #ffffff;
  --accent-200: #ffffff;
  --accent-300: #fafffc;
  --accent-400: #9cf7c0;
  --accent-500: #0fb853;
  --accent-600: #075a28;
  --accent-700: #000000;
  --accent-800: #000000;
  --accent-900: #000000;
  --accent-950: #000000;

  /* Palette Variables */
  --hero_gradient_start: #61239f;
  --hero_gradient_end: #1d3a7c;
  --hero_blob1: #764ba2;
  --hero_blob2: #ea66c7;
  --hero_blob3: #ff0000;
  --hero_badge_bg: #067413;
  --hero_badge_text: #0ea5e9;
  --hero_title_gradient_start: #ff14a9;
  --hero_title_gradient_mid: #126f06;
  --hero_title_gradient_end: #fedd0b;

  /* Typography */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --font-thin: 100;
  --font-extralight: 200;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* Layout */
  --container-max-width: 1280px;
  --header-height: 4rem;
  --footer-height: 4rem;
  --sidebar-width: 16rem;

  /* Components */
  --btn-radius: 0.375rem;
  --card-radius: 0.5rem;
  --input-radius: 0.375rem;
  --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --card-shadow-hover: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Animations */
  --transition-fast: 150ms;
  --transition-base: 300ms;
  --transition-slow: 500ms;
  --transition-slower: 700ms;

  /* Custom Variables */
  --custom-spacing: 2rem;
  --custom-border-radius: 1rem;
  --custom-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}














































/* Custom text size utilities using theme variables - Higher specificity to override Tailwind */
.text-xs    { font-size: var(--text-xs) !important; }
.text-sm    { font-size: var(--text-sm) !important; }
.text-base  { font-size: var(--text-base) !important; }
.text-lg    { font-size: var(--text-lg) !important; }
.text-xl    { font-size: var(--text-xl) !important; }
.text-2xl   { font-size: var(--text-2xl) !important; }
.text-3xl   { font-size: var(--text-3xl) !important; }
.text-4xl   { font-size: var(--text-4xl) !important; }
.text-5xl   { font-size: var(--text-5xl) !important; }
.text-6xl   { font-size: var(--text-6xl) !important; }



































/* ===================================
   DARK THEME VARIABLES
   =================================== */

[data-theme="dark"] {
  /* Dark theme overrides */
  --gray-50: #030712;
  --gray-100: #111827;
  --gray-200: #1f2937;
  --gray-300: #374151;
  --gray-400: #4b5563;
  --gray-500: #6b7280;
  --gray-600: #9ca3af;
  --gray-700: #d1d5db;
  --gray-800: #e5e7eb;
  --gray-900: #f3f4f6;
  --gray-950: #f9fafb;

  /* Dark component overrides */
  --card-bg: var(--gray-800);
  --card-border: var(--gray-700);
  --nav-bg: var(--gray-900);
  --nav-text: var(--gray-100);
  --input-bg: var(--gray-800);
  --input-border: var(--gray-600);
  --footer-bg: var(--gray-950);
  --footer-text: var(--gray-400);
  --footer-link: var(--gray-500);
  --footer-link-hover: var(--gray-200);
}

/* ===================================
   UTILITY CLASSES
   =================================== */

/* Background utilities */
.bg-primary { background-color: var(--primary-600); }
.bg-primary-light { background-color: var(--primary-100); }
.bg-primary-dark { background-color: var(--primary-800); }
.bg-secondary { background-color: var(--secondary-600); }
.bg-accent { background-color: var(--accent-600); }

/* Text utilities */
.text-primary { color: var(--primary-600); }
.text-primary-light { color: var(--primary-400); }
.text-primary-dark { color: var(--primary-800); }
.text-secondary { color: var(--secondary-600); }
.text-accent { color: var(--accent-600); }

/* Border utilities */
.border-primary { border-color: var(--primary-600); }
.border-secondary { border-color: var(--secondary-600); }
.border-accent { border-color: var(--accent-600); }

/* Shadow utilities */
.shadow-theme { box-shadow: var(--shadow-lg); }
.shadow-theme-lg { box-shadow: var(--shadow-xl); }

/* Transition utilities */
.transition-theme { transition: var(--transition-base); }
.transition-theme-fast { transition: var(--transition-fast); }
.transition-theme-slow { transition: var(--transition-slow); }

/* ===================================
   COMPONENT STYLES
   =================================== */

/* Button component */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  transition: var(--transition-base);
  cursor: pointer;
  border: 1px solid transparent;
  text-decoration: none;
}

.btn-primary {
  background-color: var(--btn-primary-bg);
  color: var(--btn-primary-text);
}

.btn-primary:hover {
  background-color: var(--btn-primary-hover);
}

.btn-secondary {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
}

.btn-secondary:hover {
  background-color: var(--btn-secondary-hover);
}

/* Card component */
.card {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--card-radius);
  box-shadow: var(--card-shadow);
  transition: var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-lg);
}

/* Form input component */
.form-input {
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  border-radius: var(--input-radius);
  padding: var(--spacing-3);
  font-size: var(--text-base);
  transition: var(--transition-base);
}

.form-input:focus {
  outline: none;
  border-color: var(--input-focus-border);
  box-shadow: 0 0 0 3px rgb(20 184 166 / 0.1);
}

/* ===================================
   RESPONSIVE BREAKPOINTS
   =================================== */

/* Mobile First Approach */
@media (min-width: 640px) {
  :root {
  --primary-50: #ffffff;
  --primary-100: #ffffff;
  --primary-200: #ffffff;
  --primary-300: #fcfaff;
  --primary-400: #c09cf7;
  --primary-500: #530fb8;
  --primary-600: #28075a;
  --primary-700: #000000;
  --primary-800: #000000;
  --primary-900: #000000;
  --primary-950: #000000;
  --secondary-50: #ffffff;
  --secondary-100: #ffffff;
  --secondary-200: #ffffff;
  --secondary-300: #fffcfa;
  --secondary-400: #f7c09c;
  --secondary-500: #b8530f;
  --secondary-600: #5a2807;
  --secondary-700: #000000;
  --secondary-800: #000000;
  --secondary-900: #000000;
  --secondary-950: #000000;
  --accent-50: #ffffff;
  --accent-100: #ffffff;
  --accent-200: #ffffff;
  --accent-300: #fafffc;
  --accent-400: #9cf7c0;
  --accent-500: #0fb853;
  --accent-600: #075a28;
  --accent-700: #000000;
  --accent-800: #000000;
  --accent-900: #000000;
  --accent-950: #000000;

  /* Palette Variables */
  --hero_gradient_start: #61239f;
  --hero_gradient_end: #1d3a7c;
  --hero_blob1: #764ba2;
  --hero_blob2: #ea66c7;
  --hero_blob3: #ff0000;
  --hero_badge_bg: #067413;
  --hero_badge_text: #0ea5e9;
  --hero_title_gradient_start: #ff14a9;
  --hero_title_gradient_mid: #126f06;
  --hero_title_gradient_end: #fedd0b;

  /* Typography */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --font-thin: 100;
  --font-extralight: 200;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* Layout */
  --container-max-width: 1280px;
  --header-height: 4rem;
  --footer-height: 4rem;
  --sidebar-width: 16rem;

  /* Components */
  --btn-radius: 0.375rem;
  --card-radius: 0.5rem;
  --input-radius: 0.375rem;
  --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --card-shadow-hover: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Animations */
  --transition-fast: 150ms;
  --transition-base: 300ms;
  --transition-slow: 500ms;
  --transition-slower: 700ms;

  /* Custom Variables */
  --custom-spacing: 2rem;
  --custom-border-radius: 1rem;
  --custom-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}















































































}

@media (min-width: 768px) {
  :root {
  --primary-50: #ffffff;
  --primary-100: #ffffff;
  --primary-200: #ffffff;
  --primary-300: #fcfaff;
  --primary-400: #c09cf7;
  --primary-500: #530fb8;
  --primary-600: #28075a;
  --primary-700: #000000;
  --primary-800: #000000;
  --primary-900: #000000;
  --primary-950: #000000;
  --secondary-50: #ffffff;
  --secondary-100: #ffffff;
  --secondary-200: #ffffff;
  --secondary-300: #fffcfa;
  --secondary-400: #f7c09c;
  --secondary-500: #b8530f;
  --secondary-600: #5a2807;
  --secondary-700: #000000;
  --secondary-800: #000000;
  --secondary-900: #000000;
  --secondary-950: #000000;
  --accent-50: #ffffff;
  --accent-100: #ffffff;
  --accent-200: #ffffff;
  --accent-300: #fafffc;
  --accent-400: #9cf7c0;
  --accent-500: #0fb853;
  --accent-600: #075a28;
  --accent-700: #000000;
  --accent-800: #000000;
  --accent-900: #000000;
  --accent-950: #000000;

  /* Palette Variables */
  --hero_gradient_start: #61239f;
  --hero_gradient_end: #1d3a7c;
  --hero_blob1: #764ba2;
  --hero_blob2: #ea66c7;
  --hero_blob3: #ff0000;
  --hero_badge_bg: #067413;
  --hero_badge_text: #0ea5e9;
  --hero_title_gradient_start: #ff14a9;
  --hero_title_gradient_mid: #126f06;
  --hero_title_gradient_end: #fedd0b;

  /* Typography */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --font-thin: 100;
  --font-extralight: 200;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* Layout */
  --container-max-width: 1280px;
  --header-height: 4rem;
  --footer-height: 4rem;
  --sidebar-width: 16rem;

  /* Components */
  --btn-radius: 0.375rem;
  --card-radius: 0.5rem;
  --input-radius: 0.375rem;
  --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --card-shadow-hover: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Animations */
  --transition-fast: 150ms;
  --transition-base: 300ms;
  --transition-slow: 500ms;
  --transition-slower: 700ms;

  /* Custom Variables */
  --custom-spacing: 2rem;
  --custom-border-radius: 1rem;
  --custom-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}















































































}

@media (min-width: 1024px) {
  :root {
  --primary-50: #ffffff;
  --primary-100: #ffffff;
  --primary-200: #ffffff;
  --primary-300: #fcfaff;
  --primary-400: #c09cf7;
  --primary-500: #530fb8;
  --primary-600: #28075a;
  --primary-700: #000000;
  --primary-800: #000000;
  --primary-900: #000000;
  --primary-950: #000000;
  --secondary-50: #ffffff;
  --secondary-100: #ffffff;
  --secondary-200: #ffffff;
  --secondary-300: #fffcfa;
  --secondary-400: #f7c09c;
  --secondary-500: #b8530f;
  --secondary-600: #5a2807;
  --secondary-700: #000000;
  --secondary-800: #000000;
  --secondary-900: #000000;
  --secondary-950: #000000;
  --accent-50: #ffffff;
  --accent-100: #ffffff;
  --accent-200: #ffffff;
  --accent-300: #fafffc;
  --accent-400: #9cf7c0;
  --accent-500: #0fb853;
  --accent-600: #075a28;
  --accent-700: #000000;
  --accent-800: #000000;
  --accent-900: #000000;
  --accent-950: #000000;

  /* Palette Variables */
  --hero_gradient_start: #61239f;
  --hero_gradient_end: #1d3a7c;
  --hero_blob1: #764ba2;
  --hero_blob2: #ea66c7;
  --hero_blob3: #ff0000;
  --hero_badge_bg: #067413;
  --hero_badge_text: #0ea5e9;
  --hero_title_gradient_start: #ff14a9;
  --hero_title_gradient_mid: #126f06;
  --hero_title_gradient_end: #fedd0b;

  /* Typography */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --font-thin: 100;
  --font-extralight: 200;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* Layout */
  --container-max-width: 1280px;
  --header-height: 4rem;
  --footer-height: 4rem;
  --sidebar-width: 16rem;

  /* Components */
  --btn-radius: 0.375rem;
  --card-radius: 0.5rem;
  --input-radius: 0.375rem;
  --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --card-shadow-hover: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Animations */
  --transition-fast: 150ms;
  --transition-base: 300ms;
  --transition-slow: 500ms;
  --transition-slower: 700ms;

  /* Custom Variables */
  --custom-spacing: 2rem;
  --custom-border-radius: 1rem;
  --custom-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}















































































}

@media (min-width: 1280px) {
  :root {
  --primary-50: #ffffff;
  --primary-100: #ffffff;
  --primary-200: #ffffff;
  --primary-300: #fcfaff;
  --primary-400: #c09cf7;
  --primary-500: #530fb8;
  --primary-600: #28075a;
  --primary-700: #000000;
  --primary-800: #000000;
  --primary-900: #000000;
  --primary-950: #000000;
  --secondary-50: #ffffff;
  --secondary-100: #ffffff;
  --secondary-200: #ffffff;
  --secondary-300: #fffcfa;
  --secondary-400: #f7c09c;
  --secondary-500: #b8530f;
  --secondary-600: #5a2807;
  --secondary-700: #000000;
  --secondary-800: #000000;
  --secondary-900: #000000;
  --secondary-950: #000000;
  --accent-50: #ffffff;
  --accent-100: #ffffff;
  --accent-200: #ffffff;
  --accent-300: #fafffc;
  --accent-400: #9cf7c0;
  --accent-500: #0fb853;
  --accent-600: #075a28;
  --accent-700: #000000;
  --accent-800: #000000;
  --accent-900: #000000;
  --accent-950: #000000;

  /* Palette Variables */
  --hero_gradient_start: #61239f;
  --hero_gradient_end: #1d3a7c;
  --hero_blob1: #764ba2;
  --hero_blob2: #ea66c7;
  --hero_blob3: #ff0000;
  --hero_badge_bg: #067413;
  --hero_badge_text: #0ea5e9;
  --hero_title_gradient_start: #ff14a9;
  --hero_title_gradient_mid: #126f06;
  --hero_title_gradient_end: #fedd0b;

  /* Typography */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --font-thin: 100;
  --font-extralight: 200;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* Layout */
  --container-max-width: 1280px;
  --header-height: 4rem;
  --footer-height: 4rem;
  --sidebar-width: 16rem;

  /* Components */
  --btn-radius: 0.375rem;
  --card-radius: 0.5rem;
  --input-radius: 0.375rem;
  --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --card-shadow-hover: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Animations */
  --transition-fast: 150ms;
  --transition-base: 300ms;
  --transition-slow: 500ms;
  --transition-slower: 700ms;

  /* Custom Variables */
  --custom-spacing: 2rem;
  --custom-border-radius: 1rem;
  --custom-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}















































































}

/* ===================================
   ANIMATIONS
   =================================== */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* ===================================
   PRINT STYLES
   =================================== */

@media print {
  :root {
  --primary-50: #ffffff;
  --primary-100: #ffffff;
  --primary-200: #ffffff;
  --primary-300: #fcfaff;
  --primary-400: #c09cf7;
  --primary-500: #530fb8;
  --primary-600: #28075a;
  --primary-700: #000000;
  --primary-800: #000000;
  --primary-900: #000000;
  --primary-950: #000000;
  --secondary-50: #ffffff;
  --secondary-100: #ffffff;
  --secondary-200: #ffffff;
  --secondary-300: #fffcfa;
  --secondary-400: #f7c09c;
  --secondary-500: #b8530f;
  --secondary-600: #5a2807;
  --secondary-700: #000000;
  --secondary-800: #000000;
  --secondary-900: #000000;
  --secondary-950: #000000;
  --accent-50: #ffffff;
  --accent-100: #ffffff;
  --accent-200: #ffffff;
  --accent-300: #fafffc;
  --accent-400: #9cf7c0;
  --accent-500: #0fb853;
  --accent-600: #075a28;
  --accent-700: #000000;
  --accent-800: #000000;
  --accent-900: #000000;
  --accent-950: #000000;

  /* Palette Variables */
  --hero_gradient_start: #61239f;
  --hero_gradient_end: #1d3a7c;
  --hero_blob1: #764ba2;
  --hero_blob2: #ea66c7;
  --hero_blob3: #ff0000;
  --hero_badge_bg: #067413;
  --hero_badge_text: #0ea5e9;
  --hero_title_gradient_start: #ff14a9;
  --hero_title_gradient_mid: #126f06;
  --hero_title_gradient_end: #fedd0b;

  /* Typography */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  --font-thin: 100;
  --font-extralight: 200;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* Layout */
  --container-max-width: 1280px;
  --header-height: 4rem;
  --footer-height: 4rem;
  --sidebar-width: 16rem;

  /* Components */
  --btn-radius: 0.375rem;
  --card-radius: 0.5rem;
  --input-radius: 0.375rem;
  --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --card-shadow-hover: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Animations */
  --transition-fast: 150ms;
  --transition-base: 300ms;
  --transition-slow: 500ms;
  --transition-slower: 700ms;

  /* Custom Variables */
  --custom-spacing: 2rem;
  --custom-border-radius: 1rem;
  --custom-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}















































































  
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
} 