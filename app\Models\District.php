<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class District extends Model
{
    use HasFactory;
    protected $table = "pin_districts";
    protected $fillable = ['name', 'state_id', 'alt_name', 'official_site'];

    public function state()
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    public function postOffices()
    {
        return $this->hasMany(PostOffice::class);
    }

    public function pincodes()
    {
        return $this->hasMany(PinCode::class, 'district', 'name');
    }

    // Default image accessor for backward compatibility
    public function getFeaturedImageUrlAttribute()
    {
        return asset('images/default-district.jpg');
    }
}