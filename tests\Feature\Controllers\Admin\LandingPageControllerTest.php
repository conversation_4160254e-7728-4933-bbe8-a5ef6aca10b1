<?php

use App\Models\LandingPageSection;
use App\Models\LandingPageContent;
use App\Services\LandingPageService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Str;
use function Pest\Laravel\{get, post, put, patch, actingAs};

uses(RefreshDatabase::class);

describe('LandingPageController', function () {
    beforeEach(function () {
        $this->service = $this->mock(LandingPageService::class);
        $this->admin = \App\Models\User::factory()->create(['role' => 'admin']);
    });

    it('shows the landing page sections index', function () {
        $sections = LandingPageSection::factory()->count(2)->create();
        $response = actingAs($this->admin)->get(route('admin.landing-page.index'));
        $response->assertStatus(200)
            ->assertViewIs('admin.landing-page.index')
            ->assertViewHas('sections', function ($viewSections) use ($sections) {
                return $viewSections->count() === $sections->count();
            });
    });

    it('shows the edit form for a section', function () {
        $section = LandingPageSection::factory()->create();
        $contents = LandingPageContent::factory()->count(2)->create(['section_id' => $section->id]);
        $response = actingAs($this->admin)->get(route('admin.landing-page.edit', $section));
        $response->assertStatus(200)
            ->assertViewIs('admin.landing-page.edit')
            ->assertViewHas('section', function ($viewSection) use ($section) {
                return $viewSection->id === $section->id;
            });
    });

    it('updates a section and its contents (text, repeater, image)', function () {
        Storage::fake('public');
        $section = LandingPageSection::factory()->create();
        $textContent = LandingPageContent::factory()->create(['section_id' => $section->id, 'type' => 'text']);
        $repeaterContent = LandingPageContent::factory()->create(['section_id' => $section->id, 'type' => 'repeater']);
        $imageContent = LandingPageContent::factory()->create(['section_id' => $section->id, 'type' => 'image']);

        $this->service->shouldReceive('clearCache')->once();

        $data = [
            'name' => 'Updated Section',
            'is_active' => 'on',
            'sort_order' => 2,
            'contents' => [
                [
                    'id' => $textContent->id,
                    'value' => 'Updated text',
                ],
                [
                    'id' => $repeaterContent->id,
                    'value' => [['title' => 'A', 'desc' => 'B']],
                ],
                [
                    'id' => $imageContent->id,
                    'value' => $imageContent->value,
                    'image' => UploadedFile::fake()->image('new-image.jpg'),
                ],
            ],
        ];

        $response = actingAs($this->admin)->put(route('admin.landing-page.update', $section), $data);
        $response->assertRedirect(route('admin.landing-page.index'));
        $response->assertSessionHas('success');
        $section->refresh();
        expect($section->name)->toBe('Updated Section');
        expect($section->is_active)->toBeTrue();
        expect($section->sort_order)->toBe(2);
        expect($textContent->fresh()->value)->toBe('Updated text');
        $repeaterValue = $repeaterContent->fresh()->value;
        if (is_string($repeaterValue)) {
            $repeaterValue = json_decode($repeaterValue, true);
        }
        expect($repeaterValue)->toBe([['title' => 'A', 'desc' => 'B']]);
        Storage::disk('public')->assertExists($imageContent->fresh()->value);
    });

    it('toggles the active status of a section', function () {
        $section = LandingPageSection::factory()->create(['is_active' => true]);
        $this->service->shouldReceive('clearCache')->once();
        $response = actingAs($this->admin)->patch(route('admin.landing-page.toggle-active', $section));
        $response->assertRedirect(route('admin.landing-page.index'));
        $response->assertSessionHas('success');
        expect($section->fresh()->is_active)->toBeFalse();
    });

    it('updates the order of sections', function () {
        $sections = LandingPageSection::factory()->count(3)->create();
        $ids = $sections->pluck('id')->toArray();
        shuffle($ids);
        $this->service->shouldReceive('clearCache')->once();
        $response = actingAs($this->admin)->post(route('admin.landing-page.reorder'), [
            'sections' => $ids
        ]);
        $response->assertJson(['success' => true]);
        foreach ($ids as $order => $id) {
            expect(LandingPageSection::find($id)->sort_order)->toBe($order + 1);
        }
    });
}); 