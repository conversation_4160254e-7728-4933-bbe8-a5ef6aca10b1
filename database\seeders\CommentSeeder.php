<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\BlogPost;
use App\Models\Comment;
use App\Models\User;

class CommentSeeder extends Seeder
{
    public function run(): void
    {
        $users = User::all();
        BlogPost::all()->each(function ($post) use ($users) {
            // Create 5-10 top-level comments per post
            $comments = Comment::factory(rand(5, 10))->create([
                'blog_post_id' => $post->id,
                'user_id' => $users->random()->id,
                'parent_id' => null,
            ]);

            // Optionally create replies for some comments
            $comments->each(function ($comment) use ($users, $post) {
                if (rand(0, 1)) { // 50% chance to create a reply
                    Comment::factory(rand(1, 2))->create([
                        'blog_post_id' => $post->id,
                        'user_id' => $users->random()->id,
                        'parent_id' => $comment->id,
                    ]);
                }
            });
        });
    }
} 