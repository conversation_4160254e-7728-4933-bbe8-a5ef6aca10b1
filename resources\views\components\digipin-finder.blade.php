<div class="mb-6 p-4 bg-bg-light dark:bg-gray-800 border border-border-light dark:border-border-dark rounded-lg">
    <h2 class="text-xl font-bold text-primary-light dark:text-primary-dark mb-2">What is DIGIPIN?</h2>
    <p class="text-text-secondary-light dark:text-text-secondary-dark mb-2">
        <strong>DIGIPIN</strong> (Digital Postal Index Number) is India Post's new, open-source, nationwide digital addressing system. Developed with IIT Hyderabad, NRSC, and ISRO, DIGIPIN divides India into a grid of 4m x 4m squares, each assigned a unique 10-character alphanumeric code based on latitude and longitude.
    </p>
    <ul class="list-disc pl-5 text-text-secondary-light dark:text-text-secondary-dark mb-2">
        <li><strong>Precision:</strong> Pinpoints exact locations, unlike traditional PIN codes that cover large areas.</li>
        <li><strong>Efficiency:</strong> Streamlines deliveries, logistics, and emergency services.</li>
        <li><strong>Inclusivity:</strong> Useful in rural or unstructured areas where addresses may not exist.</li>
        <li><strong>Privacy:</strong> No personal data is stored or linked to a DIGIPIN; it is purely a geospatial reference.</li>
        <li><strong>Offline Capable:</strong> The system can be used offline, and the encoding/decoding logic is public.</li>
    </ul>
    <p class="text-text-secondary-light dark:text-text-secondary-dark mb-1">
        <strong>How does it work?</strong> India is divided into a grid (Latitude: 2.5°–38.5° N, Longitude: 63.5°–99.5° E). Each 4m x 4m cell is assigned a unique 10-character code using 16 symbols. The code is generated by hierarchically subdividing the bounding box and encoding the location's latitude and longitude at each level.
    </p>
    <p class="text-text-secondary-light dark:text-text-secondary-dark">
        <strong>Use Cases:</strong> E-commerce, logistics, emergency services, government, and individuals for precise, easy-to-share digital addresses. Your postal address remains unchanged; DIGIPIN is an additional, precise digital layer.
    </p>
</div>

<!-- DIGIPIN Button and Result Section -->
<div x-data="digipinFinder()" class="mb-6">
    <button type="button"
        @click="getMyDigipin"
        :disabled="isLocating || isLoading"
        class="inline-flex items-center px-5 py-3 bg-primary-light dark:bg-primary-dark hover:bg-blue-700 dark:hover:bg-blue-600 disabled:bg-gray-400 dark:disabled:bg-gray-600 text-white font-semibold rounded-md transition duration-150 ease-in-out mb-4">
        <svg x-show="isLocating || isLoading" class="animate-spin h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
        </svg>
        <svg x-show="!(isLocating || isLoading)" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
        </svg>
        <span x-text="isLocating ? 'Getting Location...' : (isLoading ? 'Getting DIGIPIN...' : 'Get My DIGIPIN')"></span>
    </button>
    <template x-if="digipin">
        <div class="mt-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/30 rounded-lg p-4">
            <h4 class="text-lg font-semibold text-green-800 dark:text-green-300 mb-2">Your DIGIPIN</h4>
            <div class="flex items-center space-x-3">
                <span class="text-2xl font-mono font-bold text-green-700 dark:text-green-400" x-text="digipin"></span>
                <button @click="copyToClipboard(digipin)" class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </button>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mt-2">
                <div><span class="font-medium text-text-secondary-light dark:text-text-secondary-dark">Latitude:</span> <span class="text-text-primary-light dark:text-text-primary-dark" x-text="latitude"></span></div>
                <div><span class="font-medium text-text-secondary-light dark:text-text-secondary-dark">Longitude:</span> <span class="text-text-primary-light dark:text-text-primary-dark" x-text="longitude"></span></div>
            </div>

            <!-- Google Maps iframe -->
            <div class="mt-4 w-full h-64">
                <iframe
                    :src="'https://maps.google.com/maps?q=' + latitude + ',' + longitude + '&hl=es;z=14&output=embed'"
                    allowfullscreen="" loading="lazy" class="w-full h-full rounded-lg border border-border-light dark:border-border-dark"></iframe>
            </div>
        </div>
    </template>
    <template x-if="error">
        <div class="mt-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/30 rounded-lg p-4">
            <h4 class="text-lg font-semibold text-red-800 dark:text-red-300 mb-2">Error</h4>
            <p class="text-red-700 dark:text-red-400" x-text="error"></p>
        </div>
    </template>
    @once
    <script>
        function digipinFinder() {
            return {
                isLocating: false,
                isLoading: false,
                digipin: '',
                latitude: '',
                longitude: '',
                error: '',
                getMyDigipin() {
                    this.digipin = '';
                    this.latitude = '';
                    this.longitude = '';
                    this.error = '';
                    this.isLocating = true;
                    this.isLoading = false;
                    if (navigator.geolocation) {
                        navigator.geolocation.getCurrentPosition(
                            (position) => {
                                this.latitude = position.coords.latitude.toFixed(6);
                                this.longitude = position.coords.longitude.toFixed(6);
                                this.isLocating = false;
                                this.fetchDigipin();
                            },
                            (error) => {
                                this.isLocating = false;
                                this.getLocationByIP();
                            },
                            {
                                enableHighAccuracy: true,
                                timeout: 10000,
                                maximumAge: 0
                            }
                        );
                    } else {
                        this.isLocating = false;
                        this.getLocationByIP();
                    }
                },
                getLocationByIP() {
                    this.isLoading = true;
                    fetch('https://ipapi.co/json/')
                        .then(res => res.json())
                        .then(data => {
                            if (data.latitude && data.longitude) {
                                this.latitude = parseFloat(data.latitude).toFixed(6);
                                this.longitude = parseFloat(data.longitude).toFixed(6);
                                this.fetchDigipin();
                            } else {
                                this.error = 'Unable to get your location from IP.';
                                this.isLoading = false;
                            }
                        })
                        .catch(() => {
                            this.error = 'Unable to get your location from IP.';
                            this.isLoading = false;
                        });
                },
                fetchDigipin() {
                    this.isLoading = true;
                    this.error = '';
                    fetch('/api/digipin/generate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            latitude: parseFloat(this.latitude),
                            longitude: parseFloat(this.longitude)
                        })
                    })
                        .then(res => res.json())
                        .then(data => {
                            if (data.success) {
                                this.digipin = data.data.digipin;
                                this.latitude = data.data.latitude;
                                this.longitude = data.data.longitude;
                            } else {
                                this.error = data.message || 'Failed to generate DIGIPIN.';
                            }
                            this.isLoading = false;
                        })
                        .catch(() => {
                            this.error = 'Network error. Please try again.';
                            this.isLoading = false;
                        });
                },
                copyToClipboard(text) {
                    navigator.clipboard.writeText(text).then(() => {});
                }
            }
        }
    </script>
    @endonce
</div>
