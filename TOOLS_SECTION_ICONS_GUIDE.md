# Tools Section Icons Guide

## Overview
The tools section now supports dynamic SVG icons that can be changed from the admin panel. Icons are stored in the database and can be easily updated without touching the code.

## Implementation Details

### 1. Database Structure
Icons are stored in the `landing_page_contents` table under the tools section with two key types:
- `tools`: Contains the actual tools with their icons
- `available_icons`: Contains a library of pre-defined icons for admin reference

### 2. Template Changes
The template now uses dynamic icon rendering:
```php
{!! $tool['icon'] ?? '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="..." />' !!}
```

### 3. Current Tools Configuration
1. **Browse by State** (Primary color)
   - Icon: Map/Location icon
   - Perfect for location-based navigation

2. **Complete Pincode List** (Accent color)
   - Icon: Document/List icon
   - Great for comprehensive listings

3. **Download Pincodes** (Gradient color)
   - Icon: Download icon
   - Ideal for download functionality

## Available Icons Library

### 1. Map/Location
```svg
<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
```
**Use Case**: Perfect for location-based tools, state browsing, geographical features

### 2. Document/List
```svg
<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
```
**Use Case**: Great for lists, documentation, comprehensive data views

### 3. Download
```svg
<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
```
**Use Case**: Ideal for download features, export functionality

### 4. Search
```svg
<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
```
**Use Case**: Perfect for search functionality, lookup tools

### 5. Database
```svg
<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
```
**Use Case**: Great for data-related tools, database operations

### 6. Globe/World
```svg
<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
```
**Use Case**: Perfect for global/country features, international tools

### 7. Chart/Analytics
```svg
<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
```
**Use Case**: Great for analytics and reports, statistics tools

### 8. Settings/Cog
```svg
<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
```
**Use Case**: Perfect for configuration tools, settings panels

### 9. Shield/Security
```svg
<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
```
**Use Case**: Great for security and verification tools

### 10. Lightning/Fast
```svg
<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
```
**Use Case**: Perfect for speed and performance tools

### 11. Users/Team
```svg
<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
```
**Use Case**: Great for team and collaboration tools

### 12. Mail/Email
```svg
<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
```
**Use Case**: Perfect for postal and email features

## How to Change Icons (For Admins)

### Method 1: Database Update (Direct)
```sql
UPDATE landing_page_contents 
SET value = JSON_REPLACE(value, '$[0].icon', '<new_svg_path_here>')
WHERE key = 'tools' AND section_id = (SELECT id FROM landing_page_sections WHERE slug = 'tools');
```

### Method 2: Laravel Tinker
```php
php artisan tinker

$toolsSection = App\Models\LandingPageSection::where('slug', 'tools')->first();
$toolsContent = App\Models\LandingPageContent::where('section_id', $toolsSection->id)->where('key', 'tools')->first();
$tools = json_decode($toolsContent->value, true);

// Change first tool icon
$tools[0]['icon'] = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />';

$toolsContent->value = json_encode($tools);
$toolsContent->save();
```

### Method 3: Admin Panel (Recommended)
When building an admin panel, create a form that allows:
1. Selecting from available icons
2. Preview of the selected icon
3. Custom SVG path input for advanced users
4. Real-time preview of changes

## Icon Guidelines

### Technical Requirements
- Icons must be SVG `<path>` elements
- Use `stroke-linecap="round"` and `stroke-linejoin="round"`
- Set `stroke-width="2"` for consistency
- Ensure viewBox is compatible with 24x24 dimensions

### Design Guidelines
- Keep icons simple and recognizable
- Ensure good contrast in both light and dark modes
- Test icons at different sizes
- Maintain consistent stroke width across all icons

### Color Support
Icons automatically inherit colors based on the tool's color setting:
- `primary`: Uses primary theme colors
- `accent`: Uses accent theme colors  
- `gradient`: Uses gradient colors

## Testing
Use the provided test script to verify icon changes:
```bash
php test_tools_icons.php
```

## Fallback
If an icon is missing or invalid, the template falls back to the document/list icon to ensure the page doesn't break.

## Future Enhancements
1. Icon picker interface in admin panel
2. Custom icon upload functionality
3. Icon preview in admin interface
4. Icon categories and search
5. Icon usage analytics