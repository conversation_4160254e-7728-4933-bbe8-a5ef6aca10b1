<?php

use App\Http\Middleware\CustomThrottleRequests;
use Illuminate\Http\Request;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Support\Facades\Route;

uses()->group('middleware');

beforeEach(function () {
    // Middleware will be instantiated inside each test after mocks are set up.
    $this->next = function ($request) {
        return response('Next middleware called');
    };

    // Define routes for feature-style tests that hit actual endpoints.
    // The 'throttle:5,1' alias should point to the CustomThrottleRequests middleware.
    Route::get('/api/throttled-test', function () {
        return response()->json(['message' => 'Success']);
    })->middleware('throttle:5,1');

    Route::get('/web/throttled-test', function () {
        return response('Success');
    })->middleware('throttle:5,1');
});

it('allows requests within rate limit', function () {
    $request = Request::create('/api/test');
    $request->headers->set('Accept', 'application/json');
    $request->setRouteResolver(fn() => (new \Illuminate\Routing\Route('GET', '/api/test', [])));

    $middleware = app(CustomThrottleRequests::class);
    $response = $middleware->handle($request, $this->next, 60, 1);

    expect($response->getContent())->toBe('Next middleware called');
});

it('returns json response for api requests when throttled', function () {
    $request = Request::create('/api/test');
    $request->headers->set('Accept', 'application/json');
    $request->setRouteResolver(fn() => (new \Illuminate\Routing\Route('GET', '/api/test', [])));

    // Mock RateLimiter to simulate too many attempts
    RateLimiter::shouldReceive('tooManyAttempts')
        ->andReturn(true);
    RateLimiter::shouldReceive('availableIn')
        ->andReturn(30);

    $middleware = app(CustomThrottleRequests::class);
    $response = $middleware->handle($request, $this->next, 60, 1);

    expect($response->getStatusCode())->toBe(429);
    expect($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['status'])->toBe(429);
    expect($content['message'])->toBe('Too Many Requests');
    expect($content['error'])->toBe('Too Many Requests');
    expect($content)->toHaveKey('retry_after');
});

it('throws exception for non api requests when throttled', function () {
    $request = Request::create('/web/test');
    $request->setRouteResolver(fn() => (new \Illuminate\Routing\Route('GET', '/web/test', [])));

    // Mock RateLimiter to simulate too many attempts
    RateLimiter::shouldReceive('tooManyAttempts')
        ->andReturn(true);
    RateLimiter::shouldReceive('availableIn')
        ->andReturn(30);

    $middleware = app(CustomThrottleRequests::class);
    expect(fn() => $middleware->handle($request, $this->next, 60, 1))
        ->toThrow(ThrottleRequestsException::class);
});

it('handles throttle exception for api requests', function () {
    $request = Request::create('/api/test');
    $request->headers->set('Accept', 'application/json');
    $request->setRouteResolver(fn() => (new \Illuminate\Routing\Route('GET', '/api/test', [])));

    RateLimiter::shouldReceive('tooManyAttempts')->andReturn(true);
    RateLimiter::shouldReceive('availableIn')->andReturn(45);

    $middleware = app(CustomThrottleRequests::class);
    $response = $middleware->handle($request, $this->next, 60, 1);

    expect($response->getStatusCode())->toBe(429);
    expect($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['status'])->toBe(429);
    expect($content['message'])->toBe('Too Many Requests');
    expect($content['error'])->toBe('Too Many Requests');
    expect($content['retry_after'])->toBe(45);
});

it('uses default retry after when not provided in exception', function () {
    $request = Request::create('/api/test');
    $request->headers->set('Accept', 'application/json');
    $request->setRouteResolver(fn() => (new \Illuminate\Routing\Route('GET', '/api/test', [])));

    RateLimiter::shouldReceive('tooManyAttempts')->andReturn(true);
    RateLimiter::shouldReceive('availableIn')->andReturn(60); // This would be the default from the parent exception

    $middleware = app(CustomThrottleRequests::class);
    $response = $middleware->handle($request, $this->next, 60, 1);

    expect($response->getStatusCode())->toBe(429);
    expect($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['retry_after'])->toBe(60);
});

it('builds custom response for api requests', function () {
    $request = Request::create('/api/test');
    $request->headers->set('Accept', 'application/json');
    $request->setRouteResolver(fn() => (new \Illuminate\Routing\Route('GET', '/api/test', [])));

    // Mock RateLimiter methods
    RateLimiter::shouldReceive('tooManyAttempts')
        ->andReturn(true);
    RateLimiter::shouldReceive('availableIn')
        ->andReturn(25);

    $middleware = app(CustomThrottleRequests::class);
    $response = $middleware->handle($request, $this->next, 60, 1);

    expect($response->getStatusCode())->toBe(429);
    expect($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['status'])->toBe(429);
    expect($content['message'])->toBe('Too Many Requests');
    expect($content['error'])->toBe('Too Many Requests');
    expect($content['retry_after'])->toBe(25);
});

it('uses custom max attempts and decay minutes', function () {
    $request = Request::create('/api/test');
    $request->headers->set('Accept', 'application/json');
    $request->setRouteResolver(fn() => (new \Illuminate\Routing\Route('GET', '/api/test', [])));

    // Mock RateLimiter to simulate too many attempts
    RateLimiter::shouldReceive('tooManyAttempts')
        ->withArgs(function ($key, $maxAttempts) {
            return $maxAttempts === 30; // Custom max attempts
        })
        ->andReturn(true);
    RateLimiter::shouldReceive('availableIn')
        ->andReturn(15);

    $middleware = app(CustomThrottleRequests::class);
    $response = $middleware->handle($request, $this->next, 30, 2);

    expect($response->getStatusCode())->toBe(429);
    expect($response->getContent())->toBeJson();
});

it('handles requests with custom prefix', function () {
    $request = Request::create('/api/test');
    $request->headers->set('Accept', 'application/json');
    $request->setRouteResolver(fn() => (new \Illuminate\Routing\Route('GET', '/api/test', [])));

    // Mock RateLimiter to simulate too many attempts
    RateLimiter::shouldReceive('tooManyAttempts')
        ->andReturn(true);
    RateLimiter::shouldReceive('availableIn')
        ->andReturn(20);

    $middleware = app(CustomThrottleRequests::class);
    $response = $middleware->handle($request, $this->next, 60, 1, 'custom_prefix');

    expect($response->getStatusCode())->toBe(429);
    expect($response->getContent())->toBeJson();
});

it('passes through requests when not throttled', function () {
    $request = Request::create('/api/test');
    $request->headers->set('Accept', 'application/json');
    $request->setRouteResolver(fn() => (new \Illuminate\Routing\Route('GET', '/api/test', [])));

    // Mock RateLimiter to simulate normal flow
    RateLimiter::shouldReceive('tooManyAttempts')->andReturn(false);
    RateLimiter::shouldReceive('hit');
    RateLimiter::shouldReceive('retriesLeft')->andReturn(10); // Expect 'retriesLeft' to be called

    $middleware = app(CustomThrottleRequests::class);
    $response = $middleware->handle($request, $this->next, 60, 1);

    expect($response->getContent())->toBe('Next middleware called');
});

it('handles web requests normally when not throttled', function () {
    $request = Request::create('/web/test');
    $request->setRouteResolver(fn() => (new \Illuminate\Routing\Route('GET', '/web/test', [])));

    // Mock RateLimiter to simulate normal flow
    RateLimiter::shouldReceive('tooManyAttempts')->andReturn(false);
    RateLimiter::shouldReceive('hit');
    RateLimiter::shouldReceive('retriesLeft')->andReturn(10); // Expect 'retriesLeft' to be called

    $middleware = app(CustomThrottleRequests::class);
    $response = $middleware->handle($request, $this->next, 60, 1);

    expect($response->getContent())->toBe('Next middleware called');
});

it('identifies api requests correctly', function () {
    $apiRequest = Request::create('/api/test');
    $apiRequest->headers->set('Accept', 'application/json');

    $webRequest = Request::create('/web/test');

    expect($apiRequest->is('api/*'))->toBeTrue();
    expect($webRequest->is('api/*'))->toBeFalse();
});

it('handles edge case with empty prefix', function () {
    $request = Request::create('/api/test');
    $request->headers->set('Accept', 'application/json');
    $request->setRouteResolver(fn() => (new \Illuminate\Routing\Route('GET', '/api/test', [])));

    // Mock RateLimiter to simulate too many attempts
    RateLimiter::shouldReceive('tooManyAttempts')
        ->andReturn(true);
    RateLimiter::shouldReceive('availableIn')
        ->andReturn(10);

    $middleware = app(CustomThrottleRequests::class);
    $response = $middleware->handle($request, $this->next, 60, 1, '');

    expect($response->getStatusCode())->toBe(429);
    expect($response->getContent())->toBeJson();
    expect(json_decode($response->getContent(), true))->toEqual([
        'status' => 429,
        'message' => 'Too Many Requests',
        'error' => 'Too Many Requests',
        'retry_after' => 10,
    ]);
});

it('allows api requests within rate limit', function () {
    for ($i = 0; $i < 5; $i++) {
        $response = $this->getJson('/api/throttled-test');
        $response->assertStatus(200);
    }
});

it('returns a custom json response for api requests when throttled', function () {
    // Hit the endpoint 5 times to use up the limit
    for ($i = 0; $i < 5; $i++) {
        $this->getJson('/api/throttled-test')->assertStatus(200);
    }

    // The 6th request should be throttled
    $response = $this->getJson('/api/throttled-test');

    $response->assertStatus(429);
    $response->assertJson([
        'status' => 429,
        'message' => 'Too Many Requests',
        'error' => 'Too Many Requests',
    ]);
    expect($response->json())->toHaveKey('retry_after');
    expect($response->json('retry_after'))->toBeGreaterThan(0);
});

it('throws the default exception for web requests when throttled', function () {
    // This test expects an exception, so we don't catch it
    $this->withoutExceptionHandling();
    
    // Hit the endpoint 5 times to use up the limit
    for ($i = 0; $i < 5; $i++) {
        $this->get('/web/throttled-test')->assertStatus(200);
    }

    // The 6th request should throw the exception
    expect(fn() => $this->get('/web/throttled-test'))
        ->toThrow(ThrottleRequestsException::class);
});