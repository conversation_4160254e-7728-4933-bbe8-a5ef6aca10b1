<?php

namespace App\Http\Middleware;

use Illuminate\Routing\Middleware\ThrottleRequests as BaseThrottleRequests;
use Illuminate\Http\Request;
use Closure;
use Illuminate\Http\Exceptions\ThrottleRequestsException;


class CustomThrottleRequests extends BaseThrottleRequests
{
    public function handle($request, Closure $next, $maxAttempts = 60, $decayMinutes = 1, $prefix = '')
    {
        try {
            return parent::handle($request, $next, $maxAttempts, $decayMinutes, $prefix);
        } catch (ThrottleRequestsException $e) {
            if ($request->is('api/*')) {
                $retryAfter = $e->getHeaders()['Retry-After'] ?? 60;
                return response()->json([
                    'status' => 429,
                    'message' => 'Too Many Requests',
                    'error' => 'Too Many Requests',
                    'retry_after' => $retryAfter,
                ], 429);
            }
            throw $e;
        }
    }

    protected function buildResponse($key, $maxAttempts)
    {
        $response = parent::buildResponse($key, $maxAttempts);

        if (request()->is('api/*')) {
            $retryAfter = $this->getTimeUntilNextRetry($key);
            $response->setContent(json_encode([
                'status' => 429,
                'message' => 'Too Many Requests',
                'error' => 'Too Many Requests',
                'retry_after' => $retryAfter,
            ]));
        }

        return $response;
    }
} 