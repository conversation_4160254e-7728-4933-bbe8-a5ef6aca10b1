# Payment Gateway Management System - Design Document

## Overview

This design extends the existing PayPal payment system to support multiple payment gateways including Razorpay and QR code bank transfers. The system will provide a centralized admin management interface for configuring payment gateways, currencies, and monitoring transactions. The design maintains backward compatibility with existing Order and Plan models while adding new functionality for gateway management.

## Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend UI   │    │  Admin Panel    │    │   Webhooks      │
│                 │    │                 │    │                 │
│ - Payment Forms │    │ - Gateway Mgmt  │    │ - Razorpay      │
│ - QR Display    │    │ - Currency Cfg  │    │ - PayPal        │
│ - Status Pages  │    │ - Analytics     │    │ - Bank Transfer │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────────────────────────────────────────────────────┐
│                    Payment Service Layer                        │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │   PayPal    │  │  Razorpay   │  │  QR Bank    │            │
│  │  Gateway    │  │  Gateway    │  │  Transfer   │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
└─────────────────────────────────────────────────────────────────┘
                                 │
┌─────────────────────────────────────────────────────────────────┐
│                      Data Layer                                 │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │   Orders    │  │  Payments   │  │  Gateways   │            │
│  │   Plans     │  │  Webhooks   │  │  Settings   │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
└─────────────────────────────────────────────────────────────────┘
```

### Payment Flow Architecture

```
Customer → Plan Selection → Gateway Selection → Payment Processing → Order Completion
    │              │               │                    │                │
    │              │               │                    │                │
    ▼              ▼               ▼                    ▼                ▼
Frontend      Plan Model     Gateway Config      Payment Gateway    Order Update
                                                                         │
                                                                         ▼
                                                                   Email Notification
```

## Components and Interfaces

### 1. Payment Gateway Management

#### PaymentGateway Model
```php
class PaymentGateway extends Model
{
    protected $fillable = [
        'name',           // 'paypal', 'razorpay', 'qr_bank_transfer'
        'display_name',   // 'PayPal', 'Razorpay', 'Bank Transfer'
        'is_active',      // boolean
        'is_default',     // boolean
        'configuration',  // encrypted JSON
        'supported_currencies', // JSON array
        'sort_order',     // integer
        'webhook_url',    // string
        'webhook_secret', // encrypted string
    ];
}
```

#### PaymentGatewayService Interface
```php
interface PaymentGatewayServiceInterface
{
    public function createPayment(Order $order): PaymentResponse;
    public function verifyPayment(string $paymentId): PaymentResponse;
    public function handleWebhook(Request $request): WebhookResponse;
    public function refundPayment(string $paymentId, float $amount): RefundResponse;
    public function getPaymentStatus(string $paymentId): PaymentStatus;
}
```

### 2. Gateway Implementations

#### RazorpayGatewayService
```php
class RazorpayGatewayService implements PaymentGatewayServiceInterface
{
    private $keyId;
    private $keySecret;
    private $webhookSecret;
    
    public function createPayment(Order $order): PaymentResponse
    {
        // Create Razorpay order
        // Return checkout URL and payment ID
    }
    
    public function verifyPayment(string $paymentId): PaymentResponse
    {
        // Verify payment with Razorpay API
        // Return payment status and details
    }
    
    public function handleWebhook(Request $request): WebhookResponse
    {
        // Verify webhook signature
        // Process payment status updates
        // Update order status
    }
}
```

#### QRBankTransferService
```php
class QRBankTransferService implements PaymentGatewayServiceInterface
{
    public function createPayment(Order $order): PaymentResponse
    {
        // Generate QR code with bank details
        // Create payment record with pending status
        // Return QR code data and upload form
    }
    
    public function verifyPayment(string $paymentId): PaymentResponse
    {
        // Manual verification by admin
        // Check uploaded payment proof
        // Return verification status
    }
}
```

### 3. Enhanced Models

#### Updated Payment Model
```php
class Payment extends Model
{
    const METHOD_PAYPAL = 'paypal';
    const METHOD_RAZORPAY = 'razorpay';
    const METHOD_QR_BANK_TRANSFER = 'qr_bank_transfer';
    
    protected $fillable = [
        'order_id',
        'gateway_id',           // Foreign key to payment_gateways
        'gateway_payment_id',   // Gateway's payment ID
        'gateway_order_id',     // Gateway's order ID
        'amount',
        'currency',
        'exchange_rate',        // For currency conversion
        'gateway_fee',          // Gateway processing fee
        'net_amount',           // Amount after gateway fee
        'payment_status',
        'payment_method',
        'payment_details',      // Gateway-specific data
        'webhook_data',         // Webhook payload
        'payment_proof',        // For QR bank transfer
        'admin_notes',          // Admin verification notes
        'failed_reason',        // Failure reason
        'refund_id',           // Refund reference
        'paid_at',
        'verified_at',         // For manual verification
        'verified_by',         // Admin who verified
    ];
}
```

#### PaymentProof Model (for QR Bank Transfer)
```php
class PaymentProof extends Model
{
    protected $fillable = [
        'payment_id',
        'file_path',
        'file_name',
        'file_size',
        'mime_type',
        'uploaded_at',
        'verification_status', // 'pending', 'approved', 'rejected'
        'verification_notes',
        'verified_by',
        'verified_at',
    ];
}
```

#### CurrencyRate Model
```php
class CurrencyRate extends Model
{
    protected $fillable = [
        'from_currency',
        'to_currency',
        'rate',
        'updated_at',
    ];
}
```

### 4. Admin Management Controllers

#### Admin\PaymentGatewayController
```php
class PaymentGatewayController extends Controller
{
    public function index()
    {
        // List all payment gateways with status
    }
    
    public function show(PaymentGateway $gateway)
    {
        // Show gateway details and statistics
    }
    
    public function update(PaymentGateway $gateway, Request $request)
    {
        // Update gateway configuration
        // Validate credentials
        // Update currency settings
    }
    
    public function toggleStatus(PaymentGateway $gateway)
    {
        // Enable/disable gateway
    }
    
    public function testConnection(PaymentGateway $gateway)
    {
        // Test gateway API connection
    }
}
```

#### Admin\PaymentVerificationController
```php
class PaymentVerificationController extends Controller
{
    public function index()
    {
        // List pending payment verifications (QR bank transfer)
    }
    
    public function show(Payment $payment)
    {
        // Show payment details and proof
    }
    
    public function approve(Payment $payment, Request $request)
    {
        // Approve payment and activate order
    }
    
    public function reject(Payment $payment, Request $request)
    {
        // Reject payment with reason
    }
}
```

### 5. Frontend Components

#### Payment Gateway Selection
```php
// View: resources/views/user/payment/gateway-selection.blade.php
<div class="payment-gateways">
    @foreach($activeGateways as $gateway)
        <div class="gateway-option" data-gateway="{{ $gateway->name }}">
            <input type="radio" name="payment_gateway" value="{{ $gateway->id }}">
            <label>
                <img src="{{ $gateway->logo_url }}" alt="{{ $gateway->display_name }}">
                <span>{{ $gateway->display_name }}</span>
            </label>
        </div>
    @endforeach
</div>
```

#### QR Code Display Component
```php
// View: resources/views/user/payment/qr-display.blade.php
<div class="qr-payment-container">
    <div class="qr-code">
        {!! QrCode::size(200)->generate($qrData) !!}
    </div>
    <div class="bank-details">
        <h3>Bank Transfer Details</h3>
        <p><strong>Account Name:</strong> {{ $bankDetails['account_name'] }}</p>
        <p><strong>Account Number:</strong> {{ $bankDetails['account_number'] }}</p>
        <p><strong>IFSC Code:</strong> {{ $bankDetails['ifsc_code'] }}</p>
        <p><strong>Amount:</strong> {{ $order->amount }} {{ $order->currency }}</p>
        <p><strong>Reference:</strong> {{ $order->order_number }}</p>
    </div>
    <div class="upload-proof">
        <form action="{{ route('payment.upload-proof', $payment) }}" method="POST" enctype="multipart/form-data">
            @csrf
            <input type="file" name="payment_proof" accept="image/*,.pdf" required>
            <button type="submit">Upload Payment Proof</button>
        </form>
    </div>
</div>
```

## Data Models

### Database Schema

#### payment_gateways table
```sql
CREATE TABLE payment_gateways (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    configuration JSON NOT NULL,
    supported_currencies JSON NOT NULL,
    sort_order INT DEFAULT 0,
    webhook_url VARCHAR(255),
    webhook_secret TEXT,
    logo_url VARCHAR(255),
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_active (is_active),
    INDEX idx_sort_order (sort_order)
);
```

#### Enhanced payments table
```sql
ALTER TABLE payments ADD COLUMN (
    gateway_id BIGINT UNSIGNED,
    gateway_payment_id VARCHAR(255),
    gateway_order_id VARCHAR(255),
    exchange_rate DECIMAL(10,6) DEFAULT 1.000000,
    gateway_fee DECIMAL(10,2) DEFAULT 0.00,
    net_amount DECIMAL(10,2),
    webhook_data JSON,
    payment_proof VARCHAR(255),
    admin_notes TEXT,
    failed_reason TEXT,
    refund_id VARCHAR(255),
    verified_at TIMESTAMP NULL,
    verified_by BIGINT UNSIGNED NULL,
    
    FOREIGN KEY (gateway_id) REFERENCES payment_gateways(id),
    FOREIGN KEY (verified_by) REFERENCES users(id),
    INDEX idx_gateway_payment_id (gateway_payment_id),
    INDEX idx_status (payment_status),
    INDEX idx_method (payment_method)
);
```

#### payment_proofs table
```sql
CREATE TABLE payment_proofs (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    payment_id BIGINT UNSIGNED NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size INT UNSIGNED NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    verification_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    verification_notes TEXT,
    verified_by BIGINT UNSIGNED NULL,
    verified_at TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE,
    FOREIGN KEY (verified_by) REFERENCES users(id),
    INDEX idx_verification_status (verification_status)
);
```

#### currency_rates table
```sql
CREATE TABLE currency_rates (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    from_currency VARCHAR(3) NOT NULL,
    to_currency VARCHAR(3) NOT NULL,
    rate DECIMAL(12,6) NOT NULL,
    source VARCHAR(50) DEFAULT 'manual',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    UNIQUE KEY unique_currency_pair (from_currency, to_currency),
    INDEX idx_currencies (from_currency, to_currency)
);
```

#### webhook_logs table
```sql
CREATE TABLE webhook_logs (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    gateway_id BIGINT UNSIGNED NOT NULL,
    payment_id BIGINT UNSIGNED NULL,
    webhook_id VARCHAR(255),
    event_type VARCHAR(100) NOT NULL,
    payload JSON NOT NULL,
    signature VARCHAR(500),
    status ENUM('pending', 'processed', 'failed') DEFAULT 'pending',
    processed_at TIMESTAMP NULL,
    error_message TEXT,
    retry_count INT DEFAULT 0,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (gateway_id) REFERENCES payment_gateways(id),
    FOREIGN KEY (payment_id) REFERENCES payments(id),
    INDEX idx_status (status),
    INDEX idx_event_type (event_type),
    INDEX idx_created_at (created_at)
);
```

## Error Handling

### Payment Gateway Errors
```php
class PaymentGatewayException extends Exception
{
    protected $gatewayCode;
    protected $gatewayMessage;
    
    public function __construct($message, $gatewayCode = null, $gatewayMessage = null)
    {
        parent::__construct($message);
        $this->gatewayCode = $gatewayCode;
        $this->gatewayMessage = $gatewayMessage;
    }
}
```

### Error Response Structure
```php
class PaymentResponse
{
    public $success;
    public $paymentId;
    public $orderId;
    public $checkoutUrl;
    public $status;
    public $message;
    public $errorCode;
    public $gatewayResponse;
    
    public static function success($data): self
    {
        $response = new self();
        $response->success = true;
        // ... set success data
        return $response;
    }
    
    public static function error($message, $code = null): self
    {
        $response = new self();
        $response->success = false;
        $response->message = $message;
        $response->errorCode = $code;
        return $response;
    }
}
```

## Testing Strategy

### Unit Tests
- PaymentGatewayService implementations
- Currency conversion calculations
- Webhook signature verification
- QR code generation

### Integration Tests
- End-to-end payment flows
- Webhook processing
- Admin gateway management
- Payment verification workflow

### Feature Tests
- Payment gateway selection UI
- QR code payment flow
- Admin panel functionality
- Payment status updates

### Mock Services
```php
class MockRazorpayService implements PaymentGatewayServiceInterface
{
    public function createPayment(Order $order): PaymentResponse
    {
        // Return mock successful response for testing
    }
    
    // ... other mock implementations
}
```

## Security Considerations

### Credential Encryption
- All gateway credentials stored encrypted in database
- Use Laravel's built-in encryption for sensitive data
- Separate encryption keys for different environments

### Webhook Security
- Verify webhook signatures for all gateways
- Implement rate limiting for webhook endpoints
- Log all webhook attempts for monitoring

### File Upload Security
- Validate file types for payment proof uploads
- Scan uploaded files for malware
- Store files outside web root
- Implement file size limits

### API Security
- Use HTTPS for all payment-related communications
- Implement request signing for sensitive operations
- Add CSRF protection for admin operations
- Rate limit payment attempts per user

This design provides a comprehensive foundation for implementing the multi-gateway payment system while maintaining security, scalability, and ease of management.