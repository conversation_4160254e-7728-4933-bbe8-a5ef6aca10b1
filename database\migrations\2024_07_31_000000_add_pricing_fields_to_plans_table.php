<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            // Add missing fields for pricing section compatibility
            $table->string('currency', 10)->default('₹')->after('price');
            $table->string('billing_cycle')->default('monthly')->after('currency');
            $table->integer('sort_order')->default(0)->after('features');

            // Add soft deletes column for SoftDeletes trait
            $table->softDeletes()->after('updated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            $table->dropColumn(['currency', 'billing_cycle', 'sort_order']);
            $table->dropSoftDeletes();
        });
    }
};
