<?php

use App\Models\Page;
use Illuminate\Support\Str;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('page can be created', function () {
    $page = Page::factory()->create([
        'title' => 'Test Page',
        'content' => 'Test content',
        'template' => 'default',
        'is_active' => true,
        'show_in_menu' => true,
        'order' => 1
    ]);

    expect($page)->toBeInstanceOf(Page::class)
        ->and($page->title)->toBe('Test Page')
        ->and($page->content)->toBe('Test content')
        ->and($page->template)->toBe('default')
        ->and($page->is_active)->toBeTrue()
        ->and($page->show_in_menu)->toBeTrue()
        ->and($page->order)->toBe(1);
});

test('page factory works correctly', function () {
    $page = Page::factory()->create();
    
    expect($page)->toBeInstanceOf(Page::class)
        ->and($page->title)->not->toBeEmpty()
        ->and($page->slug)->not->toBeEmpty()
        ->and($page->content)->not->toBeEmpty()
        ->and($page->template)->toBe('default')
        ->and($page->is_active)->toBeTrue();
});

test('page factory inactive state works', function () {
    $page = Page::factory()->inactive()->create();
    
    expect($page->is_active)->toBeFalse();
});

test('page factory custom template state works', function () {
    $page = Page::factory()->customTemplate('with-sidebar')->create();
    
    expect($page->template)->toBe('with-sidebar');
});

test('meta title is set from title when empty', function () {
    $page = Page::factory()->create([
        'title' => 'Test Page',
        'meta_title' => null
    ]);
    
    expect($page->meta_title)->toBe('Test Page');
});

test('meta title is not overwritten when provided', function () {
    $page = Page::factory()->create([
        'title' => 'Test Page',
        'meta_title' => 'Custom Meta Title'
    ]);
    
    expect($page->meta_title)->toBe('Custom Meta Title');
});

test('slug is generated from title when empty', function () {
    $page = Page::factory()->make([
        'title' => 'Test Page Title',
        'slug' => null
    ]);
    
    $page->save();
    
    expect($page->slug)->toBe('test-page-title');
});

test('slug is not overwritten when provided', function () {
    $page = Page::factory()->create([
        'title' => 'Test Page',
        'slug' => 'custom-slug'
    ]);
    
    expect($page->slug)->toBe('custom-slug');
});

test('getRouteKeyName returns slug', function () {
    $page = new Page();
    
    expect($page->getRouteKeyName())->toBe('slug');
});

test('getTemplates returns correct template options', function () {
    $templates = Page::getTemplates();
    
    expect($templates)->toBeArray()
        ->and($templates)->toHaveCount(2)
        ->and($templates)->toHaveKeys(['default', 'with-sidebar'])
        ->and($templates['default'])->toBe('Default Template')
        ->and($templates['with-sidebar'])->toBe('With Sidebar Template');
});

test('active scope returns only active pages', function () {
    // Clear existing pages
    Page::query()->delete();
    
    // Create active and inactive pages
    Page::factory()->create(['is_active' => true]);
    Page::factory()->create(['is_active' => true]);
    Page::factory()->create(['is_active' => false]);
    
    $activePages = Page::active()->get();
    
    expect($activePages)->toHaveCount(2);
    $activePages->each(function ($page) {
        expect($page->is_active)->toBeTrue();
    });
});

test('inMenu scope returns only pages shown in menu', function () {
    // Clear existing pages
    Page::query()->delete();
    
    // Create pages with different show_in_menu values
    Page::factory()->create(['show_in_menu' => true]);
    Page::factory()->create(['show_in_menu' => true]);
    Page::factory()->create(['show_in_menu' => false]);
    
    $menuPages = Page::inMenu()->get();
    
    expect($menuPages)->toHaveCount(2);
    $menuPages->each(function ($page) {
        expect($page->show_in_menu)->toBeTrue();
    });
});

test('ordered scope sorts pages by order field', function () {
    // Clear existing pages
    Page::query()->delete();
    
    // Create pages with different order values
    Page::factory()->create(['order' => 3, 'title' => 'Page C']);
    Page::factory()->create(['order' => 1, 'title' => 'Page A']);
    Page::factory()->create(['order' => 2, 'title' => 'Page B']);
    
    $orderedPages = Page::ordered()->get();
    
    expect($orderedPages)->toHaveCount(3)
        ->and($orderedPages->pluck('title')->toArray())->toBe(['Page A', 'Page B', 'Page C']);
});

test('fillable attributes are correctly defined', function () {
    $page = new Page();
    $fillable = $page->getFillable();
    
    expect($fillable)->toBeArray()
        ->and($fillable)->toContain('title')
        ->and($fillable)->toContain('slug')
        ->and($fillable)->toContain('content')
        ->and($fillable)->toContain('template')
        ->and($fillable)->toContain('meta_title')
        ->and($fillable)->toContain('meta_description')
        ->and($fillable)->toContain('is_active')
        ->and($fillable)->toContain('order')
        ->and($fillable)->toContain('show_in_menu');
});

test('casts are correctly defined', function () {
    $page = new Page();
    $casts = $page->getCasts();
    
    expect($casts)->toBeArray()
        ->and($casts['is_active'])->toBe('boolean')
        ->and($casts['show_in_menu'])->toBe('boolean')
        ->and($casts['order'])->toBe('integer');
});