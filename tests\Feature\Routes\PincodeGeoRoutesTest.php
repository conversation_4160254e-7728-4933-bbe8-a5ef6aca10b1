<?php

use App\Models\PincodeGeo;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('PincodeGeoController routes', function () {
    test('search returns results for valid query', function () {
        PincodeGeo::factory()->create(['pincode' => '110001']);
        $response = $this->getJson('/pincode-geo/search?q=110');
        $response->assertOk();
        $response->assertJsonFragment(['pincode' => '110001']);
    });

    test('search returns 400 for invalid query', function () {
        $response = $this->getJson('/pincode-geo/search?q=');
        $response->assertStatus(400);
        $response->assertJsonFragment(['error' => 'Invalid search query']);
    });

    test('getPincodesByBounds returns results for valid bounds', function () {
        PincodeGeo::factory()->create([
            'pincode' => '110001',
        ]);
        $response = $this->getJson('/pincode-geo/bounds?north=29&south=28&east=78&west=77');
        $response->assertOk();
        $response->assertJsonStructure(['type', 'features']);
        $response->assertJsonFragment(['pincode' => '110001']);
    });

    test('getPincodesByBounds returns results even for invalid bounds', function () {
        PincodeGeo::factory()->create([
            'pincode' => '110001',
        ]);
        $response = $this->getJson('/pincode-geo/bounds?north=abc&south=28&east=78&west=77');
        $response->assertOk();
        $response->assertJsonStructure(['type', 'features']);
        $response->assertJsonFragment(['pincode' => '110001']);
    });

    test('show returns pincode geo data for valid pincode', function () {
        $geo = PincodeGeo::factory()->create(['pincode' => '110001']);
        $response = $this->getJson('/pincode-geo/110001');
        $response->assertOk();
        $response->assertJsonFragment(['pincode' => '110001']);
    });

    test('show returns 404 for not found pincode', function () {
        $response = $this->getJson('/pincode-geo/999999');
        $response->assertStatus(404);
        $response->assertJsonFragment(['error' => 'Pincode not found']);
    });
}); 