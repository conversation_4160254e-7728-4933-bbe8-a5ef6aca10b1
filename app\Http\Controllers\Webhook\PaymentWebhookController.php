<?php

namespace App\Http\Controllers\Webhook;

use App\Http\Controllers\Controller;
use App\Models\WebhookLog;
use App\Services\Payment\PaymentGatewayManager;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentWebhookController extends Controller
{
    protected PaymentGatewayManager $gatewayManager;

    public function __construct(PaymentGatewayManager $gatewayManager)
    {
        $this->gatewayManager = $gatewayManager;
    }

    /**
     * Handle webhook for any payment gateway
     */
    public function handle(Request $request, string $gateway)
    {
        $startTime = microtime(true);
        
        Log::info('Payment webhook received', [
            'gateway' => $gateway,
            'event' => $request->input('event', 'unknown'),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'content_type' => $request->header('Content-Type'),
            'payload_size' => strlen($request->getContent()),
        ]);

        try {
            // Validate gateway
            if (!$this->isValidGateway($gateway)) {
                Log::warning('Invalid gateway in webhook', ['gateway' => $gateway]);
                return response()->json(['error' => 'Invalid gateway'], 400);
            }

            // Process webhook using the gateway manager
            $webhookResponse = $this->gatewayManager->handleWebhook($gateway, $request);

            $processingTime = round((microtime(true) - $startTime) * 1000, 2);

            if ($webhookResponse->isSuccess()) {
                Log::info('Payment webhook processed successfully', [
                    'gateway' => $gateway,
                    'event_type' => $webhookResponse->eventType,
                    'payment_id' => $webhookResponse->paymentId,
                    'order_id' => $webhookResponse->orderId,
                    'status' => $webhookResponse->status,
                    'processing_time_ms' => $processingTime,
                    'actions_count' => count($webhookResponse->getActions()),
                ]);

                // Execute post-processing actions asynchronously
                if (!empty($webhookResponse->getActions())) {
                    $this->executeWebhookActions($webhookResponse->getActions(), $gateway);
                }

                return response()->json([
                    'status' => 'success',
                    'message' => 'Webhook processed successfully',
                    'processing_time_ms' => $processingTime,
                ], 200);
            }

            Log::warning('Payment webhook processing failed', [
                'gateway' => $gateway,
                'message' => $webhookResponse->message,
                'event_type' => $webhookResponse->eventType,
                'processing_time_ms' => $processingTime,
            ]);

            return response()->json([
                'status' => 'error',
                'message' => $webhookResponse->message ?? 'Webhook processing failed',
                'processing_time_ms' => $processingTime,
            ], 400);

        } catch (\Exception $e) {
            $processingTime = round((microtime(true) - $startTime) * 1000, 2);
            
            Log::error('Payment webhook error', [
                'gateway' => $gateway,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'processing_time_ms' => $processingTime,
                'payload' => $request->all(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Internal server error',
                'processing_time_ms' => $processingTime,
            ], 500);
        }
    }

    /**
     * Get webhook logs for debugging
     */
    public function logs(Request $request, string $gateway)
    {
        $request->validate([
            'limit' => 'integer|min:1|max:100',
            'status' => 'in:pending,processed,failed',
            'event_type' => 'string|max:100',
        ]);

        $query = WebhookLog::with(['gateway', 'payment'])
                          ->whereHas('gateway', function ($q) use ($gateway) {
                              $q->where('name', $gateway);
                          })
                          ->latest();

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('event_type')) {
            $query->where('event_type', $request->event_type);
        }

        $logs = $query->limit($request->input('limit', 50))->get();

        return response()->json([
            'success' => true,
            'logs' => $logs->map(function ($log) {
                return [
                    'id' => $log->id,
                    'event_type' => $log->event_type,
                    'status' => $log->status,
                    'payment_id' => $log->payment_id,
                    'webhook_id' => $log->webhook_id,
                    'retry_count' => $log->retry_count,
                    'error_message' => $log->error_message,
                    'created_at' => $log->created_at->toISOString(),
                    'processed_at' => $log->processed_at?->toISOString(),
                ];
            }),
        ]);
    }

    /**
     * Retry failed webhook
     */
    public function retry(Request $request, string $gateway, int $webhookLogId)
    {
        try {
            $webhookLog = WebhookLog::whereHas('gateway', function ($q) use ($gateway) {
                                $q->where('name', $gateway);
                            })
                            ->where('id', $webhookLogId)
                            ->where('status', 'failed')
                            ->firstOrFail();

            // Recreate the request from stored payload
            $fakeRequest = new Request();
            $fakeRequest->replace($webhookLog->payload);
            
            // Add signature header if available
            if ($webhookLog->signature) {
                $fakeRequest->headers->set('X-Razorpay-Signature', $webhookLog->signature);
            }

            // Process the webhook again
            $webhookResponse = $this->gatewayManager->handleWebhook($gateway, $fakeRequest);

            if ($webhookResponse->isSuccess()) {
                Log::info('Webhook retry successful', [
                    'webhook_log_id' => $webhookLogId,
                    'gateway' => $gateway,
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Webhook retry successful',
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => $webhookResponse->message ?? 'Webhook retry failed',
            ], 400);

        } catch (\Exception $e) {
            Log::error('Webhook retry error', [
                'webhook_log_id' => $webhookLogId,
                'gateway' => $gateway,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Webhook retry failed',
            ], 500);
        }
    }

    /**
     * Get webhook statistics
     */
    public function stats(string $gateway)
    {
        $stats = WebhookLog::whereHas('gateway', function ($q) use ($gateway) {
                            $q->where('name', $gateway);
                        })
                        ->selectRaw('
                            status,
                            COUNT(*) as count,
                            AVG(retry_count) as avg_retries
                        ')
                        ->groupBy('status')
                        ->get()
                        ->keyBy('status');

        $eventStats = WebhookLog::whereHas('gateway', function ($q) use ($gateway) {
                                $q->where('name', $gateway);
                            })
                            ->selectRaw('
                                event_type,
                                COUNT(*) as count,
                                SUM(CASE WHEN status = "processed" THEN 1 ELSE 0 END) as success_count
                            ')
                            ->groupBy('event_type')
                            ->get();

        return response()->json([
            'success' => true,
            'gateway' => $gateway,
            'status_stats' => $stats,
            'event_stats' => $eventStats->map(function ($stat) {
                return [
                    'event_type' => $stat->event_type,
                    'total_count' => $stat->count,
                    'success_count' => $stat->success_count,
                    'success_rate' => $stat->count > 0 ? round(($stat->success_count / $stat->count) * 100, 2) : 0,
                ];
            }),
            'total_webhooks' => WebhookLog::whereHas('gateway', function ($q) use ($gateway) {
                $q->where('name', $gateway);
            })->count(),
        ]);
    }

    /**
     * Execute webhook actions
     */
    private function executeWebhookActions(array $actions, string $gateway)
    {
        foreach ($actions as $action) {
            try {
                switch ($action['action']) {
                    case 'send_confirmation_email':
                        $this->sendConfirmationEmail($action['data'], $gateway);
                        break;
                    
                    case 'send_failure_notification':
                        $this->sendFailureNotification($action['data'], $gateway);
                        break;
                    
                    case 'update_user_subscription':
                        $this->updateUserSubscription($action['data'], $gateway);
                        break;
                    
                    case 'sync_external_system':
                        $this->syncExternalSystem($action['data'], $gateway);
                        break;
                    
                    default:
                        Log::warning('Unknown webhook action', [
                            'action' => $action['action'],
                            'gateway' => $gateway,
                        ]);
                }
            } catch (\Exception $e) {
                Log::error('Webhook action execution failed', [
                    'action' => $action['action'],
                    'gateway' => $gateway,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * Validate gateway name
     */
    private function isValidGateway(string $gateway): bool
    {
        $validGateways = ['razorpay', 'paypal', 'qr_bank_transfer'];
        return in_array($gateway, $validGateways);
    }

    /**
     * Send payment confirmation email
     */
    private function sendConfirmationEmail(array $data, string $gateway)
    {
        // TODO: Queue email job
        Log::info('Payment confirmation email queued', [
            'gateway' => $gateway,
            'data' => $data,
        ]);
    }

    /**
     * Send payment failure notification
     */
    private function sendFailureNotification(array $data, string $gateway)
    {
        // TODO: Queue notification job
        Log::info('Payment failure notification queued', [
            'gateway' => $gateway,
            'data' => $data,
        ]);
    }

    /**
     * Update user subscription status
     */
    private function updateUserSubscription(array $data, string $gateway)
    {
        // TODO: Queue subscription update job
        Log::info('User subscription update queued', [
            'gateway' => $gateway,
            'data' => $data,
        ]);
    }

    /**
     * Sync with external system
     */
    private function syncExternalSystem(array $data, string $gateway)
    {
        // TODO: Queue external sync job
        Log::info('External system sync queued', [
            'gateway' => $gateway,
            'data' => $data,
        ]);
    }
}