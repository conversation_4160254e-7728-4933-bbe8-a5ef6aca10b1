@php
    $schema = [
        '@context' => 'https://schema.org',
        '@type' => 'WebPage',
        'name' => get_setting('site_name','Pincode Directory'),
        'description' => $metaDescription ?? get_setting('meta_description'),
        'publisher' => [
            '@type' => 'Organization',
            'name' => get_setting('schema_organization_name', 'PinCode Directory'),
        ],
        'url' => url()->current(),
        'datePublished' => now()->toIso8601String(),
        'dateModified' => now()->toIso8601String(),
    ];
@endphp

<script type="application/ld+json">
    {!! json_encode($schema, JSON_PRETTY_PRINT) !!}
</script>
