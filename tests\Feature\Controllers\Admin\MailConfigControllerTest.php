<?php

use App\Services\MailConfigService;
use Illuminate\Support\Facades\Mail;
use function Pest\Laravel\{get, post};
use App\Models\User;
use App\Mail\TestConfigMail;

beforeEach(function () {
    $this->mailConfigService = mock(MailConfigService::class);
    $this->app->instance(MailConfigService::class, $this->mailConfigService);
    
    // Create and authenticate an admin user
    $admin = User::factory()->admin()->create();
    $this->actingAs($admin);
});

test('index method displays mail configuration form', function () {
    $mockConfig = [
        'mail_driver' => 'smtp',
        'mail_host' => 'smtp.mailtrap.io',
        'mail_port' => 2525,
        'mail_username' => 'test',
        'mail_password' => 'test',
        'mail_encryption' => 'tls',
        'mail_from_address' => '<EMAIL>',
        'mail_from_name' => 'Test User'
    ];

    $this->mailConfigService
        ->shouldReceive('getCurrentConfig')
        ->once()
        ->andReturn($mockConfig);

    get(route('admin.mail-config.index'))
        ->assertStatus(200)
        ->assertViewIs('admin.settings.mail-config')
        ->assertViewHas('mailConfig', $mockConfig);
});

test('update method updates mail configuration successfully', function () {
    $validData = [
        'mail_driver' => 'smtp',
        'mail_host' => 'smtp.mailtrap.io',
        'mail_port' => 2525,
        'mail_username' => 'test',
        'mail_password' => 'test',
        'mail_encryption' => 'tls',
        'mail_from_address' => '<EMAIL>',
        'mail_from_name' => 'Test User'
    ];

    $this->mailConfigService
        ->shouldReceive('updateMailConfig')
        ->once()
        ->with($validData)
        ->andReturn(true);

    post(route('admin.mail-config.update'), $validData)
        ->assertRedirect(route('admin.mail-config.index'))
        ->assertSessionHas('success', 'Mail configuration updated successfully.');
});

test('update method handles validation errors', function () {
    $invalidData = [
        'mail_driver' => '',
        'mail_host' => '',
        'mail_port' => 'invalid',
        'mail_from_address' => 'invalid-email',
        'mail_from_name' => ''
    ];

    post(route('admin.mail-config.update'), $invalidData)
        ->assertSessionHasErrors([
            'mail_driver',
            'mail_host',
            'mail_port',
            'mail_from_address',
            'mail_from_name'
        ]);
});

test('update method handles service exceptions', function () {
    $validData = [
        'mail_driver' => 'smtp',
        'mail_host' => 'smtp.mailtrap.io',
        'mail_port' => 2525,
        'mail_username' => 'test',
        'mail_password' => 'test',
        'mail_encryption' => 'tls',
        'mail_from_address' => '<EMAIL>',
        'mail_from_name' => 'Test User'
    ];

    $this->mailConfigService
        ->shouldReceive('updateMailConfig')
        ->once()
        ->andThrow(new \Exception('Service error'));

    post(route('admin.mail-config.update'), $validData)
        ->assertRedirect()
        ->assertSessionHas('error')
        ->assertSessionHasInput('mail_driver');
});

test('sendTestEmail sends test email successfully', function () {
    Mail::fake();

    $testEmail = '<EMAIL>';

    $this->mailConfigService
        ->shouldReceive('loadMailConfig')
        ->once();

    $this->mailConfigService
        ->shouldReceive('verifyMailConfig')
        ->once()
        ->andReturn(true);

    post(route('admin.mail-config.test'), ['test_email' => $testEmail])
        ->assertJson([
            'success' => true,
            'message' => 'Test email sent successfully. Please check your inbox.'
        ]);

    Mail::assertSent(TestConfigMail::class, function ($mail) use ($testEmail) {
        return $mail->hasTo($testEmail);
    });
});

test('sendTestEmail handles invalid mail configuration', function () {
    $testEmail = '<EMAIL>';

    $this->mailConfigService
        ->shouldReceive('loadMailConfig')
        ->once();

    $this->mailConfigService
        ->shouldReceive('verifyMailConfig')
        ->once()
        ->andReturn(false);

    post(route('admin.mail-config.test'), ['test_email' => $testEmail])
        ->assertJson([
            'success' => false,
            'message' => 'Mail configuration is incomplete. Please check your settings.'
        ]);
});

test('sendTestEmail handles sending errors', function () {
    Mail::fake();
    Mail::shouldReceive('to->send')
        ->andThrow(new \Exception('Mail error'));

    $testEmail = '<EMAIL>';

    $this->mailConfigService
        ->shouldReceive('loadMailConfig')
        ->once();

    $this->mailConfigService
        ->shouldReceive('verifyMailConfig')
        ->once()
        ->andReturn(true);

    post(route('admin.mail-config.test'), ['test_email' => $testEmail])
        ->assertJson([
            'success' => false,
            'message' => 'Failed to send test email: Mail error'
        ]);
});