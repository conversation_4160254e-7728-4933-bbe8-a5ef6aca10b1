<?php

namespace App\Services\Payment;

use App\Models\PaymentGateway;
use App\Models\Plan;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class PaymentValidationService
{
    /**
     * Supported currencies with their properties
     */
    private const SUPPORTED_CURRENCIES = [
        'USD' => [
            'symbol' => '$',
            'decimal_places' => 2,
            'min_amount' => 0.50,
            'max_amount' => 10000.00
        ],
        'EUR' => [
            'symbol' => '€',
            'decimal_places' => 2,
            'min_amount' => 0.50,
            'max_amount' => 10000.00
        ],
        'INR' => [
            'symbol' => '₹',
            'decimal_places' => 2,
            'min_amount' => 1.00,
            'max_amount' => 500000.00
        ],
        'GBP' => [
            'symbol' => '£',
            'decimal_places' => 2,
            'min_amount' => 0.30,
            'max_amount' => 8000.00
        ]
    ];

    /**
     * Validate payment amount and currency
     *
     * @param float $amount
     * @param string $currency
     * @param PaymentGateway $gateway
     * @return array
     */
    public function validatePaymentAmount(float $amount, string $currency, PaymentGateway $gateway): array
    {
        try {
            // Validate currency format
            $currencyValidation = $this->validateCurrency($currency);
            if (!$currencyValidation['valid']) {
                return $currencyValidation;
            }

            // Validate gateway supports currency
            $gatewayCurrencyValidation = $this->validateGatewayCurrency($currency, $gateway);
            if (!$gatewayCurrencyValidation['valid']) {
                return $gatewayCurrencyValidation;
            }

            // Validate amount format and range
            $amountValidation = $this->validateAmount($amount, $currency);
            if (!$amountValidation['valid']) {
                return $amountValidation;
            }

            // Validate decimal places
            $decimalValidation = $this->validateDecimalPlaces($amount, $currency);
            if (!$decimalValidation['valid']) {
                return $decimalValidation;
            }

            return [
                'valid' => true,
                'formatted_amount' => $this->formatAmount($amount, $currency),
                'currency_info' => self::SUPPORTED_CURRENCIES[$currency]
            ];

        } catch (\Exception $e) {
            Log::error('Payment amount validation error', [
                'amount' => $amount,
                'currency' => $currency,
                'gateway_id' => $gateway->id,
                'error' => $e->getMessage()
            ]);

            return [
                'valid' => false,
                'error' => 'Payment validation failed',
                'details' => 'Unable to validate payment amount'
            ];
        }
    }

    /**
     * Validate plan pricing against payment amount
     *
     * @param Plan $plan
     * @param float $amount
     * @param string $currency
     * @return array
     */
    public function validatePlanPayment(Plan $plan, float $amount, string $currency): array
    {
        try {
            // Get plan price in the requested currency
            $planPrice = $this->getPlanPriceInCurrency($plan, $currency);
            
            if ($planPrice === null) {
                return [
                    'valid' => false,
                    'error' => 'Plan price not available in requested currency',
                    'details' => [
                        'plan_id' => $plan->id,
                        'requested_currency' => $currency,
                        'plan_currency' => $plan->currency ?? 'USD'
                    ]
                ];
            }

            // Allow small tolerance for currency conversion differences
            $tolerance = 0.02;
            $difference = abs($amount - $planPrice);

            if ($difference > $tolerance) {
                return [
                    'valid' => false,
                    'error' => 'Payment amount does not match plan price',
                    'details' => [
                        'expected_amount' => $planPrice,
                        'provided_amount' => $amount,
                        'difference' => $difference,
                        'currency' => $currency
                    ]
                ];
            }

            return [
                'valid' => true,
                'plan_price' => $planPrice,
                'payment_amount' => $amount,
                'currency' => $currency
            ];

        } catch (\Exception $e) {
            Log::error('Plan payment validation error', [
                'plan_id' => $plan->id,
                'amount' => $amount,
                'currency' => $currency,
                'error' => $e->getMessage()
            ]);

            return [
                'valid' => false,
                'error' => 'Plan payment validation failed',
                'details' => $e->getMessage()
            ];
        }
    }

    /**
     * Validate payment request data
     *
     * @param array $paymentData
     * @return array
     */
    public function validatePaymentRequest(array $paymentData): array
    {
        $errors = [];

        // Required fields validation
        $requiredFields = ['amount', 'currency', 'gateway_id', 'plan_id'];
        foreach ($requiredFields as $field) {
            if (!isset($paymentData[$field]) || empty($paymentData[$field])) {
                $errors[$field] = "The {$field} field is required.";
            }
        }

        if (!empty($errors)) {
            return [
                'valid' => false,
                'error' => 'Missing required fields',
                'details' => $errors
            ];
        }

        // Validate amount is numeric and positive
        if (!is_numeric($paymentData['amount']) || $paymentData['amount'] <= 0) {
            $errors['amount'] = 'Amount must be a positive number.';
        }

        // Validate currency format
        if (!preg_match('/^[A-Z]{3}$/', $paymentData['currency'])) {
            $errors['currency'] = 'Currency must be a valid 3-letter ISO code.';
        }

        // Validate gateway_id is numeric
        if (!is_numeric($paymentData['gateway_id'])) {
            $errors['gateway_id'] = 'Gateway ID must be numeric.';
        }

        // Validate plan_id is numeric
        if (!is_numeric($paymentData['plan_id'])) {
            $errors['plan_id'] = 'Plan ID must be numeric.';
        }

        // Additional field validations
        if (isset($paymentData['callback_url']) && !filter_var($paymentData['callback_url'], FILTER_VALIDATE_URL)) {
            $errors['callback_url'] = 'Callback URL must be a valid URL.';
        }

        if (!empty($errors)) {
            return [
                'valid' => false,
                'error' => 'Invalid payment data',
                'details' => $errors
            ];
        }

        return ['valid' => true];
    }

    /**
     * Validate currency code
     *
     * @param string $currency
     * @return array
     */
    private function validateCurrency(string $currency): array
    {
        if (!isset(self::SUPPORTED_CURRENCIES[$currency])) {
            return [
                'valid' => false,
                'error' => 'Unsupported currency',
                'details' => [
                    'provided_currency' => $currency,
                    'supported_currencies' => array_keys(self::SUPPORTED_CURRENCIES)
                ]
            ];
        }

        return ['valid' => true];
    }

    /**
     * Validate gateway supports currency
     *
     * @param string $currency
     * @param PaymentGateway $gateway
     * @return array
     */
    private function validateGatewayCurrency(string $currency, PaymentGateway $gateway): array
    {
        $supportedCurrencies = $gateway->supported_currencies ?? [];
        
        if (!in_array($currency, $supportedCurrencies)) {
            return [
                'valid' => false,
                'error' => 'Gateway does not support this currency',
                'details' => [
                    'gateway' => $gateway->name,
                    'requested_currency' => $currency,
                    'supported_currencies' => $supportedCurrencies
                ]
            ];
        }

        return ['valid' => true];
    }

    /**
     * Validate amount range
     *
     * @param float $amount
     * @param string $currency
     * @return array
     */
    private function validateAmount(float $amount, string $currency): array
    {
        $currencyInfo = self::SUPPORTED_CURRENCIES[$currency];
        
        if ($amount < $currencyInfo['min_amount']) {
            return [
                'valid' => false,
                'error' => 'Amount below minimum allowed',
                'details' => [
                    'amount' => $amount,
                    'min_amount' => $currencyInfo['min_amount'],
                    'currency' => $currency
                ]
            ];
        }

        if ($amount > $currencyInfo['max_amount']) {
            return [
                'valid' => false,
                'error' => 'Amount exceeds maximum allowed',
                'details' => [
                    'amount' => $amount,
                    'max_amount' => $currencyInfo['max_amount'],
                    'currency' => $currency
                ]
            ];
        }

        return ['valid' => true];
    }

    /**
     * Validate decimal places
     *
     * @param float $amount
     * @param string $currency
     * @return array
     */
    private function validateDecimalPlaces(float $amount, string $currency): array
    {
        $currencyInfo = self::SUPPORTED_CURRENCIES[$currency];
        $decimalPlaces = strlen(substr(strrchr($amount, "."), 1));
        
        if ($decimalPlaces > $currencyInfo['decimal_places']) {
            return [
                'valid' => false,
                'error' => 'Too many decimal places',
                'details' => [
                    'amount' => $amount,
                    'decimal_places' => $decimalPlaces,
                    'max_decimal_places' => $currencyInfo['decimal_places'],
                    'currency' => $currency
                ]
            ];
        }

        return ['valid' => true];
    }

    /**
     * Format amount according to currency
     *
     * @param float $amount
     * @param string $currency
     * @return string
     */
    private function formatAmount(float $amount, string $currency): string
    {
        $currencyInfo = self::SUPPORTED_CURRENCIES[$currency];
        return number_format($amount, $currencyInfo['decimal_places'], '.', '');
    }

    /**
     * Get plan price in specific currency
     *
     * @param Plan $plan
     * @param string $currency
     * @return float|null
     */
    private function getPlanPriceInCurrency(Plan $plan, string $currency): ?float
    {
        // If plan is in the same currency, return the price
        if (($plan->currency ?? 'USD') === $currency) {
            return (float) $plan->price;
        }

        // For different currencies, you would typically use a currency conversion service
        // For now, we'll assume the plan price is in USD and convert if needed
        $planCurrency = $plan->currency ?? 'USD';
        
        if ($planCurrency === 'USD' && $currency !== 'USD') {
            // Use a simple conversion rate (in production, use real exchange rates)
            $conversionRates = [
                'EUR' => 0.85,
                'INR' => 83.0,
                'GBP' => 0.73
            ];
            
            if (isset($conversionRates[$currency])) {
                return round($plan->price * $conversionRates[$currency], 2);
            }
        }

        return null;
    }

    /**
     * Get supported currencies
     *
     * @return array
     */
    public function getSupportedCurrencies(): array
    {
        return self::SUPPORTED_CURRENCIES;
    }

    /**
     * Get currency info
     *
     * @param string $currency
     * @return array|null
     */
    public function getCurrencyInfo(string $currency): ?array
    {
        return self::SUPPORTED_CURRENCIES[$currency] ?? null;
    }

    /**
     * Validate refund amount
     *
     * @param float $refundAmount
     * @param float $originalAmount
     * @param string $currency
     * @return array
     */
    public function validateRefundAmount(float $refundAmount, float $originalAmount, string $currency): array
    {
        try {
            // Validate currency
            $currencyValidation = $this->validateCurrency($currency);
            if (!$currencyValidation['valid']) {
                return $currencyValidation;
            }

            // Validate refund amount is positive
            if ($refundAmount <= 0) {
                return [
                    'valid' => false,
                    'error' => 'Refund amount must be positive',
                    'details' => ['refund_amount' => $refundAmount]
                ];
            }

            // Validate refund amount doesn't exceed original amount
            if ($refundAmount > $originalAmount) {
                return [
                    'valid' => false,
                    'error' => 'Refund amount cannot exceed original payment amount',
                    'details' => [
                        'refund_amount' => $refundAmount,
                        'original_amount' => $originalAmount
                    ]
                ];
            }

            // Validate decimal places
            $decimalValidation = $this->validateDecimalPlaces($refundAmount, $currency);
            if (!$decimalValidation['valid']) {
                return $decimalValidation;
            }

            return [
                'valid' => true,
                'refund_amount' => $this->formatAmount($refundAmount, $currency),
                'original_amount' => $this->formatAmount($originalAmount, $currency)
            ];

        } catch (\Exception $e) {
            Log::error('Refund amount validation error', [
                'refund_amount' => $refundAmount,
                'original_amount' => $originalAmount,
                'currency' => $currency,
                'error' => $e->getMessage()
            ]);

            return [
                'valid' => false,
                'error' => 'Refund validation failed',
                'details' => $e->getMessage()
            ];
        }
    }
}