@extends('layouts.app')

@section('content')
    <x-breadcrumb :segments="$breadcrumbs" :pageTitle="$pageTitle" :metaDescription="$metaDescription" />

    <div class="container max-w-6xl mx-auto px-4 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-8">
                <!-- Header Section -->
                <div class="bg-white dark:bg-bg-dark shadow-lg rounded-2xl overflow-hidden mb-8">
                    <div class="bg-gradient-to-r from-primary-light to-primary-dark dark:from-primary-dark dark:to-primary-light px-8 py-6">
                        <h1 class="text-3xl md:text-4xl font-bold text-white">{{ $pageTitle }}</h1>
                        <p class="text-white/80 mt-2">Pincode: {{ $pincode->pincode }} | {{ $pincode->name }}, {{ $pincode->district }}, {{ $pincode->state }}</p>
                    </div>

                    <div class="p-8">
                        <!-- Statistics -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-primary-light dark:text-primary-dark">{{ $reviews->total() }}</div>
                                <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Total Reviews</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-accent-light dark:text-accent-dark">
                                    @if($reviews->total() > 0)
                                        {{ number_format($reviews->avg('rating'), 1) }}
                                    @else
                                        0.0
                                    @endif
                                </div>
                                <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Average Rating</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-green-600 dark:text-green-400">{{ $reviews->where('status', 'approved')->count() }}</div>
                                <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Approved Reviews</div>
                            </div>
                        </div>

                        <!-- Reviews List -->
                        <div class="space-y-6" id="reviewsList">
                            @if($reviews->count() > 0)
                                @foreach($reviews as $review)
                                    @include('partials.review-item', ['review' => $review])
                                @endforeach
                            @else
                                <div class="text-center py-12">
                                    <svg class="mx-auto h-12 w-12 text-text-secondary-light dark:text-text-secondary-dark" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                    </svg>
                                    <h3 class="mt-4 text-lg font-medium text-text-primary-light dark:text-text-primary-dark">No reviews yet</h3>
                                    <p class="mt-2 text-text-secondary-light dark:text-text-secondary-dark">Be the first to review this pincode!</p>
                                </div>
                            @endif
                        </div>

                        <!-- Load More Button -->
                        @if($reviews->hasMorePages())
                            <div class="text-center mt-8">
                                <button id="loadMoreBtn"
                                        data-next-page="{{ $reviews->nextPageUrl() }}"
                                        class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-light dark:bg-primary-dark hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark transition-colors duration-200">
                                    <span id="loadMoreText">Load More Reviews</span>
                                    <svg id="loadMoreSpinner" class="hidden animate-spin ml-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </button>
                            </div>
                        @endif
                    </div>
                </div>

                @include('pincodes.partials.share')

                <div class="mt-4 mb-3 p-3 bg-light dark:bg-bg-dark rounded-lg">
                    <p class="text-text-primary-light dark:text-text-primary-dark">Click following link to submit your Feedback:
                        <a href="https://form.jotform.com/241740272022444" target="_blank"
                           class="inline-flex items-center px-3 py-1 text-sm font-medium text-white bg-primary-light dark:bg-primary-dark rounded hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors duration-200">
                            Feedback Form
                        </a>
                    </p>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-4">
                @include('pincodes.partials.sidebar')
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    const loadMoreText = document.getElementById('loadMoreText');
    const loadMoreSpinner = document.getElementById('loadMoreSpinner');
    const reviewsList = document.getElementById('reviewsList');

    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            const nextPageUrl = this.dataset.nextPage;

            if (!nextPageUrl) return;

            // Show loading state
            loadMoreText.textContent = 'Loading...';
            loadMoreSpinner.classList.remove('hidden');
            this.disabled = true;

            fetch(nextPageUrl, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.html) {
                    // Append new reviews
                    reviewsList.insertAdjacentHTML('beforeend', data.html);

                    // Update next page URL
                    if (data.has_more_pages && data.next_page_url) {
                        this.dataset.nextPage = data.next_page_url;
                        loadMoreText.textContent = 'Load More Reviews';
                        this.disabled = false;
                    } else {
                        // No more pages, hide button
                        this.style.display = 'none';
                    }
                }
            })
            .catch(error => {
                console.error('Error loading more reviews:', error);
                loadMoreText.textContent = 'Error loading reviews';
                setTimeout(() => {
                    loadMoreText.textContent = 'Load More Reviews';
                    this.disabled = false;
                }, 3000);
            })
            .finally(() => {
                loadMoreSpinner.classList.add('hidden');
            });
        });
    }

    // Ad loading functionality
    const adContainers = document.querySelectorAll('.lazy-ad');
    adContainers.forEach(container => {
        loadAd(container);
    });

    const loadAd = (element) => {
        const position = element.dataset.adPosition;
        const device = element.dataset.adDevice;

        fetch(`/api/ads/${position}/${device}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text();
            })
            .then(adCode => {
                element.innerHTML = adCode;

                // Execute any scripts in the ad code
                const scripts = element.getElementsByTagName('script');
                Array.from(scripts).forEach(script => {
                    const newScript = document.createElement('script');
                    Array.from(script.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value));
                    newScript.appendChild(document.createTextNode(script.innerHTML));
                    script.parentNode.replaceChild(newScript, script);
                });
            })
            .catch(error => {
                console.error('Error loading ad:', error);
                element.innerHTML = 'Error loading ad';
            });
    };
});
</script>
@endpush