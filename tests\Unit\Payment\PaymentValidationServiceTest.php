<?php

namespace Tests\Unit\Payment;

use App\Models\PaymentGateway;
use App\Models\Plan;
use App\Services\Payment\PaymentValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PaymentValidationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected PaymentValidationService $service;
    protected PaymentGateway $gateway;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new PaymentValidationService();
        
        $this->gateway = PaymentGateway::factory()->create([
            'name' => 'razorpay',
            'supported_currencies' => ['USD', 'EUR', 'INR']
        ]);
    }

    public function test_validates_valid_payment_amount()
    {
        $result = $this->service->validatePaymentAmount(100.00, 'USD', $this->gateway);

        $this->assertTrue($result['valid']);
        $this->assertArrayHasKey('formatted_amount', $result);
        $this->assertArrayHasKey('currency_info', $result);
        $this->assertEquals('100.00', $result['formatted_amount']);
    }

    public function test_rejects_unsupported_currency()
    {
        $result = $this->service->validatePaymentAmount(100.00, 'JPY', $this->gateway);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Unsupported currency', $result['error']);
    }

    public function test_rejects_currency_not_supported_by_gateway()
    {
        $gatewayWithLimitedCurrencies = PaymentGateway::factory()->paypal()->create([
            'supported_currencies' => ['USD']
        ]);

        $result = $this->service->validatePaymentAmount(100.00, 'EUR', $gatewayWithLimitedCurrencies);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Gateway does not support this currency', $result['error']);
    }

    public function test_rejects_amount_below_minimum()
    {
        $result = $this->service->validatePaymentAmount(0.25, 'USD', $this->gateway);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Amount below minimum allowed', $result['error']);
        $this->assertEquals(0.25, $result['details']['amount']);
        $this->assertEquals(0.50, $result['details']['min_amount']);
    }

    public function test_rejects_amount_above_maximum()
    {
        $result = $this->service->validatePaymentAmount(15000.00, 'USD', $this->gateway);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Amount exceeds maximum allowed', $result['error']);
        $this->assertEquals(15000.00, $result['details']['amount']);
        $this->assertEquals(10000.00, $result['details']['max_amount']);
    }

    public function test_rejects_too_many_decimal_places()
    {
        $result = $this->service->validatePaymentAmount(100.123, 'USD', $this->gateway);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Too many decimal places', $result['error']);
    }

    public function test_validates_different_currency_limits()
    {
        // Test INR minimum (1.00)
        $result = $this->service->validatePaymentAmount(0.50, 'INR', $this->gateway);
        $this->assertFalse($result['valid']);

        $result = $this->service->validatePaymentAmount(1.00, 'INR', $this->gateway);
        $this->assertTrue($result['valid']);

        // Test INR maximum (500,000)
        $result = $this->service->validatePaymentAmount(600000.00, 'INR', $this->gateway);
        $this->assertFalse($result['valid']);
    }

    public function test_validates_plan_payment_matching_amount()
    {
        $plan = Plan::factory()->create([
            'price' => 99.99,
            'currency' => 'USD'
        ]);

        $result = $this->service->validatePlanPayment($plan, 99.99, 'USD');

        $this->assertTrue($result['valid']);
        $this->assertEquals(99.99, $result['plan_price']);
        $this->assertEquals(99.99, $result['payment_amount']);
    }

    public function test_rejects_plan_payment_amount_mismatch()
    {
        $plan = Plan::factory()->create([
            'price' => 99.99,
            'currency' => 'USD'
        ]);

        $result = $this->service->validatePlanPayment($plan, 89.99, 'USD');

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Payment amount does not match plan price', $result['error']);
        $this->assertEquals(99.99, $result['details']['expected_amount']);
        $this->assertEquals(89.99, $result['details']['provided_amount']);
    }

    public function test_allows_small_tolerance_for_plan_payment()
    {
        $plan = Plan::factory()->create([
            'price' => 99.99,
            'currency' => 'USD'
        ]);

        // Test with small difference within tolerance
        $result = $this->service->validatePlanPayment($plan, 100.00, 'USD');

        $this->assertTrue($result['valid']);
    }

    public function test_validates_payment_request_data()
    {
        $validData = [
            'amount' => 100.00,
            'currency' => 'USD',
            'gateway_id' => 1,
            'plan_id' => 1
        ];

        $result = $this->service->validatePaymentRequest($validData);

        $this->assertTrue($result['valid']);
    }

    public function test_rejects_payment_request_missing_fields()
    {
        $invalidData = [
            'amount' => 100.00,
            // Missing required fields
        ];

        $result = $this->service->validatePaymentRequest($invalidData);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Missing required fields', $result['error']);
        $this->assertArrayHasKey('currency', $result['details']);
        $this->assertArrayHasKey('gateway_id', $result['details']);
    }

    public function test_rejects_invalid_amount_in_request()
    {
        $invalidData = [
            'amount' => -50.00,
            'currency' => 'USD',
            'gateway_id' => 1,
            'plan_id' => 1
        ];

        $result = $this->service->validatePaymentRequest($invalidData);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Amount must be a positive number', $result['details']['amount']);
    }

    public function test_rejects_invalid_currency_format()
    {
        $invalidData = [
            'amount' => 100.00,
            'currency' => 'INVALID',
            'gateway_id' => 1,
            'plan_id' => 1
        ];

        $result = $this->service->validatePaymentRequest($invalidData);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Currency must be a valid 3-letter ISO code', $result['details']['currency']);
    }

    public function test_validates_refund_amount()
    {
        $result = $this->service->validateRefundAmount(50.00, 100.00, 'USD');

        $this->assertTrue($result['valid']);
        $this->assertEquals('50.00', $result['refund_amount']);
        $this->assertEquals('100.00', $result['original_amount']);
    }

    public function test_rejects_refund_amount_exceeding_original()
    {
        $result = $this->service->validateRefundAmount(150.00, 100.00, 'USD');

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Refund amount cannot exceed original payment amount', $result['error']);
        $this->assertEquals(150.00, $result['details']['refund_amount']);
        $this->assertEquals(100.00, $result['details']['original_amount']);
    }

    public function test_rejects_negative_refund_amount()
    {
        $result = $this->service->validateRefundAmount(-50.00, 100.00, 'USD');

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Refund amount must be positive', $result['error']);
    }

    public function test_gets_supported_currencies()
    {
        $currencies = $this->service->getSupportedCurrencies();

        $this->assertIsArray($currencies);
        $this->assertArrayHasKey('USD', $currencies);
        $this->assertArrayHasKey('EUR', $currencies);
        $this->assertArrayHasKey('INR', $currencies);
        $this->assertArrayHasKey('GBP', $currencies);
    }

    public function test_gets_currency_info()
    {
        $info = $this->service->getCurrencyInfo('USD');

        $this->assertIsArray($info);
        $this->assertEquals('$', $info['symbol']);
        $this->assertEquals(2, $info['decimal_places']);
        $this->assertEquals(0.50, $info['min_amount']);
        $this->assertEquals(10000.00, $info['max_amount']);
    }

    public function test_returns_null_for_unknown_currency_info()
    {
        $info = $this->service->getCurrencyInfo('UNKNOWN');

        $this->assertNull($info);
    }

    public function test_validates_callback_url_in_request()
    {
        $validData = [
            'amount' => 100.00,
            'currency' => 'USD',
            'gateway_id' => 1,
            'plan_id' => 1,
            'callback_url' => 'https://example.com/callback'
        ];

        $result = $this->service->validatePaymentRequest($validData);

        $this->assertTrue($result['valid']);
    }

    public function test_rejects_invalid_callback_url()
    {
        $invalidData = [
            'amount' => 100.00,
            'currency' => 'USD',
            'gateway_id' => 1,
            'plan_id' => 1,
            'callback_url' => 'not-a-valid-url'
        ];

        $result = $this->service->validatePaymentRequest($invalidData);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Callback URL must be a valid URL', $result['details']['callback_url']);
    }

    public function test_handles_plan_currency_conversion()
    {
        $plan = Plan::factory()->create([
            'price' => 100.00,
            'currency' => 'USD'
        ]);

        // Test conversion to EUR (mocked conversion rate)
        $result = $this->service->validatePlanPayment($plan, 85.00, 'EUR');

        $this->assertTrue($result['valid']);
        $this->assertEquals(85.00, $result['plan_price']);
        $this->assertEquals(85.00, $result['payment_amount']);
    }

    public function test_handles_plan_without_currency()
    {
        $plan = Plan::factory()->create([
            'price' => 100.00,
            'currency' => null // No currency set, should default to USD
        ]);

        $result = $this->service->validatePlanPayment($plan, 100.00, 'USD');

        $this->assertTrue($result['valid']);
    }

    public function test_handles_validation_exceptions()
    {
        // Test with invalid gateway (should handle gracefully)
        $invalidGateway = new PaymentGateway();
        $invalidGateway->supported_currencies = null;

        $result = $this->service->validatePaymentAmount(100.00, 'USD', $invalidGateway);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Gateway does not support this currency', $result['error']);
    }

    public function test_formats_amounts_correctly()
    {
        $result = $this->service->validatePaymentAmount(1234.5, 'USD', $this->gateway);

        $this->assertTrue($result['valid']);
        $this->assertEquals('1234.50', $result['formatted_amount']);
    }

    public function test_validates_numeric_ids_in_request()
    {
        $invalidData = [
            'amount' => 100.00,
            'currency' => 'USD',
            'gateway_id' => 'not-numeric',
            'plan_id' => 1
        ];

        $result = $this->service->validatePaymentRequest($invalidData);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Gateway ID must be numeric', $result['details']['gateway_id']);
    }
}