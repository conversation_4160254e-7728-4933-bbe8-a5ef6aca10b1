{{-- resources/views/includes/adsense.blade.php --}}

@php
    // Default values for parameters
    $adSlot = $adSlot ?? 'default';
    $format = $format ?? 'auto';
    $width = $width ?? 0;
    $height = $height ?? 0;

    // Debug information
    if (config('app.debug')) {
        error_log('Ad Banner Debug:');
        error_log('- Ad Slot: ' . $adSlot);
        error_log('- Format: ' . $format);
        error_log('- Width: ' . $width);
        error_log('- Height: ' . $height);
    }
@endphp

<div class="adsense-container my-4">
    {{-- Debug information visible in HTML source --}}
    @if(config('app.debug'))
    <!-- 
    Ad Banner Debug Info:
    - Slot: {{ $adSlot }}
    - Format: {{ $format }}
    - Width: {{ $width }}
    - Height: {{ $height }}
    - Timestamp: {{ now() }}
    -->
    @endif

    {{-- This is a dummy AdSense code for demonstration purposes only --}}
    <div 
        class="adsbygoogle"
        style="display:block;{{ $width ? "width:{$width}px;" : '' }}{{ $height ? "height:{$height}px;" : '' }}"
        data-ad-client="ca-pub-1234567890123456"
        data-ad-slot="{{ $adSlot }}"
        data-ad-format="{{ $format }}"
        @if($format === 'auto')
            data-full-width-responsive="true"
        @endif
    >
    </div>
    <script>
        // console.log('Initializing ad:', {
        //     slot: '{{ $adSlot }}',
        //     format: '{{ $format }}',
        //     width: {{ $width }},
        //     height: {{ $height }},
        //     timestamp: '{{ now() }}'
        // });
        (adsbygoogle = window.adsbygoogle || []).push({});
    </script>
</div>