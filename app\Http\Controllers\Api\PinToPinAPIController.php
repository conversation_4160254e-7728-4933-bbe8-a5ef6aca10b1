<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\PinCode;
use Illuminate\Support\Facades\Validator;
use App\Traits\TracksApiRequests;

class PinToPinAPIController extends Controller
{
    use TracksApiRequests;

    public function validatePincode(Request $request, $pincode)
    {
        $isValid = preg_match('/^\d{6}$/', $pincode) && PinCode::where('pincode', $pincode)->exists();

        $response = response()->json([
            'data' => [
                'is_valid' => $isValid,
                'pincode' => $pincode,
            ],
        ]);

        $this->trackApiRequest($request, $response);
        return $response;
    }

    public function calculateDistanceBetweenTwoPincodes(Request $request, $pincode1, $pincode2)
    {
        $from = PinCode::where('pincode', $pincode1)->first();
        $to = PinCode::where('pincode', $pincode2)->first();

        if (!$from || !$to) {
            $response = response()->json(['error' => 'One or both pincodes are invalid'], 422);
        } else {
            $distance = $this->haversineDistance($from->latitude, $from->longitude, $to->latitude, $to->longitude);

            $response = response()->json([
                'data' => [
                    'distance' => round($distance, 2),
                    'unit' => 'km',
                ],
            ]);
        }

        $this->trackApiRequest($request, $response);
        return $response;
    }

    private function haversineDistance($lat1, $lon1, $lat2, $lon2)
    {
        $lat1 = deg2rad($lat1);
        $lon1 = deg2rad($lon1);
        $lat2 = deg2rad($lat2);
        $lon2 = deg2rad($lon2);

        $dlat = $lat2 - $lat1;
        $dlon = $lon2 - $lon1;

        $a = sin($dlat / 2) ** 2 + cos($lat1) * cos($lat2) * sin($dlon / 2) ** 2;
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        $radius = 6371; // Earth's radius in kilometers
        return $radius * $c;
    }

    private function calculateBearing($lat1, $lon1, $lat2, $lon2)
    {
        $lat1 = deg2rad($lat1);
        $lon1 = deg2rad($lon1);
        $lat2 = deg2rad($lat2);
        $lon2 = deg2rad($lon2);

        $y = sin($lon2 - $lon1) * cos($lat2);
        $x = cos($lat1) * sin($lat2) - sin($lat1) * cos($lat2) * cos($lon2 - $lon1);
        $bearing = atan2($y, $x);

        return fmod((rad2deg($bearing) + 360), 360);
    }

    private function calculateMidpoint($lat1, $lon1, $lat2, $lon2)
    {
        $lat1 = deg2rad($lat1);
        $lon1 = deg2rad($lon1);
        $lat2 = deg2rad($lat2);
        $lon2 = deg2rad($lon2);

        $bx = cos($lat2) * cos($lon2 - $lon1);
        $by = cos($lat2) * sin($lon2 - $lon1);

        $midLat = atan2(sin($lat1) + sin($lat2), sqrt((cos($lat1) + $bx) * (cos($lat1) + $bx) + $by * $by));
        $midLon = $lon1 + atan2($by, cos($lat1) + $bx);

        return [
            'latitude' => round(rad2deg($midLat), 6),
            'longitude' => round(rad2deg($midLon), 6),
        ];
    }
}