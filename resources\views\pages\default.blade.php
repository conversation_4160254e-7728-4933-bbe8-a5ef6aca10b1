@extends('layouts.app')

@section('meta_title', $page->meta_title ?? $page->title)
@section('meta_description', $page->meta_description)
@section('meta_keywords', $page->meta_keywords)

@section('content')
<div class="bg-bg-light dark:bg-bg-dark min-h-screen">
    {{-- Main Content --}}
    <div class="container mx-auto px-4 py-8 lg:py-12">
        <div class="max-w-4xl mx-auto">
            {{-- Page Header --}}
            <header class="mb-8 lg:mb-12">
                @if($page->featured_image)
                    <div class="mb-6 rounded-lg overflow-hidden shadow-lg">
                        <img src="{{ asset($page->featured_image) }}" 
                             alt="{{ $page->title }}" 
                             class="w-full h-64 md:h-80 object-cover">
                    </div>
                @endif

                <div class="text-center md:text-left">
                    <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary-light dark:text-text-primary-dark mb-4 leading-tight">
                        {{ $page->title }}
                    </h1>
                </div>
            </header>

            {{-- Page Content --}}
            <article class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-border-light dark:border-border-dark overflow-hidden">
                <div class="p-6 md:p-8 lg:p-10">
                    <div class="prose prose-lg max-w-none dark:prose-invert
                               prose-headings:text-text-primary-light dark:prose-headings:text-text-primary-dark
                               prose-p:text-text-primary-light dark:prose-p:text-text-primary-dark
                               prose-strong:text-text-primary-light dark:prose-strong:text-text-primary-dark
                               prose-a:text-primary-light dark:prose-a:text-primary-dark
                               prose-a:no-underline hover:prose-a:underline
                               prose-blockquote:text-text-secondary-light dark:prose-blockquote:text-text-secondary-dark
                               prose-blockquote:border-l-primary-light dark:prose-blockquote:border-l-primary-dark
                               prose-code:text-accent-light dark:prose-code:text-accent-dark
                               prose-code:bg-bg-light dark:prose-code:bg-bg-dark
                               prose-pre:bg-bg-light dark:prose-pre:bg-bg-dark
                               prose-th:text-text-primary-light dark:prose-th:text-text-primary-dark
                               prose-td:text-text-primary-light dark:prose-td:text-text-primary-dark
                               prose-hr:border-border-light dark:prose-hr:border-border-dark">
                        {!! $page->content !!}
                    </div>
                </div>
            </article>

          

            {{-- Social Share Section --}}
            <div class="mt-8 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md border border-border-light dark:border-border-dark">
                <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Share this page</h3>
                <div class="flex flex-wrap gap-3">
                    <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->url()) }}&text={{ urlencode($page->title) }}" 
                       target="_blank" rel="noopener"
                       class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                        </svg>
                        Twitter
                    </a>
                    
                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->url()) }}" 
                       target="_blank" rel="noopener"
                       class="inline-flex items-center px-4 py-2 bg-blue-700 hover:bg-blue-800 text-white rounded-md transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                        Facebook
                    </a>
                    
                    <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(request()->url()) }}" 
                       target="_blank" rel="noopener"
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                        LinkedIn
                    </a>
                    
                    <button onclick="copyToClipboard('{{ request()->url() }}')" 
                            class="inline-flex items-center px-4 py-2 bg-text-secondary-light dark:bg-text-secondary-dark hover:bg-text-primary-light dark:hover:bg-text-primary-dark text-white rounded-md transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z"></path>
                            <path d="M3 5a2 2 0 012-2 3 3 0 003 3h6a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L10.414 13H15v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5zM15 11.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 00-1.414 1.414L15 8.414V11.586z"></path>
                        </svg>
                        Copy Link
                    </button>
                </div>
            </div>
           </div>
    </div>
</div>
@endsection

{{-- Print Styles --}}
@push('styles')
<style>
    @media print {
        .no-print { display: none !important; }
        body { font-size: 12pt; line-height: 1.4; }
        h1 { font-size: 18pt; }
        h2 { font-size: 16pt; }
        h3 { font-size: 14pt; }
    }
</style>
@endpush

{{-- JavaScript for Copy Link Functionality --}}
@push('scripts')
<script>
    function copyToClipboard(text) {
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(() => {
                showToast('Link copied to clipboard!', 'success');
            }).catch(() => {
                fallbackCopyTextToClipboard(text);
            });
        } else {
            fallbackCopyTextToClipboard(text);
        }
    }

    function fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            showToast('Link copied to clipboard!', 'success');
        } catch (err) {
            showToast('Failed to copy link', 'error');
        }
        
        textArea.remove();
    }

    function showToast(message, type = 'info') {
        // Simple toast notification (you can replace this with your preferred toast library)
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 px-6 py-3 rounded-md text-white z-50 transition-opacity duration-300 ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 'bg-blue-500'
        }`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
</script>
@endpush
