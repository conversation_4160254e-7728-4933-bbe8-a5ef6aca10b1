<?php

use App\Models\Card;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

// Tests for getCardData endpoint
test('getCardData returns all cards with correct structure', function () {
    $user = User::factory()->create();
    $card1 = Card::factory()->create([
        'title' => 'Test Card 1',
        'line2' => 'Line 2 Text',
        'line3' => 'Line 3 Text',
        'imageUrl' => 'https://example.com/image1.jpg',
        'link' => 'https://example.com/link1',
        'linkText' => 'Click Here',
    ]);
    $card2 = Card::factory()->create([
        'title' => 'Test Card 2',
        'line2' => 'Line 2 Text 2',
        'line3' => 'Line 3 Text 2',
        'imageUrl' => 'https://example.com/image2.jpg',
        'link' => 'https://example.com/link2',
        'linkText' => 'Learn More',
    ]);

    $response = $this->actingAs($user)->getJson('/api/getCardData');
    
    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'title',
                    'line2',
                    'line3',
                    'imageUrl',
                    'link',
                    'linkText',
                ]
            ]
        ])
        ->assertJsonCount(2, 'data')
        ->assertJsonFragment([
            'title' => 'Test Card 1',
            'line2' => 'Line 2 Text',
            'line3' => 'Line 3 Text',
            'imageUrl' => 'https://example.com/image1.jpg',
            'link' => 'https://example.com/link1',
            'linkText' => 'Click Here',
        ])
        ->assertJsonFragment([
            'title' => 'Test Card 2',
            'line2' => 'Line 2 Text 2',
            'line3' => 'Line 3 Text 2',
            'imageUrl' => 'https://example.com/image2.jpg',
            'link' => 'https://example.com/link2',
            'linkText' => 'Learn More',
        ]);
});

test('getCardData returns empty array when no cards exist', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->getJson('/api/getCardData');
    
    $response->assertStatus(200)
        ->assertJsonStructure(['data'])
        ->assertJsonCount(0, 'data')
        ->assertJson(['data' => []]);
});

// Tests for show endpoint
test('show returns specific card details', function () {
    $user = User::factory()->create();
    $card = Card::factory()->create([
        'title' => 'Specific Card',
        'line2' => 'Specific Line 2',
        'line3' => 'Specific Line 3',
        'imageUrl' => 'https://example.com/specific.jpg',
        'link' => 'https://example.com/specific-link',
        'linkText' => 'Specific Link Text',
    ]);

    $response = $this->actingAs($user)->getJson("/api/cards/{$card->id}");
    
    $response->assertStatus(200)
        ->assertJson([
            'id' => $card->id,
            'title' => 'Specific Card',
            'line2' => 'Specific Line 2',
            'line3' => 'Specific Line 3',
            'imageUrl' => 'https://example.com/specific.jpg',
            'link' => 'https://example.com/specific-link',
            'linkText' => 'Specific Link Text',
        ]);
});

test('show returns 404 for non-existent card', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->getJson('/api/cards/999999');
    
    $response->assertStatus(404);
});

test('getCardData only selects required fields', function () {
    $user = User::factory()->create();
    $card = Card::factory()->create([
        'title' => 'Test Card',
        'line2' => 'Line 2',
        'line3' => 'Line 3',
        'imageUrl' => 'https://example.com/image.jpg',
        'link' => 'https://example.com/link',
        'linkText' => 'Link Text',
    ]);

    $response = $this->actingAs($user)->getJson('/api/getCardData');
    
    $response->assertStatus(200);
    $cardData = $response->json('data.0');
    
    // Verify only the required fields are present
    expect($cardData)->toHaveKeys([
        'id', 'title', 'line2', 'line3', 'imageUrl', 'link', 'linkText'
    ]);
    
    // Verify the card data matches what we created
    expect($cardData['title'])->toBe('Test Card');
    expect($cardData['line2'])->toBe('Line 2');
    expect($cardData['line3'])->toBe('Line 3');
    expect($cardData['imageUrl'])->toBe('https://example.com/image.jpg');
    expect($cardData['link'])->toBe('https://example.com/link');
    expect($cardData['linkText'])->toBe('Link Text');
});

test('getCardData works without authentication', function () {
    $card = Card::factory()->create([
        'title' => 'Public Card',
        'line2' => 'Public Line 2',
        'line3' => 'Public Line 3',
        'imageUrl' => 'https://example.com/public.jpg',
        'link' => 'https://example.com/public-link',
        'linkText' => 'Public Link',
    ]);

    $response = $this->getJson('/api/getCardData');
    
    $response->assertStatus(200)
        ->assertJsonStructure(['data'])
        ->assertJsonFragment([
            'title' => 'Public Card',
            'line2' => 'Public Line 2',
            'line3' => 'Public Line 3',
            'imageUrl' => 'https://example.com/public.jpg',
            'link' => 'https://example.com/public-link',
            'linkText' => 'Public Link',
        ]);
}); 