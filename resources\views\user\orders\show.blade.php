@extends('layouts.user')

@section('title', 'Order Details')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <a href="{{ route('orders.index') }}" class="text-blue-500 hover:text-blue-700">
            &larr; Back to Orders
        </a>
    </div>

    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h1 class="text-2xl font-bold text-gray-800">Order #{{ $order->order_number }}</h1>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h2 class="text-lg font-semibold text-gray-700 mb-4">Order Information</h2>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Status:</span>
                            <span class="font-medium px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {{ $order->status === 'completed' ? 'bg-green-100 text-green-800' : 
                                   ($order->status === 'cancelled' ? 'bg-red-100 text-red-800' : 
                                   ($order->status === 'failed' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800')) }}">
                                {{ ucfirst($order->status) }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Date:</span>
                            <span class="font-medium">{{ $order->created_at->format('M d, Y H:i') }}</span>
                        </div>
                        @if($order->paid_at)
                        <div class="flex justify-between">
                            <span class="text-gray-600">Paid at:</span>
                            <span class="font-medium">{{ $order->paid_at->format('M d, Y H:i') }}</span>
                        </div>
                        @endif
                    </div>
                </div>

                <div>
                    <h2 class="text-lg font-semibold text-gray-700 mb-4">Plan Details</h2>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Plan:</span>
                            <span class="font-medium">{{ $order->plan->name ?? 'N/A' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Amount:</span>
                            <span class="font-medium">₹{{ number_format($order->amount, 2) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Request Limit:</span>
                            <span class="font-medium">{{ number_format($order->request_limit) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            @if($order->notes)
            <div class="mt-6">
                <h2 class="text-lg font-semibold text-gray-700 mb-2">Notes</h2>
                <p class="text-gray-600 bg-gray-50 p-4 rounded">{{ $order->notes }}</p>
            </div>
            @endif

            <div class="mt-8 flex justify-between">
                @if($order->isPending())
                    <div class="flex space-x-4">
                        <a href="{{ route('payment.process', $order) }}" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                            Complete Payment
                        </a>
                        
                        <form action="{{ route('orders.cancel', $order) }}" method="POST">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded" 
                                    onclick="return confirm('Are you sure you want to cancel this order?')">
                                Cancel Order
                            </button>
                        </form>
                    </div>
                @elseif($order->isCompleted())
                    <div>
                        <span class="bg-green-100 text-green-800 px-4 py-2 rounded">
                            Payment Completed
                        </span>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection