<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CourierDict;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class CourierDictController extends Controller
{
    public function index()
    {
        $terms = CourierDict::latest()->paginate(10);
        return view('admin.courier-dict.index', compact('terms'));
    }

    public function create()
    {
        return view('admin.courier-dict.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'term' => 'required|max:255|unique:courier_dict',
            'description' => 'required',
            'long_description' => 'required',
            'tag' => 'required|max:50'
        ]);

        CourierDict::create($request->all());

        return redirect()->route('admin.courier-dict.index')
            ->with('success', 'Term created successfully.');
    }

    public function edit(CourierDict $courierDict)
    {
        return view('admin.courier-dict.edit', compact('courierDict'));
    }

    public function update(Request $request, CourierDict $courierDict)
    {
        $request->validate([
            'term' => 'required|max:255|unique:courier_dict,term,' . $courierDict->id,
            'description' => 'required',
            'long_description' => 'required',
            'tag' => 'required|max:50'
        ]);

        $courierDict->update($request->all());

        return redirect()->route('admin.courier-dict.index')
            ->with('success', 'Term updated successfully.');
    }

    public function destroy(CourierDict $courierDict)
    {
        $courierDict->delete();

        return redirect()->route('admin.courier-dict.index')
            ->with('success', 'Term deleted successfully.');
    }

    public function search(Request $request)
    {
        $search = $request->get('search');
        $terms = CourierDict::search($search)->paginate(10);
        return view('admin.courier-dict.index', compact('terms', 'search'));
    }
}