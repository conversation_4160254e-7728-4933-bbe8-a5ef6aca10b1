<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DigipinDecodeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'digipin' => 'required|string|max:12', // 10 chars + 2 hyphens
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'digipin.required' => 'DIGIPIN is required.',
            'digipin.string' => 'DIGIPIN must be a string.',
            'digipin.max' => 'DIGIPIN cannot exceed 12 characters.',
        ];
    }
} 