<?php

use App\Http\Controllers\BreadcrumbController;

test('it can generate breadcrumbs from a simple path', function () {
    $controller = new BreadcrumbController();
    $path = 'about/contact';
    
    $breadcrumbs = $controller->generateBreadcrumb($path);
    
    expect($breadcrumbs)->toBeArray();
    expect($breadcrumbs)->toHaveCount(2);
    expect($breadcrumbs[0]['name'])->toBe('about');
    expect($breadcrumbs[0]['url'])->toBe(url('/about'));
    expect($breadcrumbs[1]['name'])->toBe('contact');
    expect($breadcrumbs[1]['url'])->toBe(url('/about/contact'));
});

test('it can generate breadcrumbs from a path with encoded characters', function () {
    $controller = new BreadcrumbController();
    $path = 'blog/category/web%20development';
    
    $breadcrumbs = $controller->generateBreadcrumb($path);
    
    expect($breadcrumbs)->toBeArray();
    expect($breadcrumbs)->toHaveCount(3);
    expect($breadcrumbs[0]['name'])->toBe('blog');
    expect($breadcrumbs[0]['url'])->toBe(url('/blog'));
    expect($breadcrumbs[1]['name'])->toBe('category');
    expect($breadcrumbs[1]['url'])->toBe(url('/blog/category'));
    expect($breadcrumbs[2]['name'])->toBe('web development');
    expect($breadcrumbs[2]['url'])->toBe(url('/blog/category/web%20development'));
});

test('it can generate breadcrumbs from an empty path', function () {
    $controller = new BreadcrumbController();
    $path = '';
    
    $breadcrumbs = $controller->generateBreadcrumb($path);
    
    expect($breadcrumbs)->toBeArray();
    expect($breadcrumbs)->toHaveCount(1);
    expect($breadcrumbs[0]['name'])->toBe('');
    expect($breadcrumbs[0]['url'])->toBe(url('/'));
});

test('it can generate breadcrumbs from a path with special characters', function () {
    $controller = new BreadcrumbController();
    $path = 'products/electronics/tv-&-audio';
    
    $breadcrumbs = $controller->generateBreadcrumb($path);
    
    expect($breadcrumbs)->toBeArray();
    expect($breadcrumbs)->toHaveCount(3);
    expect($breadcrumbs[0]['name'])->toBe('products');
    expect($breadcrumbs[0]['url'])->toBe(url('/products'));
    expect($breadcrumbs[1]['name'])->toBe('electronics');
    expect($breadcrumbs[1]['url'])->toBe(url('/products/electronics'));
    expect($breadcrumbs[2]['name'])->toBe('tv-&-audio');
    expect($breadcrumbs[2]['url'])->toBe(url('/products/electronics/tv-&-audio'));
});