<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;

class CookieConsentController extends Controller
{
    public function update(Request $request)
    {
        $validated = $request->validate([
            'accepted' => 'required|boolean',
            'level' => 'required|in:all,essential,none'
        ]);

        // Store the user's preference in the database if they're logged in
        if (auth()->check()) {
            auth()->user()->update([
                'cookie_consent' => $validated['accepted'],
                'cookie_consent_level' => $validated['level'],
                'cookie_consent_at' => now(),
            ]);
        }

        // Create response with cookies
        $response = response()->json(['status' => 'success']);

        // Set cookies that expire in 1 year (in minutes)
        $minutes = 60 * 24 * 365;

        // Set the main consent cookie
        $response->cookie(
            'cookie_consent',
            $validated['accepted'] ? 'accepted' : 'declined',
            $minutes
        );

        // Set the consent level cookie
        $response->cookie(
            'cookie_consent_level',
            $validated['level'],
            $minutes
        );

        return $response;
    }
}