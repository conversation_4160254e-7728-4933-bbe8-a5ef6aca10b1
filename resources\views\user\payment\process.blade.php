@extends('layouts.user')

@section('title', 'Process Payment')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="mb-6">
            <a href="{{ route('orders.show', $order) }}" class="text-blue-500 hover:text-blue-700">
                &larr; Back to Order
            </a>
        </div>

        <div class="bg-white rounded-lg shadow-lg overflow-hidden max-w-2xl mx-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <h1 class="text-2xl font-bold text-gray-800">Complete Payment</h1>
            </div>

            <div class="p-6">
                <div class="mb-6">
                    <h2 class="text-lg font-semibold text-gray-700 mb-4">Order Summary</h2>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Order Number:</span>
                            <span class="font-medium">{{ $order->order_number }}</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Plan:</span>
                            <span class="font-medium">{{ $order->plan->name ?? 'N/A' }}</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Amount:</span>
                            <span class="font-medium">₹{{ number_format($order->amount, 2) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Request Limit:</span>
                            <span class="font-medium">{{ number_format($order->request_limit) }}</span>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <h2 class="text-lg font-semibold text-gray-700 mb-4">Payment Method</h2>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="flex items-center mb-4">
                            <input id="paypal" name="payment_method" type="radio" checked
                                class="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                            <label for="paypal" class="ml-3 block text-sm font-medium text-gray-700">
                                <div class="flex items-center">
                                    <span class="mr-2">PayPal</span>
                                    <img src="{{ asset('images/paypal-logo.png') }}" alt="PayPal" class="h-6">
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                {{-- @if (config('app.env') !== 'production')
                    <div class="mt-8 p-6 bg-yellow-50 rounded-lg border border-yellow-200">
                        <h3 class="text-lg font-medium text-yellow-800">Test Payment Option</h3>
                        <p class="mt-2 text-sm text-yellow-600">This option is for testing purposes only.</p>

                        <form action="{{ route('payment.mock') }}" method="POST" class="mt-4">
                            @csrf
                            <input type="hidden" name="order_id" value="{{ $order->id }}">
                            <button type="submit"
                                class="px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-md">
                                Process Mock Payment
                            </button>
                        </form>
                    </div>
                @endif --}}

                <form action="{{ route('payment.create') }}" method="POST">
                    @csrf
                    <input type="hidden" name="order_id" value="{{ $order->id }}">

                    <div class="flex justify-end">
                        <button type="submit"
                            class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded font-medium">
                            Proceed to Payment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
