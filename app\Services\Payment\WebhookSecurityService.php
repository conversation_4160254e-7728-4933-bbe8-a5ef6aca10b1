<?php

namespace App\Services\Payment;

use App\Models\PaymentGateway;
use App\Models\WebhookLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Carbon\Carbon;

class WebhookSecurityService
{
    /**
     * Verify webhook signature for different gateways
     *
     * @param Request $request
     * @param PaymentGateway $gateway
     * @return array
     */
    public function verifyWebhookSignature(Request $request, PaymentGateway $gateway): array
    {
        try {
            // Check if gateway name starts with known gateway types
            if (str_starts_with($gateway->name, 'razorpay')) {
                return $this->verifyRazorpaySignature($request, $gateway);
            } elseif (str_starts_with($gateway->name, 'paypal')) {
                return $this->verifyPayPalSignature($request, $gateway);
            } elseif (str_starts_with($gateway->name, 'stripe')) {
                return $this->verifyStripeSignature($request, $gateway);
            } else {
                return $this->verifyGenericSignature($request, $gateway);
            }
        } catch (\Exception $e) {
            Log::error('Webhook signature verification failed', [
                'gateway' => $gateway->name,
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            return [
                'valid' => false,
                'error' => 'Signature verification failed',
                'details' => $e->getMessage()
            ];
        }
    }

    /**
     * Verify Razorpay webhook signature
     *
     * @param Request $request
     * @param PaymentGateway $gateway
     * @return array
     */
    private function verifyRazorpaySignature(Request $request, PaymentGateway $gateway): array
    {
        // Try to get webhook secret from configuration first, then direct field
        $webhookSecret = $gateway->configuration['webhook_secret'] ?? $gateway->webhook_secret ?? null;

        if (empty($webhookSecret)) {
            return [
                'valid' => false,
                'error' => 'Webhook secret not configured',
                'details' => 'Razorpay webhook secret is required for signature verification'
            ];
        }

        $signature = $request->header('X-Razorpay-Signature');
        if (empty($signature)) {
            $this->recordSecurityEvent('missing_signature', request()->ip() ?? '127.0.0.1', [
                'gateway' => $gateway->name
            ]);
            
            return [
                'valid' => false,
                'error' => 'Missing signature header',
                'details' => 'X-Razorpay-Signature header is required'
            ];
        }

        $payload = $request->getContent();
        $expectedSignature = hash_hmac('sha256', $payload, $webhookSecret);



        if (!hash_equals($expectedSignature, $signature)) {
            $this->recordSecurityEvent('invalid_signature', $request->ip() ?? '127.0.0.1', [
                'gateway' => $gateway->name,
                'expected' => $expectedSignature,
                'received' => $signature
            ]);
            
            Log::warning('Webhook security violation', [
                'type' => 'invalid_signature',
                'gateway' => $gateway->name,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);
            
            return [
                'valid' => false,
                'error' => 'Invalid signature',
                'details' => 'Signature verification failed'
            ];
        }

        return [
            'valid' => true,
            'gateway' => 'razorpay',
            'event_id' => $request->input('event.id'),
            'event_type' => $request->input('event.event')
        ];
    }

    /**
     * Verify PayPal webhook signature
     *
     * @param Request $request
     * @param PaymentGateway $gateway
     * @return array
     */
    private function verifyPayPalSignature(Request $request, PaymentGateway $gateway): array
    {
        // PayPal uses certificate-based verification
        $certId = $request->header('PAYPAL-CERT-ID');
        $signature = $request->header('PAYPAL-TRANSMISSION-SIG');
        $timestamp = $request->header('PAYPAL-TRANSMISSION-TIME');
        $authAlgo = $request->header('PAYPAL-AUTH-ALGO');

        if (empty($certId) || empty($signature) || empty($timestamp)) {
            return [
                'valid' => false,
                'error' => 'Missing PayPal headers',
                'details' => 'Required PayPal webhook headers are missing'
            ];
        }

        // For production, you would verify against PayPal's certificate
        // This is a simplified implementation
        $webhookId = $gateway->getConfigValue('webhook_id');
        if (empty($webhookId)) {
            return [
                'valid' => false,
                'error' => 'Webhook ID not configured',
                'details' => 'PayPal webhook ID is required for verification'
            ];
        }

        // In a real implementation, you would:
        // 1. Download PayPal's certificate using the cert ID
        // 2. Verify the signature using the certificate
        // 3. Check timestamp to prevent replay attacks

        return [
            'valid' => true,
            'gateway' => 'paypal',
            'event_id' => $request->input('id'),
            'event_type' => $request->input('event_type')
        ];
    }

    /**
     * Verify Stripe webhook signature
     *
     * @param Request $request
     * @param PaymentGateway $gateway
     * @return array
     */
    private function verifyStripeSignature(Request $request, PaymentGateway $gateway): array
    {
        $webhookSecret = $gateway->webhook_secret;
        if (empty($webhookSecret)) {
            return [
                'valid' => false,
                'error' => 'Webhook secret not configured',
                'details' => 'Stripe webhook secret is required for signature verification'
            ];
        }

        $signature = $request->header('Stripe-Signature');
        if (empty($signature)) {
            return [
                'valid' => false,
                'error' => 'Missing signature header',
                'details' => 'Stripe-Signature header is required'
            ];
        }

        $payload = $request->getContent();
        $timestamp = null;
        $signatures = [];

        // Parse Stripe signature header
        foreach (explode(',', $signature) as $element) {
            [$key, $value] = explode('=', $element, 2);
            if ($key === 't') {
                $timestamp = $value;
            } elseif ($key === 'v1') {
                $signatures[] = $value;
            }
        }

        if (empty($timestamp) || empty($signatures)) {
            return [
                'valid' => false,
                'error' => 'Invalid signature format',
                'details' => 'Stripe signature header format is invalid'
            ];
        }

        // Check timestamp to prevent replay attacks (5 minutes tolerance)
        if (abs(time() - $timestamp) > 300) {
            return [
                'valid' => false,
                'error' => 'Timestamp too old',
                'details' => 'Webhook timestamp is outside acceptable range'
            ];
        }

        $expectedSignature = hash_hmac('sha256', $timestamp . '.' . $payload, $webhookSecret);

        $validSignature = false;
        foreach ($signatures as $sig) {
            if (hash_equals($expectedSignature, $sig)) {
                $validSignature = true;
                break;
            }
        }

        if (!$validSignature) {
            return [
                'valid' => false,
                'error' => 'Invalid signature',
                'details' => 'Signature verification failed'
            ];
        }

        return [
            'valid' => true,
            'gateway' => 'stripe',
            'event_id' => $request->input('id'),
            'event_type' => $request->input('type')
        ];
    }

    /**
     * Verify generic webhook signature
     *
     * @param Request $request
     * @param PaymentGateway $gateway
     * @return array
     */
    private function verifyGenericSignature(Request $request, PaymentGateway $gateway): array
    {
        $webhookSecret = $gateway->webhook_secret;
        if (empty($webhookSecret)) {
            // If no secret is configured, we can't verify but we'll allow it
            return [
                'valid' => true,
                'gateway' => $gateway->name,
                'warning' => 'No webhook secret configured - signature verification skipped'
            ];
        }

        // Try common signature headers
        $signature = $request->header('X-Signature') 
                  ?? $request->header('X-Hub-Signature-256')
                  ?? $request->header('Signature');

        if (empty($signature)) {
            return [
                'valid' => false,
                'error' => 'Missing signature header',
                'details' => 'No signature header found'
            ];
        }

        $payload = $request->getContent();
        $expectedSignature = hash_hmac('sha256', $payload, $webhookSecret);

        // Handle different signature formats
        if (str_starts_with($signature, 'sha256=')) {
            $signature = substr($signature, 7);
        }

        if (!hash_equals($expectedSignature, $signature)) {
            return [
                'valid' => false,
                'error' => 'Invalid signature',
                'details' => 'Signature verification failed'
            ];
        }

        return [
            'valid' => true,
            'gateway' => $gateway->name
        ];
    }

    /**
     * Apply rate limiting to webhook endpoints
     *
     * @param Request $request
     * @param PaymentGateway $gateway
     * @return array
     */
    public function checkRateLimit(Request $request, PaymentGateway $gateway): array
    {
        $ip = $request->ip();
        $gatewayName = $gateway->name;
        
        // Different rate limits for different scenarios
        $limits = [
            'per_ip_per_minute' => 60,      // 60 requests per minute per IP
            'per_ip_per_hour' => 1000,      // 1000 requests per hour per IP
            'per_gateway_per_minute' => 500, // 500 requests per minute per gateway
            'burst_limit' => 10             // 10 requests in 10 seconds (burst protection)
        ];

        $checks = [
            "webhook_ip_{$ip}_minute" => [$limits['per_ip_per_minute'], 60],
            "webhook_ip_{$ip}_hour" => [$limits['per_ip_per_hour'], 3600],
            "webhook_gateway_{$gatewayName}_minute" => [$limits['per_gateway_per_minute'], 60],
            "webhook_burst_{$ip}" => [$limits['burst_limit'], 10]
        ];

        foreach ($checks as $key => [$limit, $duration]) {
            if (RateLimiter::tooManyAttempts($key, $limit)) {
                $retryAfter = RateLimiter::availableIn($key);
                
                Log::warning('Webhook rate limit exceeded', [
                    'ip' => $ip,
                    'gateway' => $gatewayName,
                    'limit_type' => $key,
                    'retry_after' => $retryAfter
                ]);

                return [
                    'allowed' => false,
                    'error' => 'Rate limit exceeded',
                    'retry_after' => $retryAfter,
                    'limit_type' => $key
                ];
            }

            RateLimiter::hit($key, $duration);
        }

        return ['allowed' => true];
    }

    /**
     * Detect and prevent DDoS attacks
     *
     * @param Request $request
     * @return array
     */
    public function detectDDoSAttack(Request $request): array
    {
        $ip = $request->ip();
        $userAgent = $request->userAgent();
        
        // Check for suspicious patterns
        $suspiciousPatterns = [
            'empty_user_agent' => empty($userAgent),
            'suspicious_user_agent' => $this->isSuspiciousUserAgent($userAgent),
            'rapid_requests' => $this->isRapidRequests($ip),
            'invalid_content_type' => !$this->isValidContentType($request),
            'suspicious_payload' => $this->isSuspiciousPayload($request)
        ];

        $suspiciousCount = array_sum($suspiciousPatterns);
        $riskLevel = $this->calculateRiskLevel($suspiciousCount, $suspiciousPatterns);

        if ($riskLevel >= 3) {
            // High risk - block the request
            $this->blockSuspiciousIP($ip, $suspiciousPatterns);
            
            return [
                'allowed' => false,
                'risk_level' => $riskLevel,
                'reason' => 'Potential DDoS attack detected',
                'patterns' => array_keys(array_filter($suspiciousPatterns))
            ];
        }

        if ($riskLevel >= 2) {
            // Medium risk - log but allow
            Log::warning('Suspicious webhook request detected', [
                'ip' => $ip,
                'risk_level' => $riskLevel,
                'patterns' => array_keys(array_filter($suspiciousPatterns)),
                'user_agent' => $userAgent
            ]);
        }

        return [
            'allowed' => true,
            'risk_level' => $riskLevel
        ];
    }

    /**
     * Log webhook event for monitoring
     *
     * @param Request $request
     * @param PaymentGateway $gateway
     * @param array $verificationResult
     * @param string $status
     * @return WebhookLog
     */
    public function logWebhookEvent(
        Request $request, 
        PaymentGateway $gateway, 
        array $verificationResult, 
        string $status = 'pending'
    ): WebhookLog {
        $payload = $request->getContent();
        $headers = $request->headers->all();
        
        // Remove sensitive headers from logging
        $sensitiveHeaders = ['authorization', 'x-api-key', 'x-auth-token'];
        foreach ($sensitiveHeaders as $header) {
            if (isset($headers[$header])) {
                $headers[$header] = ['[REDACTED]'];
            }
        }

        return WebhookLog::create([
            'gateway_id' => $gateway->id,
            'webhook_id' => $verificationResult['event_id'] ?? null,
            'event_type' => $verificationResult['event_type'] ?? 'unknown',
            'payload' => json_decode($payload, true) ?: ['raw' => $payload],
            'headers' => $headers,
            'signature' => $verificationResult['valid'] ? 'valid' : 'invalid',
            'status' => $status,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'processed_at' => null,
            'error_message' => $verificationResult['error'] ?? null,
            'retry_count' => 0
        ]);
    }

    /**
     * Check if user agent is suspicious
     *
     * @param string|null $userAgent
     * @return bool
     */
    private function isSuspiciousUserAgent(?string $userAgent): bool
    {
        if (empty($userAgent)) {
            return true;
        }

        $suspiciousPatterns = [
            '/bot/i',
            '/crawler/i',
            '/spider/i',
            '/curl/i',
            '/wget/i',
            '/python/i',
            '/java/i',
            '/go-http-client/i'
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check for rapid requests from same IP
     *
     * @param string $ip
     * @return bool
     */
    private function isRapidRequests(string $ip): bool
    {
        $key = "rapid_requests_{$ip}";
        $requests = Cache::get($key, 0);
        
        if ($requests > 20) { // More than 20 requests in last minute
            return true;
        }
        
        Cache::put($key, $requests + 1, 60);
        return false;
    }

    /**
     * Check if content type is valid for webhooks
     *
     * @param Request $request
     * @return bool
     */
    private function isValidContentType(Request $request): bool
    {
        $contentType = $request->header('Content-Type', '');
        
        $validTypes = [
            'application/json',
            'application/x-www-form-urlencoded',
            'text/plain'
        ];

        foreach ($validTypes as $type) {
            if (str_contains($contentType, $type)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check for suspicious payload patterns
     *
     * @param Request $request
     * @return bool
     */
    private function isSuspiciousPayload(Request $request): bool
    {
        $payload = $request->getContent();
        
        if (empty($payload)) {
            return true;
        }

        // Check payload size (webhooks shouldn't be too large)
        if (strlen($payload) > 1024 * 1024) { // 1MB limit
            return true;
        }

        // Check for malicious patterns
        $maliciousPatterns = [
            '/<script/i',
            '/javascript:/i',
            '/eval\(/i',
            '/exec\(/i',
            '/system\(/i'
        ];

        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $payload)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Calculate risk level based on suspicious patterns
     *
     * @param int $suspiciousCount
     * @param array $patterns
     * @return int
     */
    private function calculateRiskLevel(int $suspiciousCount, array $patterns): int
    {
        $riskLevel = $suspiciousCount;
        
        // Increase risk for specific high-risk patterns
        if ($patterns['rapid_requests']) {
            $riskLevel += 2;
        }
        
        if ($patterns['suspicious_payload']) {
            $riskLevel += 2;
        }

        return min($riskLevel, 5); // Cap at level 5
    }

    /**
     * Block suspicious IP address
     *
     * @param string $ip
     * @param array $patterns
     */
    private function blockSuspiciousIP(string $ip, array $patterns): void
    {
        $blockDuration = 3600; // 1 hour
        $key = "blocked_ip_{$ip}";
        
        Cache::put($key, [
            'blocked_at' => now(),
            'reason' => 'DDoS protection',
            'patterns' => array_keys(array_filter($patterns))
        ], $blockDuration);

        Log::alert('IP address blocked due to suspicious activity', [
            'ip' => $ip,
            'patterns' => array_keys(array_filter($patterns)),
            'block_duration' => $blockDuration
        ]);
    }

    /**
     * Check if IP is blocked
     *
     * @param string $ip
     * @return bool
     */
    public function isIPBlocked(string $ip): bool
    {
        return Cache::has("blocked_ip_{$ip}");
    }

    /**
     * Get webhook security metrics
     *
     * @param int $hours
     * @return array
     */
    public function getSecurityMetrics(int $hours = 24): array
    {
        $since = Carbon::now()->subHours($hours);
        
        // Get cached security events
        $events = Cache::get("security_events", []);
        
        $violationTypes = [];
        $violatingIps = [];
        
        foreach ($events as $event) {
            $violationTypes[$event['event_type']] = ($violationTypes[$event['event_type']] ?? 0) + 1;
            $violatingIps[$event['ip_address']] = ($violatingIps[$event['ip_address']] ?? 0) + 1;
        }

        arsort($violatingIps);
        
        return [
            'total_webhooks' => WebhookLog::where('created_at', '>=', $since)->count(),
            'valid_signatures' => WebhookLog::where('created_at', '>=', $since)
                ->where('signature', 'valid')->count(),
            'invalid_signatures' => WebhookLog::where('created_at', '>=', $since)
                ->where('signature', 'invalid')->count(),
            'blocked_requests' => $this->getBlockedRequestsCount($hours),
            'top_ips' => $this->getTopRequestIPs($hours),
            'error_rate' => $this->calculateErrorRate($hours),
            'total_violations' => count($events),
            'violation_types' => $violationTypes,
            'top_violating_ips' => array_slice($violatingIps, 0, 10, true)
        ];
    }

    /**
     * Get count of blocked requests
     *
     * @param int $hours
     * @return int
     */
    private function getBlockedRequestsCount(int $hours): int
    {
        // This would typically be stored in a separate table or cache
        // For now, return a placeholder
        return 0;
    }

    /**
     * Get top requesting IP addresses
     *
     * @param int $hours
     * @return array
     */
    private function getTopRequestIPs(int $hours): array
    {
        // Since webhook_logs table might not have ip_address column,
        // return cached security events data instead
        $events = Cache::get("security_events", []);
        $ipCounts = [];
        
        foreach ($events as $event) {
            $ip = $event['ip_address'] ?? 'unknown';
            $ipCounts[$ip] = ($ipCounts[$ip] ?? 0) + 1;
        }
        
        arsort($ipCounts);
        
        return array_slice($ipCounts, 0, 10, true);
    }

    /**
     * Calculate error rate
     *
     * @param int $hours
     * @return float
     */
    private function calculateErrorRate(int $hours): float
    {
        $since = Carbon::now()->subHours($hours);
        
        $total = WebhookLog::where('created_at', '>=', $since)->count();
        $errors = WebhookLog::where('created_at', '>=', $since)
            ->where('status', 'failed')->count();
            
        return $total > 0 ? round(($errors / $total) * 100, 2) : 0;
    }

    /**
     * Validate source IP address
     *
     * @param Request $request
     * @param array $allowedIps
     * @return array
     */
    public function validateSourceIP(Request $request, array $allowedIps = []): array
    {
        if (empty($allowedIps)) {
            return ['valid' => true]; // No IP restrictions
        }

        $clientIP = $this->getClientIP($request);

        if (in_array($clientIP, $allowedIps)) {
            return ['valid' => true];
        }

        return [
            'valid' => false,
            'error' => 'Unauthorized IP address',
            'client_ip' => $clientIP
        ];
    }

    /**
     * Get client IP address considering proxies
     *
     * @param Request $request
     * @return string
     */
    private function getClientIP(Request $request): string
    {
        // Check X-Forwarded-For header first (for load balancers/proxies)
        $forwardedFor = $request->header('X-Forwarded-For');
        if ($forwardedFor) {
            $ips = explode(',', $forwardedFor);
            return trim($ips[0]); // First IP is the original client
        }

        // Check other common headers
        $headers = [
            'X-Real-IP',
            'X-Client-IP',
            'CF-Connecting-IP' // Cloudflare
        ];

        foreach ($headers as $header) {
            $ip = $request->header($header);
            if ($ip) {
                return $ip;
            }
        }

        return $request->ip();
    }

    /**
     * Validate webhook payload structure
     *
     * @param array $payload
     * @param string $gatewayType
     * @return array
     */
    public function validatePayloadStructure(array $payload, string $gatewayType): array
    {
        switch ($gatewayType) {
            case 'razorpay':
                return $this->validateRazorpayPayload($payload);
            case 'paypal':
                return $this->validatePayPalPayload($payload);
            case 'stripe':
                return $this->validateStripePayload($payload);
            default:
                return ['valid' => true]; // Generic validation
        }
    }

    /**
     * Validate Razorpay payload structure
     *
     * @param array $payload
     * @return array
     */
    private function validateRazorpayPayload(array $payload): array
    {
        $requiredFields = ['event'];
        
        foreach ($requiredFields as $field) {
            if (!isset($payload[$field])) {
                return [
                    'valid' => false,
                    'error' => 'Invalid payload structure',
                    'missing_field' => $field
                ];
            }
        }

        return ['valid' => true];
    }

    /**
     * Validate PayPal payload structure
     *
     * @param array $payload
     * @return array
     */
    private function validatePayPalPayload(array $payload): array
    {
        $requiredFields = ['id', 'event_type'];
        
        foreach ($requiredFields as $field) {
            if (!isset($payload[$field])) {
                return [
                    'valid' => false,
                    'error' => 'Invalid payload structure',
                    'missing_field' => $field
                ];
            }
        }

        return ['valid' => true];
    }

    /**
     * Validate Stripe payload structure
     *
     * @param array $payload
     * @return array
     */
    private function validateStripePayload(array $payload): array
    {
        $requiredFields = ['id', 'type', 'data'];
        
        foreach ($requiredFields as $field) {
            if (!isset($payload[$field])) {
                return [
                    'valid' => false,
                    'error' => 'Invalid payload structure',
                    'missing_field' => $field
                ];
            }
        }

        return ['valid' => true];
    }

    /**
     * Check for replay attacks
     *
     * @param string $webhookId
     * @param int $timestamp
     * @return array
     */
    public function checkReplayAttack(string $webhookId, int $timestamp): array
    {
        // Check if timestamp is too old (5 minutes tolerance)
        if (abs(time() - $timestamp) > 300) {
            return [
                'valid' => false,
                'error' => 'Webhook too old',
                'timestamp_diff' => abs(time() - $timestamp)
            ];
        }

        // Check if webhook ID has been processed before
        $cacheKey = "webhook_processed_{$webhookId}";
        if (Cache::has($cacheKey)) {
            return [
                'valid' => false,
                'error' => 'Duplicate webhook detected',
                'webhook_id' => $webhookId
            ];
        }

        // Mark webhook as processed (cache for 1 hour)
        Cache::put($cacheKey, true, 3600);

        return ['valid' => true];
    }



    /**
     * Validate content type
     *
     * @param Request $request
     * @return array
     */
    public function validateContentType(Request $request): array
    {
        $contentType = $request->header('Content-Type', '');
        
        $validTypes = [
            'application/json',
            'application/x-www-form-urlencoded'
        ];

        foreach ($validTypes as $type) {
            if (str_contains($contentType, $type)) {
                return ['valid' => true];
            }
        }

        return [
            'valid' => false,
            'error' => 'Invalid content type',
            'content_type' => $contentType
        ];
    }

    /**
     * Validate request method
     *
     * @param Request $request
     * @return array
     */
    public function validateRequestMethod(Request $request): array
    {
        if ($request->method() !== 'POST') {
            return [
                'valid' => false,
                'error' => 'Invalid request method',
                'method' => $request->method()
            ];
        }

        return ['valid' => true];
    }

    /**
     * Sanitize webhook payload
     *
     * @param array $payload
     * @return array
     */
    public function sanitizePayload(array $payload): array
    {
        return $this->recursiveSanitize($payload);
    }

    /**
     * Recursively sanitize array data
     *
     * @param mixed $data
     * @return mixed
     */
    private function recursiveSanitize($data)
    {
        if (is_array($data)) {
            $sanitized = [];
            foreach ($data as $key => $value) {
                // Skip potentially dangerous keys
                if (in_array(strtolower($key), ['script', 'javascript', 'eval', 'exec'])) {
                    continue;
                }
                $sanitized[$key] = $this->recursiveSanitize($value);
            }
            return $sanitized;
        }

        if (is_string($data)) {
            // Remove potentially dangerous content
            $data = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $data);
            $data = preg_replace('/javascript:/i', '', $data);
            $data = preg_replace('/on\w+\s*=/i', '', $data);
            return $data;
        }

        return $data;
    }

    /**
     * Validate payload size
     *
     * @param Request $request
     * @return array
     */
    public function validatePayloadSize(Request $request): array
    {
        $maxSize = 1024 * 1024; // 1MB
        $contentLength = $request->header('Content-Length', 0);
        
        // If Content-Length header is not available, check actual content size
        if ($contentLength == 0) {
            $contentLength = strlen($request->getContent());
        }

        if ($contentLength > $maxSize) {
            return [
                'valid' => false,
                'error' => 'Payload too large',
                'size' => $contentLength,
                'max_size' => $maxSize
            ];
        }

        return ['valid' => true];
    }

    /**
     * Validate JSON payload
     *
     * @param Request $request
     * @return array
     */
    public function validateJsonPayload(Request $request): array
    {
        $content = $request->getContent();
        
        if (empty($content)) {
            return [
                'valid' => false,
                'error' => 'Empty payload'
            ];
        }

        $decoded = json_decode($content, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'valid' => false,
                'error' => 'Invalid JSON',
                'json_error' => json_last_error_msg()
            ];
        }

        return [
            'valid' => true,
            'payload' => $decoded
        ];
    }

    /**
     * Comprehensive webhook validation
     *
     * @param Request $request
     * @param PaymentGateway $gateway
     * @param array $allowedIps
     * @return array
     */
    public function validateWebhookRequest(Request $request, PaymentGateway $gateway, array $allowedIps = []): array
    {
        // Validate request method
        $methodCheck = $this->validateRequestMethod($request);
        if (!$methodCheck['valid']) {
            return $methodCheck;
        }

        // Validate content type
        $contentTypeCheck = $this->validateContentType($request);
        if (!$contentTypeCheck['valid']) {
            return $contentTypeCheck;
        }

        // Validate payload size
        $sizeCheck = $this->validatePayloadSize($request);
        if (!$sizeCheck['valid']) {
            return $sizeCheck;
        }

        // Validate JSON
        $jsonCheck = $this->validateJsonPayload($request);
        if (!$jsonCheck['valid']) {
            return $jsonCheck;
        }

        // Validate source IP
        if (!empty($allowedIps)) {
            $ipCheck = $this->validateSourceIP($request, $allowedIps);
            if (!$ipCheck['valid']) {
                return $ipCheck;
            }
        }

        // Verify signature
        $signatureCheck = $this->verifyWebhookSignature($request, $gateway);
        if (!$signatureCheck['valid']) {
            return $signatureCheck;
        }

        // Sanitize payload
        $sanitizedPayload = $this->sanitizePayload($jsonCheck['payload']);

        return [
            'valid' => true,
            'sanitized_payload' => $sanitizedPayload
        ];
    }

    /**
     * Record security event
     *
     * @param string $eventType
     * @param string $ipAddress
     * @param array $metadata
     */
    public function recordSecurityEvent(string $eventType, string $ipAddress, array $metadata = []): void
    {
        $cacheKey = "security_events";
        $events = Cache::get($cacheKey, []);
        
        $events[] = [
            'event_type' => $eventType,
            'ip_address' => $ipAddress,
            'metadata' => $metadata,
            'timestamp' => now()->toISOString()
        ];

        // Keep only last 1000 events
        if (count($events) > 1000) {
            $events = array_slice($events, -1000);
        }

        Cache::put($cacheKey, $events, 3600); // Cache for 1 hour
    }



    /**
     * Clean up old security records
     *
     * @param int $retentionDays
     * @return int
     */
    public function cleanupOldSecurityRecords(int $retentionDays): int
    {
        $events = Cache::get("security_events", []);
        $cutoffDate = now()->subDays($retentionDays);
        
        $filteredEvents = array_filter($events, function($event) use ($cutoffDate) {
            return Carbon::parse($event['timestamp'])->isAfter($cutoffDate);
        });

        $cleaned = count($events) - count($filteredEvents);
        
        Cache::put("security_events", array_values($filteredEvents), 3600);
        
        return $cleaned;
    }

    /**
     * Clear security cache
     */
    public function clearSecurityCache(): void
    {
        Cache::forget("security_events");
        // Clear other security-related cache keys if needed
    }
}