<?php

use App\Models\User;
use App\Http\Middleware\ApiRateLimiting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Log;

uses()->group('middleware');

beforeEach(function () {
    $this->middleware = new ApiRateLimiting();
    $this->next = function ($request) {
        return response('Next middleware called');
    };
});

it('returns 401 for unauthenticated requests', function () {
    $request = Request::create('/api/test');

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getStatusCode())->toBe(401)
        ->and($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['error'])->toBe('Unauthenticated.');
});

it('allows requests within rate limit for regular users', function () {
    $user = User::factory()->create(['is_premium' => false]);

    $request = Request::create('/api/test');
    $request->setUserResolver(function () use ($user) {
        return $user;
    });

    // Mock RateLimiter to simulate normal flow
    RateLimiter::shouldReceive('tooManyAttempts')
        ->with('api:' . $user->id, 60)
        ->andReturn(false);
    RateLimiter::shouldReceive('hit')
        ->with('api:' . $user->id, 60)
        ->andReturn(true);

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});

it('allows requests within rate limit for premium users', function () {
    $user = User::factory()->create(['is_premium' => true]);

    $request = Request::create('/api/test');
    $request->setUserResolver(function () use ($user) {
        return $user;
    });

    // Mock RateLimiter to simulate normal flow
    RateLimiter::shouldReceive('tooManyAttempts')
        ->with('api:' . $user->id, 120)
        ->andReturn(false);
    RateLimiter::shouldReceive('hit')
        ->with('api:' . $user->id, 60)
        ->andReturn(true);

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});

it('returns 429 for regular users when rate limit exceeded', function () {
    $user = User::factory()->create(['is_premium' => false]);

    $request = Request::create('/api/test');
    $request->setUserResolver(function () use ($user) {
        return $user;
    });

    // Mock RateLimiter to simulate rate limit exceeded
    RateLimiter::shouldReceive('tooManyAttempts')
        ->with('api:' . $user->id, 60)
        ->andReturn(true);
    RateLimiter::shouldReceive('availableIn')
        ->with('api:' . $user->id)
        ->andReturn(30);

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getStatusCode())->toBe(429)
        ->and($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['error'])->toBe('Too many requests')
        ->and($content['retry_after'])->toBe(30);
});

it('returns 429 for premium users when rate limit exceeded', function () {
    $user = User::factory()->create(['is_premium' => true]);

    $request = Request::create('/api/test');
    $request->setUserResolver(function () use ($user) {
        return $user;
    });

    // Mock RateLimiter to simulate rate limit exceeded
    RateLimiter::shouldReceive('tooManyAttempts')
        ->with('api:' . $user->id, 120)
        ->andReturn(true);
    RateLimiter::shouldReceive('availableIn')
        ->with('api:' . $user->id)
        ->andReturn(45);

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getStatusCode())->toBe(429)
        ->and($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['error'])->toBe('Too many requests')
        ->and($content['retry_after'])->toBe(45);
});

it('uses correct rate limit for regular users', function () {
    $user = User::factory()->create(['is_premium' => false]);

    $request = Request::create('/api/test');
    $request->setUserResolver(function () use ($user) {
        return $user;
    });

    // Mock RateLimiter to verify correct limit is used
    RateLimiter::shouldReceive('tooManyAttempts')
        ->withArgs(function ($key, $maxAttempts) {
            return $maxAttempts === 60; // Regular user limit
        })
        ->andReturn(false);
    RateLimiter::shouldReceive('hit')
        ->with('api:' . $user->id, 60)
        ->andReturn(true);

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});

it('uses correct rate limit for premium users', function () {
    $user = User::factory()->create(['is_premium' => true]);

    $request = Request::create('/api/test');
    $request->setUserResolver(function () use ($user) {
        return $user;
    });

    // Mock RateLimiter to verify correct limit is used
    RateLimiter::shouldReceive('tooManyAttempts')
        ->withArgs(function ($key, $maxAttempts) {
            return $maxAttempts === 120; // Premium user limit
        })
        ->andReturn(false);
    RateLimiter::shouldReceive('hit')
        ->with('api:' . $user->id, 60)
        ->andReturn(true);

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});

it('logs api request headers', function () {
    $user = User::factory()->create();

    $request = Request::create('/api/test');
    $request->setUserResolver(function () use ($user) {
        return $user;
    });
    $request->headers->set('User-Agent', 'Test Agent');
    $request->headers->set('Accept', 'application/json');

    // Mock Log facade
    Log::shouldReceive('info')
        ->with('API Request Headers:', Mockery::any())
        ->once();

    // Mock RateLimiter for normal flow
    RateLimiter::shouldReceive('tooManyAttempts')
        ->andReturn(false);
    RateLimiter::shouldReceive('hit')
        ->andReturn(true);

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});

it('logs error when no user found', function () {
    $request = Request::create('/api/test');
    $request->setUserResolver(function () {
        return null;
    });

    // Mock Log facade
    Log::shouldReceive('error')
        ->with('API Authentication failed - no user found')
        ->once();

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getStatusCode())->toBe(401)
        ->and($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['error'])->toBe('Unauthenticated.');
});

it('handles null user resolver', function () {
    $request = Request::create('/api/test');

    // Mock Log facade
    Log::shouldReceive('error')
        ->with('API Authentication failed - no user found')
        ->once();

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getStatusCode())->toBe(401)
        ->and($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['error'])->toBe('Unauthenticated.');
});

it('handles requests without user resolver', function () {
    $request = Request::create('/api/test');

    // Mock Log facade
    Log::shouldReceive('error')
        ->with('API Authentication failed - no user found')
        ->once();

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getStatusCode())->toBe(401)
        ->and($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['error'])->toBe('Unauthenticated.');
});

it('handles different user types', function () {
    $regularUser = User::factory()->create(['is_premium' => false]);
    $premiumUser = User::factory()->create(['is_premium' => true]);

    $users = [
        ['user' => $regularUser, 'limit' => 60],
        ['user' => $premiumUser, 'limit' => 120]
    ];

    foreach ($users as $userData) {
        $request = Request::create('/api/test');
        $request->setUserResolver(function () use ($userData) {
            return $userData['user'];
        });

        // Mock RateLimiter
        RateLimiter::shouldReceive('tooManyAttempts')
            ->with('api:' . $userData['user']->id, $userData['limit'])
            ->andReturn(false);
        RateLimiter::shouldReceive('hit')
            ->with('api:' . $userData['user']->id, 60)
            ->andReturn(true);

        $response = $this->middleware->handle($request, $this->next);

        expect($response->getContent())->toBe('Next middleware called');
    }
});

it('handles different http methods', function () {
    $user = User::factory()->create();

    $methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];

    foreach ($methods as $method) {
        $request = Request::create('/api/test', $method);
        $request->setUserResolver(function () use ($user) {
            return $user;
        });

        // Mock RateLimiter
        RateLimiter::shouldReceive('tooManyAttempts')
            ->andReturn(false);
        RateLimiter::shouldReceive('hit')
            ->andReturn(true);

        $response = $this->middleware->handle($request, $this->next);

        expect($response->getContent())->toBe('Next middleware called');
    }
});

it('handles different api routes', function () {
    $user = User::factory()->create();

    $routes = [
        '/api/users',
        '/api/orders',
        '/api/products',
        '/api/auth/profile',
        '/api/v1/data'
    ];

    foreach ($routes as $route) {
        $request = Request::create($route);
        $request->setUserResolver(function () use ($user) {
            return $user;
        });

        // Mock RateLimiter
        RateLimiter::shouldReceive('tooManyAttempts')
            ->andReturn(false);
        RateLimiter::shouldReceive('hit')
            ->andReturn(true);

        $response = $this->middleware->handle($request, $this->next);

        expect($response->getContent())->toBe('Next middleware called');
    }
});

it('handles edge case with empty route', function () {
    $user = User::factory()->create();

    $request = Request::create('');
    $request->setUserResolver(function () use ($user) {
        return $user;
    });

    // Mock RateLimiter
    RateLimiter::shouldReceive('tooManyAttempts')
        ->andReturn(false);
    RateLimiter::shouldReceive('hit')
        ->andReturn(true);

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});

it('handles edge case with root route', function () {
    $user = User::factory()->create();

    $request = Request::create('/');
    $request->setUserResolver(function () use ($user) {
        return $user;
    });

    // Mock RateLimiter
    RateLimiter::shouldReceive('tooManyAttempts')
        ->andReturn(false);
    RateLimiter::shouldReceive('hit')
        ->andReturn(true);

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});

it('handles requests with different headers', function () {
    $user = User::factory()->create();

    $headers = [
        'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept' => 'application/json',
        'Content-Type' => 'application/json',
        'Authorization' => 'Bearer token123'
    ];

    $request = Request::create('/api/test');
    $request->setUserResolver(function () use ($user) {
        return $user;
    });

    foreach ($headers as $key => $value) {
        $request->headers->set($key, $value);
    }

    // Mock Log facade
    Log::shouldReceive('info')
        ->with('API Request Headers:', Mockery::any())
        ->once();

    // Mock RateLimiter
    RateLimiter::shouldReceive('tooManyAttempts')
        ->andReturn(false);
    RateLimiter::shouldReceive('hit')
        ->andReturn(true);

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});

it('handles requests with query parameters', function () {
    $user = User::factory()->create();

    $request = Request::create('/api/test?param1=value1&param2=value2');
    $request->setUserResolver(function () use ($user) {
        return $user;
    });

    // Mock RateLimiter
    RateLimiter::shouldReceive('tooManyAttempts')
        ->andReturn(false);
    RateLimiter::shouldReceive('hit')
        ->andReturn(true);

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});

it('handles requests with post data', function () {
    $user = User::factory()->create();

    $request = Request::create('/api/test', 'POST', ['data' => 'test']);
    $request->setUserResolver(function () use ($user) {
        return $user;
    });

    // Mock RateLimiter
    RateLimiter::shouldReceive('tooManyAttempts')
        ->andReturn(false);
    RateLimiter::shouldReceive('hit')
        ->andReturn(true);

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});