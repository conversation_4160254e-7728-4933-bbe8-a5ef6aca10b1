<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\PinCode;
use Illuminate\Support\Facades\Validator;
use App\Traits\TracksApiRequests;

class PinToPinDistanceController extends Controller
{
    use TracksApiRequests;

    public function validatePincode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pincode' => 'required|digits:6',
        ]);

        if ($validator->fails()) {
            $response = response()->json(['valid' => false, 'errors' => $validator->errors()], 422);
        } else {
            $pincode = PinCode::where('pincode', $request->pincode)->first();
            $response = response()->json(['valid' => $pincode !== null]);
        }

        $this->trackApiRequest($request, $response);
        return $response;
    }

    public function calculateDistance(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'from_pincode' => 'required|digits:6|exists:pin_codes,pincode',
            'to_pincode' => 'required|digits:6|exists:pin_codes,pincode',
        ]);

        if ($validator->fails()) {
            $response = response()->json(['error' => $validator->errors()], 422);
        } else {
            $from = PinCode::where('pincode', $request->from_pincode)->first();
            $to = PinCode::where('pincode', $request->to_pincode)->first();

            if (!$from || !$to) {
                $response = response()->json(['error' => 'One or both pincodes not found'], 404);
            } else {
                $distance = $this->haversineDistance($from->latitude, $from->longitude, $to->latitude, $to->longitude);
                $bearing = $this->calculateBearing($from->latitude, $from->longitude, $to->latitude, $to->longitude);
                $midpoint = $this->calculateMidpoint($from->latitude, $from->longitude, $to->latitude, $to->longitude);

                $response = response()->json([
                    'from' => [
                        'pincode' => $from->pincode,
                        'name' => $from->name,
                        'state' => $from->state,
                    ],
                    'to' => [
                        'pincode' => $to->pincode,
                        'name' => $to->name,
                        'state' => $to->state,
                    ],
                    'distance' => [
                        'km' => round($distance, 2),
                        'miles' => round($distance * 0.621371, 2),
                        'nautical_miles' => round($distance * 0.539957, 2),
                    ],
                    'bearing' => round($bearing, 2),
                    'midpoint' => $midpoint,
                ]);
            }
        }

        $this->trackApiRequest($request, $response);
        return $response;
    }

    private function haversineDistance($lat1, $lon1, $lat2, $lon2)
    {
        // Cast string coordinates to float
        $lat1 = (float)$lat1;
        $lon1 = (float)$lon1;
        $lat2 = (float)$lat2;
        $lon2 = (float)$lon2;

        $lat1 = deg2rad($lat1);
        $lon1 = deg2rad($lon1);
        $lat2 = deg2rad($lat2);
        $lon2 = deg2rad($lon2);

        $dlat = $lat2 - $lat1;
        $dlon = $lon2 - $lon1;

        $a = sin($dlat/2)**2 + cos($lat1) * cos($lat2) * sin($dlon/2)**2;
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        $radius = 6371; // Earth's radius in kilometers
        return $radius * $c;
    }

    private function calculateBearing($lat1, $lon1, $lat2, $lon2)
    {
        // Cast string coordinates to float
        $lat1 = (float)$lat1;
        $lon1 = (float)$lon1;
        $lat2 = (float)$lat2;
        $lon2 = (float)$lon2;

        $lat1 = deg2rad($lat1);
        $lon1 = deg2rad($lon1);
        $lat2 = deg2rad($lat2);
        $lon2 = deg2rad($lon2);

        $y = sin($lon2 - $lon1) * cos($lat2);
        $x = cos($lat1) * sin($lat2) - sin($lat1) * cos($lat2) * cos($lon2 - $lon1);
        $bearing = atan2($y, $x);

        return fmod((rad2deg($bearing) + 360), 360);
    }

    private function calculateMidpoint($lat1, $lon1, $lat2, $lon2)
    {
        // Cast string coordinates to float
        $lat1 = (float)$lat1;
        $lon1 = (float)$lon1;
        $lat2 = (float)$lat2;
        $lon2 = (float)$lon2;

        $lat1 = deg2rad($lat1);
        $lon1 = deg2rad($lon1);
        $lat2 = deg2rad($lat2);
        $lon2 = deg2rad($lon2);

        $bx = cos($lat2) * cos($lon2 - $lon1);
        $by = cos($lat2) * sin($lon2 - $lon1);

        $midLat = atan2(sin($lat1) + sin($lat2), sqrt((cos($lat1) + $bx) * (cos($lat1) + $bx) + $by * $by));
        $midLon = $lon1 + atan2($by, cos($lat1) + $bx);

        return [
            'latitude' => round(rad2deg($midLat), 6),
            'longitude' => round(rad2deg($midLon), 6),
        ];
    }
}