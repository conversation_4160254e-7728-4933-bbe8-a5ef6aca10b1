<?php if(isset($landingPage['stats']) && $landingPage['stats']['active']): ?>
        <section id="stats" class="py-20 bg-bg-light dark:bg-bg-dark transition-colors duration-300">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center max-w-3xl mx-auto mb-16">
                    <span
                        class="inline-block px-3 py-1 rounded-full bg-primary-light text-white dark:bg-primary-dark dark:text-white text-xs font-semibold tracking-wider uppercase mb-3"><?php echo e($landingPage['stats']['content']['badge_text'] ?? 'Coverage'); ?></span>
                    <h2
                        class="text-3xl md:text-4xl font-extrabold mb-4 text-text-primary-light dark:text-text-primary-dark">
                        <?php echo e($landingPage['stats']['content']['heading'] ?? 'Complete Coverage Across India'); ?>

                    </h2>
                    <p class="text-lg text-text-secondary-light dark:text-text-secondary-dark"><?php echo e($landingPage['stats']['content']['subheading'] ?? 'Our comprehensive database covers postal codes across all states and districts'); ?></p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div
                        class="card p-6 text-center card-hover bg-white dark:bg-bg-dark rounded-xl shadow-lg transition-colors duration-300 border border-border-light dark:border-border-dark">
                        <div
                            class="w-16 h-16 mx-auto bg-primary-light text-white rounded-full flex items-center justify-center mb-4 dark:bg-primary-dark dark:text-white">
                            <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <span
                            class="text-4xl font-extrabold text-primary-light dark:text-primary-dark"><?php echo e($landingPage['stats']['content']['states_count'] ?? '36'); ?></span>
                        <p class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark mt-2"><?php echo e($landingPage['stats']['content']['states_label'] ?? 'States & Union Territories'); ?></p>
                        <p class="text-text-secondary-light mt-2 dark:text-text-secondary-dark"><?php echo e($landingPage['stats']['content']['states_description'] ?? 'Complete coverage across all states and union territories in India'); ?></p>
                    </div>

                    <div
                        class="card p-6 text-center card-hover transform translate-y-4 bg-white dark:bg-bg-dark rounded-xl shadow-lg transition-colors duration-300 border border-border-light dark:border-border-dark">
                        <div
                            class="w-16 h-16 mx-auto bg-primary-light text-white rounded-full flex items-center justify-center mb-4 dark:bg-primary-dark dark:text-white">
                            <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <span
                            class="text-4xl font-extrabold text-primary-light dark:text-primary-dark"><?php echo e($landingPage['stats']['content']['districts_count'] ?? '700+'); ?></span>
                        <p class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark mt-2"><?php echo e($landingPage['stats']['content']['districts_label'] ?? 'Districts Mapped'); ?></p>
                        <p class="text-text-secondary-light mt-2 dark:text-text-secondary-dark"><?php echo e($landingPage['stats']['content']['districts_description'] ?? 'Detailed mapping of all districts with their corresponding pincodes'); ?></p>
                    </div>

                    <div
                        class="card p-6 text-center card-hover bg-white dark:bg-bg-dark rounded-xl shadow-lg transition-colors duration-300 border border-border-light dark:border-border-dark">
                        <div
                            class="w-16 h-16 mx-auto bg-primary-light text-white rounded-full flex items-center justify-center mb-4 dark:bg-primary-dark dark:text-white">
                            <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <span
                            class="text-4xl font-extrabold text-primary-light dark:text-primary-dark"><?php echo e($landingPage['stats']['content']['delivery_offices_count'] ?? '155,000+'); ?></span>
                        <p class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark mt-2"><?php echo e($landingPage['stats']['content']['delivery_offices_label'] ?? 'Delivery Offices'); ?></p>
                        <p class="text-text-secondary-light mt-2 dark:text-text-secondary-dark"><?php echo e($landingPage['stats']['content']['delivery_offices_description'] ?? 'Information on post offices and their delivery status across India'); ?></p>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/home/<USER>/stats-section.blade.php ENDPATH**/ ?>