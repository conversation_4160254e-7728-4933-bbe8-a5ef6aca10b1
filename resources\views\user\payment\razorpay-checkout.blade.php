@extends('layouts.app')

@section('title', 'Razorpay Payment')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex align-items-center">
                        <img src="https://razorpay.com/assets/razorpay-logo.svg" alt="Razorpay" height="24" class="me-3">
                        <h4 class="mb-0">Secure Payment with Razorpay</h4>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Order Summary -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="order-details">
                                <h5 class="mb-3">Order Details</h5>
                                <div class="table-responsive">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Plan:</strong></td>
                                            <td>{{ $order->plan->name ?? 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Duration:</strong></td>
                                            <td>{{ $order->plan->duration ?? 'N/A' }} {{ $order->plan->duration_type ?? '' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Order ID:</strong></td>
                                            <td><code>{{ $order->order_number }}</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Amount:</strong></td>
                                            <td><h5 class="text-primary mb-0">{{ $order->currency }} {{ number_format($order->amount, 2) }}</h5></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="payment-methods bg-light p-3 rounded">
                                <h6 class="mb-3">Accepted Payment Methods</h6>
                                <div class="row g-2">
                                    <div class="col-6 text-center">
                                        <i class="fab fa-cc-visa fa-2x text-primary"></i>
                                        <small class="d-block">Visa</small>
                                    </div>
                                    <div class="col-6 text-center">
                                        <i class="fab fa-cc-mastercard fa-2x text-warning"></i>
                                        <small class="d-block">Mastercard</small>
                                    </div>
                                    <div class="col-6 text-center">
                                        <i class="fas fa-university fa-2x text-info"></i>
                                        <small class="d-block">Net Banking</small>
                                    </div>
                                    <div class="col-6 text-center">
                                        <i class="fas fa-mobile-alt fa-2x text-success"></i>
                                        <small class="d-block">UPI</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Form -->
                    <form id="razorpay-payment-form">
                        @csrf
                        <input type="hidden" name="order_id" value="{{ $order->id }}">
                        <input type="hidden" name="razorpay_order_id" value="{{ $razorpayOrder['id'] }}">
                        
                        <!-- Customer Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="customer_name" 
                                       value="{{ auth()->user()->name ?? '' }}" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="customer_email" 
                                       value="{{ auth()->user()->email ?? '' }}" required>
                            </div>
                            <div class="col-md-6 mt-3">
                                <label class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" id="customer_phone" 
                                       value="{{ auth()->user()->phone ?? '' }}" required>
                            </div>
                            <div class="col-md-6 mt-3">
                                <label class="form-label">Country</label>
                                <select class="form-select" id="customer_country">
                                    <option value="IN" selected>India</option>
                                    <option value="US">United States</option>
                                    <option value="GB">United Kingdom</option>
                                    <option value="CA">Canada</option>
                                </select>
                            </div>
                        </div>

                        <!-- Payment Button -->
                        <div class="text-center">
                            <button type="button" id="razorpay-button" class="btn btn-primary btn-lg px-5">
                                <i class="fas fa-lock me-2"></i>
                                Pay {{ $order->currency }} {{ number_format($order->amount, 2) }}
                                <span class="spinner-border spinner-border-sm ms-2" style="display: none;"></span>
                            </button>
                        </div>

                        <!-- Security Information -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="security-info bg-light p-3 rounded text-center">
                                    <div class="d-flex justify-content-center align-items-center mb-2">
                                        <i class="fas fa-shield-alt text-success fa-lg me-2"></i>
                                        <strong>Secure Payment</strong>
                                    </div>
                                    <small class="text-muted">
                                        Your payment information is encrypted and secure. 
                                        Razorpay is PCI DSS compliant and your card details are never stored.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Loading Overlay -->
                    <div id="payment-loading" class="payment-overlay" style="display: none;">
                        <div class="text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>Processing Payment...</h5>
                            <p class="text-muted">Please do not close this window or press the back button.</p>
                        </div>
                    </div>

                    <!-- Error Display -->
                    <div id="payment-error" class="alert alert-danger mt-3" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span id="error-message"></span>
                    </div>
                </div>
            </div>

            <!-- Help Section -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="mb-3">
                        <i class="fas fa-question-circle me-2"></i>
                        Need Help?
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="small">Payment Issues:</h6>
                            <ul class="small text-muted">
                                <li>Ensure your card has sufficient balance</li>
                                <li>Check if international transactions are enabled</li>
                                <li>Try using a different payment method</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="small">Contact Support:</h6>
                            <p class="small text-muted">
                                <i class="fas fa-envelope me-1"></i> <EMAIL><br>
                                <i class="fas fa-phone me-1"></i> +91-XXXXXXXXXX
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.payment-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 0.375rem;
}

.card {
    position: relative;
}

.payment-methods i {
    margin-bottom: 0.5rem;
}

.security-info {
    border-left: 4px solid #28a745;
}

#razorpay-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

@media (max-width: 768px) {
    .payment-methods .row {
        text-align: center;
    }
    
    .payment-methods .col-6 {
        margin-bottom: 1rem;
    }
}
</style>
@endpush

@push('scripts')
<!-- Razorpay Checkout Script -->
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const razorpayButton = document.getElementById('razorpay-button');
    const paymentForm = document.getElementById('razorpay-payment-form');
    const loadingOverlay = document.getElementById('payment-loading');
    const errorDiv = document.getElementById('payment-error');
    const errorMessage = document.getElementById('error-message');

    // Razorpay configuration
    const razorpayOptions = {
        key: '{{ $razorpayConfig["key_id"] }}',
        amount: {{ $razorpayOrder['amount'] }},
        currency: '{{ $razorpayOrder['currency'] }}',
        order_id: '{{ $razorpayOrder['id'] }}',
        name: '{{ config('app.name') }}',
        description: 'Payment for {{ $order->plan->name ?? 'Plan' }}',
        image: '{{ asset('images/logo.png') }}',
        prefill: {
            name: '{{ auth()->user()->name ?? '' }}',
            email: '{{ auth()->user()->email ?? '' }}',
            contact: '{{ auth()->user()->phone ?? '' }}'
        },
        notes: {
            order_id: '{{ $order->id }}',
            order_number: '{{ $order->order_number }}'
        },
        theme: {
            color: '#007bff'
        },
        modal: {
            ondismiss: function() {
                hideLoading();
                showError('Payment was cancelled. Please try again.');
            }
        },
        handler: function(response) {
            handlePaymentSuccess(response);
        }
    };

    // Initialize Razorpay
    const razorpay = new Razorpay(razorpayOptions);

    // Handle payment button click
    razorpayButton.addEventListener('click', function() {
        if (!validateForm()) {
            return;
        }

        // Update prefill data with current form values
        razorpayOptions.prefill.name = document.getElementById('customer_name').value;
        razorpayOptions.prefill.email = document.getElementById('customer_email').value;
        razorpayOptions.prefill.contact = document.getElementById('customer_phone').value;

        showLoading();
        hideError();

        // Open Razorpay checkout
        try {
            razorpay.open();
        } catch (error) {
            hideLoading();
            showError('Failed to initialize payment. Please try again.');
            console.error('Razorpay initialization error:', error);
        }
    });

    // Handle payment success
    function handlePaymentSuccess(response) {
        showLoading();
        
        // Send payment details to server for verification
        fetch('{{ route("user.payment.razorpay.verify") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                order_id: '{{ $order->id }}',
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_order_id: response.razorpay_order_id,
                razorpay_signature: response.razorpay_signature,
                customer_details: {
                    name: document.getElementById('customer_name').value,
                    email: document.getElementById('customer_email').value,
                    phone: document.getElementById('customer_phone').value,
                    country: document.getElementById('customer_country').value
                }
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Redirect to success page
                window.location.href = data.redirect_url || '{{ route("user.payment.success", $order->id) }}';
            } else {
                hideLoading();
                showError(data.message || 'Payment verification failed. Please contact support.');
            }
        })
        .catch(error => {
            hideLoading();
            showError('Payment verification failed. Please contact support.');
            console.error('Payment verification error:', error);
        });
    }

    // Handle payment errors
    razorpay.on('payment.failed', function(response) {
        hideLoading();
        const errorMsg = response.error.description || 'Payment failed. Please try again.';
        showError(errorMsg);
        
        // Log error details for debugging
        console.error('Razorpay payment failed:', response.error);
        
        // Send error details to server for logging
        fetch('{{ route("user.payment.razorpay.error") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                order_id: '{{ $order->id }}',
                error: response.error
            })
        }).catch(err => console.error('Error logging failed:', err));
    });

    // Form validation
    function validateForm() {
        const requiredFields = ['customer_name', 'customer_email', 'customer_phone'];
        let isValid = true;

        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        // Email validation
        const email = document.getElementById('customer_email').value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (email && !emailRegex.test(email)) {
            document.getElementById('customer_email').classList.add('is-invalid');
            isValid = false;
        }

        // Phone validation
        const phone = document.getElementById('customer_phone').value;
        const phoneRegex = /^[+]?[\d\s\-\(\)]{10,}$/;
        if (phone && !phoneRegex.test(phone)) {
            document.getElementById('customer_phone').classList.add('is-invalid');
            isValid = false;
        }

        if (!isValid) {
            showError('Please fill in all required fields correctly.');
        }

        return isValid;
    }

    // Show loading overlay
    function showLoading() {
        loadingOverlay.style.display = 'flex';
        razorpayButton.disabled = true;
        razorpayButton.querySelector('.spinner-border').style.display = 'inline-block';
    }

    // Hide loading overlay
    function hideLoading() {
        loadingOverlay.style.display = 'none';
        razorpayButton.disabled = false;
        razorpayButton.querySelector('.spinner-border').style.display = 'none';
    }

    // Show error message
    function showError(message) {
        errorMessage.textContent = message;
        errorDiv.style.display = 'block';
        errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    // Hide error message
    function hideError() {
        errorDiv.style.display = 'none';
    }

    // Real-time form validation
    document.querySelectorAll('input[required]').forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value.trim()) {
                this.classList.remove('is-invalid');
            }
        });
    });

    // Prevent form submission on Enter key
    paymentForm.addEventListener('submit', function(e) {
        e.preventDefault();
        razorpayButton.click();
    });
});

// Handle page visibility change (user switches tabs)
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        // Check if payment was completed in another tab
        checkPaymentStatus();
    }
});

// Check payment status
function checkPaymentStatus() {
    fetch('{{ route("user.payment.status", $order->id) }}')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'completed') {
                window.location.href = '{{ route("user.payment.success", $order->id) }}';
            }
        })
        .catch(error => {
            console.error('Status check error:', error);
        });
}
</script>
@endpush