<?php

use App\Models\PinCode;
use App\Models\State;
use App\Models\District;
use App\Models\PostOffice;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

// Tests for getPincode endpoint
test('get pincode endpoint returns pincode details for valid pincode', function () {
    $user = User::factory()->create();
    $state = State::factory()->create(['name' => 'Delhi']);
    $district = District::factory()->create(['name' => 'New Delhi', 'state_id' => $state->id]);
    
    $pincode = PinCode::factory()->create([
        'pincode' => '110001',
        'state' => 'Delhi',
        'district' => 'New Delhi',
        'name' => 'Connaught Place',
    ]);

    $response = $this->actingAs($user)->getJson('/api/pincode/110001');
    
    $response->assertStatus(200)
        ->assertJson([
            'status' => 'success',
            'data' => [
                'pincode' => '110001',
                'state' => 'Delhi',
                'district' => 'New Delhi',
                'post_office' => 'Connaught Place',
            ],
        ]);
});

test('get pincode endpoint returns 404 for non-existent pincode', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->getJson('/api/pincode/999999');
    
    $response->assertStatus(404)
        ->assertJson([
            'status' => 'error',
            'message' => 'Pincode not found!',
        ]);
});

// Tests for getStateNames endpoint
test('get state names endpoint returns matching states', function () {
    $user = User::factory()->create();
    $state1 = State::factory()->create(['name' => 'Delhi']);
    $state2 = State::factory()->create(['name' => 'Delhi NCR']);
    $state3 = State::factory()->create(['name' => 'Maharashtra']);

    $response = $this->actingAs($user)->getJson('/api/pincodes/state/Delhi');
    
    $response->assertStatus(200)
        ->assertJsonStructure(['data'])
        ->assertJsonCount(2, 'data')
        ->assertJsonFragment(['id' => $state1->id, 'name' => 'Delhi'])
        ->assertJsonFragment(['id' => $state2->id, 'name' => 'Delhi NCR']);
});

test('get state names endpoint returns empty array for no matches', function () {
    $user = User::factory()->create();
    State::factory()->create(['name' => 'Delhi']);

    $response = $this->actingAs($user)->getJson('/api/pincodes/state/NonExistent');
    
    $response->assertStatus(200)
        ->assertJsonStructure(['data'])
        ->assertJsonCount(0, 'data');
});

// Tests for getDistrictNames endpoint
test('get district names endpoint returns matching districts', function () {
    $user = User::factory()->create();
    $state = State::factory()->create();
    $district1 = District::factory()->create(['name' => 'New Delhi', 'state_id' => $state->id]);
    $district2 = District::factory()->create(['name' => 'Old Delhi', 'state_id' => $state->id]);
    $district3 = District::factory()->create(['name' => 'Mumbai', 'state_id' => $state->id]);

    $response = $this->actingAs($user)->getJson("/api/pincodes/district/{$state->id}/Delhi");
    
    $response->assertStatus(200)
        ->assertJsonCount(2)
        ->assertJsonFragment(['New Delhi'])
        ->assertJsonFragment(['Old Delhi']);
});

test('get district names endpoint returns empty array for no matches', function () {
    $user = User::factory()->create();
    $state = State::factory()->create();
    District::factory()->create(['name' => 'New Delhi', 'state_id' => $state->id]);

    $response = $this->actingAs($user)->getJson("/api/pincodes/district/{$state->id}/NonExistent");
    
    $response->assertStatus(200)
        ->assertJson([]);
});

// Tests for getTehsilNames endpoint
test('get tehsil names endpoint returns matching tehsils', function () {
    $user = User::factory()->create();
    $pincode1 = PinCode::factory()->create([
        'branch_type' => 'PO',
        'state' => 'Delhi',
        'district' => 'New Delhi',
        'name' => 'Connaught Place',
    ]);
    $pincode2 = PinCode::factory()->create([
        'branch_type' => 'PO',
        'state' => 'Delhi',
        'district' => 'New Delhi',
        'name' => 'Connaught Place Extension',
    ]);
    $pincode3 = PinCode::factory()->create([
        'branch_type' => 'PO',
        'state' => 'Maharashtra',
        'district' => 'Mumbai',
        'name' => 'Connaught Place',
    ]);

    $response = $this->actingAs($user)->getJson('/api/pincodes/tehsil/Delhi/New Delhi/Connaught');
    
    $response->assertStatus(200)
        ->assertJsonCount(2)
        ->assertJsonFragment(['Connaught Place'])
        ->assertJsonFragment(['Connaught Place Extension']);
});

test('get tehsil names endpoint returns empty array for no matches', function () {
    $user = User::factory()->create();
    PinCode::factory()->create([
        'branch_type' => 'PO',
        'state' => 'Delhi',
        'district' => 'New Delhi',
        'name' => 'Connaught Place',
    ]);

    $response = $this->actingAs($user)->getJson('/api/pincodes/tehsil/Delhi/New Delhi/NonExistent');
    
    $response->assertStatus(200)
        ->assertJson([]);
});

// Tests for getPincodesByPostName endpoint
test('get pincodes by post name endpoint returns matching post offices', function () {
    $user = User::factory()->create();
    $pincode1 = PinCode::factory()->create([
        'state' => 'Delhi',
        'district' => 'New Delhi',
        'name' => 'Connaught Place',
    ]);
    $pincode2 = PinCode::factory()->create([
        'state' => 'Delhi',
        'district' => 'New Delhi',
        'name' => 'Connaught Place Extension',
    ]);
    $pincode3 = PinCode::factory()->create([
        'state' => 'Maharashtra',
        'district' => 'Mumbai',
        'name' => 'Connaught Place',
    ]);

    $response = $this->actingAs($user)->getJson('/api/pincodes/post/Delhi/New Delhi/Connaught');
    
    $response->assertStatus(200)
        ->assertJsonCount(2)
        ->assertJsonFragment(['Connaught Place'])
        ->assertJsonFragment(['Connaught Place Extension']);
});

test('get pincodes by post name endpoint returns empty array for no matches', function () {
    $user = User::factory()->create();
    PinCode::factory()->create([
        'state' => 'Delhi',
        'district' => 'New Delhi',
        'name' => 'Connaught Place',
    ]);

    $response = $this->actingAs($user)->getJson('/api/pincodes/post/Delhi/New Delhi/NonExistent');
    
    $response->assertStatus(200)
        ->assertJson([]);
});

// Tests for getPincodeDetails endpoint
test('get pincode details endpoint returns detailed pincode information', function () {
    $user = User::factory()->create();
    $pincode = PinCode::factory()->create([
        'circle' => 'Delhi Circle',
        'pincode' => '110001',
        'region' => 'Delhi Region',
        'division' => 'Delhi Division',
        'name' => 'Connaught Place',
        'branch_type' => 'PO',
        'delivery_status' => 'Delivery',
        'district' => 'New Delhi',
        'state' => 'Delhi',
        'latitude' => 28.6139,
        'longitude' => 77.2090,
    ]);

    $response = $this->actingAs($user)->getJson('/api/pincodes/details/Delhi/New Delhi/Connaught Place');
    
    $response->assertStatus(200)
        ->assertJson([
            'status' => 'success',
            'data' => [
                'postalCircle' => 'Delhi Circle',
                'postalCode' => '110001',
                'postalRegion' => 'Delhi Region',
                'postalDivision' => 'Delhi Division',
                'postOfficeName' => 'Connaught Place',
                'branchType' => 'PO',
                'deliveryStatus' => 'Delivery',
                'districtName' => 'New Delhi',
                'stateName' => 'Delhi',
                'location' => [
                    'latitude' => 28.6139,
                    'longitude' => 77.2090,
                ],
            ],
        ]);
});

test('get pincode details endpoint returns 404 for non-existent pincode', function () {
    $user = User::factory()->create();

    $response = $this->actingAs($user)->getJson('/api/pincodes/details/Delhi/New Delhi/NonExistent');
    
    $response->assertStatus(404)
        ->assertJson([
            'status' => 'error',
            'message' => 'Pincode not found!',
        ]);
});

test('get pincode details endpoint handles partial state and district matches', function () {
    $user = User::factory()->create();
    $pincode = PinCode::factory()->create([
        'state' => 'Delhi',
        'district' => 'New Delhi',
        'name' => 'Connaught Place',
    ]);

    $response = $this->actingAs($user)->getJson('/api/pincodes/details/Del/New/Connaught Place');
    
    $response->assertStatus(200)
        ->assertJson([
            'status' => 'success',
            'data' => [
                'postOfficeName' => 'Connaught Place',
            ],
        ]);
}); 