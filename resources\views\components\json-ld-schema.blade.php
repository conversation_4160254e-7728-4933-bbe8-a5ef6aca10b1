@props([
    'appName',
    'appUrl',
    'appVersion',
    'appDescription',
    'orgName',
    'datePublished',
    'dateModified',
    'thumbnailUrl',
    'downloadUrl',
    'softwareHelpUrl',
    'featureList',
    'requirements',
    'price',
    'currency',
    'ratingValue',
    'reviewCount',
    'reviewAuthor',
    'reviewBody',
    'homeUrl',
    'logoUrl',
    'contactPhone'
])

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  "name": "{{ $appName }}",
  "url": "{{ $appUrl }}",
  "applicationCategory": "WebApplication",
  "operatingSystem": "All",
  "softwareVersion": "{{ $appVersion }}",
  "description": "{{ $appDescription }}",
  "creator": {
    "@type": "Organization",
    "name": "{{ $orgName }}"
  },
  "datePublished": "{{ $datePublished }}",
  "thumbnailUrl": "{{ $thumbnailUrl }}",
  "downloadUrl": "{{ $downloadUrl }}",
  "installUrl": "{{ $appUrl }}",
  "softwareHelp": "{{ $softwareHelpUrl }}",
  "featureList": @json($featureList),
  "requirements": "{{ $requirements }}",
  "offers": {
    "@type": "Offer",
    "price": "{{ $price }}",
    "priceCurrency": "{{ $currency }}"
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "{{ $ratingValue }}",
    "reviewCount": "{{ $reviewCount }}"
  },
  "review": {
    "@type": "Review",
    "reviewRating": {
      "@type": "Rating",
      "ratingValue": "5",
      "bestRating": "5"
    },
    "author": {
      "@type": "Person",
      "name": "{{ $reviewAuthor }}"
    },
    "reviewBody": "{{ $reviewBody }}"
  }
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "{{ $appName }}",
  "url": "{{ $appUrl }}",
  "description": "{{ $appDescription }}",
  "publisher": {
    "@type": "Organization",
    "name": "{{ $orgName }}"
  },
  "datePublished": "{{ $datePublished }}",
  "dateModified": "{{ $dateModified }}"
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Home",
      "item": "{{ $homeUrl }}"
    },
    {
      "@type": "ListItem",
      "position": 2,
      "name": "{{ $appName }}",
      "item": "{{ $appUrl }}"
    }
  ]
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "{{ $orgName }}",
  "url": "{{ $appUrl }}",
  "logo": "{{ $logoUrl }}",
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "{{ $contactPhone }}",
    "contactType": "Customer Service"
  },
  "sameAs": [
    "https://www.linkedin.com/in/nsk-multiservices/",
    "https://www.instagram.com/nskmultiservices/",
    "https://x.com/digi_nsk",
    "https://www.facebook.com/nskmultiservices/"
  ]
}
</script>