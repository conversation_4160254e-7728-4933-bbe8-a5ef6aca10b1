@extends('layouts.user')

@section('title', 'API Tokens')

@section('page-title', 'API Tokens')

@section('content')
    <div class="space-y-6">
        <!-- Page Description -->
        <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm dark:shadow-md dark:shadow-gray-800/30 p-6 border border-border-light dark:border-border-dark">
            <div class="max-w-3xl">
                <h2 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">{{ __('API Token Management') }}</h2>
                <p class="mt-1 text-sm text-text-secondary-light dark:text-text-secondary-dark">
                    {{ __('Create and manage API tokens to access our API services. Each token has a limited lifespan and can be revoked at any time.') }}
                </p>
            </div>
        </div>

        <!-- API Tokens Component -->
        <x-user-dashboard.api-tokens :tokens="$tokens" />
    </div>
@endsection
