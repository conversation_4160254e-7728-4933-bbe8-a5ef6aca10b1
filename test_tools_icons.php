<?php

require_once 'vendor/autoload.php';

// Load Laravel application
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\LandingPageSection;
use App\Models\LandingPageContent;

echo "Testing Tools Section Icons...\n\n";

// Test if tools section exists
$toolsSection = LandingPageSection::where('slug', 'tools')->first();

if ($toolsSection) {
    echo "✅ Tools section found:\n";
    echo "   - Name: {$toolsSection->name}\n";
    echo "   - Slug: {$toolsSection->slug}\n";
    echo "   - Active: " . ($toolsSection->is_active ? 'Yes' : 'No') . "\n";
    echo "   - Sort Order: {$toolsSection->sort_order}\n\n";
    
    // Get tools content
    $toolsContent = LandingPageContent::where('section_id', $toolsSection->id)
        ->where('key', 'tools')
        ->first();
    
    if ($toolsContent) {
        $tools = is_string($toolsContent->value) ? json_decode($toolsContent->value, true) : $toolsContent->value;
        
        echo "🔧 Tools with Icons:\n";
        foreach ($tools as $index => $tool) {
            echo "   Tool " . ($index + 1) . ": {$tool['title']}\n";
            echo "     - Color: {$tool['color']}\n";
            echo "     - Has Icon: " . (isset($tool['icon']) && !empty($tool['icon']) ? 'Yes' : 'No') . "\n";
            if (isset($tool['icon'])) {
                echo "     - Icon Length: " . strlen($tool['icon']) . " characters\n";
                echo "     - Icon Preview: " . substr($tool['icon'], 0, 50) . "...\n";
            }
            echo "\n";
        }
    }
    
    // Get available icons
    $availableIconsContent = LandingPageContent::where('section_id', $toolsSection->id)
        ->where('key', 'available_icons')
        ->first();
    
    if ($availableIconsContent) {
        $availableIcons = is_string($availableIconsContent->value) ? json_decode($availableIconsContent->value, true) : $availableIconsContent->value;
        
        echo "📚 Available Icons for Admin:\n";
        foreach ($availableIcons as $index => $iconData) {
            echo "   " . ($index + 1) . ". {$iconData['name']}: {$iconData['description']}\n";
        }
        echo "\n   Total Available Icons: " . count($availableIcons) . "\n";
    }
    
    echo "\n✅ Tools section icons updated successfully!\n";
    
} else {
    echo "❌ Tools section not found in database!\n";
}

echo "\n🎉 Test completed!\n";