<?php

use App\Models\User;
use App\Models\Order;
use App\Models\Payment;
use App\Models\Plan;

beforeEach(function () {
    $this->admin = User::factory()->create([
        'role' => 'admin',
        'status' => 'active'
    ]);
    
    $this->actingAs($this->admin);
});

describe('Order Management Integration', function () {
    
    describe('Order Listing', function () {
        it('displays all orders with pagination', function () {
            Order::factory()->count(15)->create();

            $response = $this->get(route('admin.orders.index'));

            $response->assertStatus(200);
            $response->assertViewIs('admin.orders.index');
            $response->assertViewHas('orders');
        });

        it('filters orders by status', function () {
            Order::factory()->count(3)->create(['status' => 'completed']);
            Order::factory()->count(2)->create(['status' => 'pending']);

            $response = $this->get(route('admin.orders.index', ['status' => 'completed']));

            $response->assertStatus(200);
        });

        it('filters orders by date range', function () {
            Order::factory()->create(['created_at' => now()->subDays(5)]);
            Order::factory()->create(['created_at' => now()->subDays(1)]);

            $response = $this->get(route('admin.orders.index', [
                'date_from' => now()->subDays(3)->format('Y-m-d'),
                'date_to' => now()->format('Y-m-d')
            ]));

            $response->assertStatus(200);
        });

        it('searches orders by user name or email', function () {
            $user = User::factory()->create(['name' => 'John Doe']);
            Order::factory()->create(['user_id' => $user->id]);

            $response = $this->get(route('admin.orders.index', ['search' => 'John']));

            $response->assertStatus(200);
        });
    });

    describe('Order Details', function () {
        it('displays order details', function () {
            $order = Order::factory()->create();
            Payment::factory()->create(['order_id' => $order->id]);

            $response = $this->get(route('admin.orders.show', $order));

            $response->assertStatus(200);
            $response->assertViewIs('admin.orders.show');
            $response->assertViewHas('order', $order);
        });

        it('shows order with user information', function () {
            $user = User::factory()->create();
            $order = Order::factory()->create(['user_id' => $user->id]);

            $response = $this->get(route('admin.orders.show', $order));

            $response->assertStatus(200);
            $response->assertViewHas('order');
            
            $viewOrder = $response->viewData('order');
            expect($viewOrder->user->id)->toBe($user->id);
        });

        it('shows order with payment information', function () {
            $order = Order::factory()->create();
            $payment = Payment::factory()->create([
                'order_id' => $order->id,
                'amount' => $order->amount
            ]);

            $response = $this->get(route('admin.orders.show', $order));

            $response->assertStatus(200);
        });
    });

    describe('Order Status Management', function () {
        it('updates order status to completed', function () {
            $order = Order::factory()->create(['status' => 'pending']);

            $response = $this->put(route('admin.orders.update', $order), [
                'status' => 'completed',
                'notes' => 'Order completed successfully'
            ]);

            $response->assertRedirect(route('admin.orders.show', $order));
            $response->assertSessionHas('success');
            
            $order->refresh();
            expect($order->status)->toBe('completed');
        });

        it('updates order status to cancelled', function () {
            $order = Order::factory()->create(['status' => 'pending']);

            $response = $this->put(route('admin.orders.update', $order), [
                'status' => 'cancelled',
                'notes' => 'Order cancelled by admin'
            ]);

            $response->assertRedirect(route('admin.orders.show', $order));
            $response->assertSessionHas('success');
            
            $order->refresh();
            expect($order->status)->toBe('cancelled');
        });

        it('validates order status update', function () {
            $order = Order::factory()->create();

            $response = $this->put(route('admin.orders.update', $order), [
                'status' => 'invalid_status'
            ]);

            $response->assertSessionHasErrors(['status']);
        });

        it('prevents updating completed order status', function () {
            $order = Order::factory()->create(['status' => 'completed']);

            $response = $this->put(route('admin.orders.update', $order), [
                'status' => 'pending'
            ]);

            $response->assertRedirect(route('admin.orders.show', $order));
            $response->assertSessionHas('error');
            
            $order->refresh();
            expect($order->status)->toBe('completed');
        });
    });

    describe('Order Analytics', function () {
        it('calculates total revenue correctly', function () {
            Order::factory()->create(['amount' => 100, 'status' => 'completed']);
            Order::factory()->create(['amount' => 200, 'status' => 'completed']);
            Order::factory()->create(['amount' => 150, 'status' => 'pending']);

            $response = $this->get(route('admin.orders.index'));

            $response->assertStatus(200);
        });

        it('shows order statistics by period', function () {
            Order::factory()->create([
                'amount' => 100,
                'status' => 'completed',
                'created_at' => now()->subDays(1)
            ]);
            Order::factory()->create([
                'amount' => 200,
                'status' => 'completed',
                'created_at' => now()->subDays(7)
            ]);

            $response = $this->get(route('admin.orders.index'));

            $response->assertStatus(200);
        });
    });

    describe('Order Export', function () {
        it('exports orders to CSV', function () {
            Order::factory()->count(5)->create();

            $response = $this->get(route('admin.orders.index', ['export' => 'csv']));

            $response->assertStatus(200);
            $response->assertHeader('Content-Type', 'text/csv; charset=UTF-8');
        });

        it('exports filtered orders', function () {
            Order::factory()->count(3)->create(['status' => 'completed']);
            Order::factory()->count(2)->create(['status' => 'pending']);

            $response = $this->get(route('admin.orders.index', [
                'export' => 'csv',
                'status' => 'completed'
            ]));

            $response->assertStatus(200);
        });
    });

    describe('Refund Management', function () {
        it('processes refund for completed order', function () {
            $order = Order::factory()->create([
                'status' => 'completed',
                'amount' => 100
            ]);

            $response = $this->post(route('admin.orders.refund', $order), [
                'refund_amount' => 100,
                'refund_reason' => 'Customer request'
            ]);

            $response->assertRedirect(route('admin.orders.show', $order));
            $response->assertSessionHas('success');
            
            $order->refresh();
            expect($order->status)->toBe('refunded');
        });

        it('validates refund amount', function () {
            $order = Order::factory()->create([
                'status' => 'completed',
                'amount' => 100
            ]);

            $response = $this->post(route('admin.orders.refund', $order), [
                'refund_amount' => 150,
                'refund_reason' => 'Customer request'
            ]);

            $response->assertSessionHasErrors(['refund_amount']);
        });

        it('prevents refund for non-completed orders', function () {
            $order = Order::factory()->create(['status' => 'pending']);

            $response = $this->post(route('admin.orders.refund', $order), [
                'refund_amount' => 50,
                'refund_reason' => 'Customer request'
            ]);

            $response->assertRedirect(route('admin.orders.show', $order));
            $response->assertSessionHas('error');
        });
    });

    describe('Order Notes', function () {
        it('adds admin notes to order', function () {
            $order = Order::factory()->create();

            $response = $this->post(route('admin.orders.add-note', $order), [
                'note' => 'Admin note about this order'
            ]);

            $response->assertRedirect(route('admin.orders.show', $order));
            $response->assertSessionHas('success');
        });

        it('validates note content', function () {
            $order = Order::factory()->create();

            $response = $this->post(route('admin.orders.add-note', $order), [
                'note' => ''
            ]);

            $response->assertSessionHasErrors(['note']);
        });
    });

    describe('Bulk Operations', function () {
        it('updates multiple order statuses', function () {
            $orders = Order::factory()->count(3)->create(['status' => 'pending']);
            $orderIds = $orders->pluck('id')->toArray();

            $response = $this->post(route('admin.orders.bulk-update'), [
                'order_ids' => $orderIds,
                'status' => 'completed'
            ]);

            $response->assertRedirect(route('admin.orders.index'));
            $response->assertSessionHas('success');
            
            foreach ($orders as $order) {
                $order->refresh();
                expect($order->status)->toBe('completed');
            }
        });

        it('validates bulk operation data', function () {
            $response = $this->post(route('admin.orders.bulk-update'), [
                'order_ids' => [],
                'status' => 'completed'
            ]);

            $response->assertSessionHasErrors(['order_ids']);
        });
    });

    describe('Authorization', function () {
        it('prevents non-admin from accessing order management', function () {
            $regularUser = User::factory()->create(['role' => 'user']);
            $this->actingAs($regularUser);

            $response = $this->get(route('admin.orders.index'));

            $response->assertStatus(403);
        });

        it('prevents guest from accessing order management', function () {
            auth()->logout();

            $response = $this->get(route('admin.orders.index'));

            $response->assertRedirect(route('admin.login'));
        });
    });
});