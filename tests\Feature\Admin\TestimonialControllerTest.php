<?php

namespace Tests\Feature\Admin;

use App\Models\Testimonial;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->admin = User::factory()->create([
        'role' => 'admin',
        'status' => 'active',
    ]);
});

test('admin can view testimonials index', function () {
    Testimonial::factory()->count(3)->create();

    $response = $this->actingAs($this->admin)
        ->get(route('admin.testimonials.index'));

    $response->assertStatus(200)
        ->assertViewIs('admin.testimonials.index')
        ->assertViewHas('testimonials');
});

test('admin can create testimonial', function () {
    $testimonialData = [
        'name' => '<PERSON>',
        'position' => 'CEO',
        'company' => 'Test Company',
        'content' => 'This is a great testimonial!',
        'rating' => 5,
        'is_active' => true,
        'sort_order' => 1,
    ];

    $response = $this->actingAs($this->admin)
        ->post(route('admin.testimonials.store'), $testimonialData);

    $response->assertRedirect(route('admin.testimonials.index'))
        ->assertSessionHas('success');

    $this->assertDatabaseHas('testimonials', $testimonialData);
});

test('admin can create testimonial with avatar', function () {
    Storage::fake('public');

    $file = UploadedFile::fake()->image('avatar.jpg');

    $testimonialData = [
        'name' => 'Jane Doe',
        'position' => 'CTO',
        'company' => 'Tech Corp',
        'content' => 'Amazing service!',
        'rating' => 5,
        'is_active' => true,
        'sort_order' => 1,
        'avatar' => $file,
    ];

    $response = $this->actingAs($this->admin)
        ->post(route('admin.testimonials.store'), $testimonialData);

    $response->assertRedirect(route('admin.testimonials.index'));

    $this->assertDatabaseHas('testimonials', [
        'name' => 'Jane Doe',
        'position' => 'CTO',
        'company' => 'Tech Corp',
    ]);

    $testimonial = Testimonial::where('name', 'Jane Doe')->first();
    $this->assertNotNull($testimonial->avatar);
});

test('admin can edit testimonial', function () {
    $testimonial = Testimonial::factory()->create();

    $response = $this->actingAs($this->admin)
        ->get(route('admin.testimonials.edit', $testimonial));

    $response->assertStatus(200)
        ->assertViewIs('admin.testimonials.edit')
        ->assertViewHas('testimonial');
});

test('admin can update testimonial', function () {
    $testimonial = Testimonial::factory()->create();

    $updatedData = [
        'name' => 'Updated Name',
        'position' => 'Updated Position',
        'company' => 'Updated Company',
        'content' => 'Updated content',
        'rating' => 4,
        'is_active' => false,
        'sort_order' => 5,
    ];

    $response = $this->actingAs($this->admin)
        ->put(route('admin.testimonials.update', $testimonial), $updatedData);

    $response->assertRedirect(route('admin.testimonials.index'))
        ->assertSessionHas('success');

    $this->assertDatabaseHas('testimonials', $updatedData);
});

test('admin can delete testimonial', function () {
    $testimonial = Testimonial::factory()->create();

    $response = $this->actingAs($this->admin)
        ->delete(route('admin.testimonials.destroy', $testimonial));

    $response->assertRedirect(route('admin.testimonials.index'))
        ->assertSessionHas('success');

    $this->assertDatabaseMissing('testimonials', ['id' => $testimonial->id]);
});

test('admin can toggle testimonial status', function () {
    $testimonial = Testimonial::factory()->create(['is_active' => true]);

    $response = $this->actingAs($this->admin)
        ->patch(route('admin.testimonials.toggle-status', $testimonial));

    $response->assertRedirect(route('admin.testimonials.index'))
        ->assertSessionHas('success');

    $this->assertDatabaseHas('testimonials', [
        'id' => $testimonial->id,
        'is_active' => false
    ]);
});

test('testimonial validation works', function () {
    $response = $this->actingAs($this->admin)
        ->post(route('admin.testimonials.store'), []);

    $response->assertSessionHasErrors(['name', 'position', 'company', 'content', 'rating']);
});

test('testimonial model scopes work', function () {
    Testimonial::factory()->create(['is_active' => true, 'sort_order' => 2]);
    Testimonial::factory()->create(['is_active' => true, 'sort_order' => 1]);
    Testimonial::factory()->create(['is_active' => false, 'sort_order' => 3]);

    $activeTestimonials = Testimonial::active()->get();
    $this->assertEquals(2, $activeTestimonials->count());

    $orderedTestimonials = Testimonial::ordered()->get();
    $this->assertEquals(1, $orderedTestimonials->first()->sort_order);
}); 