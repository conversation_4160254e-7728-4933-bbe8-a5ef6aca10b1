<?php $__env->startSection('content'); ?>
<div class="container px-4 py-6 mx-auto">
    <div class="mb-6">
        <div class="bg-white dark:bg-bg-dark rounded-lg shadow-md border border-border-light dark:border-border-dark">
            <div class="p-4 border-b border-border-light dark:border-border-dark">
                <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">Edit District</h3>
            </div>
            <div class="p-4">
                <!-- Note about URL slugs -->
                <div class="p-4 mb-6 text-sm text-blue-700 dark:text-blue-200 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    <p><strong>Note:</strong> The slug (URL) is based on the State Name, District Name, and Post Office Name. In the existing database, all entries are stored in lowercase.</p>
                    <p>Please remember that URLs with lowercase and uppercase letters can be treated differently on some systems. Therefore, always ensure that the above-mentioned values are kept in lowercase to maintain consistency and avoid SEO issues.</p>
                </div>

                <form action="<?php echo e(route('admin.districts.update', $district)); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    
                    <div class="mb-4">
                        <label for="name" class="block mb-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">District Name</label>
                        <input type="text" id="name" name="name" value="<?php echo e(old('name', $district->name)); ?>" 
                            class="w-full px-3 py-2 border border-border-light dark:border-border-dark rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                            required>
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="mb-4">
                        <label for="state_id" class="block mb-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">State</label>
                        <select id="state_id" name="state_id" 
                            class="w-full px-3 py-2 border border-border-light dark:border-border-dark rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark <?php $__errorArgs = ['state_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                            required>
                            <option value="">Select State</option>
                            <?php $__currentLoopData = $states; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $state): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($state->id); ?>" <?php echo e(old('state_id', $district->state_id) == $state->id ? 'selected' : ''); ?>>
                                    <?php echo e($state->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['state_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="mb-6">
                        <label for="featured_image" class="block mb-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Featured Image</label>
                        <?php if($district->featured_image_url !== asset('images/default-district.jpg')): ?>
                            <div class="mb-3">
                                <img src="<?php echo e($district->featured_image_url); ?>" alt="<?php echo e($district->name); ?>" class="max-h-48 rounded border border-border-light dark:border-border-dark">
                            </div>
                        <?php endif; ?>
                        <input type="file" id="featured_image" name="featured_image" 
                            class="w-full px-3 py-2 text-sm text-text-secondary-light dark:text-text-secondary-dark border border-border-light dark:border-border-dark rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark <?php $__errorArgs = ['featured_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <?php $__errorArgs = ['featured_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-sm text-text-secondary-light dark:text-text-secondary-dark">Recommended size: 800x600 pixels. Max file size: 2MB</p>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-primary-light dark:bg-primary-dark rounded-md hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                            Update District
                        </button>
                        <a href="<?php echo e(route('admin.districts.index')); ?>" class="px-4 py-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark bg-gray-200 dark:bg-bg-dark rounded-md hover:bg-gray-300 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-border-light dark:focus:ring-border-dark">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/admin/districts/edit.blade.php ENDPATH**/ ?>