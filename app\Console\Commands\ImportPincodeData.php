<?php

namespace App\Console\Commands;

use App\Models\PincodeGeo;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportPincodeData extends Command
{
    protected $signature = 'pincode:import {file}';
    protected $description = 'Import pincode data from GeoJSON file';

    public function handle()
    {
        $filePath = $this->argument('file');
        // dd($filePath);
        if (!file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        $this->info('Starting import...');
        
        $geojson = json_decode(file_get_contents($filePath), true);
        // dd($geojson);
        if (!$geojson || !isset($geojson['features'])) {
            $this->error('Invalid GeoJSON file');
            return 1;
        }

        $this->info('Number of features to import: ' . count($geojson['features']));

        $bar = $this->output->createProgressBar(count($geojson['features']));
        $bar->start();

        $batchSize = 100;
        $batch = [];
        
        foreach ($geojson['features'] as $feature) {
            $properties = $feature['properties'];
            $geometry = $feature['geometry'];
            
            $batch[] = [
                'pincode' => $properties['Pincode'],
                'office_name' => $properties['Office_Name'],
                'division' => $properties['Division'],
                'region' => $properties['Region'] ?? null,
                'circle' => $properties['Circle'],
                'geometry' => json_encode($geometry),
                'created_at' => now(),
                'updated_at' => now(),
            ];
            
            if (count($batch) >= $batchSize) {
                $this->info('Inserting batch of size: ' . count($batch));
                $this->line('First record in batch: ' . json_encode($batch[0]));
                try {
                    DB::table('pincodes_geo')->insert($batch);
                    $this->info('Batch inserted successfully.');
                } catch (\Exception $e) {
                    $this->error('Insert failed for batch of size ' . count($batch) . ': ' . $e->getMessage());
                    return 1;
                }
                $batch = [];
            }
            
            $bar->advance();
        }
        
        // Insert remaining records
        if (!empty($batch)) {
            $this->info('Inserting final batch of size: ' . count($batch));
            $this->line('First record in final batch: ' . json_encode($batch[0]));
            try {
                DB::table('pincodes_geo')->insert($batch);
                $this->info('Final batch inserted successfully.');
            } catch (\Exception $e) {
                $this->error('Insert failed for final batch of size ' . count($batch) . ': ' . $e->getMessage());
                return 1;
            }
        }
        
        $bar->finish();
        $this->info("\nImport completed successfully!");
        
        return 0;
    }
}
// php -d memory_limit=2G artisan pincode:import all_india_pincode_boundary-19312.geojson