<?php

namespace Tests\Feature\Controllers;

use App\Models\Order;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class OrderControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->admin = User::factory()->create(['role' => 'admin']);
    }

    #[Test]
    public function it_can_display_orders_list()
    {
        // Create some orders
        $orders = Order::factory()->count(3)->create();

        $response = $this->actingAs($this->admin)
            ->get(route('admin.orders.index'));

        $response->assertStatus(200)
            ->assertViewIs('admin.orders.index')
            ->assertViewHas('orders')
            ->assertSee($orders[0]->id)
            ->assertSee($orders[1]->id)
            ->assertSee($orders[2]->id);
    }

    #[Test]
    public function it_paginates_orders_list()
    {
        // Create more orders than the pagination limit
        $orders = Order::factory()->count(15)->create();

        // Test first page
        $response = $this->actingAs($this->admin)
            ->get(route('admin.orders.index'));

        $response->assertStatus(200)
            ->assertViewIs('admin.orders.index')
            ->assertViewHas('orders');

        // Get the paginated orders from the view
        $paginatedOrders = $response->viewData('orders');
        
        // Assert that we have the correct number of items per page
        $this->assertEquals(10, $paginatedOrders->count());
        
        // Assert that the first 10 orders are on the first page
        for ($i = 0; $i < 10; $i++) {
            $this->assertTrue($paginatedOrders->contains($orders[$i]));
        }

        // Assert that orders beyond the first page are not visible
        for ($i = 10; $i < 15; $i++) {
            $this->assertFalse($paginatedOrders->contains($orders[$i]));
        }

        // Test second page
        $response = $this->actingAs($this->admin)
            ->get(route('admin.orders.index', ['page' => 2]));

        $response->assertStatus(200)
            ->assertViewIs('admin.orders.index')
            ->assertViewHas('orders');

        // Get the paginated orders from the second page
        $paginatedOrders = $response->viewData('orders');
        
        // Assert that we have the remaining orders on the second page
        $this->assertEquals(5, $paginatedOrders->count());
        
        // Assert that the remaining orders are on the second page
        for ($i = 10; $i < 15; $i++) {
            $this->assertTrue($paginatedOrders->contains($orders[$i]));
        }
    }

    #[Test]
    public function it_can_display_single_order_details()
    {
        $order = Order::factory()->create();

        $response = $this->actingAs($this->admin)
            ->get(route('admin.orders.show', $order));

        $response->assertStatus(200)
            ->assertViewIs('admin.orders.show')
            ->assertViewHas('order')
            ->assertSee($order->id);
    }

    #[Test]
    public function it_can_update_order_status()
    {
        $order = Order::factory()->create(['status' => 'pending']);

        $response = $this->actingAs($this->admin)
            ->patch(route('admin.orders.update', $order), [
                'status' => 'completed',
                'notes' => 'Order completed successfully'
            ]);

        $response->assertRedirect(route('admin.orders.show', $order))
            ->assertSessionHas('success', 'Order updated successfully.');

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'status' => 'completed',
            'notes' => 'Order completed successfully'
        ]);
    }

    #[Test]
    public function it_validates_order_status_update()
    {
        $order = Order::factory()->create();

        $response = $this->actingAs($this->admin)
            ->patch(route('admin.orders.update', $order), [
                'status' => 'invalid_status',
                'notes' => 'Some notes'
            ]);

        $response->assertSessionHasErrors('status');
    }

    #[Test]
    public function it_validates_notes_length()
    {
        $order = Order::factory()->create();

        $response = $this->actingAs($this->admin)
            ->patch(route('admin.orders.update', $order), [
                'status' => 'completed',
                'notes' => str_repeat('a', 501) // Exceeds 500 characters
            ]);

        $response->assertSessionHasErrors('notes');
    }

    #[Test]
    public function it_can_delete_an_order()
    {
        $order = Order::factory()->create();

        $response = $this->actingAs($this->admin)
            ->delete(route('admin.orders.destroy', $order));

        $response->assertRedirect(route('admin.orders.index'))
            ->assertSessionHas('success', 'Order deleted successfully.');

        $this->assertDatabaseMissing('orders', ['id' => $order->id]);
    }

    #[Test]
    public function non_admin_users_cannot_access_orders_list()
    {
        $user = User::factory()->create(['role' => 'user']);

        $response = $this->actingAs($user)
            ->get(route('admin.orders.index'));

        $response->assertStatus(403);
    }

    #[Test]
    public function non_admin_users_cannot_view_order_details()
    {
        $user = User::factory()->create(['role' => 'user']);
        $order = Order::factory()->create();

        $response = $this->actingAs($user)
            ->get(route('admin.orders.show', $order));

        $response->assertStatus(403);
    }

    #[Test]
    public function non_admin_users_cannot_update_orders()
    {
        $user = User::factory()->create(['role' => 'user']);
        $order = Order::factory()->create();

        $response = $this->actingAs($user)
            ->patch(route('admin.orders.update', $order), [
                'status' => 'completed',
                'notes' => 'Some notes'
            ]);

        $response->assertStatus(403);
    }

    #[Test]
    public function non_admin_users_cannot_delete_orders()
    {
        $user = User::factory()->create(['role' => 'user']);
        $order = Order::factory()->create();

        $response = $this->actingAs($user)
            ->delete(route('admin.orders.destroy', $order));

        $response->assertStatus(403);
    }

    #[Test]
    public function it_requires_authentication_to_access_orders()
    {
        $response = $this->get(route('admin.orders.index'));
        $response->assertRedirect(route('admin.login'));

        $order = Order::factory()->create();
        
        $response = $this->get(route('admin.orders.show', $order));
        $response->assertRedirect(route('admin.login'));

        $response = $this->patch(route('admin.orders.update', $order), [
            'status' => 'completed',
            'notes' => 'Some notes'
        ]);
        $response->assertRedirect(route('admin.login'));

        $response = $this->delete(route('admin.orders.destroy', $order));
        $response->assertRedirect(route('admin.login'));
    }
} 