<?php

namespace App\Http\Middleware;

use App\Services\Payment\WebhookSecurityService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class WebhookSecurityMiddleware
{
    private WebhookSecurityService $securityService;

    public function __construct(WebhookSecurityService $securityService)
    {
        $this->securityService = $securityService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $ip = $request->ip();

        // Check if IP is blocked
        if ($this->securityService->isIPBlocked($ip)) {
            Log::warning('Blocked IP attempted webhook access', [
                'ip' => $ip,
                'user_agent' => $request->userAgent(),
                'url' => $request->fullUrl()
            ]);

            return response()->json([
                'error' => 'Access denied'
            ], Response::HTTP_FORBIDDEN);
        }

        // Detect DDoS attacks
        $ddosCheck = $this->securityService->detectDDoSAttack($request);
        if (!$ddosCheck['allowed']) {
            Log::alert('DDoS attack detected on webhook endpoint', [
                'ip' => $ip,
                'risk_level' => $ddosCheck['risk_level'],
                'reason' => $ddosCheck['reason'],
                'patterns' => $ddosCheck['patterns'] ?? []
            ]);

            return response()->json([
                'error' => 'Request blocked due to suspicious activity'
            ], Response::HTTP_TOO_MANY_REQUESTS);
        }

        // Apply rate limiting (this will be gateway-specific in the controller)
        // We'll add a basic IP-based rate limit here as a fallback
        $rateLimitKey = 'webhook_ip_' . $ip;
        if (\Illuminate\Support\Facades\RateLimiter::tooManyAttempts($rateLimitKey, 100)) {
            $retryAfter = \Illuminate\Support\Facades\RateLimiter::availableIn($rateLimitKey);
            
            Log::warning('Webhook rate limit exceeded', [
                'ip' => $ip,
                'retry_after' => $retryAfter
            ]);

            return response()->json([
                'error' => 'Rate limit exceeded',
                'retry_after' => $retryAfter
            ], Response::HTTP_TOO_MANY_REQUESTS);
        }

        \Illuminate\Support\Facades\RateLimiter::hit($rateLimitKey, 3600); // 1 hour window

        // Add security headers to the request for downstream processing
        $request->attributes->set('webhook_security_check', [
            'ip' => $ip,
            'risk_level' => $ddosCheck['risk_level'] ?? 0,
            'timestamp' => now()
        ]);

        return $next($request);
    }
}