<?php $__env->startSection('content'); ?>
    <div class="container mx-auto px-4 py-6">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-xl md:text-2xl font-bold mb-4 md:mb-6 text-text-primary-light dark:text-text-primary-dark">Search
                Results for "<?php echo e(e($query)); ?>"</h1>
            <?php
                $it_causes_error = true;
            ?>
            
            <div class="mb-6">
                <form action="<?php echo e(route('search')); ?>" method="GET" class="flex flex-col gap-4">
                    <div class="w-full">
                        <input type="text" name="query" value="<?php echo e(e($query)); ?>"
                            class="w-full px-4 py-2 border border-border-light dark:border-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark"
                            placeholder="Enter pincode or post office name">
                    </div>
                    <div class="flex flex-wrap gap-4">
                        <div class="flex items-center">
                            <input type="radio" id="type-pincode" name="type" value="pincode"
                                <?php echo e($type === 'pincode' ? 'checked' : ''); ?>

                                class="h-4 w-4 text-primary-light dark:text-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark bg-white dark:bg-bg-dark border-border-light dark:border-border-dark">
                            <label for="type-pincode"
                                class="ml-2 text-sm text-text-primary-light dark:text-text-primary-dark">Pincode</label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="type-name" name="type" value="name"
                                <?php echo e($type === 'name' ? 'checked' : ''); ?>

                                class="h-4 w-4 text-primary-light dark:text-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark bg-white dark:bg-bg-dark border-border-light dark:border-border-dark">
                            <label for="type-name"
                                class="ml-2 text-sm text-text-primary-light dark:text-text-primary-dark">Post Office</label>
                        </div>
                        <button type="submit"
                            class="px-4 py-2 bg-primary-light dark:bg-primary-dark text-white rounded-md hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark w-full md:w-auto transition-colors">
                            Search
                        </button>
                    </div>
                </form>
            </div>

            <?php if($results && count($results) > 0): ?>
                <div
                    class="bg-white dark:bg-bg-dark shadow-md rounded-lg overflow-hidden border border-border-light dark:border-border-dark">
                    <!-- Desktop Table (hidden on mobile) -->
                    <div class="hidden md:block">
                        <table class="min-w-full divide-y divide-border-light dark:divide-border-dark">
                            <thead class="bg-gray-50 dark:bg-gray-800">
                                <tr>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                        Pincode</th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                        Post Office</th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                        District</th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                        State</th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                        Details</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                                <?php $__currentLoopData = $results; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $result): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                        <td
                                            class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                            <?php echo e($result->pincode); ?></td>
                                        <td
                                            class="px-6 py-4 whitespace-nowrap text-sm font-medium text-text-primary-light dark:text-text-primary-dark">
                                            <?php echo e(ucfirst($result->name)); ?></td>
                                        <td
                                            class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                            <?php echo e(ucfirst($result->district)); ?></td>
                                        <td
                                            class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                            <?php echo e(ucfirst($result->state)); ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <a href="<?php echo e(route('pincodes.details-by-name', ['state' => $result->state, 'district' => $result->district, 'name' => $result->name])); ?>"
                                                class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light">View
                                                Details</a>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Mobile Card View (hidden on desktop) -->
                    <div class="md:hidden">
                        <div class="divide-y divide-border-light dark:divide-border-dark">
                            <?php $__currentLoopData = $results; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $result): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="p-4">
                                    <div class="flex justify-between items-center mb-2">
                                        <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">
                                            <?php echo e(ucfirst($result->name)); ?></h3>
                                        <span
                                            class="bg-gray-100 dark:bg-bg-dark text-gray-800 dark:text-text-primary-dark text-sm font-medium px-2.5 py-0.5 rounded border border-border-light dark:border-border-dark"><?php echo e($result->pincode); ?></span>
                                    </div>
                                    <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1">
                                        <span class="font-medium">District:</span> <?php echo e(ucfirst($result->district)); ?>

                                    </div>
                                    <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-3">
                                        <span class="font-medium">State:</span> <?php echo e(ucfirst($result->state)); ?>

                                    </div>
                                    <div class="mt-2">
                                        <a href="<?php echo e(route('pincodes.details-by-name', ['state' => $result->state, 'district' => $result->district, 'name' => $result->name])); ?>"
                                            class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light text-sm font-medium">View
                                            Details</a>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <?php echo e($results->appends(['query' => $query, 'type' => $type])->links()); ?>

                </div>
            <?php elseif($query): ?>
                <div
                    class="bg-white dark:bg-bg-dark p-4 md:p-6 rounded-lg shadow-md border border-border-light dark:border-border-dark">
                    <p class="text-text-primary-light dark:text-text-primary-dark">No results found for
                        "<?php echo e(e($query)); ?>". Please try a different search term.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/pincodes/search-results.blade.php ENDPATH**/ ?>