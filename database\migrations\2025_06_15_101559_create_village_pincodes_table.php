<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('village_pincodes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('state_id')->constrained('pin_states')->onDelete('cascade');
            $table->string('state_name_en');
            $table->string('district_code')->nullable();
            $table->string('district_name_en');
            $table->string('subdistrict_code')->nullable();
            $table->string('subdistrict_name_en')->nullable();
            $table->string('village_code')->nullable();
            $table->string('village_name_en');
            $table->string('pincode', 6);
            $table->timestamps();

            // Add indexes for better performance
            $table->index('state_id');
            $table->index('district_code');
            $table->index('pincode');
            $table->index('village_name_en');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('village_pincodes');
    }
};
