<?php

use App\Models\User;
use App\Http\Middleware\AdminMiddleware;
use Illuminate\Http\Request;

uses()->group('middleware');

beforeEach(function () {
    $this->middleware = new AdminMiddleware();
    $this->next = function ($request) {
        return response('Next middleware called');
    };
});

it('allows admin users with active status to pass', function () {
    $user = User::factory()->create([
        'role' => 'admin',
        'status' => 'active'
    ]);
    
    $this->actingAs($user);
    
    $request = Request::create('/admin/dashboard');
    $response = $this->middleware->handle($request, $this->next);
    
    expect($response->getContent())->toBe('Next middleware called');
});

it('blocks unauthenticated users', function () {
    $request = Request::create('/admin/dashboard');
    $response = $this->middleware->handle($request, $this->next);
    
    expect($response->getStatusCode())->toBe(403)
        ->and($response->getContent())->toBeJson()
        ->and(json_decode($response->getContent(), true))->toBe(['error' => 'Unauthorized access']);
});

it('blocks non admin users', function () {
    $user = User::factory()->create([
        'role' => 'user',
        'status' => 'active'
    ]);
    
    $this->actingAs($user);
    
    $request = Request::create('/admin/dashboard');
    $response = $this->middleware->handle($request, $this->next);
    
    expect($response->getStatusCode())->toBe(403)
        ->and($response->getContent())->toBeJson()
        ->and(json_decode($response->getContent(), true))->toBe(['error' => 'Unauthorized access']);
});

it('blocks inactive admin users', function () {
    $user = User::factory()->create([
        'role' => 'admin',
        'status' => 'inactive'
    ]);
    
    $this->actingAs($user);
    
    $request = Request::create('/admin/dashboard');
    $response = $this->middleware->handle($request, $this->next);
    
    expect($response->getStatusCode())->toBe(403)
        ->and($response->getContent())->toBeJson()
        ->and(json_decode($response->getContent(), true))->toBe(['error' => 'Unauthorized access']);
});

it('returns json response for api requests when unauthorized', function () {
    $user = User::factory()->create([
        'role' => 'user',
        'status' => 'active'
    ]);
    
    $this->actingAs($user);
    
    $request = Request::create('/api/admin/dashboard');
    $request->headers->set('Accept', 'application/json');
    
    $response = $this->middleware->handle($request, $this->next);
    
    expect($response->getStatusCode())->toBe(403)
        ->and($response->getContent())->toBeJson()
        ->and(json_decode($response->getContent(), true))->toBe(['error' => 'Unauthorized access']);
});

it('returns json response for json requests when unauthorized', function () {
    $user = User::factory()->create([
        'role' => 'user',
        'status' => 'active'
    ]);
    
    $this->actingAs($user);
    
    $request = Request::create('/admin/dashboard');
    $request->headers->set('Accept', 'application/json');
    
    $response = $this->middleware->handle($request, $this->next);
    
    expect($response->getStatusCode())->toBe(403)
        ->and($response->getContent())->toBeJson()
        ->and(json_decode($response->getContent(), true))->toBe(['error' => 'Unauthorized access']);
});

it('returns json response in testing environment when unauthorized', function () {
    $user = User::factory()->create([
        'role' => 'user',
        'status' => 'active'
    ]);
    
    $this->actingAs($user);
    
    $request = Request::create('/admin/dashboard');
    $response = $this->middleware->handle($request, $this->next);
    
    expect($response->getStatusCode())->toBe(403)
        ->and($response->getContent())->toBeJson()
        ->and(json_decode($response->getContent(), true))->toBe(['error' => 'Unauthorized access']);
});

it('redirects to login with error message for web requests in production', function () {
    $originalEnv = app()->environment();
    
    app()->detectEnvironment(function () {
        return 'production';
    });
    
    $user = User::factory()->create([
        'role' => 'user',
        'status' => 'active'
    ]);
    
    $this->actingAs($user);
    
    $request = Request::create('/admin/dashboard');
    $response = $this->middleware->handle($request, $this->next);
    
    expect($response->getStatusCode())->toBe(302)
        ->and($response->getTargetUrl())->toContain('admin/login');
    
    app()->detectEnvironment(function () use ($originalEnv) {
        return $originalEnv;
    });
});

it('handles null user gracefully', function () {
    $request = Request::create('/admin/dashboard');
    $request->setUserResolver(function () {
        return null;
    });
    
    $response = $this->middleware->handle($request, $this->next);
    
    expect($response->getStatusCode())->toBe(403)
        ->and($response->getContent())->toBeJson()
        ->and(json_decode($response->getContent(), true))->toBe(['error' => 'Unauthorized access']);
});