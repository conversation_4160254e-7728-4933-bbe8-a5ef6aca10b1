@extends('admin.layouts.admin')

@section('title', 'Manage Reviews')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-xl sm:text-2xl font-bold text-text-primary-light dark:text-text-primary-dark">Manage Reviews</h1>
    </div>

    @if(session('status'))
        <div class="mb-4 bg-green-100 dark:bg-green-900/20 border-l-4 border-green-500 dark:border-green-700 text-green-700 dark:text-green-200 p-4">
            {{ session('status') }}
        </div>
    @endif

    <!-- Mobile Card View (hidden on desktop) -->
    <div class="block lg:hidden space-y-4">
        @forelse($reviews as $review)
        <div class="bg-white dark:bg-bg-dark rounded-lg shadow border border-border-light dark:border-border-dark p-4">
            <div class="flex justify-between items-start mb-3">
                <div class="flex-1">
                    <div class="font-medium text-text-primary-light dark:text-text-primary-dark text-lg">
                        {{ $review->user->name }}
                    </div>
                    <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark mt-1">
                        {{ $review->created_at->format('M d, Y') }}
                    </div>
                </div>
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    {{ $review->status === 'approved' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' : 
                       ($review->status === 'rejected' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400' : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400') }}">
                    {{ ucfirst($review->status) }}
                </span>
            </div>
            
            <div class="space-y-3 mb-4">
                <div>
                    <span class="text-text-secondary-light dark:text-text-secondary-dark font-medium text-sm">Pincode:</span>
                    <span class="text-text-primary-light dark:text-text-primary-dark ml-2">{{ $review->pincode->pincode ?? 'N/A' }}</span>
                </div>
                <div>
                    <span class="text-text-secondary-light dark:text-text-secondary-dark font-medium text-sm">Review:</span>
                    <div class="text-text-primary-light dark:text-text-primary-dark mt-1 text-sm leading-relaxed">
                        {{ Str::limit($review->review, 150) }}
                    </div>
                </div>
            </div>
            
            <div class="pt-3 border-t border-border-light dark:border-border-dark">
                <div class="flex flex-wrap gap-2">
                    <a href="{{ route('admin.reviews.edit', $review) }}" class="inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-primary-light dark:bg-primary-dark rounded-md hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none">
                        Edit
                    </a>
                    
                    @if($review->status === 'pending')
                        <form action="{{ route('admin.reviews.approve', $review) }}" method="POST" class="inline">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-green-600 dark:bg-green-700 rounded-md hover:bg-green-700 dark:hover:bg-green-600 focus:outline-none">
                                Approve
                            </button>
                        </form>
                        
                        <form action="{{ route('admin.reviews.reject', $review) }}" method="POST" class="inline">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-red-600 dark:bg-red-700 rounded-md hover:bg-red-700 dark:hover:bg-red-600 focus:outline-none">
                                Reject
                            </button>
                        </form>
                    @endif

                    <form action="{{ route('admin.reviews.destroy', $review) }}" method="POST" class="inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-red-600 dark:bg-red-700 rounded-md hover:bg-red-700 dark:hover:bg-red-600 focus:outline-none" onclick="return confirm('Are you sure you want to delete this review?')">
                            Delete
                        </button>
                    </form>
                </div>
            </div>
        </div>
        @empty
        <div class="bg-white dark:bg-bg-dark rounded-lg shadow border border-border-light dark:border-border-dark p-8 text-center">
            <div class="text-text-secondary-light dark:text-text-secondary-dark">
                No reviews found.
            </div>
        </div>
        @endforelse
    </div>

    <!-- Desktop Table View (hidden on mobile) -->
    <div class="hidden lg:block bg-white dark:bg-bg-dark rounded-lg shadow overflow-hidden border border-border-light dark:border-border-dark">
        <table class="min-w-full divide-y divide-border-light dark:divide-border-dark">
            <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">User</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Pincode</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Review</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                @forelse($reviews as $review)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-text-primary-light dark:text-text-primary-dark">
                            {{ $review->user->name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                            {{ $review->pincode->pincode ?? 'N/A' }}
                        </td>
                        <td class="px-6 py-4 text-sm text-text-secondary-light dark:text-text-secondary-dark">
                            {{ Str::limit($review->review, 100) }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {{ $review->status === 'approved' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' : 
                                   ($review->status === 'rejected' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400' : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400') }}">
                                {{ ucfirst($review->status) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                            {{ $review->created_at->format('M d, Y') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <a href="{{ route('admin.reviews.edit', $review) }}" class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light">Edit</a>
                            
                            @if($review->status === 'pending')
                                <form action="{{ route('admin.reviews.approve', $review) }}" method="POST" class="inline">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit" class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300">Approve</button>
                                </form>
                                
                                <form action="{{ route('admin.reviews.reject', $review) }}" method="POST" class="inline">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">Reject</button>
                                </form>
                            @endif

                            <form action="{{ route('admin.reviews.destroy', $review) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to delete this review?')">
                                    Delete
                                </button>
                            </form>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-text-secondary-light dark:text-text-secondary-dark">
                            No reviews found.
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <div class="mt-4">
        {{ $reviews->links() }}
    </div>
</div>
@endsection