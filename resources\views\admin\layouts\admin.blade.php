<?php $errors = $errors ?? new \Illuminate\Support\ViewErrorBag; ?>
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - Admin</title>

    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <link rel="icon" href="{{ uploads_url(get_setting('site_favicon')) }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    <script>
        (function() {
            try {
                var theme = localStorage.getItem('theme');
                if (theme === 'dark' || (!theme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                    document.documentElement.classList.add('dark');
                    document.documentElement.setAttribute('data-theme', 'dark');
                } else {
                    document.documentElement.classList.remove('dark');
                    document.documentElement.setAttribute('data-theme', 'light');
                }
            } catch (e) {}
        })();
    </script>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @stack('styles')

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>

<body class="font-sans antialiased bg-bg-light dark:bg-bg-dark" x-data="{ sidebarOpen: false }">
    <div class="min-h-screen flex">
        <!-- Sidebar -->
        @include('admin.layouts.sidebar')

        <!-- Main Content -->
        <div class="flex-1 flex flex-col">
            <!-- Top Navigation - Fixed sticky header structure -->
            <header class="bg-bg-light dark:bg-bg-dark shadow-sm border-b border-border-light dark:border-border-dark sticky top-0 z-50">
                <div class="flex items-center justify-between px-4 py-3">
                    <!-- Mobile menu button -->
                    <button @click="sidebarOpen = !sidebarOpen" class="lg:hidden p-2 rounded-md text-text-secondary-light dark:text-text-secondary-dark hover:text-primary-light dark:hover:text-primary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-light dark:focus:ring-primary-dark">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>

                    <!-- Page title -->
                    <div class="flex-1 px-4 lg:px-0">
                        <h1 class="text-2xl font-semibold text-text-primary-light dark:text-text-primary-dark">@yield('title', 'Dashboard')</h1>
                    </div>

                    <!-- User menu and Theme Switcher -->
                    <div class="flex items-center space-x-4">
                        <!-- Theme Switcher Button (Admin) -->
                        <button onclick="toggleTheme()"
                        class="relative w-16 h-8 bg-border-light/30 dark:bg-border-dark/30 rounded-full p-1 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                        <div
                            class="absolute left-1 top-1 w-6 h-6 bg-accent-light dark:bg-primary-dark rounded-full flex items-center justify-center transition-transform duration-300 dark:translate-x-8">
                            <span class="text-sm dark:hidden">☀️</span>
                            <span class="text-sm hidden dark:inline">🌙</span>
                        </div>
                        <div class="flex justify-between items-center px-2">
                            <span class="text-xs text-text-secondary-light dark:text-text-secondary-dark">☀️</span>
                            <span class="text-xs text-text-secondary-light dark:text-text-secondary-dark">🌙</span>
                        </div>
                    </button>
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                                <span class="sr-only">Open user menu</span>
                                <div class="h-8 w-8 rounded-full bg-primary-light dark:bg-primary-dark flex items-center justify-center">
                                    <span class="text-text-primary-dark dark:text-text-primary-light font-medium">{{ auth()->user()->name[0] ?? 'A' }}</span>
                                </div>
                            </button>

                            <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-bg-light dark:bg-bg-dark ring-1 ring-border-light dark:ring-border-dark z-50">
                                <div class="py-1">
                                    <a href="{{ route('admin.profile.index') }}" class="block px-4 py-2 text-sm text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10">Profile</a>
                                    <form method="POST" action="{{ route('admin.logout') }}">
                                        @csrf
                                        <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10">Logout</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto bg-bg-light dark:bg-bg-dark">
                <div class="py-6">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        @if(session('success'))
                            <div class="mb-4 bg-accent-light/10 dark:bg-accent-dark/10 border border-accent-light dark:border-accent-dark text-accent-light dark:text-accent-dark px-4 py-3 rounded">
                                {{ session('success') }}
                            </div>
                        @endif

                        @if(session('error'))
                            <div class="mb-4 bg-accent-dark/10 dark:bg-accent-light/10 border border-accent-dark dark:border-accent-light text-accent-dark dark:text-accent-light px-4 py-3 rounded">
                                {{ session('error') }}
                            </div>
                        @endif

                        @if($errors->any())
                            <div class="mb-4 bg-accent-dark/10 dark:bg-accent-light/10 border border-accent-dark dark:border-accent-light text-accent-dark dark:text-accent-light px-4 py-3 rounded">
                                <ul class="list-disc list-inside">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        {{ $slot ?? '' }}
                        @yield('content')
                    </div>
                </div>
            </main>
        </div>
    </div>

    @stack('scripts')
    <!-- Theme Toggle Script for Admin Panel -->
    <script>
        function setThemeIcon(isDark) {
            const icon = isDark ? '☀️' : '🌙';
            const adminIcon = document.getElementById('admin-theme-switcher-icon');
            if (adminIcon) adminIcon.textContent = icon;
        }
        function toggleTheme() {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark');
            if (isDark) {
                html.classList.remove('dark');
                html.setAttribute('data-theme', 'light');
                localStorage.setItem('theme', 'light');
            } else {
                html.classList.add('dark');
                html.setAttribute('data-theme', 'dark');
                localStorage.setItem('theme', 'dark');
            }
            setThemeIcon(!isDark);
        }
        document.addEventListener('DOMContentLoaded', function() {
            setThemeIcon(document.documentElement.classList.contains('dark'));
        });
    </script>
</body>

</html>