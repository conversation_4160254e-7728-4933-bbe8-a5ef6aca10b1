<?php

namespace Tests\Feature\Models;

use App\Models\CourierDict;
use Illuminate\Support\Str;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

uses(RefreshDatabase::class);

beforeEach(function() {
    // The CourierDict model uses RLIKE, which is not available in SQLite by default.
    // We add a custom REGEXP function to the SQLite connection for testing purposes.
    if (DB::getDriverName() === 'sqlite') {
        DB::connection()->getPdo()->sqliteCreateFunction('REGEXP', function ($pattern, $value) {
            mb_regex_encoding('UTF-8');
            // This is a simplified version of RLIKE, but sufficient for the model's slug generation.
            // It matches if the value starts with the pattern.
            return preg_match('/^' . $pattern . '/', $value) ? 1 : 0;
        });
    }
});

it('generates a slug when creating a new term', function () {
    $term = CourierDict::factory()->create(['term' => 'Test Term']);
    expect($term->slug)->toBe('test-term');
});

it('generates a unique slug when the slug already exists', function () {
    CourierDict::factory()->create(['term' => 'Test Term']); // slug: test-term
    $newTerm = CourierDict::factory()->create(['term' => 'Test Term']);
    expect($newTerm->slug)->toBe('test-term-1');

    $anotherTerm = CourierDict::factory()->create(['term' => 'Test Term']);
    expect($anotherTerm->slug)->toBe('test-term-2');
});

it('updates the slug when the term is updated', function () {
    $term = CourierDict::factory()->create(['term' => 'Old Term']);
    expect($term->slug)->toBe('old-term');

    $term->update(['term' => 'New Term']);
    expect($term->slug)->toBe('new-term');
});

it('does not update the slug when the term is not updated', function () {
    $term = CourierDict::factory()->create(['term' => 'Same Term']);
    $originalSlug = $term->slug;

    $term->update(['description' => 'New Description']);
    expect($term->slug)->toBe($originalSlug);
});

it('generates a unique slug on update when the new slug exists', function () {
    CourierDict::factory()->create(['term' => 'Existing Term']); // slug: existing-term
    $termToUpdate = CourierDict::factory()->create(['term' => 'Some Other Term']);

    $termToUpdate->update(['term' => 'Existing Term']);
    expect($termToUpdate->slug)->toBe('existing-term-1');
});


it('gets related terms by tag', function () {
    $tag = 'shipping';
    $term1 = CourierDict::factory()->create(['tag' => $tag]);
    CourierDict::factory()->create(['tag' => $tag]);
    CourierDict::factory()->create(['tag' => $tag]);
    CourierDict::factory()->create(['tag' => $tag]); // a 3rd related one
    CourierDict::factory()->create(['tag' => 'billing']); // unrelated

    $relatedTerms = $term1->relatedTerms();

    expect($relatedTerms)->toHaveCount(2);
    expect($relatedTerms->pluck('id'))->not->toContain($term1->id);
    expect($relatedTerms->pluck('tag')->unique()->all())->toEqual([$tag]);
});

it('gets random related terms', function () {
    $term = CourierDict::factory()->create();
    CourierDict::factory()->count(15)->create();

    $randomTerms = $term->randomRelatedTerms();

    expect($randomTerms)->toHaveCount(10);
    expect($randomTerms->pluck('id'))->not->toContain($term->id);
});

it('can be searched by term', function () {
    $term1 = CourierDict::factory()->create(['term' => 'Find Me']);
    CourierDict::factory()->create(['term' => 'Ignore Me']);

    $results = CourierDict::search('Find')->get();

    expect($results)->toHaveCount(1);
    expect($results->first()->id)->toBe($term1->id);
});

it('can be searched by description', function () {
    $term1 = CourierDict::factory()->create(['description' => 'Description has the word']);
    CourierDict::factory()->create(['description' => 'Another one']);

    $results = CourierDict::search('word')->get();

    expect($results)->toHaveCount(1);
    expect($results->first()->id)->toBe($term1->id);
});

it('can be searched by tag exclusively', function () {
    // This term should be found ONLY by its tag.
    $term1 = CourierDict::factory()->create(['tag' => 'exclusive-tag', 'term' => 'Term A', 'description' => 'Desc A']);
    
    // This term should be ignored.
    CourierDict::factory()->create(['tag' => 'other-tag']);

    // This term has the same tag, but the search string is in its term, so it should be found by the LIKE search.
    $term3 = CourierDict::factory()->create(['tag' => 'exclusive-tag', 'term' => 'Term containing exclusive-tag']);

    // When searching for 'exclusive-tag' it should find both term1 (by tag) and term3 (by 'LIKE').
    $resultsWithLike = CourierDict::search('exclusive-tag')->get();
    expect($resultsWithLike)->toHaveCount(2);

    // However, if we search for a tag that is NOT in the term/description, it should be exclusive.
    $resultsExclusive = CourierDict::search('exclusive-tag')->where('id', $term1->id)->get();
    expect($resultsExclusive)->toHaveCount(1)
        ->and($resultsExclusive->first()->id)->toBe($term1->id);
});