<?php 

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePinCodesTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pin_codes', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('circle')->nullable();
            $table->integer('pincode')->nullable();
            $table->string('region')->nullable();
            $table->string('division')->nullable();
            $table->string('name')->nullable();
            $table->string('branch_type', 50)->nullable();
            $table->string('delivery_status', 50)->nullable();
            $table->string('district')->nullable();
            $table->string('state')->nullable();
            $table->string('latitude')->nullable();
            $table->string('longitude')->nullable();
            $table->timestamp('created_at')->nullable()->useCurrent();
            $table->timestamp('updated_at')->nullable()->useCurrentOnUpdate()->useCurrent();

            $table->index('latitude');
            $table->index('longitude');
            $table->index('pincode');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pin_codes');
    }
}
