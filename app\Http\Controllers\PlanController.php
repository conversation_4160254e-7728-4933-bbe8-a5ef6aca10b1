<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PlanController extends Controller
{
    /**
     * Display a listing of the plans for only logged in users inside the user dashboard.
     */
    public function index()
    {
        $plans = Plan::where('is_active', true)
            ->orderBy('price', 'asc')
            ->get();
        $currentPlanId = Auth::user()->getCurrentPlan();

        return view('user.plans.index', compact('plans', 'currentPlanId'));
    }

    public function publicPlans()
    {
        $plans = Plan::where('is_active', true)
            ->orderBy('price', 'asc')
            ->get();

        return view('plans.public-view', compact('plans'));
    }
}