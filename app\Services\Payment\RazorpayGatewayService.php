<?php

namespace App\Services\Payment;

use App\Models\Order;
use App\Models\Payment;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class RazorpayGatewayService extends BasePaymentGatewayService
{
    private string $keyId;
    private string $keySecret;
    private string $webhookSecret;
    private string $baseUrl;

    public function __construct($gateway)
    {
        parent::__construct($gateway);
        
        $this->keyId = $this->config['key_id'];
        $this->keySecret = $this->config['key_secret'];
        $this->webhookSecret = $this->config['webhook_secret'] ?? '';
        $this->baseUrl = 'https://api.razorpay.com/v1';
    }

    /**
     * Create a payment for the given order.
     */
    public function createPayment(Order $order): PaymentResponse
    {
        try {
            $this->validateOrder($order);

            // Create payment record first
            $payment = $this->createPaymentRecord($order);

            // Create Razorpay order
            $razorpayOrder = $this->createRazorpayOrder($order, $payment);

            if (!$razorpayOrder) {
                $this->handleFailedPayment($payment, 'Failed to create Razorpay order');
                return PaymentResponse::error('Failed to create payment order');
            }

            // Update payment record with Razorpay order ID
            $this->updatePaymentRecord($payment, [
                'gateway_order_id' => $razorpayOrder['id'],
                'payment_details' => $razorpayOrder,
            ]);

            // Generate checkout URL/data
            $checkoutData = $this->generateCheckoutData($order, $payment, $razorpayOrder);

            $this->logGatewayInteraction('create_payment', [
                'order_id' => $order->id,
                'payment_id' => $payment->id,
                'razorpay_order_id' => $razorpayOrder['id'],
            ]);

            return PaymentResponse::success([
                'payment_id' => $payment->id,
                'order_id' => $razorpayOrder['id'],
                'checkout_url' => null, // Razorpay uses client-side checkout
                'status' => 'pending',
                'message' => 'Payment order created successfully',
                'gateway_response' => $razorpayOrder,
                'metadata' => $checkoutData,
            ]);

        } catch (\Exception $e) {
            if (isset($payment)) {
                $this->handleFailedPayment($payment, $e->getMessage());
            }
            throw $this->handleGatewayException($e, 'create_payment');
        }
    }

    /**
     * Verify a payment by its ID.
     */
    public function verifyPayment(string $paymentId): PaymentResponse
    {
        try {
            // Find payment record
            $payment = Payment::where('gateway_payment_id', $paymentId)
                            ->orWhere('id', $paymentId)
                            ->first();

            if (!$payment) {
                return PaymentResponse::error('Payment not found');
            }

            // Fetch payment from Razorpay
            $razorpayPayment = $this->fetchRazorpayPayment($paymentId);

            if (!$razorpayPayment) {
                return PaymentResponse::error('Payment not found in gateway');
            }

            // Update payment record
            $status = $this->mapRazorpayStatus($razorpayPayment['status']);
            $updateData = [
                'gateway_payment_id' => $razorpayPayment['id'],
                'payment_status' => $status,
                'payment_details' => array_merge($payment->payment_details ?? [], $razorpayPayment),
            ];

            if ($status === Payment::STATUS_COMPLETED) {
                $updateData['paid_at'] = now();
                $this->handleSuccessfulPayment($payment, $razorpayPayment);
            } elseif ($status === Payment::STATUS_FAILED) {
                $updateData['failed_reason'] = $razorpayPayment['error_description'] ?? 'Payment failed';
                $this->handleFailedPayment($payment, $updateData['failed_reason'], $razorpayPayment);
            }

            $this->updatePaymentRecord($payment, $updateData);

            $this->logGatewayInteraction('verify_payment', [
                'payment_id' => $paymentId,
                'status' => $status,
            ]);

            return PaymentResponse::success([
                'payment_id' => $payment->id,
                'order_id' => $payment->gateway_order_id,
                'status' => $status,
                'message' => 'Payment verification completed',
                'gateway_response' => $razorpayPayment,
            ]);

        } catch (\Exception $e) {
            throw $this->handleGatewayException($e, 'verify_payment');
        }
    }

    /**
     * Handle webhook notifications from Razorpay.
     */
    public function handleWebhook(Request $request): WebhookResponse
    {
        try {
            // Extract event type first
            $payload = $request->all();
            $event = $payload['event']['event'] ?? $payload['event'] ?? 'unknown';

            // Log webhook event
            $webhookLog = $this->logWebhookEvent($request, $event);

            // Verify webhook signature
            if (!$this->verifyWebhookSignature($request)) {
                $webhookLog->markAsFailed('Invalid webhook signature');
                return WebhookResponse::error('Invalid webhook signature');
            }
            $paymentData = $payload['payload']['payment']['entity'] ?? null;

            // For unknown events, we can be more lenient about the payload structure
            if (!$paymentData && !str_starts_with($event, 'unknown')) {
                $webhookLog->markAsFailed('Invalid webhook payload');
                return WebhookResponse::error('Invalid webhook payload');
            }

            // Handle unknown events gracefully without requiring payment data
            if (!$paymentData && str_starts_with($event, 'unknown')) {
                $webhookLog->markAsProcessed();
                return WebhookResponse::success([
                    'event_type' => $event,
                    'message' => 'Unknown event handled gracefully'
                ]);
            }

            // Find payment record
            $payment = Payment::where('gateway_payment_id', $paymentData['id'])
                            ->orWhere('gateway_order_id', $paymentData['order_id'] ?? '')
                            ->first();

            if (!$payment) {
                $webhookLog->markAsFailed('Payment not found');
                return WebhookResponse::error('Payment not found');
            }

            // Update webhook log with payment
            $webhookLog->update(['payment_id' => $payment->id]);

            // Process webhook event
            $response = $this->processWebhookEvent($event, $paymentData, $payment);

            $webhookLog->markAsProcessed();

            return $response;

        } catch (\Exception $e) {
            if (isset($webhookLog)) {
                $webhookLog->markAsFailed($e->getMessage());
            }
            
            Log::error('Razorpay webhook processing failed', [
                'error' => $e->getMessage(),
                'payload' => $request->all(),
            ]);

            return WebhookResponse::error('Webhook processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Refund a payment.
     */
    public function refundPayment(string $paymentId, float $amount): RefundResponse
    {
        try {
            $payment = Payment::where('gateway_payment_id', $paymentId)
                            ->orWhere('id', $paymentId)
                            ->first();

            if (!$payment || !$payment->canBeRefunded()) {
                return RefundResponse::error('Payment cannot be refunded');
            }

            // Create refund in Razorpay
            $refundData = $this->createRazorpayRefund($payment->gateway_payment_id, $amount);

            if (!$refundData) {
                return RefundResponse::error('Failed to create refund');
            }

            // Update payment record
            $this->updatePaymentRecord($payment, [
                'refund_id' => $refundData['id'],
                'payment_status' => Payment::STATUS_REFUNDED,
            ]);

            $this->logGatewayInteraction('refund_payment', [
                'payment_id' => $paymentId,
                'refund_amount' => $amount,
                'refund_id' => $refundData['id'],
            ]);

            return RefundResponse::success([
                'refund_id' => $refundData['id'],
                'payment_id' => $payment->id,
                'refund_amount' => $amount,
                'currency' => $payment->currency,
                'status' => 'processed',
                'message' => 'Refund processed successfully',
                'gateway_response' => $refundData,
            ]);

        } catch (\Exception $e) {
            throw $this->handleGatewayException($e, 'refund_payment');
        }
    }

    /**
     * Get the current status of a payment.
     */
    public function getPaymentStatus(string $paymentId): PaymentStatus
    {
        try {
            $razorpayPayment = $this->fetchRazorpayPayment($paymentId);

            if (!$razorpayPayment) {
                return PaymentStatus::error('Payment not found');
            }

            $status = $this->mapRazorpayStatus($razorpayPayment['status']);

            return PaymentStatus::success([
                'payment_id' => $razorpayPayment['id'],
                'order_id' => $razorpayPayment['order_id'],
                'status' => $status,
                'amount' => $this->parseAmountFromGateway($razorpayPayment['amount'], $razorpayPayment['currency']),
                'currency' => strtoupper($razorpayPayment['currency']),
                'gateway_response' => $razorpayPayment,
                'paid_at' => isset($razorpayPayment['created_at']) ? 
                    new \DateTime('@' . $razorpayPayment['created_at']) : null,
            ]);

        } catch (\Exception $e) {
            throw $this->handleGatewayException($e, 'get_payment_status');
        }
    }

    /**
     * Test the gateway connection and credentials.
     */
    public function testConnection(): bool
    {
        try {
            $response = Http::withBasicAuth($this->keyId, $this->keySecret)
                          ->get($this->baseUrl . '/payments', ['count' => 1]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Razorpay connection test failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Calculate gateway fee for Razorpay.
     */
    public function calculateFee(float $amount, string $currency): float
    {
        // Razorpay charges approximately 2% + GST
        // This is a simplified calculation - actual fees depend on payment method
        return round($amount * 0.024, 2); // 2.4% including GST
    }

    /**
     * Create Razorpay order.
     */
    private function createRazorpayOrder(Order $order, Payment $payment): ?array
    {
        $orderData = [
            'amount' => $this->formatAmountForGateway($order->amount, $order->currency ?? 'INR'),
            'currency' => strtoupper($order->currency ?? 'INR'),
            'receipt' => $this->generatePaymentReference($order),
            'notes' => [
                'order_id' => $order->id,
                'payment_id' => $payment->id,
                'customer_email' => $order->user->email ?? '',
            ],
        ];

        $response = Http::withBasicAuth($this->keyId, $this->keySecret)
                      ->post($this->baseUrl . '/orders', $orderData);

        if ($response->successful()) {
            return $response->json();
        }

        Log::error('Failed to create Razorpay order', [
            'response' => $response->json(),
            'order_data' => $orderData,
        ]);

        return null;
    }

    /**
     * Fetch payment from Razorpay.
     */
    private function fetchRazorpayPayment(string $paymentId): ?array
    {
        $response = Http::withBasicAuth($this->keyId, $this->keySecret)
                      ->get($this->baseUrl . '/payments/' . $paymentId);

        if ($response->successful()) {
            return $response->json();
        }

        return null;
    }

    /**
     * Create refund in Razorpay.
     */
    private function createRazorpayRefund(string $paymentId, float $amount): ?array
    {
        $refundData = [
            'amount' => $this->formatAmountForGateway($amount, 'INR'),
        ];

        $response = Http::withBasicAuth($this->keyId, $this->keySecret)
                      ->post($this->baseUrl . '/payments/' . $paymentId . '/refund', $refundData);

        if ($response->successful()) {
            return $response->json();
        }

        return null;
    }

    /**
     * Generate checkout data for frontend.
     */
    private function generateCheckoutData(Order $order, Payment $payment, array $razorpayOrder): array
    {
        return [
            'key' => $this->keyId,
            'amount' => $razorpayOrder['amount'],
            'currency' => $razorpayOrder['currency'],
            'order_id' => $razorpayOrder['id'],
            'name' => config('app.name'),
            'description' => "Payment for Order #{$order->id}",
            'prefill' => [
                'name' => $order->user->name ?? '',
                'email' => $order->user->email ?? '',
                'contact' => $order->user->phone ?? '',
            ],
            'theme' => [
                'color' => '#3399cc',
            ],
            'callback_url' => url('/payment/razorpay/callback'),
            'cancel_url' => route('payment.cancel'),
        ];
    }

    /**
     * Process webhook event.
     */
    private function processWebhookEvent(string $event, array $paymentData, Payment $payment): WebhookResponse
    {
        $response = WebhookResponse::success([
            'event_type' => $event,
            'payment_id' => $payment->id,
            'order_id' => $payment->gateway_order_id,
        ]);

        switch ($event) {
            case 'payment.captured':
                $this->handleSuccessfulPayment($payment, $paymentData);
                $response->setStatus('completed')
                        ->setMessage('Payment captured successfully')
                        ->addAction('send_confirmation_email', ['payment_id' => $payment->id]);
                break;

            case 'payment.failed':
                $reason = $paymentData['error_description'] ?? 'Payment failed';
                $this->handleFailedPayment($payment, $reason, $paymentData);
                $response->setStatus('failed')
                        ->setMessage('Payment failed')
                        ->addAction('send_failure_notification', ['payment_id' => $payment->id]);
                break;

            case 'payment.authorized':
                $this->updatePaymentRecord($payment, [
                    'payment_status' => Payment::STATUS_PENDING,
                    'gateway_payment_id' => $paymentData['id'],
                    'payment_details' => array_merge($payment->payment_details ?? [], $paymentData),
                ]);
                $response->setStatus('authorized')
                        ->setMessage('Payment authorized');
                break;

            default:
                $response->setMessage('Webhook event processed');
        }

        return $response->setPayload($paymentData);
    }

    /**
     * Verify webhook signature.
     */
    protected function verifyWebhookSignature(Request $request): bool
    {
        if (empty($this->webhookSecret)) {
            return true; // Skip verification if no secret configured
        }

        $signature = $request->header('X-Razorpay-Signature');
        if (!$signature) {
            return false;
        }

        $payload = $request->getContent();
        $expectedSignature = hash_hmac('sha256', $payload, $this->webhookSecret);

        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Map Razorpay status to internal status.
     */
    private function mapRazorpayStatus(string $razorpayStatus): string
    {
        return match($razorpayStatus) {
            'captured' => Payment::STATUS_COMPLETED,
            'authorized' => Payment::STATUS_PENDING,
            'failed' => Payment::STATUS_FAILED,
            'refunded' => Payment::STATUS_REFUNDED,
            default => Payment::STATUS_PENDING,
        };
    }

    /**
     * Format amount for Razorpay (uses paise for INR).
     */
    protected function formatAmountForGateway(float $amount, string $currency): int
    {
        // Razorpay uses smallest currency unit (paise for INR)
        return (int) ($amount * 100);
    }

    /**
     * Parse amount from Razorpay response.
     */
    protected function parseAmountFromGateway(int|float $amount, string $currency): float
    {
        // Convert from paise to rupees
        return $amount / 100;
    }
}