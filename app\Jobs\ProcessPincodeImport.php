<?php

namespace App\Jobs;

use App\Models\PincodeImport;
use App\Models\PinCode;
use App\Models\State;
use App\Models\District;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ProcessPincodeImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $import;
    public $timeout = 3600; // 1 hour timeout
    public $tries = 3;

    /**
     * Create a new job instance.
     *
     * @param PincodeImport $import
     * @return void
     */
    public function __construct(PincodeImport $import)
    {
        $this->import = $import;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $this->import->update(['status' => 'processing']);
            
            // Handle both storage paths and direct file paths
            $filePath = $this->import->file_path;
            
            // If it's a relative path, assume it's a storage path
            if (!file_exists($filePath) && !str_starts_with($filePath, '/')) {
                // Try to get from storage
                try {
                    $fileContent = Storage::disk('local')->get($filePath);
                    $handle = fopen('php://temp', 'r+');
                    fwrite($handle, $fileContent);
                    rewind($handle);
                } catch (\Exception $e) {
                    throw new \Exception("File not found: {$filePath}");
                }
            } else {
                // Direct file path
                if (!file_exists($filePath)) {
                    throw new \Exception("File not found: {$filePath}");
                }
                $handle = fopen($filePath, 'r');
            }

            $totalRecords = 0;
            $successfulRecords = 0;
            $failedRecords = 0;
            $errors = [];
            $batchSize = 25; // Smaller batch size for queue processing
            $batch = [];
            $processedRows = 0;

            // Skip header row if needed
            if ($this->import->has_header) {
                fgetcsv($handle);
            }

            // Clear cache keys that might be affected by the import
            $this->clearPincodeCaches();

            while (($row = fgetcsv($handle)) !== false) {
                $totalRecords++;
                $processedRows++;

                try {
                    $this->validateAndProcessRow($row, $batch);
                } catch (\Exception $e) {
                    $failedRecords++;
                    $this->addError($errors, $totalRecords, $e->getMessage());
                }

                // Process batch when it reaches the batch size
                if (count($batch) >= $batchSize) {
                    $this->processBatch($batch);
                    $successfulRecords += count($batch);
                    $batch = [];
                }

                // Update progress every 1000 records
                if ($processedRows % 1000 === 0) {
                    $this->import->update([
                        'total_records' => $totalRecords,
                        'successful_records' => $successfulRecords,
                        'failed_records' => $failedRecords,
                    ]);
                }
            }

            // Process remaining records in the batch
            if (!empty($batch)) {
                $this->processBatch($batch);
                $successfulRecords += count($batch);
            }

            fclose($handle);

            // Final update
            $this->import->update([
                'status' => 'completed',
                'total_records' => $totalRecords,
                'successful_records' => $successfulRecords,
                'failed_records' => $failedRecords,
                'has_errors' => !empty($errors),
                'error_details' => !empty($errors) ? json_encode(array_slice($errors, 0, 250)) : null,
            ]);

        } catch (\Exception $e) {
            $this->import->update([
                'status' => 'failed',
                'error_details' => json_encode([
                    ['row' => 0, 'error' => $e->getMessage()]
                ])
            ]);
            throw $e;
        }
    }

    /**
     * Process a batch of pincode records.
     */
    protected function processBatch(array $batch)
    {
        try {
            foreach ($batch as $record) {
                $this->processPincodeRecord($record);
            }
        } catch (\Exception $e) {
            // Log batch processing error but continue with other batches
            Log::error('Batch processing error: ' . $e->getMessage());
        }
    }

    /**
     * Validate and process a single row.
     */
    protected function validateAndProcessRow(array $row, array &$batch)
    {
                    // Validate row data
                    if (count($row) < 5) {
                        throw new \Exception('Row does not have enough columns. Expected: pincode, name, district, state, delivery_status');
                    }

                    list($pincode, $name, $district, $state, $deliveryStatus) = $row;

                    // Validate pincode
                    if (!preg_match('/^\d{6}$/', $pincode)) {
                        throw new \Exception('Invalid pincode format. Must be 6 digits.');
                    }

                    // Validate required fields
                    if (empty(trim($name))) {
                        throw new \Exception('Area name is required.');
                    }

                    if (empty(trim($district))) {
                        throw new \Exception('District is required.');
                    }

                    if (empty(trim($state))) {
                        throw new \Exception('State is required.');
                    }

                    // Validate delivery status
                    if (!in_array(strtolower($deliveryStatus), ['delivery', 'non-delivery'])) {
                        $deliveryStatus = 'Delivery'; // Default to Delivery
                    }

                    // Add to batch for processing
                    $batch[] = [
                        'pincode' => $pincode,
                        'name' => trim($name),
                        'district' => trim($district),
                        'state' => trim($state),
                        'delivery_status' => ucfirst(strtolower($deliveryStatus)),
                    ];
    }

    /**
     * Add an error to the errors array with limits.
     */
    protected function addError(array &$errors, int $row, string $message)
    {
                    $errors[] = [
            'row' => $row,
            'error' => $message,
                    ];

        // Limit error storage to prevent memory issues
        if (count($errors) > 250) {
            $errors = array_slice($errors, -250);
        }
    }

    /**
     * Clear pincode-related caches.
     */
    protected function clearPincodeCaches()
    {
        Cache::forget('pincode_states');
        Cache::forget('pincode_branch_types');
        Cache::forget('pincode_divisions');

        // Clear any paginated result caches
        $cacheKeys = Cache::get('pincode_list_cache_keys', []);
        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }
        Cache::put('pincode_list_cache_keys', [], now()->addDay());
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception)
    {
        Log::error('Pincode import job failed permanently', [
            'import_id' => $this->import->id,
            'error' => $exception->getMessage()
        ]);

        $this->import->update([
            'status' => 'failed',
            'error_details' => json_encode(['error' => $exception->getMessage()])
        ]);
    }

    /**
     * Process a single pincode record.
     */
    protected function processPincodeRecord(array $record)
    {
        // Find or create state
        $state = State::firstOrCreate(['name' => $record['state']]);

        // Find or create district
        $district = District::firstOrCreate([
            'name' => $record['district'],
            'state_id' => $state->id
        ]);

        // Check if pincode already exists
        $existingPincode = PinCode::where('pincode', $record['pincode'])->first();

        if ($existingPincode) {
            // Update existing record if update_existing is true
            if ($this->import->update_existing) {
                $existingPincode->update([
                    'name' => $record['name'],
                    'district' => $record['district'],
                    'state' => $record['state'],
                    'delivery_status' => $record['delivery_status'],
                ]);
            }
        } else {
            // Create new record
            PinCode::create([
                'pincode' => $record['pincode'],
                'name' => $record['name'],
                'district' => $record['district'],
                'state' => $record['state'],
                'delivery_status' => $record['delivery_status'],
            ]);
        }
    }
} 