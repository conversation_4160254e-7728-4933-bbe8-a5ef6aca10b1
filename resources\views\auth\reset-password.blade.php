@extends('layouts.app')

@section('content')
    <div class="min-h-screen flex items-center justify-center bg-bg-light dark:bg-bg-dark py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <form method="POST" action="{{ route('password.store') }}" class="space-y-6">
                @csrf

                <!-- Password Reset Token -->
                <input type="hidden" name="token" value="{{ $request->route('token') }}">

                <!-- Email Address -->
                <div>
                    <x-input-label for="email" :value="__('Email')"
                        class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-2" />
                    <x-text-input id="email"
                        class="appearance-none rounded-md relative block w-full px-3 py-3 border border-border-light dark:border-border-dark placeholder-text-secondary-light dark:placeholder-text-secondary-dark text-text-primary-light dark:text-text-primary-dark bg-bg-light dark:bg-bg-dark focus:outline-none focus:ring-2 focus:ring-primary-light focus:border-primary-light focus:z-10 sm:text-sm transition-colors duration-200"
                        type="email" name="email" :value="old('email', $request->email)" required autofocus autocomplete="username" />
                    <x-input-error :messages="$errors->get('email')" class="mt-2 text-sm text-red-600 dark:text-red-400" />
                </div>

                <!-- Password -->
                <div>
                    <x-input-label for="password" :value="__('Password')"
                        class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-2" />
                    <x-text-input id="password"
                        class="appearance-none rounded-md relative block w-full px-3 py-3 border border-border-light dark:border-border-dark placeholder-text-secondary-light dark:placeholder-text-secondary-dark text-text-primary-light dark:text-text-primary-dark bg-bg-light dark:bg-bg-dark focus:outline-none focus:ring-2 focus:ring-primary-light focus:border-primary-light focus:z-10 sm:text-sm transition-colors duration-200"
                        type="password" name="password" required autocomplete="new-password" />
                    <x-input-error :messages="$errors->get('password')" class="mt-2 text-sm text-red-600 dark:text-red-400" />
                </div>

                <!-- Confirm Password -->
                <div>
                    <x-input-label for="password_confirmation" :value="__('Confirm Password')"
                        class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-2" />
                    <x-text-input id="password_confirmation"
                        class="appearance-none rounded-md relative block w-full px-3 py-3 border border-border-light dark:border-border-dark placeholder-text-secondary-light dark:placeholder-text-secondary-dark text-text-primary-light dark:text-text-primary-dark bg-bg-light dark:bg-bg-dark focus:outline-none focus:ring-2 focus:ring-primary-light focus:border-primary-light focus:z-10 sm:text-sm transition-colors duration-200"
                        type="password" name="password_confirmation" required autocomplete="new-password" />
                    <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2 text-sm text-red-600 dark:text-red-400" />
                </div>

                <div class="flex items-center justify-end mt-4">
                    <x-primary-button
                        class="inline-flex justify-center py-3 px-6 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-light hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:bg-primary-dark dark:hover:bg-accent-dark dark:focus:ring-offset-bg-dark transition-colors duration-200">
                        {{ __('Reset Password') }}
                    </x-primary-button>
                </div>
            </form>
        </div>
    </div>
@endsection
