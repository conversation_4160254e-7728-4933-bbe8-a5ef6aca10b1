<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class PlanController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('admin');
    }

    /**
     * Display a listing of the plans.
     */
    public function index()
    {
        $plans = Plan::latest()->paginate(10);
        return view('admin.plans.index', compact('plans'));
    }

    /**
     * Show the form for creating a new plan.
     */
    public function create()
    {
        return view('admin.plans.create');
    }

    /**
     * Store a newly created plan in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:plans,slug',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|string|in:monthly,yearly',
            'request_limit' => 'required|integer|min:1',
            'description' => 'nullable|string',
            'features' => 'nullable|string', // JSON string from tests
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean'
        ]);

        // Handle features - could be JSON string or array
        if (isset($validated['features']) && is_string($validated['features'])) {
            $validated['features'] = json_decode($validated['features'], true);
        }

        $validated['is_active'] = $request->boolean('is_active', false);

        Plan::create($validated);

        return redirect()->route('admin.plans.index')
            ->with('success', 'Plan created successfully.');
    }

    /**
     * Show the form for editing the specified plan.
     */
    public function edit(Plan $plan)
    {
        return view('admin.plans.edit', compact('plan'));
    }

    /**
     * Update the specified plan in storage.
     */
    public function update(Request $request, Plan $plan)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => "required|string|max:255|unique:plans,slug,{$plan->id}",
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|string|in:monthly,yearly',
            'request_limit' => 'required|integer|min:1',
            'description' => 'nullable|string',
            'features' => 'nullable|string', // JSON string from tests
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean'
        ]);

        // Handle features - could be JSON string or array
        if (isset($validated['features']) && is_string($validated['features'])) {
            $validated['features'] = json_decode($validated['features'], true);
        }

        $validated['is_active'] = $request->boolean('is_active', false);

        $plan->update($validated);

        return redirect()->route('admin.plans.index')
            ->with('success', 'Plan updated successfully.');
    }

    /**
     * Remove the specified plan from storage.
     */
    public function destroy(Plan $plan)
    {
        // Check if plan has any orders
        if ($plan->orders()->exists()) {
            // Check if there are completed orders - if so, soft delete
            if ($plan->orders()->where('status', 'completed')->exists()) {
                $plan->delete(); // This will soft delete due to SoftDeletes trait
                return redirect()->route('admin.plans.index')
                    ->with('success', 'Plan archived successfully.');
            } else {
                // Has orders but none completed - prevent deletion
                return redirect()->route('admin.plans.index')
                    ->with('error', 'Cannot delete plan as it has associated orders.');
            }
        }

        // No orders - hard delete
        $plan->forceDelete();

        return redirect()->route('admin.plans.index')
            ->with('success', 'Plan deleted successfully.');
    }

    /**
     * Toggle the active status of a plan.
     */
    public function toggleStatus(Plan $plan)
    {
        $plan->update(['is_active' => !$plan->is_active]);

        return redirect()->route('admin.plans.index')
            ->with('success', 'Plan status updated successfully.');
    }
} 