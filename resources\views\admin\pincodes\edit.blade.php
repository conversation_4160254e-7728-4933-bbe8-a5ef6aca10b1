@extends('admin.layouts.admin')

@section('title', '<PERSON> Pincode')
@section('page-title', '<PERSON> Pincode')

@section('content')
    <div class="bg-white dark:bg-bg-dark shadow-md rounded-lg overflow-hidden border border-border-light dark:border-border-dark">
        <div class="px-6 py-4 border-b border-border-light dark:border-border-dark">
            <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">Edit Pincode Information</h2>
        </div>

        <form action="{{ route('admin.pincodes.update', $pincode->id) }}" method="POST" class="p-6">
            @csrf
            @method('PUT')
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="pincode" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">Pincode</label>
                    <input type="text" name="pincode" id="pincode" value="{{ old('pincode', $pincode->pincode) }}"
                        required
                        class="w-full border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 @error('pincode') border-red-500 @enderror">
                    @error('pincode')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="area_name" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">Area Name</label>
                    <input type="text" name="area_name" id="area_name"
                        value="{{ old('area_name', $pincode->area_name) }}" required
                        class="w-full border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 @error('area_name') border-red-500 @enderror">
                    @error('area_name')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="contact_number" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">Contact Number</label>
                    <input type="text" name="contact_number" id="contact_number" value="{{ old('contact_number') }}"
                        required
                        class="w-full border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 @error('contact_number') border-red-500 @enderror">
                    @error('contact_number')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="city" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">City</label>
                    <input type="text" name="city" id="city" value="{{ old('city', $pincode->city) }}" required
                        class="w-full border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 @error('city') border-red-500 @enderror">
                    @error('city')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="state" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">State</label>
                    <select name="state" id="state" required
                        class="w-full border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 @error('state') border-red-500 @enderror">
                        <option value="">Select State</option>
                        <option value="Andhra Pradesh"
                            {{ old('state', $pincode->state) == 'Andhra Pradesh' ? 'selected' : '' }}>Andhra Pradesh
                        </option>
                        <option value="Arunachal Pradesh"
                            {{ old('state', $pincode->state) == 'Arunachal Pradesh' ? 'selected' : '' }}>Arunachal Pradesh
                        </option>
                        <option value="Assam" {{ old('state', $pincode->state) == 'Assam' ? 'selected' : '' }}>Assam
                        </option>
                        <option value="Bihar" {{ old('state', $pincode->state) == 'Bihar' ? 'selected' : '' }}>Bihar
                        </option>
                        <option value="Chhattisgarh"
                            {{ old('state', $pincode->state) == 'Chhattisgarh' ? 'selected' : '' }}>Chhattisgarh</option>
                        <option value="Goa" {{ old('state', $pincode->state) == 'Goa' ? 'selected' : '' }}>Goa</option>
                        <option value="Gujarat" {{ old('state', $pincode->state) == 'Gujarat' ? 'selected' : '' }}>Gujarat
                        </option>
                        <option value="Haryana" {{ old('state', $pincode->state) == 'Haryana' ? 'selected' : '' }}>Haryana
                        </option>
                        <option value="Himachal Pradesh"
                            {{ old('state', $pincode->state) == 'Himachal Pradesh' ? 'selected' : '' }}>Himachal Pradesh
                        </option>
                        <option value="Jharkhand" {{ old('state', $pincode->state) == 'Jharkhand' ? 'selected' : '' }}>
                            Jharkhand</option>
                        <option value="Karnataka" {{ old('state', $pincode->state) == 'Karnataka' ? 'selected' : '' }}>
                            Karnataka</option>
                        <option value="Kerala" {{ old('state', $pincode->state) == 'Kerala' ? 'selected' : '' }}>Kerala
                        </option>
                        <option value="Madhya Pradesh"
                            {{ old('state', $pincode->state) == 'Madhya Pradesh' ? 'selected' : '' }}>Madhya Pradesh
                        </option>
                        <option value="Maharashtra" {{ old('state', $pincode->state) == 'Maharashtra' ? 'selected' : '' }}>
                            Maharashtra</option>
                        <option value="Manipur" {{ old('state', $pincode->state) == 'Manipur' ? 'selected' : '' }}>Manipur
                        </option>
                        <option value="Meghalaya" {{ old('state', $pincode->state) == 'Meghalaya' ? 'selected' : '' }}>
                            Meghalaya</option>
                        <option value="Mizoram" {{ old('state', $pincode->state) == 'Mizoram' ? 'selected' : '' }}>Mizoram
                        </option>
                        <option value="Nagaland" {{ old('state', $pincode->state) == 'Nagaland' ? 'selected' : '' }}>
                            Nagaland</option>
                        <option value="Odisha" {{ old('state', $pincode->state) == 'Odisha' ? 'selected' : '' }}>Odisha
                        </option>
                        <option value="Punjab" {{ old('state', $pincode->state) == 'Punjab' ? 'selected' : '' }}>Punjab
                        </option>
                        <option value="Rajasthan" {{ old('state', $pincode->state) == 'Rajasthan' ? 'selected' : '' }}>
                            Rajasthan</option>
                        <option value="Sikkim" {{ old('state', $pincode->state) == 'Sikkim' ? 'selected' : '' }}>Sikkim
                        </option>
                        <option value="Tamil Nadu" {{ old('state', $pincode->state) == 'Tamil Nadu' ? 'selected' : '' }}>
                            Tamil Nadu</option>
                        <option value="Telangana" {{ old('state', $pincode->state) == 'Telangana' ? 'selected' : '' }}>
                            Telangana</option>
                        <option value="Tripura" {{ old('state', $pincode->state) == 'Tripura' ? 'selected' : '' }}>Tripura
                        </option>
                        <option value="Uttar Pradesh"
                            {{ old('state', $pincode->state) == 'Uttar Pradesh' ? 'selected' : '' }}>Uttar Pradesh</option>
                        <option value="Uttarakhand" {{ old('state', $pincode->state) == 'Uttarakhand' ? 'selected' : '' }}>
                            Uttarakhand</option>
                        <option value="West Bengal" {{ old('state', $pincode->state) == 'West Bengal' ? 'selected' : '' }}>
                            West Bengal</option>
                        <option value="Andaman and Nicobar Islands"
                            {{ old('state', $pincode->state) == 'Andaman and Nicobar Islands' ? 'selected' : '' }}>Andaman
                            and Nicobar Islands</option>
                        <option value="Chandigarh" {{ old('state', $pincode->state) == 'Chandigarh' ? 'selected' : '' }}>
                            Chandigarh</option>
                        <option value="Dadra and Nagar Haveli and Daman and Diu"
                            {{ old('state', $pincode->state) == 'Dadra and Nagar Haveli and Daman and Diu' ? 'selected' : '' }}>
                            Dadra and Nagar Haveli and Daman and Diu</option>
                        <option value="Delhi" {{ old('state', $pincode->state) == 'Delhi' ? 'selected' : '' }}>Delhi
                        </option>
                        <option value="Jammu and Kashmir"
                            {{ old('state', $pincode->state) == 'Jammu and Kashmir' ? 'selected' : '' }}>Jammu and Kashmir
                        </option>
                        <option value="Ladakh" {{ old('state', $pincode->state) == 'Ladakh' ? 'selected' : '' }}>Ladakh
                        </option>
                        <option value="Lakshadweep" {{ old('state', $pincode->state) == 'Lakshadweep' ? 'selected' : '' }}>
                            Lakshadweep</option>
                        <option value="Puducherry" {{ old('state', $pincode->state) == 'Puducherry' ? 'selected' : '' }}>
                            Puducherry</option>
                    </select>
                    @error('state')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="district" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">District</label>
                    <input type="text" name="district" id="district" value="{{ old('district', $pincode->district) }}"
                        required
                        class="w-full border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 @error('district') border-red-500 @enderror">
                    @error('district')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="status" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">Status</label>
                    <select name="status" id="status"
                        class="w-full border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 @error('status') border-red-500 @enderror">
                        <option value="active" {{ old('status', $pincode->status) == 'active' ? 'selected' : '' }}>Active
                        </option>
                        <option value="inactive" {{ old('status', $pincode->status) == 'inactive' ? 'selected' : '' }}>
                            Inactive</option>
                    </select>
                    @error('status')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="mt-8 flex justify-end">
                <a href="{{ route('admin.pincodes.index') }}"
                    class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-text-primary-dark px-4 py-2 rounded-md text-sm font-medium mr-2">
                    Cancel
                </a>
                <button type="submit"
                    class="bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-white px-4 py-2 rounded-md text-sm font-medium">
                    Update Pincode
                </button>
            </div>
        </form>
    </div>
@endsection
