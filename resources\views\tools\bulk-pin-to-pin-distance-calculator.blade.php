@extends('layouts.app')

@section('json-ld')
    {{-- @include($activeTemplate.'pincodes.json-ld.webpage')
    @include($activeTemplate.'pincodes.json-ld.breadcrumbs')
    @include($activeTemplate.'pincodes.json-ld.organization') --}}
@endsection

@section('content')
    <x-breadcrumb :segments="$breadcrumbs" :pageTitle="$pageTitle" :metaDescription="$metaDescription" />

    <section class="py-16">
        <div class="container max-w-6xl mx-auto px-4">
            <div class="flex flex-wrap -mx-4 justify-center">
                <div class="w-full lg:w-2/3 px-4">
                    <div class="space-y-8">
                        <div class="container mt-10">
                            <h2 class="text-3xl font-bold text-text-primary-light dark:text-text-primary-dark">Bulk
                                Pin-to-Pin Distance Calculator</h2>
                            <p class="text-sm mb-6 text-text-secondary-light dark:text-text-secondary-dark">Calculate
                                distances between multiple pincodes in one go</p>

                            <!-- Added descriptive content -->
                            <div
                                class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark p-6 mb-8">
                                <h2 class="text-xl font-semibold mb-4 text-text-primary-light dark:text-text-primary-dark">
                                    Calculate Distances Between Multiple Pincodes</h2>
                                <div class="prose max-w-none text-text-secondary-light dark:text-text-secondary-dark">
                                    <p class="mb-4">This tool allows you to calculate distances between multiple pairs of
                                        pincodes at once by uploading an Excel file. It's perfect for logistics planning,
                                        delivery route optimization, or any scenario where you need to determine distances
                                        between multiple locations.</p>

                                    <div
                                        class="bg-primary-light/10 dark:bg-primary-dark/10 p-4 rounded-lg mb-4 border border-primary-light/20 dark:border-primary-dark/20">
                                        <h3 class="text-lg font-medium text-primary-light dark:text-primary-dark mb-2">How
                                            to Use:</h3>
                                        <ol
                                            class="list-decimal list-inside space-y-2 text-text-primary-light dark:text-text-primary-dark">
                                            <li>Prepare an Excel file with your data</li>
                                            <li>Source pincode should be in Column B</li>
                                            <li>Destination pincode should be in Column D</li>
                                            <li>The calculated distance will be placed in Column E</li>
                                            <li>Upload your Excel file using the form below</li>
                                            <li>Click "Upload and Calculate" to process your data</li>
                                            <li>Download the results with calculated distances</li>
                                        </ol>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                        <div>
                                            <h3
                                                class="font-medium mb-2 text-text-primary-light dark:text-text-primary-dark">
                                                Features:</h3>
                                            <ul
                                                class="list-disc list-inside space-y-1 text-text-secondary-light dark:text-text-secondary-dark">
                                                <li>Bulk processing of multiple pincode pairs</li>
                                                <li>Accurate distance calculation</li>
                                                <li>Fast processing of large datasets</li>
                                                <li>Downloadable results in Excel format</li>
                                            </ul>
                                        </div>
                                        <div>
                                            <h3
                                                class="font-medium mb-2 text-text-primary-light dark:text-text-primary-dark">
                                                File Requirements:</h3>
                                            <p class="mb-2 text-text-secondary-light dark:text-text-secondary-dark">Your
                                                Excel file should have:</p>
                                            <ul
                                                class="list-disc list-inside space-y-1 text-text-secondary-light dark:text-text-secondary-dark">
                                                <li>Column B: Source Pincode</li>
                                                <li>Column D: Destination Pincode</li>
                                                <li>Column E: Will contain calculated distance</li>
                                                <li>Maximum file size: 5MB</li>
                                                <li>Supported formats: .xlsx, .xls</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- End of added content -->

                            <div
                                class="bg-white dark:bg-bg-dark shadow-md rounded-lg overflow-hidden border border-border-light dark:border-border-dark">
                                <div class="bg-primary-light dark:bg-primary-dark text-white px-6 py-4">
                                    <h5 class="text-xl font-semibold mb-0">Upload Excel File</h5>
                                </div>
                                <div class="p-6">
                                    @if (session('status'))
                                        <div
                                            class="bg-green-50 dark:bg-green-900/20 border-l-4 border-green-500 dark:border-green-400 p-4 mb-6">
                                            <div class="flex">
                                                <div class="flex-shrink-0">
                                                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd"
                                                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                            clip-rule="evenodd" />
                                                    </svg>
                                                </div>
                                                <div class="ml-3">
                                                    <p class="text-sm text-green-700 dark:text-green-300">
                                                        {{ session('status') }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    @endif

                                    <form method="POST" action="{{ route('pincode.upload') }}" enctype="multipart/form-data"
                                        class="space-y-6">
                                        @csrf

                                        <div class="space-y-2">
                                            <label for="excel_file"
                                                class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Excel
                                                File</label>
                                            <input type="file"
                                                class="w-full border border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-light/10 dark:file:bg-primary-dark/10 file:text-primary-light dark:file:text-primary-dark hover:file:bg-primary-light/20 dark:hover:file:bg-primary-dark/20 @error('excel_file') border-red-500 dark:border-red-400 @enderror"
                                                id="excel_file" name="excel_file" required>
                                            <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                                Upload an Excel file with source and destination pincodes in columns B and D
                                                respectively</p>

                                            @error('excel_file')
                                                <p class="text-red-500 dark:text-red-400 text-sm mt-1">{{ $message }}</p>
                                            @enderror
                                        </div>

                                        <button type="submit"
                                            class="w-full bg-primary-light dark:bg-primary-dark hover:bg-blue-700 dark:hover:bg-blue-400 text-white font-bold py-3 px-4 rounded-md transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-bg-dark shadow-lg hover:shadow-xl">
                                            <span class="flex items-center justify-center">
                                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12">
                                                    </path>
                                                </svg>
                                                Upload and Calculate
                                            </span>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="w-full lg:w-1/3 px-4">
                    <div class="sticky top-20">
                        @include('pincodes.partials.sidebar')
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Reviews Section -->
    <div class="container max-w-6xl mx-auto px-4 py-8">
        <x-tool-reviews
            :reviews="$reviews"
            :tool="$tool"
            :hasMoreReviews="$hasMoreReviews ?? false"
            :totalReviewsCount="$totalReviewsCount ?? 0"
        />
    </div>
@endsection