@extends('admin.layouts.admin')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Testimonial Details</h1>
            <div class="flex space-x-3">
                <a href="{{ route('admin.testimonials.edit', $testimonial) }}" 
                   class="bg-primary-light dark:bg-primary-dark text-white px-4 py-2 rounded-lg hover:bg-primary-dark dark:hover:bg-primary-light transition-colors">
                    Edit
                </a>
                <a href="{{ route('admin.testimonials.index') }}" 
                   class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                    Back to List
                </a>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <!-- Header with Avatar and Basic Info -->
            <div class="bg-gradient-to-r from-primary-light to-accent-light dark:from-primary-dark dark:to-accent-dark p-6 text-white">
                <div class="flex items-center space-x-6">
                    <img src="{{ $testimonial->avatar_url }}" 
                         alt="{{ $testimonial->name }}" 
                         class="h-24 w-24 rounded-full object-cover border-4 border-white/20 shadow-lg">
                    <div>
                        <h2 class="text-2xl font-bold">{{ $testimonial->name }}</h2>
                        <p class="text-lg opacity-90">{{ $testimonial->position }}, {{ $testimonial->company }}</p>
                        <div class="flex items-center mt-2">
                            @for($i = 1; $i <= 5; $i++)
                                <svg class="w-5 h-5 {{ $i <= $testimonial->rating ? 'text-yellow-300' : 'text-white/30' }}" 
                                     fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                            @endfor
                            <span class="ml-2 text-sm opacity-90">({{ $testimonial->rating }} out of 5)</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Section -->
            <div class="p-6">
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Testimonial Content</h3>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <p class="text-gray-700 dark:text-gray-300 text-lg leading-relaxed italic">
                            "{{ $testimonial->content }}"
                        </p>
                    </div>
                </div>

                <!-- Details Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Basic Information</h3>
                        <dl class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Full Name</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $testimonial->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Position</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $testimonial->position }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Company</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $testimonial->company }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Rating</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $testimonial->rating }} out of 5 stars</dd>
                            </div>
                        </dl>
                    </div>

                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Management Details</h3>
                        <dl class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                                <dd>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $testimonial->is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                                        {{ $testimonial->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Sort Order</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $testimonial->sort_order }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $testimonial->created_at->format('M j, Y \a\t g:i A') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $testimonial->updated_at->format('M j, Y \a\t g:i A') }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Actions -->
                <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex flex-wrap gap-3">
                        <a href="{{ route('admin.testimonials.edit', $testimonial) }}" 
                           class="bg-primary-light dark:bg-primary-dark text-white px-4 py-2 rounded-lg hover:bg-primary-dark dark:hover:bg-primary-light transition-colors">
                            Edit Testimonial
                        </a>
                        
                        <form action="{{ route('admin.testimonials.toggle-status', $testimonial) }}" method="POST" class="inline">
                            @csrf
                            @method('PATCH')
                            <button type="submit" 
                                    class="bg-{{ $testimonial->is_active ? 'yellow' : 'green' }}-500 text-white px-4 py-2 rounded-lg hover:bg-{{ $testimonial->is_active ? 'yellow' : 'green' }}-600 transition-colors">
                                {{ $testimonial->is_active ? 'Deactivate' : 'Activate' }}
                            </button>
                        </form>
                        
                        <form action="{{ route('admin.testimonials.destroy', $testimonial) }}" method="POST" class="inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors"
                                    onclick="return confirm('Are you sure you want to delete this testimonial? This action cannot be undone.')">
                                Delete Testimonial
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 