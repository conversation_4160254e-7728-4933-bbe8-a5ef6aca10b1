@extends('admin.layouts.admin')

@section('content')
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6">
            <h1 class="text-xl sm:text-2xl font-bold text-text-primary-light dark:text-text-primary-dark mb-4 sm:mb-0">
                <span class="block sm:hidden">Courier Dict</span>
                <span class="hidden sm:block">Courier Dictionary Management</span>
            </h1>
            {{-- <a href="{{ route('admin.courier-dict.create') }}" class="inline-flex items-center px-4 py-2 bg-primary-light dark:bg-primary-dark border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-dark dark:hover:bg-primary-light focus:bg-primary-dark dark:focus:bg-primary-light active:bg-primary-dark dark:active:bg-primary-light focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-2 dark:focus:ring-offset-bg-dark transition ease-in-out duration-150">Add New Term</a> --}}
        </div>

        <div class="bg-white dark:bg-bg-dark shadow rounded-lg border border-border-light dark:border-border-dark">
            <div class="p-3 sm:p-6">
                <form action="{{ route('admin.courier-dict.search') }}" method="GET" class="mb-4 sm:mb-6">
                    <div class="flex flex-col sm:flex-row gap-2 sm:gap-0">
                        <input type="text" name="search"
                            class="flex-1 px-3 py-2 border border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md sm:rounded-l-md sm:rounded-r-none shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark text-sm"
                            placeholder="Search terms..." value="{{ $search ?? '' }}">
                        <button type="submit"
                            class="w-full sm:w-auto px-4 py-2 bg-bg-light dark:bg-gray-700 border border-border-light dark:border-border-dark sm:border-l-0 rounded-md sm:rounded-l-none sm:rounded-r-md text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark hover:bg-gray-100 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark transition-colors">
                            Search
                        </button>
                    </div>
                </form>

                @if (session('success'))
                    <div class="mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 text-green-700 dark:text-green-300 px-3 sm:px-4 py-3 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-400 dark:text-green-500" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3 text-sm">
                                {{ session('success') }}
                            </div>
                        </div>
                    </div>
                @endif

                <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 dark:ring-white dark:ring-opacity-10 rounded-lg">
                    <div class="overflow-x-auto">
                        <!-- Desktop Table View -->
                        <table class="hidden sm:table min-w-full divide-y divide-border-light dark:divide-border-dark">
                            <thead class="bg-bg-light dark:bg-bg-dark">
                                <tr>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                        Term
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                        Description
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                        Tag
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                                @forelse($terms as $term)
                                    <tr class="hover:bg-bg-light dark:hover:bg-gray-800/50 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-text-primary-light dark:text-text-primary-dark">
                                            {{ $term->term }}
                                        </td>
                                        <td class="px-6 py-4 text-sm text-text-secondary-light dark:text-text-secondary-dark max-w-xs">
                                            <div class="truncate">
                                                {{ Str::limit($term->description, 100) }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span
                                                class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-primary-light/10 dark:bg-primary-dark/20 text-primary-light dark:text-primary-dark">
                                                {{ $term->tag }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <a href="{{ route('admin.courier-dict.edit', $term) }}"
                                                class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-light dark:text-primary-dark bg-primary-light/10 dark:bg-primary-dark/20 hover:bg-primary-light/20 dark:hover:bg-primary-dark/30 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-bg-dark focus:ring-primary-light dark:focus:ring-primary-dark transition-colors">
                                                Edit
                                            </a>
                                            <form action="{{ route('admin.courier-dict.destroy', $term) }}" method="POST"
                                                class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit"
                                                    class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-900/50 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-bg-dark focus:ring-red-500 transition-colors"
                                                    onclick="return confirm('Are you sure?')">
                                                    Delete
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="px-6 py-12 text-center text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                            <div class="flex flex-col items-center">
                                                <svg class="mx-auto h-12 w-12 text-text-secondary-light dark:text-text-secondary-dark" stroke="currentColor"
                                                    fill="none" viewBox="0 0 48 48">
                                                    <path
                                                        d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.713-3.714M14 40v-4c0-1.313.253-2.566.713-3.714m0 0A10.003 10.003 0 0124 26c4.21 0 7.813 2.602 9.288 6.286M30 14a6 6 0 11-12 0 6 6 0 0112 0zm12 6a4 4 0 11-8 0 4 4 0 018 0zm-28 0a4 4 0 11-8 0 4 4 0 018 0z"
                                                        stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
                                                </svg>
                                                <h3 class="mt-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">No terms found</h3>
                                                <p class="mt-1 text-sm text-text-secondary-light dark:text-text-secondary-dark">Get started by adding a new term.</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>

                        <!-- Mobile Card View -->
                        <div class="block sm:hidden">
                            @forelse($terms as $term)
                                <div class="bg-white dark:bg-bg-dark border-b border-border-light dark:border-border-dark p-4 hover:bg-bg-light dark:hover:bg-gray-800/50 transition-colors">
                                    <div class="flex flex-col space-y-3">
                                        <!-- Term and Tag Row -->
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1 min-w-0">
                                                <h3 class="text-base font-medium text-text-primary-light dark:text-text-primary-dark truncate">
                                                    {{ $term->term }}
                                                </h3>
                                            </div>
                                            <div class="ml-3 flex-shrink-0">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-primary-light/10 dark:bg-primary-dark/20 text-primary-light dark:text-primary-dark">
                                                    {{ $term->tag }}
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <!-- Description -->
                                        <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                            <p class="line-clamp-2">{{ $term->description }}</p>
                                        </div>
                                        
                                        <!-- Actions -->
                                        <div class="flex flex-col sm:flex-row gap-2 pt-2">
                                            <a href="{{ route('admin.courier-dict.edit', $term) }}"
                                                class="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-primary-light dark:text-primary-dark bg-primary-light/10 dark:bg-primary-dark/20 hover:bg-primary-light/20 dark:hover:bg-primary-dark/30 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-bg-dark focus:ring-primary-light dark:focus:ring-primary-dark transition-colors text-center">
                                                Edit
                                            </a>
                                            <form action="{{ route('admin.courier-dict.destroy', $term) }}" method="POST" class="flex-1">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit"
                                                    class="w-full inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 dark:text-red-400 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-900/50 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-bg-dark focus:ring-red-500 transition-colors"
                                                    onclick="return confirm('Are you sure?')">
                                                    Delete
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="px-4 py-12 text-center text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                    <div class="flex flex-col items-center">
                                        <svg class="mx-auto h-12 w-12 text-text-secondary-light dark:text-text-secondary-dark" stroke="currentColor"
                                            fill="none" viewBox="0 0 48 48">
                                            <path
                                                d="M34 40h10v-4a6 6 0 00-10.712-3.714M34 40H14m20 0v-4a9.971 9.971 0 00-.712-3.714M14 40H4v-4a6 6 0 0110.713-3.714M14 40v-4c0-1.313.253-2.566.713-3.714m0 0A10.003 10.003 0 0124 26c4.21 0 7.813 2.602 9.288 6.286M30 14a6 6 0 11-12 0 6 6 0 0112 0zm12 6a4 4 0 11-8 0 4 4 0 018 0zm-28 0a4 4 0 11-8 0 4 4 0 018 0z"
                                                stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
                                        </svg>
                                        <h3 class="mt-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">No terms found</h3>
                                        <p class="mt-1 text-sm text-text-secondary-light dark:text-text-secondary-dark text-center">Get started by adding a new term.</p>
                                    </div>
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>

                @if (method_exists($terms, 'links'))
                    <div class="mt-4 sm:mt-6">
                        {{ $terms->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>

    <style>
        /* Custom CSS for line clamping if not available in your Tailwind build */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
@endsection