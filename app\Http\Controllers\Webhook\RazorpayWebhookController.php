<?php

namespace App\Http\Controllers\Webhook;

use App\Http\Controllers\Controller;
use App\Models\PaymentGateway;
use App\Models\WebhookLog;
use App\Services\Payment\PaymentGatewayManager;
use App\Services\Payment\WebhookSecurityService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class RazorpayWebhookController extends Controller
{
    protected PaymentGatewayManager $gatewayManager;
    protected WebhookSecurityService $securityService;

    public function __construct(
        PaymentGatewayManager $gatewayManager,
        WebhookSecurityService $securityService
    ) {
        $this->gatewayManager = $gatewayManager;
        $this->securityService = $securityService;
        
        // Apply webhook security middleware
        // $this->middleware('webhook.security');
    }

    /**
     * Handle Razorpay webhook notifications
     */
    public function handle(Request $request)
    {
        // Get Razorpay gateway configuration
        $gateway = PaymentGateway::where('name', 'razorpay')->where('is_active', true)->first();
        if (!$gateway) {
            Log::error('Razorpay gateway not found or inactive');
            return response()->json(['error' => 'Gateway not available'], 404);
        }

        // Apply rate limiting specific to this gateway
        $rateLimitCheck = $this->securityService->checkRateLimit($request, $gateway);
        if (!$rateLimitCheck['allowed']) {
            return response()->json([
                'error' => $rateLimitCheck['error'],
                'retry_after' => $rateLimitCheck['retry_after'] ?? null
            ], 429);
        }

        // Validate JSON payload
        $rawPayload = $request->getContent();
        if (!empty($rawPayload)) {
            $decodedPayload = json_decode($rawPayload, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::warning('Razorpay webhook received malformed JSON', [
                    'error' => json_last_error_msg(),
                    'ip' => $request->ip()
                ]);

                // Log the failed attempt
                $this->securityService->logWebhookEvent($request, $gateway, ['valid' => false, 'error' => 'Invalid JSON payload'], 'failed');

                return response()->json([
                    'error' => 'Invalid JSON payload'
                ], 400);
            }
        }

        // Verify webhook signature
        $signatureVerification = $this->securityService->verifyWebhookSignature($request, $gateway);
        if (!$signatureVerification['valid']) {
            Log::warning('Razorpay webhook signature verification failed', [
                'ip' => $request->ip(),
                'error' => $signatureVerification['error'],
                'details' => $signatureVerification['details'] ?? null,
                'webhook_secret_exists' => !empty($gateway->webhook_secret),
                'config_webhook_secret_exists' => !empty($gateway->configuration['webhook_secret'] ?? null),
                'signature_header' => $request->header('X-Razorpay-Signature'),
                'payload_length' => strlen($request->getContent())
            ]);

            // Log the failed attempt
            $this->securityService->logWebhookEvent($request, $gateway, $signatureVerification, 'failed');

            return response()->json([
                'error' => 'Invalid signature'
            ], 400);
        }

        // Check for duplicate webhook processing
        $eventId = $signatureVerification['event_id'] ?? null;
        if ($eventId) {
            $existingLog = WebhookLog::where('gateway_id', $gateway->id)
                                   ->where('webhook_id', $eventId)
                                   ->where('status', 'processed')
                                   ->first();

            if ($existingLog) {
                Log::info('Duplicate webhook detected, skipping processing', [
                    'event_id' => $eventId,
                    'existing_log_id' => $existingLog->id,
                    'gateway' => $gateway->name
                ]);

                return response()->json([
                    'status' => 'success',
                    'message' => 'Webhook already processed',
                ], 200);
            }
        }

        // Log the webhook event
        $webhookLog = $this->securityService->logWebhookEvent($request, $gateway, $signatureVerification, 'pending');

        Log::info('Razorpay webhook received and verified', [
            'event' => $request->input('event'),
            'event_id' => $signatureVerification['event_id'] ?? null,
            'webhook_log_id' => $webhookLog->id,
            'ip' => $request->ip()
        ]);

        try {
            // Process webhook using the gateway manager
            $webhookResponse = $this->gatewayManager->handleWebhook('razorpay', $request);

            if ($webhookResponse->isSuccess()) {
                // Update webhook log status
                $webhookLog->update([
                    'status' => 'processed',
                    'processed_at' => now()
                ]);

                Log::info('Razorpay webhook processed successfully', [
                    'event_type' => $webhookResponse->eventType,
                    'payment_id' => $webhookResponse->paymentId,
                    'status' => $webhookResponse->status,
                    'actions' => count($webhookResponse->getActions()),
                    'webhook_log_id' => $webhookLog->id
                ]);

                // Execute any post-processing actions
                $this->executeWebhookActions($webhookResponse->getActions());

                return response()->json([
                    'status' => 'success',
                    'message' => 'Webhook processed successfully',
                ], 200);
            }

            // Update webhook log with failure
            $webhookLog->update([
                'status' => 'failed',
                'error_message' => $webhookResponse->message,
                'processed_at' => now()
            ]);

            Log::warning('Razorpay webhook processing failed', [
                'message' => $webhookResponse->message,
                'event_type' => $webhookResponse->eventType,
                'webhook_log_id' => $webhookLog->id
            ]);

            return response()->json([
                'status' => 'error',
                'message' => $webhookResponse->message ?? 'Webhook processing failed',
            ], 400);

        } catch (\Exception $e) {
            // Update webhook log with error
            $webhookLog->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'processed_at' => now()
            ]);

            Log::error('Razorpay webhook error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'webhook_log_id' => $webhookLog->id,
                'payload' => $request->all(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Internal server error',
            ], 500);
        }
    }

    /**
     * Execute post-webhook actions
     */
    private function executeWebhookActions(array $actions)
    {
        foreach ($actions as $action) {
            try {
                switch ($action['action']) {
                    case 'send_confirmation_email':
                        $this->sendConfirmationEmail($action['data']);
                        break;
                    
                    case 'send_failure_notification':
                        $this->sendFailureNotification($action['data']);
                        break;
                    
                    case 'update_user_subscription':
                        $this->updateUserSubscription($action['data']);
                        break;
                    
                    default:
                        Log::warning('Unknown webhook action', ['action' => $action['action']]);
                }
            } catch (\Exception $e) {
                Log::error('Webhook action execution failed', [
                    'action' => $action['action'],
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * Send payment confirmation email
     */
    private function sendConfirmationEmail(array $data)
    {
        // TODO: Implement email sending logic
        Log::info('Payment confirmation email queued', $data);
    }

    /**
     * Send payment failure notification
     */
    private function sendFailureNotification(array $data)
    {
        // TODO: Implement failure notification logic
        Log::info('Payment failure notification queued', $data);
    }

    /**
     * Update user subscription status
     */
    private function updateUserSubscription(array $data)
    {
        // TODO: Implement subscription update logic
        Log::info('User subscription update queued', $data);
    }
}