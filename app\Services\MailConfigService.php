<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;

class MailConfigService
{
    /**
     * Mail configuration keys mapping
     */
    protected $mailConfigKeys = [
        'mail_driver' => 'MAIL_MAILER',
        'mail_host' => 'MAIL_HOST',
        'mail_port' => 'MAIL_PORT',
        'mail_username' => 'MAIL_USERNAME',
        'mail_password' => 'MAIL_PASSWORD',
        'mail_encryption' => 'MAIL_ENCRYPTION',
        'mail_from_address' => 'MAIL_FROM_ADDRESS',
        'mail_from_name' => 'MAIL_FROM_NAME',
    ];

    /**
     * Load mail configuration from .env into application config
     */
    public function loadMailConfig(\Closure $env = null)
    {
        $env = $env ?: 'env';

        $mailConfig = [
            'default' => $env('MAIL_MAILER', 'smtp'),
            'mailers' => [
                'smtp' => [
                    'transport' => 'smtp',
                    'host' => $env('MAIL_HOST', 'smtp.mailgun.org'),
                    'port' => $env('MAIL_PORT', 587),
                    'username' => $env('MAIL_USERNAME'),
                    'password' => $env('MAIL_PASSWORD'),
                    'encryption' => $env('MAIL_ENCRYPTION', 'tls'),
                    'timeout' => null,
                    'local_domain' => $env('MAIL_EHLO_DOMAIN'),
                ],
            ],
            'from' => [
                'address' => $env('MAIL_FROM_ADDRESS', '<EMAIL>'),
                'name' => $env('MAIL_FROM_NAME', 'Example'),
            ],
        ];

        // Update the config at runtime
        Config::set('mail', $mailConfig);

        return $mailConfig;
    }

    /**
     * Get current mail configuration
     */
    public function getCurrentConfig(\Closure $env = null): array
    {
        $env = $env ?: 'env';
        
        return [
            'mail_driver' => $env('MAIL_MAILER', 'smtp'),
            'mail_host' => $env('MAIL_HOST', 'smtp.mailgun.org'),
            'mail_port' => $env('MAIL_PORT', 587),
            'mail_username' => $env('MAIL_USERNAME', ''),
            'mail_password' => $env('MAIL_PASSWORD', ''),
            'mail_encryption' => $env('MAIL_ENCRYPTION', 'tls'),
            'mail_from_address' => $env('MAIL_FROM_ADDRESS', '<EMAIL>'),
            'mail_from_name' => $env('MAIL_FROM_NAME', 'Example'),
        ];
    }

    /**
     * Update mail configuration in .env file
     */
    public function updateMailConfig(array $config)
    {

        // Update .env file
        if (!$this->updateEnvironmentFile($config)) {
            return false;
        }

        // Update runtime config
        $this->loadMailConfig();

        return true;
    }

    /**
     * Update the .env file with new mail configuration
     */
    protected function updateEnvironmentFile(array $data)
    {

        $envData = [];

        // Map form keys to .env keys
        foreach ($data as $key => $value) {
            if (isset($this->mailConfigKeys[$key])) {
                $envKey = $this->mailConfigKeys[$key];
                // Special handling for mail from name (needs quotes)
                if ($key === 'mail_from_name') {
                    $value = '"' . str_replace('"', '\\"', $value) . '"';
                }
                $envData[$envKey] = $value;
            }
        }

        if (empty($envData)) {
            return false;
        }

        try {
            $envFile = base_path('.env');
            $envContent = File::get($envFile);

            foreach ($envData as $key => $value) {
                // Replace existing value or add new one
                if (preg_match("/^{$key}=(.*)$/m", $envContent)) {
                    $envContent = preg_replace(
                        "/^{$key}=(.*)$/m",
                        "{$key}={$value}",
                        $envContent
                    );
                } else {
                    $envContent .= "\n{$key}={$value}";
                }
            }

            File::put($envFile, $envContent);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update environment file', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * Verify mail configuration is properly set
     */
    public function verifyMailConfig(): bool
    {
        $config = $this->getCurrentConfig();
        
        // Check if essential mail settings are configured
        $requiredSettings = [
            'mail_driver',
            'mail_host',
            'mail_port',
            'mail_from_address',
            'mail_from_name'
        ];

        foreach ($requiredSettings as $setting) {
            if (empty($config[$setting])) {
                Log::warning("Missing required mail configuration: {$setting}");
                return false;
            }
        }

        // Additional checks for SMTP
        if ($config['mail_driver'] === 'smtp') {
            if (empty($config['mail_username']) || empty($config['mail_password'])) {
                Log::warning('SMTP credentials are required but not configured');
                return false;
            }
        }

        return true;
    }
}

