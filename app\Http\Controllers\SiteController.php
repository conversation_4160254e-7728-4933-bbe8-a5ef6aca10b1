<?php

namespace App\Http\Controllers;

use App\Mail\ContactFormMail;
use App\Models\BlogPost;
use App\Models\Page;
use App\Models\State;
use App\Models\Testimonial;
use Illuminate\Http\Request;

use Mail;

class SiteController extends Controller
{

    public function index(\App\Services\LandingPageService $landingPageService)
    {
        $landingPage = $landingPageService->getLandingPageData();
        $pageTitle = "Welcome to BharatPostal Info";
        $stateList = State::all();

        // Get latest 3 published blog posts for homepage
        $latestPosts = BlogPost::published()
            ->with(['author', 'category', 'tags'])
            ->latest('published_at')
            ->take(3)
            ->get();

        // Get paginated posts for other sections if needed
        $posts = BlogPost::where('is_published', 1)->orderBy('id', 'DESC')->paginate(6);

        // Load active plans for pricing section
        $plans = \App\Models\Plan::where('is_active', true)
            ->orderBy('sort_order', 'asc')
            ->orderBy('price', 'asc')
            ->get();

        // Load active testimonials for testimonials section
        $testimonials = Testimonial::active()->ordered()->take(6)->get();

        return view('home.welcome-modern-animated', [
            'landingPage' => $landingPage,
            'stateList' => $stateList,
            'pageTitle' => $pageTitle,
            'posts' => $posts,
            'latestPosts' => $latestPosts,
            'plans' => $plans,
            'testimonials' => $testimonials,
        ]);
    }

    public function pageShow($slug)
    {
        $page = Page::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        return view("pages.{$page->template}", compact('page'));
    }


    public function contact()
    {
        $title = "Welcome to BharatPostal Info";
        return view('pages.contact-us', compact('title'));
    }

    public function submitContact(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        try {
            // Send email to admin
            Mail::to(get_setting('contact_email', config('mail.from.address')))
                ->queue(new ContactFormMail($validated));

            return back()->with('success', 'Thank you for your message. We will get back to you soon!');
        } catch (\Exception $e) {
            report($e); // Log the error
            return back()
                ->withInput()
                ->with('error', 'Sorry, there was a problem sending your message. Please try again later.');
        }
    }

    public function about()
    {
        $title = "Welcome to BharatPostal Info";
        $pageData = [
            'about' => [
                'content' => [
                    'heading' => 'About Us',
                    'subheading' => 'Welcome to BharatPostal Info',
                ]
            ]
        ];
        return view("pages.about-us", compact('title', 'pageData'));
    }
}
