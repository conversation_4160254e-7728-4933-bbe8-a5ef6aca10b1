<?php

use App\Models\User;
use Illuminate\Support\Facades\Route;

beforeEach(function () {
    $this->admin = User::factory()->create([
        'role' => 'admin',
        'status' => 'active'
    ]);
    
    $this->regularUser = User::factory()->create([
        'role' => 'user',
        'status' => 'active'
    ]);
    
    $this->inactiveAdmin = User::factory()->create([
        'role' => 'admin',
        'status' => 'inactive'
    ]);
});

describe('Admin Middleware Integration', function () {
    
    describe('Authentication Middleware', function () {
        it('redirects unauthenticated users to admin login', function () {
            $response = $this->get(route('admin.dashboard'));

            $response->assertRedirect(route('admin.login'));
        });

        it('allows authenticated admin to access admin routes', function () {
            $this->actingAs($this->admin);

            $response = $this->get(route('admin.dashboard'));

            $response->assertStatus(200);
        });

        it('maintains authentication across multiple requests', function () {
            $this->actingAs($this->admin);

            $response1 = $this->get(route('admin.dashboard'));
            $response2 = $this->get(route('admin.users.index'));
            $response3 = $this->get(route('admin.settings.index'));

            $response1->assertStatus(200);
            $response2->assertStatus(200);
            $response3->assertStatus(200);
        });
    });

    describe('Admin Role Middleware', function () {
        it('allows admin users to access admin routes', function () {
            $this->actingAs($this->admin);

            $adminRoutes = [
                'admin.dashboard',
                'admin.users.index',
                'admin.settings.index',
                'admin.blog.posts.index',
                'admin.orders.index'
            ];

            foreach ($adminRoutes as $route) {
                $response = $this->get(route($route));
                $response->assertStatus(200);
            }
        });

        it('blocks regular users from admin routes', function () {
            $this->actingAs($this->regularUser);

            $adminRoutes = [
                'admin.dashboard',
                'admin.users.index',
                'admin.settings.index',
                'admin.blog.posts.index',
                'admin.orders.index'
            ];

            foreach ($adminRoutes as $route) {
                $response = $this->get(route($route));
                $response->assertStatus(403);
            }
        });

        it('blocks inactive admin users', function () {
            $this->actingAs($this->inactiveAdmin);

            $response = $this->get(route('admin.dashboard'));

            $response->assertStatus(403);
        });
    });

    describe('CSRF Protection', function () {
        it('requires CSRF token for POST requests', function () {
            $this->actingAs($this->admin);

            $response = $this->post(route('admin.users.store'), [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'role' => 'user',
                'status' => 'active'
            ], ['HTTP_X-Requested-With' => 'XMLHttpRequest']);

            // Without CSRF token, should get 419 status
            $response->assertStatus(419);
        });

        it('accepts requests with valid CSRF token', function () {
            $this->actingAs($this->admin);

            $response = $this->withSession(['_token' => 'test-token'])
                ->post(route('admin.users.store'), [
                    'name' => 'Test User',
                    'email' => '<EMAIL>',
                    'password' => 'password123',
                    'password_confirmation' => 'password123',
                    'role' => 'user',
                    'status' => 'active',
                    '_token' => 'test-token'
                ]);

            $response->assertRedirect();
        });
    });

    describe('Session Management', function () {
        it('maintains admin session across requests', function () {
            $this->actingAs($this->admin);
            session(['admin_data' => 'test_value']);

            $response = $this->get(route('admin.dashboard'));

            expect(session('admin_data'))->toBe('test_value');
        });

        it('clears session on logout', function () {
            $this->actingAs($this->admin);
            session(['admin_data' => 'test_value']);

            $response = $this->post(route('admin.logout'));

            $response->assertRedirect(route('admin.login'));
            expect(session('admin_data'))->toBeNull();
        });

        it('regenerates session ID on login', function () {
            $oldSessionId = session()->getId();

            $response = $this->post(route('admin.login.post'), [
                'email' => $this->admin->email,
                'password' => 'password'
            ]);

            $newSessionId = session()->getId();
            expect($newSessionId)->not->toBe($oldSessionId);
        });
    });

    describe('Rate Limiting', function () {
        it('applies rate limiting to login attempts', function () {
            // Simulate multiple failed login attempts
            for ($i = 0; $i < 6; $i++) {
                $response = $this->post(route('admin.login.post'), [
                    'email' => $this->admin->email,
                    'password' => 'wrong_password'
                ]);
            }

            // Next attempt should be rate limited
            $response = $this->post(route('admin.login.post'), [
                'email' => $this->admin->email,
                'password' => 'password'
            ]);

            $response->assertStatus(429);
        });
    });

    describe('Route Protection', function () {
        it('protects all admin routes with authentication', function () {
            $adminRoutes = [
                ['GET', 'admin.dashboard'],
                ['GET', 'admin.users.index'],
                ['GET', 'admin.users.create'],
                ['GET', 'admin.settings.index'],
                ['GET', 'admin.blog.posts.index'],
                ['GET', 'admin.orders.index'],
                ['GET', 'admin.profile.index']
            ];

            foreach ($adminRoutes as [$method, $routeName]) {
                $response = $this->call($method, route($routeName));
                $response->assertRedirect(route('admin.login'));
            }
        });

        it('protects admin POST routes', function () {
            $postRoutes = [
                ['POST', 'admin.users.store', []],
                ['POST', 'admin.blog.posts.store', []],
                ['POST', 'admin.settings.api.update', []],
                ['POST', 'admin.clear-cache', []]
            ];

            foreach ($postRoutes as [$method, $routeName, $data]) {
                $response = $this->call($method, route($routeName), $data);
                $response->assertRedirect(route('admin.login'));
            }
        });
    });

    describe('Permission Levels', function () {
        it('allows super admin access to all features', function () {
            $superAdmin = User::factory()->create([
                'role' => 'admin',
                'status' => 'active',
                'is_super_admin' => true
            ]);
            $this->actingAs($superAdmin);

            $response = $this->get(route('admin.users.index'));
            $response->assertStatus(200);
        });

        it('restricts certain features for regular admin', function () {
            $regularAdmin = User::factory()->create([
                'role' => 'admin',
                'status' => 'active',
                'is_super_admin' => false
            ]);
            $this->actingAs($regularAdmin);

            $response = $this->get(route('admin.dashboard'));
            $response->assertStatus(200);
        });
    });

    describe('API Route Protection', function () {
        it('protects admin API routes', function () {
            $response = $this->get(route('admin.dashboard.stats'));

            $response->assertRedirect(route('admin.login'));
        });

        it('allows authenticated admin to access API routes', function () {
            $this->actingAs($this->admin);

            $response = $this->get(route('admin.dashboard.stats'));

            $response->assertStatus(200);
        });
    });

    describe('Error Handling', function () {
        it('handles unauthorized access gracefully', function () {
            $this->actingAs($this->regularUser);

            $response = $this->get(route('admin.dashboard'));

            $response->assertStatus(403);
            $response->assertViewIs('errors.403');
        });

        it('handles unauthenticated access gracefully', function () {
            $response = $this->get(route('admin.dashboard'));

            $response->assertRedirect(route('admin.login'));
        });
    });

    describe('Security Headers', function () {
        it('includes security headers in admin responses', function () {
            $this->actingAs($this->admin);

            $response = $this->get(route('admin.dashboard'));

            $response->assertStatus(200);
            // Check for common security headers
            $response->assertHeader('X-Frame-Options');
            $response->assertHeader('X-Content-Type-Options');
        });
    });
});