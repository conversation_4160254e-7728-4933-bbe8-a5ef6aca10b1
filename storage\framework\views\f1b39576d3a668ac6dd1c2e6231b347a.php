<div x-data="cookieConsent()" x-show="!isAccepted" x-transition x-cloak
    class="fixed bottom-0 left-0 right-0 bg-gray-900 text-white p-4 shadow-lg z-50">
    <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row items-center justify-between gap-4">
            <div class="flex-1">
                <p class="text-sm">
                    We use cookies to enhance your browsing experience and analyze our traffic.
                    By clicking "Accept", you consent to our use of cookies. Read our
                    <a href="<?php echo e(route('pages.show', 'privacy-policy')); ?>" class="underline hover:text-gray-300"
                        target="_blank">Privacy Policy</a>
                    and <a href="<?php echo e(route('pages.show', 'cookie-policy')); ?>" class="underline hover:text-gray-300"
                        target="_blank">Cookie Policy</a>
                    to learn more.
                </p>
            </div>
            <div class="flex gap-3">
                <button @click="accept"
                    class="px-6 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-sm font-medium transition-colors">
                    Accept All
                </button>
                <button @click="acceptEssential"
                    class="px-6 py-2 bg-gray-700 hover:bg-gray-800 rounded-lg text-sm font-medium transition-colors">
                    Essential Only
                </button>
                <button @click="decline"
                    class="px-6 py-2 bg-gray-700 hover:bg-gray-800 rounded-lg text-sm font-medium transition-colors">
                    Decline All
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    function cookieConsent() {
        return {
            isAccepted: false,

            init() {
                this.isAccepted = this.checkConsentStatus();
            },

            checkConsentStatus() {
                const localConsent = localStorage.getItem('cookie_consent');
                const cookieConsent = this.getCookie('cookie_consent');
                return localConsent === 'accepted' || cookieConsent === 'accepted' || localConsent === 'declined' ||
                    cookieConsent === 'declined';
            },

            getCookie(name) {
                const value = `; ${document.cookie}`;
                const parts = value.split(`; ${name}=`);
                if (parts.length === 2) return parts.pop().split(';').shift();
                return null;
            },

            setCookie(name, value, days) {
                const date = new Date();
                date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
                const expires = `expires=${date.toUTCString()}`;
                document.cookie = `${name}=${value};${expires};path=/;SameSite=Lax`;
            },

            accept() {
                this.isAccepted = true;
                localStorage.setItem('cookie_consent', 'accepted');
                this.setCookie('cookie_consent', 'accepted', 365);
                this.setCookie('cookie_consent_level', 'all', 365);
                this.updateServerPreference('all');
            },

            acceptEssential() {
                this.isAccepted = true;
                localStorage.setItem('cookie_consent', 'accepted');
                this.setCookie('cookie_consent', 'accepted', 365);
                this.setCookie('cookie_consent_level', 'essential', 365);
                this.updateServerPreference('essential');
            },

            decline() {
                this.isAccepted = true;
                localStorage.setItem('cookie_consent', 'declined');
                this.setCookie('cookie_consent', 'declined', 365);
                this.setCookie('cookie_consent_level', 'none', 365);
                this.updateServerPreference('none');
            },

            updateServerPreference(level) {
                fetch('<?php echo e(route('cookie-consent.update')); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        accepted: level !== 'none',
                        level: level
                    })
                });
            }
        }
    }
</script>
<?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/components/cookie-consent.blade.php ENDPATH**/ ?>