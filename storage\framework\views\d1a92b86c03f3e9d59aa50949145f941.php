<?php $__env->startSection('title', 'Create Pincode'); ?>
<?php $__env->startSection('page-title', 'Create New Pincode'); ?>

<?php $__env->startSection('content'); ?>
<div class="bg-white dark:bg-bg-dark shadow-md rounded-lg overflow-hidden border border-border-light dark:border-border-dark">
    <div class="px-6 py-4 border-b border-border-light dark:border-border-dark">
        <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">Pincode Information</h2>
    </div>

    <form action="<?php echo e(route('admin.pincodes.store')); ?>" method="POST" class="p-6">
        <?php echo csrf_field(); ?>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="pincode" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">Pincode</label>
                <input type="text" name="pincode" id="pincode" value="<?php echo e(old('pincode')); ?>" required
                    class="w-full border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 <?php $__errorArgs = ['pincode'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                <?php $__errorArgs = ['pincode'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div>
                <label for="area_name" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">Area Name</label>
                <input type="text" name="area_name" id="area_name" value="<?php echo e(old('area_name')); ?>" required
                    class="w-full border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 <?php $__errorArgs = ['area_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                <?php $__errorArgs = ['area_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div>
                <label for="contact_number" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">Contact Number</label>
                <input type="text" name="contact_number" id="contact_number" value="<?php echo e(old('contact_number')); ?>" required
                    class="w-full border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 <?php $__errorArgs = ['contact_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                <?php $__errorArgs = ['contact_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div>
                <label for="city" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">City</label>
                <input type="text" name="city" id="city" value="<?php echo e(old('city')); ?>" required
                    class="w-full border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div>
                <label for="state" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">State</label>
                <select name="state" id="state" required
                    class="w-full border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 <?php $__errorArgs = ['state'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                    <option value="">Select State</option>
                    <option value="Andhra Pradesh" <?php echo e(old('state') == 'Andhra Pradesh' ? 'selected' : ''); ?>>Andhra Pradesh</option>
                    <option value="Arunachal Pradesh" <?php echo e(old('state') == 'Arunachal Pradesh' ? 'selected' : ''); ?>>Arunachal Pradesh</option>
                    <option value="Assam" <?php echo e(old('state') == 'Assam' ? 'selected' : ''); ?>>Assam</option>
                    <option value="Bihar" <?php echo e(old('state') == 'Bihar' ? 'selected' : ''); ?>>Bihar</option>
                    <option value="Chhattisgarh" <?php echo e(old('state') == 'Chhattisgarh' ? 'selected' : ''); ?>>Chhattisgarh</option>
                    <option value="Goa" <?php echo e(old('state') == 'Goa' ? 'selected' : ''); ?>>Goa</option>
                    <option value="Gujarat" <?php echo e(old('state') == 'Gujarat' ? 'selected' : ''); ?>>Gujarat</option>
                    <option value="Haryana" <?php echo e(old('state') == 'Haryana' ? 'selected' : ''); ?>>Haryana</option>
                    <option value="Himachal Pradesh" <?php echo e(old('state') == 'Himachal Pradesh' ? 'selected' : ''); ?>>Himachal Pradesh</option>
                    <option value="Jharkhand" <?php echo e(old('state') == 'Jharkhand' ? 'selected' : ''); ?>>Jharkhand</option>
                    <option value="Karnataka" <?php echo e(old('state') == 'Karnataka' ? 'selected' : ''); ?>>Karnataka</option>
                    <option value="Kerala" <?php echo e(old('state') == 'Kerala' ? 'selected' : ''); ?>>Kerala</option>
                    <option value="Madhya Pradesh" <?php echo e(old('state') == 'Madhya Pradesh' ? 'selected' : ''); ?>>Madhya Pradesh</option>
                    <option value="Maharashtra" <?php echo e(old('state') == 'Maharashtra' ? 'selected' : ''); ?>>Maharashtra</option>
                    <option value="Manipur" <?php echo e(old('state') == 'Manipur' ? 'selected' : ''); ?>>Manipur</option>
                    <option value="Meghalaya" <?php echo e(old('state') == 'Meghalaya' ? 'selected' : ''); ?>>Meghalaya</option>
                    <option value="Mizoram" <?php echo e(old('state') == 'Mizoram' ? 'selected' : ''); ?>>Mizoram</option>
                    <option value="Nagaland" <?php echo e(old('state') == 'Nagaland' ? 'selected' : ''); ?>>Nagaland</option>
                    <option value="Odisha" <?php echo e(old('state') == 'Odisha' ? 'selected' : ''); ?>>Odisha</option>
                    <option value="Punjab" <?php echo e(old('state') == 'Punjab' ? 'selected' : ''); ?>>Punjab</option>
                    <option value="Rajasthan" <?php echo e(old('state') == 'Rajasthan' ? 'selected' : ''); ?>>Rajasthan</option>
                    <option value="Sikkim" <?php echo e(old('state') == 'Sikkim' ? 'selected' : ''); ?>>Sikkim</option>
                    <option value="Tamil Nadu" <?php echo e(old('state') == 'Tamil Nadu' ? 'selected' : ''); ?>>Tamil Nadu</option>
                    <option value="Telangana" <?php echo e(old('state') == 'Telangana' ? 'selected' : ''); ?>>Telangana</option>
                    <option value="Tripura" <?php echo e(old('state') == 'Tripura' ? 'selected' : ''); ?>>Tripura</option>
                    <option value="Uttar Pradesh" <?php echo e(old('state') == 'Uttar Pradesh' ? 'selected' : ''); ?>>Uttar Pradesh</option>
                    <option value="Uttarakhand" <?php echo e(old('state') == 'Uttarakhand' ? 'selected' : ''); ?>>Uttarakhand</option>
                    <option value="West Bengal" <?php echo e(old('state') == 'West Bengal' ? 'selected' : ''); ?>>West Bengal</option>
                    <option value="Andaman and Nicobar Islands" <?php echo e(old('state') == 'Andaman and Nicobar Islands' ? 'selected' : ''); ?>>Andaman and Nicobar Islands</option>
                    <option value="Chandigarh" <?php echo e(old('state') == 'Chandigarh' ? 'selected' : ''); ?>>Chandigarh</option>
                    <option value="Dadra and Nagar Haveli and Daman and Diu" <?php echo e(old('state') == 'Dadra and Nagar Haveli and Daman and Diu' ? 'selected' : ''); ?>>Dadra and Nagar Haveli and Daman and Diu</option>
                    <option value="Delhi" <?php echo e(old('state') == 'Delhi' ? 'selected' : ''); ?>>Delhi</option>
                    <option value="Jammu and Kashmir" <?php echo e(old('state') == 'Jammu and Kashmir' ? 'selected' : ''); ?>>Jammu and Kashmir</option>
                    <option value="Ladakh" <?php echo e(old('state') == 'Ladakh' ? 'selected' : ''); ?>>Ladakh</option>
                    <option value="Lakshadweep" <?php echo e(old('state') == 'Lakshadweep' ? 'selected' : ''); ?>>Lakshadweep</option>
                    <option value="Puducherry" <?php echo e(old('state') == 'Puducherry' ? 'selected' : ''); ?>>Puducherry</option>
                </select>
                <?php $__errorArgs = ['state'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div>
                <label for="district" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">District</label>
                <input type="text" name="district" id="district" value="<?php echo e(old('district')); ?>" required
                    class="w-full border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 <?php $__errorArgs = ['district'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                <?php $__errorArgs = ['district'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div>
                <label for="status" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">Status</label>
                <select name="status" id="status" 
                    class="w-full border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                    <option value="active" <?php echo e(old('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                    <option value="inactive" <?php echo e(old('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                </select>
                <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>

        <div class="mt-8 flex justify-end">
            <a href="<?php echo e(route('admin.pincodes.index')); ?>" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-text-primary-dark px-4 py-2 rounded-md text-sm font-medium mr-2">
                Cancel
            </a>
            <button type="submit" class="bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-white px-4 py-2 rounded-md text-sm font-medium">
                Create Pincode
            </button>
        </div>
    </form>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/admin/pincodes/create.blade.php ENDPATH**/ ?>