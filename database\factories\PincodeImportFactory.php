<?php

namespace Database\Factories;

use App\Models\PincodeImport;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class PincodeImportFactory extends Factory
{
    protected $model = PincodeImport::class;

    public function definition()
    {
        return [
            'user_id' => User::factory(),
            'filename' => $this->faker->words(1, true) . '.csv',
            'file_path' => 'imports/' . $this->faker->uuid . '.csv',
            'status' => $this->faker->randomElement(['processing', 'completed', 'failed']),
            'has_header' => $this->faker->boolean,
            'update_existing' => $this->faker->boolean,
            'total_records' => $this->faker->numberBetween(100, 1000),
            'successful_records' => $this->faker->numberBetween(0, 1000),
            'failed_records' => $this->faker->numberBetween(0, 50),
            'has_errors' => $this->faker->boolean,
            'error_details' => $this->faker->boolean ? json_encode([
                [
                    'row' => $this->faker->numberBetween(1, 100),
                    'data' => [$this->faker->words(1, true)],
                    'error' => $this->faker->sentence()
                ]
            ]) : null,
        ];
    }

    /**
     * Indicate that the import is processing.
     */
    public function processing()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'processing',
                'has_errors' => false,
                'error_details' => null,
            ];
        });
    }

    /**
     * Indicate that the import is completed successfully.
     */
    public function completed()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'completed',
                'has_errors' => false,
                'error_details' => null,
                'failed_records' => 0,
            ];
        });
    }

    /**
     * Indicate that the import failed.
     */
    public function failed()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'failed',
                'has_errors' => true,
                'successful_records' => 0,
            ];
        });
    }

    /**
     * Indicate that the import completed with errors.
     */
    public function completedWithErrors()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'completed',
                'has_errors' => true,
                'failed_records' => $this->faker->numberBetween(1, 50),
            ];
        });
    }
} 