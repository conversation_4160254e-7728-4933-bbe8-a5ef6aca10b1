@extends('layouts.app')

@include('layouts.partials.tools-json-ld')

@section('content')
    <x-breadcrumb :segments="$breadcrumbs" :pageTitle="$pageTitle" />
    <div class="container max-w-6xl mx-auto py-8 px-4 bg-bg-light dark:bg-bg-dark transition-colors duration-300">
        <div class="flex flex-wrap -mx-4">
            <div class="w-full lg:w-2/3 px-4">
                <h2 class="text-3xl font-bold mb-6 text-text-primary-light dark:text-text-primary-dark">India Post Office & Pincode Finder</h2>
                <div class="mb-8">
                    <p class="mb-4 text-text-secondary-light dark:text-text-secondary-dark">Find accurate pincode information for any location in India. Our comprehensive directory helps you search by post office name or pincode number to get precise postal details instantly.</p>

                    <h2 class="text-xl font-semibold mb-2 text-text-primary-light dark:text-text-primary-dark">How to Use This Tool</h2>
                    <p class="mb-4 text-text-secondary-light dark:text-text-secondary-dark">To get started, please follow these simple steps:</p>

                    <ul class="list-disc pl-5 mb-4 space-y-2 text-text-secondary-light dark:text-text-secondary-dark">
                        <li><strong class="text-text-primary-light dark:text-text-primary-dark">Select State:</strong> Choose your state from the dropdown menu to narrow down the search area.</li>
                        <li><strong class="text-text-primary-light dark:text-text-primary-dark">Choose District:</strong> Select the appropriate district from the available options.</li>
                        <li><strong class="text-text-primary-light dark:text-text-primary-dark">Search Method:</strong> Enter either a post office name OR a pincode number - the tool will search automatically as you type.</li>
                    </ul>
                </div>

                <div class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-border-light dark:border-border-dark mb-8 transition-colors duration-300">
                    <div class="bg-gray-50 dark:bg-slate-700 p-4 rounded-t-lg border-b border-border-light dark:border-border-dark transition-colors duration-300">
                        <h2 class="text-xl font-semibold mb-2 text-text-primary-light dark:text-text-primary-dark">Search Pincode Directory</h2>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark">Enter your location details below to find accurate pincode information. Results will appear automatically as you type.</p>
                    </div>
                    <div class="p-6" x-data="searchForm()" x-init="init()">
                        <form id="locationForm" @submit.prevent class="space-y-4">
                            <div>
                                <label for="state" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Select State:</label>
                                <select name="state" id="state" x-model="stateId" @change="fetchDistricts()"
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-slate-700 text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors duration-300">
                                    <option value="">Select State</option>
                                    @foreach ($m_states as $state)
                                        <option value="{{ $state->id }}">{{ ucfirst($state->name) }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div>
                                <label for="district" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Select District:</label>
                                <select name="district" id="district" x-model="districtId" @change="handleDistrictChange()"
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-slate-700 text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors duration-300 disabled:bg-gray-100 dark:disabled:bg-gray-600 disabled:text-gray-500 dark:disabled:text-gray-400"
                                    :disabled="!districts.length || isLoading">
                                    <option value="">Select District</option>
                                    <template x-for="district in districts" :key="district.id">
                                        <option :value="district.name" x-text="district.name.charAt(0).toUpperCase() + district.name.slice(1)"></option>
                                    </template>
                                </select>
                            </div>
                            <div>
                                <label for="post_office" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Enter Post Office Name:</label>
                                <input type="text" name="post_office" id="post_office" x-model.debounce.300ms="postOffice"
                                    @input="handleInputToggle('postOffice')" :disabled="!districtId || isLoading"
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-slate-700 text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors duration-300 disabled:bg-gray-100 dark:disabled:bg-gray-600 disabled:text-gray-500 dark:disabled:text-gray-400"
                                    placeholder="Ex. Nilaj">
                            </div>

                            <div class="flex items-center justify-center my-4">
                                <div class="w-full border-t border-border-light dark:border-border-dark"></div>
                                <span class="bg-white dark:bg-slate-800 px-4 text-sm text-text-secondary-light dark:text-text-secondary-dark font-medium">OR</span>
                                <div class="w-full border-t border-border-light dark:border-border-dark"></div>
                            </div>

                            <div>
                                <label for="pin_code" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Enter Pincode Number:</label>
                                <input type="text" name="pin_code" id="pin_code" x-model.debounce.300ms="pinCode"
                                    @input="handleInputToggle('pinCode')" :disabled="!districtId || isLoading"
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-slate-700 text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors duration-300 disabled:bg-gray-100 dark:disabled:bg-gray-600 disabled:text-gray-500 dark:disabled:text-gray-400"
                                    placeholder="Ex. 441901">
                            </div>
                        </form>

                        <div class="mt-6">
                            <div id="results">
                                <template x-if="searchResults.length">
                                    <div>
                                        <h4 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark mb-3">Search Results:</h4>
                                        <div class="space-y-3">
                                            <template x-for="result in searchResults" :key="result.id">
                                                <div class="border border-border-light dark:border-border-dark rounded-lg p-4 bg-gray-50 dark:bg-slate-700 hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 transition-colors duration-300">
                                                    <p class="mb-2">
                                                        <span class="font-semibold text-text-primary-light dark:text-text-primary-dark">Post Office Name:</span>
                                                        <a :href="window.location.origin + '/pincodes/' + result.state + '/' + result.district + '/' + result.name"
                                                            class="text-primary-light dark:text-primary-dark hover:text-primary-light/80 dark:hover:text-primary-dark/80 ml-1 font-medium"
                                                            x-text="result.name.charAt(0).toUpperCase() + result.name.slice(1)"></a>
                                                    </p>
                                                    <p>
                                                        <span class="font-semibold text-text-primary-light dark:text-text-primary-dark">Pincode:</span>
                                                        <a :href="window.location.origin + '/pincodes/' + result.state + '/' + result.district + '/postal-code/' + result.pincode"
                                                            class="text-primary-light dark:text-primary-dark hover:text-primary-light/80 dark:hover:text-primary-dark/80 ml-1 font-medium"
                                                            x-text="result.pincode"></a>
                                                    </p>
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </template>
                                <template x-if="stateId && districtId && (postOffice || pinCode) && !searchResults.length && !isLoading">
                                    <div class="text-center py-8 text-text-secondary-light dark:text-text-secondary-dark">
                                        <p class="font-medium">No results found. Please try different search criteria.</p>
                                    </div>
                                </template>
                            </div>
                        </div>

                        <p class="mt-4 text-sm text-text-secondary-light dark:text-text-secondary-dark italic">Results appear automatically as you type. If you need assistance, feel free to contact our support team!</p>
                    </div>
                </div>

                <div class="bg-primary-light/10 dark:bg-primary-dark/10 border-l-4 border-primary-light dark:border-primary-dark text-primary-light dark:text-primary-dark p-4 mb-8 rounded-r-md transition-colors duration-300" role="alert">
                    <h4 class="font-bold mb-2">About India's Postal System</h4>
                    <p class="text-text-primary-light dark:text-text-primary-dark">The 6-digit pincode system in India was introduced on August 15, 1972, by the Indian Postal Service. Each pincode represents a specific geographical region, making mail sorting and delivery more efficient across the country.</p>
                </div>

                <div class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-border-light dark:border-border-dark mb-8 transition-colors duration-300">
                    <div class="bg-gray-50 dark:bg-slate-700 p-4 rounded-t-lg border-b border-border-light dark:border-border-dark transition-colors duration-300">
                        <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">Understanding Pincode Structure</h2>
                    </div>
                    <div class="p-6">
                        <p class="mb-4 text-text-secondary-light dark:text-text-secondary-dark">Indian pincodes follow a systematic structure where each digit has significance:</p>
                        <ul class="list-disc pl-5 mb-4 space-y-2 text-text-secondary-light dark:text-text-secondary-dark">
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">First digit:</strong> One of the 9 postal zones in India</li>
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">Second digit:</strong> Sub-region within the zone</li>
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">Third digit:</strong> Sorting district</li>
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">Last three digits:</strong> Specific post office code</li>
                        </ul>
                        <p class="mb-4 text-text-secondary-light dark:text-text-secondary-dark">Benefits of knowing your pincode:</p>
                        <ul class="list-disc pl-5 space-y-2 text-text-secondary-light dark:text-text-secondary-dark">
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">Faster Delivery:</strong> Ensures accurate and quick mail delivery</li>
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">Online Shopping:</strong> Required for e-commerce and form filling</li>
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">Address Verification:</strong> Essential for official documentation</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="w-full lg:w-1/3 px-4">
                <div class="sticky top-20">
                    @include('pincodes.partials.sidebar')
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div x-show="isLoading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;" x-cloak>
        <div class="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-xl">
            <div class="animate-spin rounded-full h-10 w-10 border-4 border-primary-light dark:border-primary-dark border-t-transparent"></div>
            <p class="mt-3 text-text-primary-light dark:text-text-primary-dark">Loading...</p>
        </div>
    </div>

    <!-- Reviews Section -->
    <div class="container max-w-6xl mx-auto px-4 py-8">
        <x-tool-reviews
            :reviews="$reviews"
            :tool="$tool"
            :hasMoreReviews="$hasMoreReviews ?? false"
            :totalReviewsCount="$totalReviewsCount ?? 0"
        />
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('searchForm', () => ({
                stateId: '',
                districtId: '',
                districts: [],
                searchResults: [],
                postOffice: '',
                pinCode: '',
                isLoading: false,
                searchTimeout: null,

                init() {
                    // Initialize any required data
                },

                async fetchDistricts() {
                    if (!this.stateId) {
                        this.resetFields();
                        return;
                    }

                    try {
                        this.isLoading = true;
                        const response = await fetch(`/get-districts/${this.stateId}`);
                        if (!response.ok) throw new Error('Network response was not ok');
                        this.districts = await response.json();
                    } catch (error) {
                        console.error('Error fetching districts:', error);
                        this.districts = [];
                    } finally {
                        this.isLoading = false;
                    }
                },

                resetFields() {
                    this.districtId = '';
                    this.postOffice = '';
                    this.pinCode = '';
                    this.districts = [];
                    this.searchResults = [];
                },

                handleDistrictChange() {
                    this.postOffice = '';
                    this.pinCode = '';
                    this.searchResults = [];
                },

                handleInputToggle(field) {
                    if (field === 'postOffice') {
                        this.pinCode = '';
                    } else {
                        this.postOffice = '';
                    }
                    this.debounceSearch(field === 'postOffice' ? this.postOffice : this.pinCode);
                },

                debounceSearch(query) {
                    // Clear any existing timeout
                    if (this.searchTimeout) {
                        clearTimeout(this.searchTimeout);
                    }

                    // Set a new timeout
                    this.searchTimeout = setTimeout(() => {
                        this.performSearch(query);
                    }, 300); // Wait 300ms after last keystroke before searching
                },

                async performSearch(query) {
                    if (!query || !this.stateId || !this.districtId) return;

                    try {
                        this.isLoading = true;
                        const response = await fetch('/post-office-search', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector(
                                    'meta[name="csrf-token"]').content
                            },
                            body: JSON.stringify({
                                query: query,
                                stateId: this.stateId,
                                districtId: this.districtId,
                                searchType: this.pinCode ? 'pincode' : 'postoffice'
                            })
                        });

                        if (!response.ok) {
                            const errorData = await response.json();
                            throw new Error(errorData.message || 'Network response was not ok');
                        }

                        const data = await response.json();
                        this.searchResults = Array.isArray(data) ? data : [];

                    } catch (error) {
                        console.error('Error performing search:', error);
                        this.searchResults = [];
                    } finally {
                        this.isLoading = false;
                    }
                }
            }));
        });
    </script>
@endpush