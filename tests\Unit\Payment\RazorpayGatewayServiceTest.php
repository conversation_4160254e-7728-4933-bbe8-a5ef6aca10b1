<?php

namespace Tests\Unit\Payment;

use App\Models\Order;
use App\Models\Payment;
use App\Models\PaymentGateway;
use App\Services\Payment\RazorpayGatewayService;
use App\Services\Payment\PaymentResponse;
use App\Services\Payment\WebhookResponse;
use App\Services\Payment\RefundResponse;
use App\Services\Payment\PaymentStatus;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Tests\TestCase;
use Mockery;

class RazorpayGatewayServiceTest extends TestCase
{
    use RefreshDatabase;

    protected RazorpayGatewayService $service;
    protected PaymentGateway $gateway;
    protected Order $order;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test gateway configuration with unique name
        $this->gateway = PaymentGateway::factory()->create([
            'name' => 'razorpay_' . uniqid(),
            'display_name' => 'Razorpay',
            'is_active' => true,
            'configuration' => [
                'key_id' => 'rzp_test_1234567890',
                'key_secret' => 'test_secret_key',
                'webhook_secret' => 'test_webhook_secret'
            ],
            'supported_currencies' => ['INR']
        ]);

        $this->order = Order::factory()->create([
            'amount' => 1000.00,
            'currency' => 'INR'
        ]);

        $this->service = new RazorpayGatewayService($this->gateway);
    }

    public function test_creates_payment_successfully()
    {
        // Mock the Razorpay API response
        $mockRazorpayOrder = [
            'id' => 'order_test123',
            'amount' => 100000, // Amount in paise
            'currency' => 'INR',
            'status' => 'created'
        ];

        // We'll need to mock the actual Razorpay API calls
        // For now, let's test the service structure
        $this->assertInstanceOf(RazorpayGatewayService::class, $this->service);
    }

    public function test_validates_required_configuration()
    {
        // Test with missing configuration
        $invalidGateway = PaymentGateway::factory()->create([
            'name' => 'razorpay_invalid_' . uniqid(),
            'configuration' => []
        ]);

        $this->expectException(PaymentGatewayException::class);
        $this->expectExceptionMessage('Missing required Razorpay configuration');

        new RazorpayGatewayService($invalidGateway);
    }

    public function test_validates_currency_support()
    {
        $unsupportedOrder = Order::factory()->create([
            'amount' => 100.00,
            'currency' => 'USD'
        ]);

        $this->expectException(PaymentGatewayException::class);
        $this->expectExceptionMessage('Currency not supported by gateway');

        $this->service->createPayment($unsupportedOrder);
    }

    public function test_converts_amount_to_paise()
    {
        $amount = 1000.50; // INR
        $expectedPaise = 100050; // Amount in paise

        $convertedAmount = $this->service->convertToPaise($amount);
        $this->assertEquals($expectedPaise, $convertedAmount);
    }

    public function test_generates_correct_checkout_data()
    {
        $checkoutData = $this->service->generateCheckoutData($this->order, 'order_test123');

        $this->assertIsArray($checkoutData);
        $this->assertArrayHasKey('key', $checkoutData);
        $this->assertArrayHasKey('amount', $checkoutData);
        $this->assertArrayHasKey('currency', $checkoutData);
        $this->assertArrayHasKey('order_id', $checkoutData);
        $this->assertEquals('rzp_test_1234567890', $checkoutData['key']);
        $this->assertEquals(100000, $checkoutData['amount']); // Amount in paise
        $this->assertEquals('INR', $checkoutData['currency']);
        $this->assertEquals('order_test123', $checkoutData['order_id']);
    }

    public function test_verifies_webhook_signature()
    {
        $payload = json_encode(['event' => 'payment.captured']);
        $signature = hash_hmac('sha256', $payload, 'test_webhook_secret');

        $request = Request::create('/webhook', 'POST', [], [], [], [
            'HTTP_X_RAZORPAY_SIGNATURE' => $signature
        ], $payload);

        $isValid = $this->service->verifyWebhookSignature($request);
        $this->assertTrue($isValid);
    }

    public function test_rejects_invalid_webhook_signature()
    {
        $payload = json_encode(['event' => 'payment.captured']);
        $invalidSignature = 'invalid_signature';

        $request = Request::create('/webhook', 'POST', [], [], [], [
            'HTTP_X_RAZORPAY_SIGNATURE' => $invalidSignature
        ], $payload);

        $isValid = $this->service->verifyWebhookSignature($request);
        $this->assertFalse($isValid);
    }

    public function test_handles_payment_captured_webhook()
    {
        // Create a payment record for the webhook
        $payment = Payment::factory()->create([
            'gateway_payment_id' => 'pay_test123',
            'gateway_order_id' => 'order_test123',
            'gateway_id' => $this->gateway->id,
            'payment_status' => Payment::STATUS_PENDING
        ]);

        $webhookPayload = [
            'event' => [
                'event' => 'payment.captured'
            ],
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'amount' => 100000,
                        'currency' => 'INR',
                        'status' => 'captured'
                    ]
                ]
            ]
        ];

        $request = Request::create('/webhook', 'POST', [], [], [], [
            'CONTENT_TYPE' => 'application/json'
        ], json_encode($webhookPayload));
        
        // Mock the webhook signature verification
        $this->service = Mockery::mock(RazorpayGatewayService::class, [$this->gateway])
            ->makePartial()
            ->shouldAllowMockingProtectedMethods()
            ->shouldReceive('verifyWebhookSignature')
            ->andReturn(true)
            ->getMock();

        $response = $this->service->handleWebhook($request);

        $this->assertInstanceOf(WebhookResponse::class, $response);
        $this->assertTrue($response->success);
        $this->assertEquals('payment.captured', $response->eventType);
    }

    public function test_handles_payment_failed_webhook()
    {
        // Create a payment record for the webhook
        $payment = Payment::factory()->create([
            'gateway_payment_id' => 'pay_test123',
            'gateway_order_id' => 'order_test123',
            'gateway_id' => $this->gateway->id,
            'payment_status' => Payment::STATUS_PENDING
        ]);

        $webhookPayload = [
            'event' => [
                'event' => 'payment.failed'
            ],
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'amount' => 100000,
                        'currency' => 'INR',
                        'status' => 'failed',
                        'error_code' => 'BAD_REQUEST_ERROR',
                        'error_description' => 'Payment failed'
                    ]
                ]
            ]
        ];

        $request = Request::create('/webhook', 'POST', [], [], [], [
            'CONTENT_TYPE' => 'application/json'
        ], json_encode($webhookPayload));
        
        // Mock the webhook signature verification
        $this->service = Mockery::mock(RazorpayGatewayService::class, [$this->gateway])
            ->makePartial()
            ->shouldAllowMockingProtectedMethods()
            ->shouldReceive('verifyWebhookSignature')
            ->andReturn(true)
            ->getMock();

        $response = $this->service->handleWebhook($request);

        $this->assertInstanceOf(WebhookResponse::class, $response);
        $this->assertTrue($response->success);
        $this->assertEquals('payment.failed', $response->eventType);
    }

    public function test_validates_payment_signature()
    {
        $paymentId = 'pay_test123';
        $orderId = 'order_test123';
        $signature = hash_hmac('sha256', $orderId . '|' . $paymentId, 'test_secret_key');

        $isValid = $this->service->validatePaymentSignature($orderId, $paymentId, $signature);
        $this->assertTrue($isValid);
    }

    public function test_rejects_invalid_payment_signature()
    {
        $paymentId = 'pay_test123';
        $orderId = 'order_test123';
        $invalidSignature = 'invalid_signature';

        $isValid = $this->service->validatePaymentSignature($orderId, $paymentId, $invalidSignature);
        $this->assertFalse($isValid);
    }

    public function test_formats_error_response()
    {
        $errorData = [
            'code' => 'BAD_REQUEST_ERROR',
            'description' => 'Invalid payment method',
            'field' => 'method'
        ];

        $response = $this->service->formatErrorResponse($errorData);

        $this->assertInstanceOf(PaymentResponse::class, $response);
        $this->assertFalse($response->success);
        $this->assertEquals('BAD_REQUEST_ERROR', $response->errorCode);
        $this->assertStringContainsString('Invalid payment method', $response->message);
    }

    public function test_handles_api_exceptions()
    {
        // Test handling of Razorpay API exceptions
        $this->expectException(PaymentGatewayException::class);

        // Simulate an API error
        $this->service->handleApiException(new \Exception('API connection failed'));
    }

    public function test_supports_refund_operations()
    {
        $payment = Payment::factory()->create([
            'gateway_payment_id' => 'pay_test123',
            'amount' => 1000.00,
            'currency' => 'INR',
            'payment_status' => Payment::STATUS_COMPLETED,
            'gateway_id' => $this->gateway->id
        ]);

        $refundAmount = 500.00;

        // Mock the refund response
        $this->service = Mockery::mock(RazorpayGatewayService::class, [$this->gateway])
            ->makePartial()
            ->shouldReceive('createRazorpayRefund')
            ->with('pay_test123', 500.00)
            ->andReturn([
                'id' => 'rfnd_test123',
                'amount' => 50000,
                'currency' => 'INR',
                'status' => 'processed'
            ])
            ->getMock();

        $response = $this->service->refundPayment('pay_test123', $refundAmount);

        $this->assertInstanceOf(RefundResponse::class, $response);
        $this->assertTrue($response->success);
        $this->assertEquals('rfnd_test123', $response->refundId);
        $this->assertEquals(500.00, $response->refundAmount);
    }

    public function test_gets_payment_status()
    {
        $paymentId = 'pay_test123';

        // Mock the payment status response
        $this->service = Mockery::mock(RazorpayGatewayService::class, [$this->gateway])
            ->makePartial()
            ->shouldReceive('fetchRazorpayPayment')
            ->with($paymentId)
            ->andReturn([
                'id' => $paymentId,
                'order_id' => 'order_test123',
                'status' => 'captured',
                'amount' => 100000,
                'currency' => 'INR',
                'created_at' => time()
            ])
            ->getMock();

        $status = $this->service->getPaymentStatus($paymentId);

        $this->assertInstanceOf(PaymentStatus::class, $status);
        $this->assertTrue($status->success);
        $this->assertEquals('completed', $status->status);
        $this->assertEquals(1000.00, $status->amount);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}