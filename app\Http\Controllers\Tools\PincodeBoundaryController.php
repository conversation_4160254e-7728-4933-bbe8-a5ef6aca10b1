<?php

namespace App\Http\Controllers\Tools;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\PinCode;

class PincodeBoundaryController extends Controller
{
    /**
     * Find the nearest pincode for given coordinates (lat, lng)
     * GET /pincode-geo/nearest?lat=...&lng=...
     */
    public function findPincodeByCoordinates(Request $request)
    {
        // abort(500, 'test');
        $lat = $request->query('lat');
        $lng = $request->query('lng');
        \Log::debug('findPincodeByCoordinates called', ['lat' => $lat, 'lng' => $lng]);
        if (!$lat || !$lng) {
            \Log::warning('Latitude or longitude missing', ['lat' => $lat, 'lng' => $lng]);
            return response()->json(['error' => 'Latitude and longitude are required.'], 400);
        }
        $distance = 10; // km, bounding box
        $latRange = $distance / 111; // 1 deg lat ~ 111km
        $lngRange = $distance / (111 * cos(deg2rad($lat)));
        \Log::debug('Bounding box', [
            'lat_min' => $lat - $latRange,
            'lat_max' => $lat + $latRange,
            'lng_min' => $lng - $lngRange,
            'lng_max' => $lng + $lngRange
        ]);
        $nearest = PinCode::select(DB::raw("*, (6371 * acos(cos(radians($lat)) * cos(radians(latitude)) * cos(radians(longitude) - radians($lng)) + sin(radians($lat)) * sin(radians(latitude)))) AS distance"))
            ->whereBetween('latitude', [$lat - $latRange, $lat + $latRange])
            ->whereBetween('longitude', [$lng - $lngRange, $lng + $lngRange])
            ->orderBy('distance')
            ->first();
        \Log::debug('Nearest pincode result', ['result' => $nearest]);
        if (!$nearest) {
            \Log::warning('No pincode found near this location', ['lat' => $lat, 'lng' => $lng]);
            return response()->json(['error' => 'No pincode found near this location.'], 404);
        }
        // Fetch boundary from PincodeGeo
        $boundary = null;
        $geo = \App\Models\PincodeGeo::where('pincode', $nearest->pincode)->first();
        if ($geo) {
            $boundary = [
                'type' => 'Feature',
                'properties' => [
                    'pincode' => $geo->pincode,
                    'office_name' => $geo->office_name,
                    'division' => $geo->division,
                    'region' => $geo->region,
                    'circle' => $geo->circle,
                ],
                'geometry' => $geo->geometry,
            ];
        }
        $result = $nearest->toArray();
        $result['boundary'] = $boundary;
        return response()->json($result);
    }

    /**
     * Get the boundary (GeoJSON) for a given pincode using PincodeGeo model
     * GET /pincode-geo/boundary/{pincode}
     */
    public function getBoundaryByPincode($pincode)
    {
        $pincodeData = \App\Models\PincodeGeo::where('pincode', $pincode)->first();
        if (!$pincodeData) {
            return response()->json(['error' => 'Pincode not found'], 404);
        }
        return response()->json([
            'type' => 'Feature',
            'properties' => [
                'pincode' => $pincodeData->pincode,
                'office_name' => $pincodeData->office_name,
                'division' => $pincodeData->division,
                'region' => $pincodeData->region,
                'circle' => $pincodeData->circle,
            ],
            'geometry' => $pincodeData->geometry,
        ]);
    }
}
