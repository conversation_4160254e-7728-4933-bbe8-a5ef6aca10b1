<?php

use App\Models\User;
use App\Models\CourierDict;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\Feature\Controllers\Admin\Traits\ActsAsAdmin;

uses(RefreshDatabase::class, WithFaker::class, ActsAsAdmin::class);

// php artisan test tests/Feature/Controllers/Admin/CourierDictControllerTest.php

/*
|--------------------------------------------------------------------------
| Helper Functions
|--------------------------------------------------------------------------
*/

function createCourierDictData() {
    return [
        'term' => 'Express Delivery',
        'description' => 'Fast delivery service',
        'long_description' => 'A premium delivery service that ensures packages are delivered within 24-48 hours.',
        'tag' => 'delivery'
    ];
}

/*
|--------------------------------------------------------------------------
| Index Tests
|--------------------------------------------------------------------------
*/

test('admin can view courier dictionary index', function () {
    $this->actingAsAdmin();
    CourierDict::factory()->count(3)->create();

    $response = $this->get(route('admin.courier-dict.index'));

    $response->assertStatus(200);
    $response->assertViewIs('admin.courier-dict.index');
    $response->assertViewHas('terms');
    // Don't assert specific content since factory creates random data
});

test('courier dictionary index shows pagination', function () {
    $this->actingAsAdmin();
    CourierDict::factory()->count(15)->create();

    $response = $this->get(route('admin.courier-dict.index'));

    $response->assertStatus(200);
    $terms = $response->viewData('terms');
    expect($terms->count())->toBeLessThanOrEqual(10);
    expect($terms->hasPages())->toBeTrue();
});

/*
|--------------------------------------------------------------------------
| Create Tests
|--------------------------------------------------------------------------
*/

test('admin can view create courier dictionary form', function () {
    $this->actingAsAdmin();

    $response = $this->get(route('admin.courier-dict.create'));

    $response->assertStatus(200);
    $response->assertViewIs('admin.courier-dict.create');
});

/*
|--------------------------------------------------------------------------
| Store Tests
|--------------------------------------------------------------------------
*/

test('admin can create a new courier dictionary term', function () {
    $this->actingAsAdmin();
    $data = createCourierDictData();

    $response = $this->post(route('admin.courier-dict.store'), $data);

    $response->assertRedirect(route('admin.courier-dict.index'));
    $response->assertSessionHas('success', 'Term created successfully.');
    
    $this->assertDatabaseHas('courier_dict', [
        'term' => 'Express Delivery',
        'description' => 'Fast delivery service',
        'tag' => 'delivery'
    ]);
});

test('store validates required fields', function () {
    $this->actingAsAdmin();

    $response = $this->post(route('admin.courier-dict.store'), []);

    $response->assertSessionHasErrors(['term', 'description', 'long_description', 'tag']);
});

test('store validates term uniqueness', function () {
    $this->actingAsAdmin();
    CourierDict::factory()->create(['term' => 'Express Delivery']);

    $response = $this->post(route('admin.courier-dict.store'), [
        'term' => 'Express Delivery',
        'description' => 'Another description',
        'long_description' => 'Another long description',
        'tag' => 'delivery'
    ]);

    $response->assertSessionHasErrors(['term']);
});

test('store validates field lengths', function () {
    $this->actingAsAdmin();

    $response = $this->post(route('admin.courier-dict.store'), [
        'term' => str_repeat('a', 256), // Too long
        'description' => 'Valid description',
        'long_description' => 'Valid long description',
        'tag' => str_repeat('b', 51) // Too long
    ]);

    $response->assertSessionHasErrors(['term', 'tag']);
});

/*
|--------------------------------------------------------------------------
| Edit Tests
|--------------------------------------------------------------------------
*/

test('admin can view edit courier dictionary form', function () {
    $this->actingAsAdmin();
    $courierDict = CourierDict::factory()->create();

    $response = $this->get(route('admin.courier-dict.edit', $courierDict));

    $response->assertStatus(200);
    $response->assertViewIs('admin.courier-dict.edit');
    $response->assertViewHas('courierDict');
});

test('edit shows correct courier dictionary data', function () {
    $this->actingAsAdmin();
    $courierDict = CourierDict::factory()->create([
        'term' => 'Test Term',
        'description' => 'Test Description'
    ]);

    $response = $this->get(route('admin.courier-dict.edit', $courierDict));

    $response->assertStatus(200);
    $response->assertSee('Test Term');
    $response->assertSee('Test Description');
});

/*
|--------------------------------------------------------------------------
| Update Tests
|--------------------------------------------------------------------------
*/

test('admin can update courier dictionary term', function () {
    $this->actingAsAdmin();
    $courierDict = CourierDict::factory()->create();
    $data = createCourierDictData();

    $response = $this->put(route('admin.courier-dict.update', $courierDict), $data);

    $response->assertRedirect(route('admin.courier-dict.index'));
    $response->assertSessionHas('success', 'Term updated successfully.');
    
    $this->assertDatabaseHas('courier_dict', [
        'id' => $courierDict->id,
        'term' => 'Express Delivery',
        'description' => 'Fast delivery service'
    ]);
});

test('update validates required fields', function () {
    $this->actingAsAdmin();
    $courierDict = CourierDict::factory()->create();

    $response = $this->put(route('admin.courier-dict.update', $courierDict), []);

    $response->assertSessionHasErrors(['term', 'description', 'long_description', 'tag']);
});

test('update validates term uniqueness excluding current record', function () {
    $this->actingAsAdmin();
    $courierDict1 = CourierDict::factory()->create(['term' => 'Term 1']);
    $courierDict2 = CourierDict::factory()->create(['term' => 'Term 2']);

    // Should allow updating with same term
    $response = $this->put(route('admin.courier-dict.update', $courierDict1), [
        'term' => 'Term 1', // Same term, should be allowed
        'description' => 'Updated description',
        'long_description' => 'Updated long description',
        'tag' => 'updated'
    ]);

    $response->assertRedirect(route('admin.courier-dict.index'));
    $response->assertSessionHas('success');

    // Should not allow updating with another record's term
    $response = $this->put(route('admin.courier-dict.update', $courierDict1), [
        'term' => 'Term 2', // Another record's term, should fail
        'description' => 'Updated description',
        'long_description' => 'Updated long description',
        'tag' => 'updated'
    ]);

    $response->assertSessionHasErrors(['term']);
});

test('update validates field lengths', function () {
    $this->actingAsAdmin();
    $courierDict = CourierDict::factory()->create();

    $response = $this->put(route('admin.courier-dict.update', $courierDict), [
        'term' => str_repeat('a', 256), // Too long
        'description' => 'Valid description',
        'long_description' => 'Valid long description',
        'tag' => str_repeat('b', 51) // Too long
    ]);

    $response->assertSessionHasErrors(['term', 'tag']);
});

/*
|--------------------------------------------------------------------------
| Destroy Tests
|--------------------------------------------------------------------------
*/

test('admin can delete courier dictionary term', function () {
    $this->actingAsAdmin();
    $courierDict = CourierDict::factory()->create();

    $response = $this->delete(route('admin.courier-dict.destroy', $courierDict));

    $response->assertRedirect(route('admin.courier-dict.index'));
    $response->assertSessionHas('success', 'Term deleted successfully.');
    
    // Check if the record is soft deleted
    $this->assertSoftDeleted('courier_dict', ['id' => $courierDict->id]);
});

test('delete removes the correct record', function () {
    $this->actingAsAdmin();
    $courierDict1 = CourierDict::factory()->create(['term' => 'Term 1']);
    $courierDict2 = CourierDict::factory()->create(['term' => 'Term 2']);

    $this->delete(route('admin.courier-dict.destroy', $courierDict1));

    // Check if the first record is soft deleted
    $this->assertSoftDeleted('courier_dict', ['id' => $courierDict1->id]);
    
    // Check if the second record still exists
    $this->assertDatabaseHas('courier_dict', ['id' => $courierDict2->id]);
});

/*
|--------------------------------------------------------------------------
| Search Tests
|--------------------------------------------------------------------------
*/

test('admin can search courier dictionary terms', function () {
    $this->actingAsAdmin();
    CourierDict::factory()->create(['term' => 'Express Delivery']);
    CourierDict::factory()->create(['term' => 'Standard Delivery']);
    CourierDict::factory()->create(['term' => 'Pickup Service']);

    $response = $this->get(route('admin.courier-dict.search', ['search' => 'Express']));

    $response->assertStatus(200);
    $response->assertViewIs('admin.courier-dict.index');
    $response->assertViewHas('terms');
    $response->assertViewHas('search');
    $response->assertSee('Express Delivery');
    $response->assertDontSee('Standard Delivery');
});

test('search returns empty results for non-matching terms', function () {
    $this->actingAsAdmin();
    CourierDict::factory()->create(['term' => 'Express Delivery']);

    $response = $this->get(route('admin.courier-dict.search', ['search' => 'NonExistent']));

    $response->assertStatus(200);
    $terms = $response->viewData('terms');
    expect($terms->count())->toBe(0);
});

test('search handles empty search parameter', function () {
    $this->actingAsAdmin();
    CourierDict::factory()->count(3)->create();

    $response = $this->get(route('admin.courier-dict.search', ['search' => '']));

    $response->assertStatus(200);
    $terms = $response->viewData('terms');
    expect($terms->count())->toBeGreaterThan(0);
});

/*
|--------------------------------------------------------------------------
| Authorization Tests
|--------------------------------------------------------------------------
*/

test('non-admin users cannot access courier dictionary routes', function () {
    $user = User::factory()->create(['role' => 'user']);
    $this->actingAs($user);
    $courierDict = CourierDict::factory()->create();

    $this->get(route('admin.courier-dict.index'))->assertStatus(403);
    $this->get(route('admin.courier-dict.create'))->assertStatus(403);
    $this->post(route('admin.courier-dict.store'), createCourierDictData())->assertStatus(403);
    $this->get(route('admin.courier-dict.edit', $courierDict))->assertStatus(403);
    $this->put(route('admin.courier-dict.update', $courierDict), createCourierDictData())->assertStatus(403);
    $this->delete(route('admin.courier-dict.destroy', $courierDict))->assertStatus(403);
    $this->get(route('admin.courier-dict.search', ['search' => 'test']))->assertStatus(403);
});

test('guest users are redirected to login for courier dictionary routes', function () {
    $courierDict = CourierDict::factory()->create();

    $this->get(route('admin.courier-dict.index'))->assertRedirect(route('admin.login'));
    $this->get(route('admin.courier-dict.create'))->assertRedirect(route('admin.login'));
    $this->post(route('admin.courier-dict.store'), createCourierDictData())->assertRedirect(route('admin.login'));
    $this->get(route('admin.courier-dict.edit', $courierDict))->assertRedirect(route('admin.login'));
    $this->put(route('admin.courier-dict.update', $courierDict), createCourierDictData())->assertRedirect(route('admin.login'));
    $this->delete(route('admin.courier-dict.destroy', $courierDict))->assertRedirect(route('admin.login'));
    $this->get(route('admin.courier-dict.search', ['search' => 'test']))->assertRedirect(route('admin.login'));
});

/*
|--------------------------------------------------------------------------
| Edge Cases and Error Handling
|--------------------------------------------------------------------------
*/

test('store handles special characters in term', function () {
    $this->actingAsAdmin();

    $response = $this->post(route('admin.courier-dict.store'), [
        'term' => 'Express & Overnight Delivery',
        'description' => 'Fast delivery service',
        'long_description' => 'A premium delivery service with special characters.',
        'tag' => 'delivery'
    ]);

    $response->assertRedirect(route('admin.courier-dict.index'));
    $response->assertSessionHas('success');
    
    $this->assertDatabaseHas('courier_dict', [
        'term' => 'Express & Overnight Delivery'
    ]);
});

test('update preserves existing data when validation fails', function () {
    $this->actingAsAdmin();
    $courierDict = CourierDict::factory()->create([
        'term' => 'Original Term',
        'description' => 'Original Description'
    ]);

    $response = $this->put(route('admin.courier-dict.update', $courierDict), [
        'term' => '', // Invalid
        'description' => 'Updated Description',
        'long_description' => 'Updated long description',
        'tag' => 'updated'
    ]);

    $response->assertSessionHasErrors(['term']);
    
    // Original data should remain unchanged
    $courierDict->refresh();
    expect($courierDict->term)->toBe('Original Term');
    expect($courierDict->description)->toBe('Original Description');
});

test('search is case insensitive', function () {
    $this->actingAsAdmin();
    CourierDict::factory()->create(['term' => 'Express Delivery']);

    $response = $this->get(route('admin.courier-dict.search', ['search' => 'express']));

    $response->assertStatus(200);
    $response->assertSee('Express Delivery');
});

/*
|--------------------------------------------------------------------------
| Integration Tests
|--------------------------------------------------------------------------
*/

test('full CRUD workflow for courier dictionary', function () {
    $this->actingAsAdmin();
    $data = createCourierDictData();

    // Create
    $response = $this->post(route('admin.courier-dict.store'), $data);
    $response->assertRedirect(route('admin.courier-dict.index'));
    
    $courierDict = CourierDict::where('term', 'Express Delivery')->first();
    expect($courierDict)->not->toBeNull();

    // Read
    $response = $this->get(route('admin.courier-dict.index'));
    $response->assertStatus(200);

    // Update
    $updateData = [
        'term' => 'Premium Express Delivery',
        'description' => 'Premium fast delivery service',
        'long_description' => 'A premium delivery service with enhanced features.',
        'tag' => 'premium'
    ];
    
    $response = $this->put(route('admin.courier-dict.update', $courierDict), $updateData);
    $response->assertRedirect(route('admin.courier-dict.index'));
    
    $courierDict->refresh();
    expect($courierDict->term)->toBe('Premium Express Delivery');

    // Delete
    $response = $this->delete(route('admin.courier-dict.destroy', $courierDict));
    $response->assertRedirect(route('admin.courier-dict.index'));
    
    // Check if the record is soft deleted
    $this->assertSoftDeleted('courier_dict', ['id' => $courierDict->id]);
}); 