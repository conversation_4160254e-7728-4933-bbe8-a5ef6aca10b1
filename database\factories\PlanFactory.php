<?php

namespace Database\Factories;

use App\Models\Plan;
use Illuminate\Database\Eloquent\Factories\Factory;

class PlanFactory extends Factory
{
    protected $model = Plan::class;

    public function definition()
    {
        return [
            'name' => $this->faker->words(3, true),
            'description' => $this->faker->sentence(),
            'price' => $this->faker->randomFloat(2, 10, 1000),
            'currency' => $this->faker->randomElement(['USD', 'EUR', 'INR', 'GBP']),
            'billing_cycle' => $this->faker->randomElement(['monthly', 'yearly']),
            'is_active' => true,
            'features' => [
                'feature1' => $this->faker->sentence(),
                'feature2' => $this->faker->sentence(),
            ],
            'slug' => $this->faker->unique()->slug(),
            'request_limit' => $this->faker->numberBetween(1, 1000),
            'sort_order' => $this->faker->numberBetween(1, 10),
        ];
    }
} 