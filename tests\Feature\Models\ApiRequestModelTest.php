<?php

use App\Models\ApiRequest;
use App\Models\User;
use Laravel\Sanctum\PersonalAccessToken;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('ApiRequest Model', function () {
    
    describe('fillable attributes', function () {
        it('can mass assign all fillable attributes', function () {
            $data = [
                'user_id' => 1,
                'personal_access_token_id' => 1,
                'endpoint' => '/api/users',
                'method' => 'GET',
                'status' => 200,
                'ip_address' => '***********',
                'request_data' => ['param' => 'value'],
                'response_data' => ['result' => 'success'],
            ];

            $apiRequest = new ApiRequest($data);

            expect($apiRequest->user_id)->toBe(1)
                ->and($apiRequest->personal_access_token_id)->toBe(1)
                ->and($apiRequest->endpoint)->toBe('/api/users')
                ->and($apiRequest->method)->toBe('GET')
                ->and($apiRequest->status)->toBe(200)
                ->and($apiRequest->ip_address)->toBe('***********')
                ->and($apiRequest->request_data)->toBe(['param' => 'value'])
                ->and($apiRequest->response_data)->toBe(['result' => 'success']);
        });

        it('has the correct fillable attributes', function () {
            $expectedFillable = [
                'user_id',
                'personal_access_token_id',
                'endpoint',
                'method',
                'status',
                'ip_address',
                'request_data',
                'response_data',
            ];

            expect((new ApiRequest())->getFillable())->toBe($expectedFillable);
        });
    });

    describe('casts', function () {
        it('casts status to integer', function () {
            $apiRequest = ApiRequest::factory()->create(['status' => '200']);

            expect($apiRequest->status)->toBeInt()
                ->and($apiRequest->status)->toBe(200);
        });

        it('casts request_data to json/array', function () {
            $requestData = ['param1' => 'value1', 'param2' => 'value2'];
            $apiRequest = ApiRequest::factory()->create(['request_data' => $requestData]);

            expect($apiRequest->request_data)->toBeArray()
                ->and($apiRequest->request_data)->toBe($requestData);
        });

        it('casts response_data to json/array', function () {
            $responseData = ['result' => 'success', 'data' => ['id' => 1]];
            $apiRequest = ApiRequest::factory()->create(['response_data' => $responseData]);

            expect($apiRequest->response_data)->toBeArray()
                ->and($apiRequest->response_data)->toBe($responseData);
        });

        it('handles null json data correctly', function () {
            $apiRequest = ApiRequest::factory()->create([
                'request_data' => null,
                'response_data' => null,
            ]);

            expect($apiRequest->request_data)->toBeNull()
                ->and($apiRequest->response_data)->toBeNull();
        });

        it('has the correct casts configuration', function () {
            $expectedCasts = [
                'status' => 'integer',
                'request_data' => 'json',
                'response_data' => 'json',
            ];

            expect((new ApiRequest())->getCasts())->toMatchArray($expectedCasts);
        });
    });

    describe('relationships', function () {
        it('belongs to a user', function () {
            $user = User::factory()->create();
            $apiRequest = ApiRequest::factory()->create(['user_id' => $user->id]);

            expect($apiRequest->user)->toBeInstanceOf(User::class)
                ->and($apiRequest->user->id)->toBe($user->id);
        });

        it('user relationship returns BelongsTo instance', function () {
            $apiRequest = new ApiRequest();
            
            expect($apiRequest->user())->toBeInstanceOf(\Illuminate\Database\Eloquent\Relations\BelongsTo::class);
        });

        it('belongs to a personal access token', function () {
            $user = User::factory()->create();
            $token = $user->createToken('test-token');
            $apiRequest = ApiRequest::factory()->create([
                'personal_access_token_id' => $token->accessToken->id
            ]);

            expect($apiRequest->token)->toBeInstanceOf(PersonalAccessToken::class)
                ->and($apiRequest->token->id)->toBe($token->accessToken->id);
        });

        it('token relationship returns BelongsTo instance', function () {
            $apiRequest = new ApiRequest();
            
            expect($apiRequest->token())->toBeInstanceOf(\Illuminate\Database\Eloquent\Relations\BelongsTo::class);
        });

        it('can handle null relationships', function () {
            $apiRequest = ApiRequest::factory()->create([
                'user_id' => null,
                'personal_access_token_id' => null,
            ]);

            expect($apiRequest->user)->toBeNull()
                ->and($apiRequest->token)->toBeNull();
        });
    });

    describe('model creation', function () {
        it('can create an api request with factory', function () {
            $apiRequest = ApiRequest::factory()->create();

            expect($apiRequest)->toBeInstanceOf(ApiRequest::class)
                ->and($apiRequest->exists)->toBeTrue()
                ->and($apiRequest->id)->not->toBeNull();
        });

        it('can create api request with custom attributes', function () {
            $apiRequest = ApiRequest::factory()->create([
                'endpoint' => '/api/custom',
                'method' => 'POST',
                'status' => 201,
                'ip_address' => '********',
            ]);

            expect($apiRequest->endpoint)->toBe('/api/custom')
                ->and($apiRequest->method)->toBe('POST')
                ->and($apiRequest->status)->toBe(201)
                ->and($apiRequest->ip_address)->toBe('********');
        });

        it('can create api request with complex json data', function () {
            $requestData = [
                'filters' => ['status' => 'active'],
                'pagination' => ['page' => 1, 'limit' => 10],
                'includes' => ['user', 'profile']
            ];

            $responseData = [
                'data' => [
                    ['id' => 1, 'name' => 'John'],
                    ['id' => 2, 'name' => 'Jane'],
                ],
                'meta' => ['total' => 2, 'page' => 1]
            ];

            $apiRequest = ApiRequest::factory()->create([
                'request_data' => $requestData,
                'response_data' => $responseData,
            ]);

            expect($apiRequest->request_data)->toBe($requestData)
                ->and($apiRequest->response_data)->toBe($responseData);
        });
    });

    describe('database operations', function () {
        it('can update api request attributes', function () {
            $apiRequest = ApiRequest::factory()->create(['status' => 200]);

            $apiRequest->update(['status' => 404]);

            expect($apiRequest->fresh()->status)->toBe(404);
        });

        it('can delete api request', function () {
            $apiRequest = ApiRequest::factory()->create();
            $id = $apiRequest->id;

            $apiRequest->delete();

            expect(ApiRequest::find($id))->toBeNull();
        });

        it('persists json data correctly', function () {
            $requestData = ['key' => 'value', 'nested' => ['array' => true]];
            $apiRequest = ApiRequest::factory()->create(['request_data' => $requestData]);

            $fresh = ApiRequest::find($apiRequest->id);

            expect($fresh->request_data)->toBe($requestData);
        });
    });

    describe('edge cases', function () {
        it('handles empty strings for optional fields', function () {
            $apiRequest = ApiRequest::factory()->create([
                'endpoint' => '',
                'method' => '',
                'ip_address' => '',
            ]);

            expect($apiRequest->endpoint)->toBe('')
                ->and($apiRequest->method)->toBe('')
                ->and($apiRequest->ip_address)->toBe('');
        });

        it('handles very long endpoint urls', function () {
            $longEndpoint = '/api/very/long/endpoint/with/many/segments/' . str_repeat('a', 100);
            $apiRequest = ApiRequest::factory()->create(['endpoint' => $longEndpoint]);

            expect($apiRequest->endpoint)->toBe($longEndpoint);
        });

        it('handles different http methods', function () {
            $methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'];

            foreach ($methods as $method) {
                $apiRequest = ApiRequest::factory()->create(['method' => $method]);
                expect($apiRequest->method)->toBe($method);
            }
        });

        it('handles various http status codes', function () {
            $statusCodes = [200, 201, 400, 401, 403, 404, 422, 500];

            foreach ($statusCodes as $status) {
                $apiRequest = ApiRequest::factory()->create(['status' => $status]);
                expect($apiRequest->status)->toBe($status);
            }
        });
    });
});