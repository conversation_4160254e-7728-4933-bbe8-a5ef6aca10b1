<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Throwable;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Symfony\Component\HttpKernel\Exception\HttpException;

class ApiExceptionFormatter
{
    public function handle(Request $request, Closure $next)
    {
        try {
            return $next($request);
        } catch (ThrottleRequestsException $e) {
            $retryAfter = $e->getHeaders()['Retry-After'] ?? 60;
            return response()->json([
                'status' => 429,
                'message' => 'Too Many Requests',
                'error' => 'Too Many Requests',
                'retry_after' => $retryAfter,
            ], 429);
        } catch (HttpException $e) {
            $message = $e->getMessage();
            
            if ($message === null || $message === '') {
                $message = 'HTTP error';
            }

            return response()->json([
                'status' => $e->getStatusCode(),
                'message' => $message,
                'error' => $message,
            ], $e->getStatusCode());
        } catch (Throwable $e) {
            \Illuminate\Support\Facades\Log::error('ApiExceptionFormatter caught: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
            return response()->json([
                'status' => 500,
                'message' => 'Internal server error',
                'error' => 'Internal server error',
            ], 500);
        }
    }
} 