import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
            ],
            refresh: true,
        }),
    ],
    build: {
        // Enable minification for production builds
        minify: 'terser',
        // Optimize chunk splitting for better caching
        rollupOptions: {
            output: {
                manualChunks: {
                    vendor: ['alpinejs'],
                    axios: ['axios'],
                },
                chunkFileNames: 'assets/js/[name]-[hash].js',
                entryFileNames: 'assets/js/[name]-[hash].js',
                assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
            },
            // Configure Terser options for better dead code elimination
            plugins: [
                {
                    name: 'terser',
                    renderChunk(code) {
                        return code;
                    }
                }
            ]
        },
        // Enhanced Terser options
        terserOptions: {
            compress: {
                pure_funcs: ['console.log', 'console.info', 'console.debug'],
                passes: 2,
                dead_code: true,
                drop_debugger: true,
                drop_console: true,
                pure_getters: true,
                unsafe: true,
                unsafe_arrows: true,
                unsafe_comps: true,
                unsafe_Function: true,
                unsafe_math: true,
                unsafe_methods: true,
                unsafe_proto: true,
                unsafe_regexp: true,
                unsafe_undefined: true,
            },
            mangle: {
                safari10: true,
                toplevel: true,
            },
            format: {
                comments: false,
                ascii_only: true,
            },
        },
        // Disable module preloading
        modulePreload: false,
        // Enable source map in development only
        sourcemap: process.env.NODE_ENV === 'development',
        // Enable CSS code splitting
        cssCodeSplit: true,
        // Enable dynamic imports
        dynamicImportVarsOptions: {
            warnOnError: false,
        },
        // Enable tree shaking
        treeshake: {
            moduleSideEffects: false,
            propertyReadSideEffects: false,
            tryCatchDeoptimization: false,
        },
    },
    optimizeDeps: {
        include: ['axios', 'alpinejs'],
        exclude: [],
    },
});