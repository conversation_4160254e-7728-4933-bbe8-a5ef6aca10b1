<?php if(isset($landingPage['pricing']) && $landingPage['pricing']['active']): ?>
<section class="py-20 bg-white dark:bg-bg-dark">
    <div class="max-w-6xl mx-auto px-4">
        <div class="text-center mb-16" data-aos="fade-up">
            <span
                class="inline-block px-4 py-1 rounded-full bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark text-xs font-semibold tracking-wider uppercase mb-3">
                Pricing
            </span>
            <h2 class="text-3xl md:text-4xl font-extrabold mb-4 text-primary-light dark:text-primary-dark">
                <?php echo e($landingPage['pricing']['content']['heading'] ?? 'Choose Your Plan'); ?>

            </h2>
            <p class="text-lg text-text-secondary-light dark:text-text-secondary-dark">
                <?php echo e($landingPage['pricing']['content']['subheading'] ?? 'Flexible plans for individuals, businesses, and enterprises'); ?>

            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
            <?php $__empty_1 = true; $__currentLoopData = $landingPage['pricing']['content']['plans'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="<?php echo e($plan['popular'] ? 'bg-white dark:bg-bg-dark rounded-xl shadow-2xl p-8 flex flex-col items-center border-4 border-primary-light dark:border-primary-dark relative' : 'bg-primary-50 dark:bg-bg-dark rounded-xl shadow-lg p-8 flex flex-col items-center border border-border-light dark:border-border-dark'); ?>"
                    data-aos="fade-up" data-aos-delay="<?php echo e($index * 100); ?>">
                    
                    <?php if($plan['popular']): ?>
                        <div class="absolute -top-5 left-1/2 -translate-x-1/2 bg-primary-light dark:bg-primary-dark text-white text-xs font-bold px-4 py-1 rounded-full shadow">
                            Most Popular
                        </div>
                    <?php endif; ?>
                    
                    <h3 class="font-bold text-xl mb-2 text-primary-light dark:text-primary-dark">
                        <?php echo e($plan['name']); ?>

                    </h3>
                    
                    <div class="text-4xl font-extrabold text-primary-light dark:text-primary-dark mb-4">
                        <?php if($plan['price'] === null): ?>
                            Custom
                        <?php elseif($plan['price'] == 0): ?>
                            <?php echo e($plan['currency']); ?>0
                        <?php else: ?>
                            <?php echo e($plan['currency']); ?><?php echo e(number_format($plan['price'], 0)); ?>

                            <span class="text-base font-normal text-text-secondary-light dark:text-text-secondary-dark">
                                /<?php echo e($plan['billing_period']); ?>

                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <p class="text-text-secondary-light dark:text-text-secondary-dark mb-6 text-center">
                        <?php echo e($plan['description']); ?>

                    </p>
                    
                    <ul class="mb-8 space-y-3 text-text-primary-light dark:text-text-primary-dark flex-grow">
                        <?php $__currentLoopData = $plan['features']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="flex items-start">
                                <i class="fa-solid fa-check text-green-500 mr-3 mt-1 flex-shrink-0"></i>
                                <span><?php echo e($feature); ?></span>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                    
                    <a href="<?php echo e($plan['cta_link']); ?>" 
                        class="w-full text-center px-6 py-3 <?php echo e($plan['popular'] ? 'bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light' : ($plan['color'] === 'accent' ? 'bg-accent-light dark:bg-accent-dark hover:bg-accent-dark dark:hover:bg-accent-light' : 'bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light')); ?> text-white rounded-lg font-semibold shadow transition-colors duration-300">
                        <?php echo e($plan['cta_text']); ?>

                    </a>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-3 text-center text-text-secondary-light dark:text-text-secondary-dark">
                    No plans available at the moment. Please check back later.
                </div>
            <?php endif; ?>
        </div>
        
        <?php if(isset($landingPage['pricing']['content']['free_trial_text'])): ?>
            <div class="text-center mt-12">
                <p class="text-text-secondary-light dark:text-text-secondary-dark mb-2">
                    <?php echo e($landingPage['pricing']['content']['free_trial_text']); ?>

                </p>
                <?php if(isset($landingPage['pricing']['content']['money_back_guarantee'])): ?>
                    <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">
                        <?php echo e($landingPage['pricing']['content']['money_back_guarantee']); ?>

                    </p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>
<?php endif; ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/home/<USER>/pricing-section.blade.php ENDPATH**/ ?>