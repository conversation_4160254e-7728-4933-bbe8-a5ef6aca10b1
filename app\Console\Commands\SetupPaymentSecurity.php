<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class SetupPaymentSecurity extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payment:setup-security 
                            {--force : Force setup even if directories exist}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up secure file storage and permissions for payment system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up payment security infrastructure...');

        try {
            // Create secure storage directories
            $this->createSecureDirectories();

            // Set up .htaccess protection
            $this->setupHtaccessProtection();

            // Create index.html files to prevent directory listing
            $this->createIndexFiles();

            // Set proper file permissions
            $this->setFilePermissions();

            // Validate configuration
            $this->validateConfiguration();

            $this->info('✅ Payment security setup completed successfully!');
            
            $this->displaySecuritySummary();

        } catch (\Exception $e) {
            $this->error('❌ Failed to set up payment security: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Create secure storage directories
     */
    private function createSecureDirectories(): void
    {
        $this->info('Creating secure storage directories...');

        $directories = [
            'payment_proofs',
            'payment_proofs/temp',
            'payment_proofs/archive',
            'logs/payment_security',
        ];

        foreach ($directories as $directory) {
            if (!Storage::disk('private')->exists($directory)) {
                Storage::disk('private')->makeDirectory($directory);
                $this->line("  ✓ Created directory: {$directory}");
            } else {
                $this->line("  - Directory already exists: {$directory}");
            }
        }
    }

    /**
     * Set up .htaccess protection for storage directories
     */
    private function setupHtaccessProtection(): void
    {
        $this->info('Setting up .htaccess protection...');

        $htaccessContent = <<<'HTACCESS'
# Deny all access to this directory
<Files "*">
    Order Deny,Allow
    Deny from all
</Files>

# Prevent directory browsing
Options -Indexes

# Disable script execution
<FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Additional security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
HTACCESS;

        $storagePath = storage_path('app/private');
        $htaccessPath = $storagePath . '/.htaccess';

        if (!file_exists($htaccessPath) || $this->option('force')) {
            File::put($htaccessPath, $htaccessContent);
            $this->line('  ✓ Created .htaccess protection');
        } else {
            $this->line('  - .htaccess already exists');
        }

        // Also create .htaccess in payment_proofs directory
        $paymentProofsPath = $storagePath . '/payment_proofs/.htaccess';
        if (!file_exists($paymentProofsPath) || $this->option('force')) {
            File::put($paymentProofsPath, $htaccessContent);
            $this->line('  ✓ Created .htaccess for payment_proofs');
        }
    }

    /**
     * Create index.html files to prevent directory listing
     */
    private function createIndexFiles(): void
    {
        $this->info('Creating index.html files...');

        $indexContent = <<<'HTML'
<!DOCTYPE html>
<html>
<head>
    <title>Access Denied</title>
</head>
<body>
    <h1>Access Denied</h1>
    <p>You don't have permission to access this directory.</p>
</body>
</html>
HTML;

        $directories = [
            'payment_proofs',
            'payment_proofs/temp',
            'payment_proofs/archive',
            'logs/payment_security',
        ];

        foreach ($directories as $directory) {
            $indexPath = $directory . '/index.html';
            if (!Storage::disk('private')->exists($indexPath) || $this->option('force')) {
                Storage::disk('private')->put($indexPath, $indexContent);
                $this->line("  ✓ Created index.html in {$directory}");
            }
        }
    }

    /**
     * Set proper file permissions
     */
    private function setFilePermissions(): void
    {
        $this->info('Setting file permissions...');

        $storagePath = storage_path('app/private');

        try {
            // Set directory permissions to 750 (owner: rwx, group: r-x, others: ---)
            if (is_dir($storagePath)) {
                chmod($storagePath, 0750);
                $this->line('  ✓ Set directory permissions for private storage');
            }

            // Set permissions for payment_proofs directory
            $paymentProofsPath = $storagePath . '/payment_proofs';
            if (is_dir($paymentProofsPath)) {
                chmod($paymentProofsPath, 0750);
                $this->line('  ✓ Set directory permissions for payment_proofs');
            }

            // Set .htaccess file permissions to 644
            $htaccessPath = $storagePath . '/.htaccess';
            if (file_exists($htaccessPath)) {
                chmod($htaccessPath, 0644);
                $this->line('  ✓ Set .htaccess file permissions');
            }

        } catch (\Exception $e) {
            $this->warn('  ⚠ Could not set file permissions (this may be normal on some systems): ' . $e->getMessage());
        }
    }

    /**
     * Validate configuration
     */
    private function validateConfiguration(): void
    {
        $this->info('Validating configuration...');

        // Check if private disk is configured
        $privateDisk = config('filesystems.disks.private');
        if (!$privateDisk) {
            throw new \Exception('Private disk not configured in filesystems.php');
        }

        $this->line('  ✓ Private disk configuration found');

        // Check if storage directory exists and is writable
        $storagePath = storage_path('app/private');
        if (!is_dir($storagePath)) {
            throw new \Exception('Private storage directory does not exist');
        }

        if (!is_writable($storagePath)) {
            throw new \Exception('Private storage directory is not writable');
        }

        $this->line('  ✓ Storage directory is accessible and writable');

        // Check payment configuration
        $paymentConfig = config('payment.security.file_upload');
        if (!$paymentConfig) {
            $this->warn('  ⚠ Payment security configuration not found - using defaults');
        } else {
            $this->line('  ✓ Payment security configuration loaded');
        }
    }

    /**
     * Display security summary
     */
    private function displaySecuritySummary(): void
    {
        $this->newLine();
        $this->info('🔒 Security Summary:');
        $this->line('  • Secure storage directories created outside web root');
        $this->line('  • .htaccess files configured to deny web access');
        $this->line('  • Directory listing disabled with index.html files');
        $this->line('  • File permissions set for secure access');
        $this->line('  • Payment file upload security configured');
        
        $this->newLine();
        $this->info('📁 Storage Location:');
        $this->line('  ' . storage_path('app/private/payment_proofs'));
        
        $this->newLine();
        $this->info('⚙️  Next Steps:');
        $this->line('  1. Configure your web server to deny access to storage/app/private');
        $this->line('  2. Set up proper backup for payment proof files');
        $this->line('  3. Configure malware scanning if available');
        $this->line('  4. Review and adjust rate limiting settings in config/payment.php');
        
        $this->newLine();
        $this->comment('For additional security, consider:');
        $this->comment('  • Setting up ClamAV for malware scanning');
        $this->comment('  • Configuring fail2ban for brute force protection');
        $this->comment('  • Using a separate server/service for file storage');
    }
}