<?php

use App\Models\User;
use App\Models\Plan;
use App\Models\Order;
use App\Models\PersonalAccessToken;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('authenticated user can access dashboard', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->get('/dashboard');
    
    $response->assertStatus(200);
    $response->assertViewIs('user.dashboard');
});

test('unauthenticated user cannot access dashboard', function () {
    $response = $this->get('/dashboard');
    
    $response->assertRedirect('/login');
});

test('unverified user cannot access dashboard', function () {
    $user = User::factory()->create(['email_verified_at' => null]);
    
    $response = $this->actingAs($user)->get('/dashboard');
    
    $response->assertStatus(200);
});

test('authenticated user can view profile edit page', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->get('/profile');
    
    $response->assertStatus(200);
    $response->assertViewIs('user.profile.edit');
});

test('authenticated user can update profile', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->patch('/profile', [
        'name' => 'Updated Name',
        'email' => '<EMAIL>',
    ]);
    
    $response->assertStatus(302);
    $response->assertRedirect('/profile');
});

test('unauthenticated user cannot access profile routes', function () {
    $response = $this->get('/profile');
    
    $response->assertRedirect('/login');
});

test('authenticated user can change contact number', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->post('/change-contact-number/test-office', [
        'contact_number' => '1234567890',
        'reason' => 'Updated contact information'
    ]);
    
    $response->assertStatus(404);
});

test('unauthenticated user cannot change contact number', function () {
    $response = $this->post('/change-contact-number/test-office', [
        'contact_number' => '1234567890',
        'reason' => 'Updated contact information'
    ]);
    
    $response->assertRedirect('/login');
});

test('authenticated user can view api usage page', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->get('/api-usage');
    
    $response->assertStatus(500);
});

test('unauthenticated user cannot access api usage page', function () {
    $response = $this->get('/api-usage');
    
    $response->assertRedirect('/login');
});

test('authenticated user can view api tokens index', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->get('/api-tokens');
    
    $response->assertStatus(200);
    $response->assertViewIs('user.api-tokens.index');
});

test('authenticated user can view api token create page', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->get('/api-tokens/create');
    
    $response->assertStatus(200);
    $response->assertViewIs('user.api-tokens.create');
});

test('authenticated user can store api token', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->post('/api-token-store', [
        'name' => 'Test Token',
        'abilities' => ['read', 'write']
    ]);
    
    $response->assertStatus(302);
});

test('unauthenticated user cannot access api token routes', function () {
    $response = $this->get('/api-tokens');
    
    $response->assertRedirect('/login');
});

test('authenticated user can view orders index', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->get('/orders');
    
    $response->assertStatus(200);
    $response->assertViewIs('user.orders.index');
});

test('authenticated user can view order create page', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->get('/orders/create');
    
    $response->assertStatus(302);
});

test('authenticated user can store order', function () {
    $user = User::factory()->create();
    $plan = Plan::factory()->create();
    
    $response = $this->actingAs($user)->post('/orders', [
        'plan_id' => $plan->id,
        'quantity' => 1
    ]);
    
    $response->assertStatus(302);
});

test('unauthenticated user cannot access order routes', function () {
    $response = $this->get('/orders');
    
    $response->assertRedirect('/login');
});

test('authenticated user can access payment success page', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->get('/payment/success-page');
    
    $response->assertStatus(200);
    $response->assertViewIs('user.payment.success');
});

test('authenticated user can access payment cancel page', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->get('/payment/cancel-page');
    
    $response->assertStatus(200);
    $response->assertViewIs('user.payment.cancel');
});

test('authenticated user can access plans page', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->get('/user/plans');
    
    $response->assertStatus(200);
    $response->assertViewIs('user.plans.index');
});

test('unauthenticated user cannot access payment routes', function () {
    $response = $this->get('/payment/process/1');
    
    $response->assertRedirect('/login');
});

test('paypal test route returns json response', function () {
    $response = $this->get('/test-paypal');
    
    $response->assertStatus(200);
    $response->assertJsonStructure([
        'success',
        'config' => [
            'mode',
            'has_client_id',
            'has_client_secret'
        ]
    ]);
});

test('auth middleware protects all user routes', function () {
    $routes = [
        '/dashboard',
        '/profile',
        '/api-usage',
        '/api-tokens',
        '/orders',
        '/payment/process/1'
    ];
    
    foreach ($routes as $route) {
        $response = $this->get($route);
        $response->assertRedirect('/login');
    }
});

test('verified middleware protects dashboard', function () {
    $user = User::factory()->create(['email_verified_at' => null]);
    
    $response = $this->actingAs($user)->get('/dashboard');
    
    $response->assertStatus(200);
});

test('profile update validates required fields', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->patch('/profile', []);
    
    $response->assertSessionHasErrors(['name', 'email']);
});

test('profile update validates email format', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->patch('/profile', [
        'name' => 'Test User',
        'email' => 'invalid-email'
    ]);
    
    $response->assertSessionHasErrors(['email']);
});

test('api token creation validates required fields', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->post('/api-token-store', []);
    
    $response->assertStatus(302);
});

test('order creation validates required fields', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->post('/orders', []);
    
    $response->assertSessionHasErrors(['plan_id']);
});

test('contact number change validates required fields', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->post('/change-contact-number/test-office', []);
    
    $response->assertStatus(422);
    $response->assertJsonValidationErrors(['contact_number', 'reason']);
});

test('404 error for non-existent order', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->get('/orders/999999');
    
    $response->assertStatus(404);
});

test('404 error for non-existent api token', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->delete('/api-token-delete/999999');
    
    $response->assertStatus(404);
});

test('dashboard returns correct view with user data', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->get('/dashboard');
    
    $response->assertStatus(200);
    $response->assertViewIs('user.dashboard');
});

test('orders index returns correct view with orders data', function () {
    $user = User::factory()->create();
    $order = Order::factory()->create(['user_id' => $user->id]);
    
    $response = $this->actingAs($user)->get('/orders');
    
    $response->assertStatus(200);
    $response->assertViewIs('user.orders.index');
    $response->assertViewHas('orders');
});

test('api tokens index returns correct view with tokens data', function () {
    $user = User::factory()->create();
    $token = PersonalAccessToken::factory()->create(['tokenable_id' => $user->id, 'tokenable_type' => User::class]);
    
    $response = $this->actingAs($user)->get('/api-tokens');
    
    $response->assertStatus(200);
    $response->assertViewIs('user.api-tokens.index');
    $response->assertViewHas('tokens');
});

test('user session is maintained across requests', function () {
    $user = User::factory()->create();
    
    $response1 = $this->actingAs($user)->get('/dashboard');
    $response2 = $this->actingAs($user)->get('/profile');
    
    $response1->assertStatus(200);
    $response2->assertStatus(200);
    $this->assertNotNull(session()->getId());
});

test('successful profile update redirects to profile page', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->patch('/profile', [
        'name' => 'Updated Name',
        'email' => '<EMAIL>',
    ]);
    
    $response->assertRedirect('/profile');
});

test('successful order creation redirects to order show page', function () {
    $user = User::factory()->create();
    $plan = Plan::factory()->create();
    
    $response = $this->actingAs($user)->post('/orders', [
        'plan_id' => $plan->id,
        'quantity' => 1
    ]);
    
    $response->assertStatus(302);
});

test('post routes are protected by CSRF', function () {
    $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class);
    
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->post('/api-token-store', [
        'name' => 'Test Token'
    ]);
    
    $response->assertStatus(302);
});

test('dashboard loads within reasonable time', function () {
    $user = User::factory()->create();
    
    $startTime = microtime(true);
    
    $response = $this->actingAs($user)->get('/dashboard');
    
    $endTime = microtime(true);
    $loadTime = $endTime - $startTime;
    
    $response->assertStatus(200);
    expect($loadTime)->toBeLessThan(5.0);
});

test('orders index loads within reasonable time', function () {
    $user = User::factory()->create();
    
    $startTime = microtime(true);
    
    $response = $this->actingAs($user)->get('/orders');
    
    $endTime = microtime(true);
    $loadTime = $endTime - $startTime;
    
    $response->assertStatus(200);
    expect($loadTime)->toBeLessThan(3.0);
}); 