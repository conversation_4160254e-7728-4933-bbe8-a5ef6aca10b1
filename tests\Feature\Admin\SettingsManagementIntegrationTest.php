<?php

use App\Models\User;
use App\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Mail;
use App\Mail\TestConfigMail;

beforeEach(function () {
    $this->admin = User::factory()->create([
        'role' => 'admin',
        'status' => 'active'
    ]);
    
    $this->actingAs($this->admin);
});

describe('Settings Management Integration', function () {
    
    describe('General Settings', function () {
        it('displays settings index page', function () {
            $response = $this->get(route('admin.settings.index'));

            $response->assertStatus(200);
            $response->assertViewIs('admin.settings.index');
        });

        it('updates general settings successfully', function () {
            $settingsData = [
                'site_name' => 'Updated Site Name',
                'site_description' => 'Updated site description',
                'contact_email' => '<EMAIL>',
                'items_per_page' => 20,
                'maintenance_mode' => false
            ];

            $response = $this->put(route('admin.settings.update'), $settingsData);

            $response->assertRedirect(route('admin.settings.index'));
            $response->assertSessionHas('success');
            
            foreach ($settingsData as $key => $value) {
                $this->assertDatabaseHas('settings', [
                    'key' => $key,
                    'value' => is_bool($value) ? ($value ? '1' : '0') : $value
                ]);
            }
        });

        it('validates settings data', function () {
            $response = $this->put(route('admin.settings.update'), [
                'site_name' => '',
                'contact_email' => 'invalid-email',
                'items_per_page' => 'not-a-number'
            ]);

            $response->assertSessionHasErrors([
                'site_name', 'contact_email', 'items_per_page'
            ]);
        });

        it('clears cache after settings update', function () {
            Cache::shouldReceive('forget')->with('settings')->once();

            $response = $this->put(route('admin.settings.update'), [
                'site_name' => 'New Site Name',
                'contact_email' => '<EMAIL>',
                'items_per_page' => 15
            ]);

            $response->assertRedirect(route('admin.settings.index'));
        });
    });

    describe('API Settings', function () {
        it('updates API settings successfully', function () {
            $apiSettings = [
                'api_enabled' => true,
                'rate_limit' => 100
            ];

            $response = $this->post(route('admin.settings.api.update'), $apiSettings);

            $response->assertRedirect(route('admin.settings.index'));
            $response->assertSessionHas('success', 'API settings updated successfully.');
        });

        it('validates API rate limit', function () {
            $response = $this->post(route('admin.settings.api.update'), [
                'api_enabled' => true,
                'rate_limit' => 1500 // Above max limit
            ]);

            $response->assertSessionHasErrors(['rate_limit']);
        });

        it('validates API rate limit minimum', function () {
            $response = $this->post(route('admin.settings.api.update'), [
                'api_enabled' => true,
                'rate_limit' => 0 // Below minimum
            ]);

            $response->assertSessionHasErrors(['rate_limit']);
        });
    });

    describe('Mail Configuration', function () {
        it('displays mail configuration page', function () {
            $response = $this->get(route('admin.mail-config.index'));

            $response->assertStatus(200);
            $response->assertViewIs('admin.mail-config.index');
        });

        it('updates mail configuration successfully', function () {
            $mailConfig = [
                'mail_driver' => 'smtp',
                'mail_host' => 'smtp.gmail.com',
                'mail_port' => 587,
                'mail_username' => '<EMAIL>',
                'mail_password' => 'password123',
                'mail_encryption' => 'tls',
                'mail_from_address' => '<EMAIL>',
                'mail_from_name' => 'Test Site'
            ];

            $response = $this->post(route('admin.mail-config.update'), $mailConfig);

            $response->assertRedirect(route('admin.mail-config.index'));
            $response->assertSessionHas('success');
        });

        it('validates mail configuration fields', function () {
            $response = $this->post(route('admin.mail-config.update'), [
                'mail_driver' => '',
                'mail_host' => '',
                'mail_port' => 'invalid',
                'mail_from_address' => 'invalid-email'
            ]);

            $response->assertSessionHasErrors([
                'mail_driver', 'mail_host', 'mail_port', 'mail_from_address'
            ]);
        });

        it('sends test email successfully', function () {
            Mail::fake();

            $response = $this->post(route('admin.mail-config.test'), [
                'test_email' => '<EMAIL>'
            ]);

            $response->assertRedirect(route('admin.mail-config.index'));
            $response->assertSessionHas('success');
            
            Mail::assertSent(TestConfigMail::class);
        });

        it('validates test email address', function () {
            $response = $this->post(route('admin.mail-config.test'), [
                'test_email' => 'invalid-email'
            ]);

            $response->assertSessionHasErrors(['test_email']);
        });
    });

    describe('Maintenance Mode', function () {
        it('enables maintenance mode', function () {
            $response = $this->post(route('admin.settings.maintenance'));

            $response->assertRedirect();
            $response->assertSessionHas('success');
        });

        it('disables maintenance mode', function () {
            // Simulate maintenance mode being on
            Artisan::call('down');

            $response = $this->post(route('admin.settings.maintenance'));

            $response->assertRedirect();
            $response->assertSessionHas('success');
        });

        it('toggles maintenance mode via admin controller', function () {
            $response = $this->post(route('admin.maintenance.toggle'));

            $response->assertRedirect();
            $response->assertSessionHas('success');
        });
    });

    describe('Login Settings', function () {
        it('toggles user login functionality', function () {
            $response = $this->post(route('admin.settings.login'));

            $response->assertRedirect(route('admin.settings.index'));
            $response->assertSessionHas('success');
        });
    });

    describe('API Key Generation', function () {
        it('generates new API key', function () {
            $response = $this->post(route('admin.generate.apikey'));

            $response->assertRedirect(route('admin.settings.index'));
            $response->assertSessionHas('success');
        });

        it('stores generated API key in settings', function () {
            $response = $this->post(route('admin.generate.apikey'));

            $this->assertDatabaseHas('settings', [
                'key' => 'api_key'
            ]);
        });
    });

    describe('Cache Management', function () {
        it('clears application cache', function () {
            Cache::put('test_cache_key', 'test_value');

            $response = $this->get(route('admin.clear-cache'));

            $response->assertRedirect();
            $response->assertSessionHas('success', 'Cache cleared successfully');
            
            expect(Cache::get('test_cache_key'))->toBeNull();
        });

        it('clears multiple cache types', function () {
            $response = $this->get(route('admin.clear-cache'));

            $response->assertRedirect();
            $response->assertSessionHas('success');
        });
    });

    describe('SEO Settings', function () {
        it('updates SEO meta settings', function () {
            $seoSettings = [
                'meta_title' => 'Site Meta Title',
                'meta_description' => 'Site meta description for SEO',
                'meta_keywords' => 'keyword1, keyword2, keyword3',
                'google_analytics_id' => 'GA-123456789',
                'google_search_console' => 'google-site-verification-code'
            ];

            $response = $this->put(route('admin.settings.update'), $seoSettings);

            $response->assertRedirect(route('admin.settings.index'));
            $response->assertSessionHas('success');
            
            foreach ($seoSettings as $key => $value) {
                $this->assertDatabaseHas('settings', [
                    'key' => $key,
                    'value' => $value
                ]);
            }
        });

        it('validates SEO settings', function () {
            $response = $this->put(route('admin.settings.update'), [
                'meta_title' => str_repeat('a', 256), // Too long
                'google_analytics_id' => 'invalid-format'
            ]);

            $response->assertSessionHasErrors([
                'meta_title', 'google_analytics_id'
            ]);
        });
    });

    describe('Social Media Settings', function () {
        it('updates social media links', function () {
            $socialSettings = [
                'facebook_url' => 'https://facebook.com/example',
                'twitter_url' => 'https://twitter.com/example',
                'linkedin_url' => 'https://linkedin.com/company/example',
                'instagram_url' => 'https://instagram.com/example'
            ];

            $response = $this->put(route('admin.settings.update'), $socialSettings);

            $response->assertRedirect(route('admin.settings.index'));
            $response->assertSessionHas('success');
            
            foreach ($socialSettings as $key => $value) {
                $this->assertDatabaseHas('settings', [
                    'key' => $key,
                    'value' => $value
                ]);
            }
        });

        it('validates social media URLs', function () {
            $response = $this->put(route('admin.settings.update'), [
                'facebook_url' => 'invalid-url',
                'twitter_url' => 'not-a-url'
            ]);

            $response->assertSessionHasErrors([
                'facebook_url', 'twitter_url'
            ]);
        });
    });

    describe('Backup Settings', function () {
        it('triggers database backup', function () {
            $response = $this->post(route('admin.backup.database'));

            $response->assertRedirect();
            $response->assertSessionHas('success');
        });
    });

    describe('Authorization', function () {
        it('prevents non-admin from accessing settings', function () {
            $regularUser = User::factory()->create(['role' => 'user']);
            $this->actingAs($regularUser);

            $response = $this->get(route('admin.settings.index'));

            $response->assertStatus(403);
        });

        it('prevents guest from accessing settings', function () {
            auth()->logout();

            $response = $this->get(route('admin.settings.index'));

            $response->assertRedirect(route('admin.login'));
        });
    });
});