<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Payment Security Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains security settings for payment operations including
    | file upload security, rate limiting, and CSRF protection.
    |
    */

    'security' => [
        
        /*
        |--------------------------------------------------------------------------
        | File Upload Security
        |--------------------------------------------------------------------------
        |
        | Configuration for secure file uploads for payment proofs
        |
        */
        'file_upload' => [
            'max_file_size' => env('PAYMENT_MAX_FILE_SIZE', 5242880), // 5MB in bytes
            'allowed_mime_types' => [
                'image/jpeg',
                'image/jpg', 
                'image/png',
                'image/gif',
                'image/webp',
                'application/pdf'
            ],
            'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf'],
            'max_uploads_per_hour' => env('PAYMENT_MAX_UPLOADS_PER_HOUR', 10),
            'storage_disk' => 'private',
            'storage_path' => 'payment_proofs',
            'enable_malware_scanning' => env('PAYMENT_ENABLE_MALWARE_SCAN', true),
            'enable_content_validation' => env('PAYMENT_ENABLE_CONTENT_VALIDATION', true),
        ],

        /*
        |--------------------------------------------------------------------------
        | Rate Limiting
        |--------------------------------------------------------------------------
        |
        | Rate limiting configuration for different payment operations
        |
        */
        'rate_limiting' => [
            'payment_creation' => [
                'max_attempts' => env('PAYMENT_CREATE_MAX_ATTEMPTS', 5),
                'decay_minutes' => env('PAYMENT_CREATE_DECAY_MINUTES', 60),
            ],
            'file_upload' => [
                'max_attempts' => env('PAYMENT_UPLOAD_MAX_ATTEMPTS', 10),
                'decay_minutes' => env('PAYMENT_UPLOAD_DECAY_MINUTES', 60),
            ],
            'payment_verification' => [
                'max_attempts' => env('PAYMENT_VERIFY_MAX_ATTEMPTS', 20),
                'decay_minutes' => env('PAYMENT_VERIFY_DECAY_MINUTES', 60),
            ],
            'status_check' => [
                'max_attempts' => env('PAYMENT_STATUS_MAX_ATTEMPTS', 30),
                'decay_minutes' => env('PAYMENT_STATUS_DECAY_MINUTES', 60),
            ],
        ],

        /*
        |--------------------------------------------------------------------------
        | CSRF Protection
        |--------------------------------------------------------------------------
        |
        | CSRF protection settings for payment operations
        |
        */
        'csrf' => [
            'token_expiry_minutes' => env('PAYMENT_CSRF_EXPIRY', 30),
            'max_tokens_per_user' => env('PAYMENT_CSRF_MAX_TOKENS', 5),
            'validate_ip' => env('PAYMENT_CSRF_VALIDATE_IP', false),
            'validate_user_agent' => env('PAYMENT_CSRF_VALIDATE_USER_AGENT', false),
        ],

        /*
        |--------------------------------------------------------------------------
        | Payment Validation
        |--------------------------------------------------------------------------
        |
        | Settings for payment amount and currency validation
        |
        */
        'validation' => [
            'amount_tolerance' => env('PAYMENT_AMOUNT_TOLERANCE', 0.01),
            'enable_currency_conversion' => env('PAYMENT_ENABLE_CURRENCY_CONVERSION', true),
            'default_currency' => env('PAYMENT_DEFAULT_CURRENCY', 'USD'),
            'supported_currencies' => [
                'USD' => [
                    'symbol' => '$',
                    'decimal_places' => 2,
                    'min_amount' => 0.50,
                    'max_amount' => 10000.00
                ],
                'EUR' => [
                    'symbol' => '€',
                    'decimal_places' => 2,
                    'min_amount' => 0.50,
                    'max_amount' => 10000.00
                ],
                'INR' => [
                    'symbol' => '₹',
                    'decimal_places' => 2,
                    'min_amount' => 1.00,
                    'max_amount' => 500000.00
                ],
                'GBP' => [
                    'symbol' => '£',
                    'decimal_places' => 2,
                    'min_amount' => 0.30,
                    'max_amount' => 8000.00
                ]
            ],
        ],

        /*
        |--------------------------------------------------------------------------
        | Logging and Monitoring
        |--------------------------------------------------------------------------
        |
        | Security logging and monitoring settings
        |
        */
        'logging' => [
            'log_all_attempts' => env('PAYMENT_LOG_ALL_ATTEMPTS', true),
            'log_failed_attempts' => env('PAYMENT_LOG_FAILED_ATTEMPTS', true),
            'log_suspicious_activity' => env('PAYMENT_LOG_SUSPICIOUS_ACTIVITY', true),
            'alert_on_multiple_failures' => env('PAYMENT_ALERT_ON_FAILURES', true),
            'failure_threshold' => env('PAYMENT_FAILURE_THRESHOLD', 5),
        ],

        /*
        |--------------------------------------------------------------------------
        | IP Whitelisting
        |--------------------------------------------------------------------------
        |
        | IP addresses that are allowed to bypass certain security checks
        |
        */
        'ip_whitelist' => [
            'admin_ips' => env('PAYMENT_ADMIN_IPS', ''),
            'webhook_ips' => [
                // Razorpay webhook IPs
                '***********',
                '***********',
                '*************',
                '************',
                // PayPal webhook IPs (add as needed)
            ],
        ],

        /*
        |--------------------------------------------------------------------------
        | Encryption
        |--------------------------------------------------------------------------
        |
        | Settings for encrypting sensitive payment data
        |
        */
        'encryption' => [
            'algorithm' => 'AES-256-CBC',
            'key_rotation_days' => env('PAYMENT_KEY_ROTATION_DAYS', 90),
            'encrypt_gateway_credentials' => env('PAYMENT_ENCRYPT_CREDENTIALS', true),
            'encrypt_webhook_secrets' => env('PAYMENT_ENCRYPT_WEBHOOK_SECRETS', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Gateway Configuration
    |--------------------------------------------------------------------------
    |
    | Default configuration for payment gateways
    |
    */
    'gateways' => [
        'razorpay' => [
            'name' => 'Razorpay',
            'display_name' => 'Razorpay',
            'description' => 'Pay with UPI, Cards, Net Banking, and Wallets',
            'logo_url' => '/images/gateways/razorpay.png',
            'supported_currencies' => ['INR'],
            'webhook_events' => [
                'payment.authorized',
                'payment.captured',
                'payment.failed',
                'order.paid'
            ],
        ],
        'paypal' => [
            'name' => 'PayPal',
            'display_name' => 'PayPal',
            'description' => 'Pay securely with PayPal',
            'logo_url' => '/images/gateways/paypal.png',
            'supported_currencies' => ['USD', 'EUR', 'GBP'],
            'webhook_events' => [
                'PAYMENT.CAPTURE.COMPLETED',
                'PAYMENT.CAPTURE.DENIED',
                'CHECKOUT.ORDER.APPROVED'
            ],
        ],
        'qr_bank_transfer' => [
            'name' => 'QR Bank Transfer',
            'display_name' => 'Bank Transfer (QR)',
            'description' => 'Pay via bank transfer using QR code',
            'logo_url' => '/images/gateways/bank-transfer.png',
            'supported_currencies' => ['INR', 'USD'],
            'requires_manual_verification' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    |
    | Settings for payment notifications and alerts
    |
    */
    'notifications' => [
        'admin_email' => env('PAYMENT_ADMIN_EMAIL', '<EMAIL>'),
        'send_payment_alerts' => env('PAYMENT_SEND_ALERTS', true),
        'send_security_alerts' => env('PAYMENT_SEND_SECURITY_ALERTS', true),
        'alert_channels' => ['mail', 'log'], // mail, slack, log
    ],

    /*
    |--------------------------------------------------------------------------
    | Testing Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for testing payment functionality
    |
    */
    'testing' => [
        'enable_test_mode' => env('PAYMENT_TEST_MODE', false),
        'test_gateway_responses' => env('PAYMENT_TEST_RESPONSES', false),
        'mock_webhook_verification' => env('PAYMENT_MOCK_WEBHOOKS', false),
        'bypass_rate_limiting' => env('PAYMENT_BYPASS_RATE_LIMITING', false),
    ],

];