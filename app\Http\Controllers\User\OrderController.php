<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OrderController extends Controller
{
    /**
     * Display a listing of the orders.
     */
    public function index()
    {
        $orders = Auth::user()->orders()->with('plan')->latest()->paginate(10);
        
        return view('user.orders.index', compact('orders'));
    }

    /**
     * Show the form for creating a new order.
     */
    public function create()
    {
        // Redirect to plans page instead of showing create form
        return redirect()->route('user.plans.index');
    }

    /**
     * Store a newly created order in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'plan_id' => 'required|exists:plans,id',
            'amount' => 'required|numeric|min:0',
            'request_limit' => 'required|integer|min:1',
            'notes' => 'nullable|string',
        ]);

        // Get user's current plan
        $currentPlanId = Auth::user()->getCurrentPlan();
        
        // Prevent creating order for the same plan
        if ($currentPlanId === $validated['plan_id']) {
            return redirect()->back()
                ->with('error', 'You are already subscribed to this plan.');
        }
        
        $order = Order::create([
            'user_id' => Auth::id(),
            'plan_id' => $validated['plan_id'],
            'order_number' => 'ORD-' . uniqid(),
            'amount' => $validated['amount'],
            'request_limit' => $validated['request_limit'],
            'status' => Order::STATUS_PENDING,
            'notes' => $validated['notes'] ?? null,
        ]);
        
        // Redirect to payment page
        return redirect()->route('payment.process', $order)
            ->with('status', 'order-created');
    }

    /**
     * Display the specified order.
     */
    public function show(Order $order)
    {
        // Ensure user can only view their own orders
        if ($order->user_id !== Auth::id()) {
            abort(403);
        }
        
        return view('user.orders.show', compact('order'));
    }

    /**
     * Cancel the specified order.
     */
    public function cancel(Order $order)
    {
        // Ensure user can only cancel their own pending orders
        if ($order->user_id !== Auth::id() || !$order->isPending()) {
            abort(403);
        }
        
        $order->update(['status' => Order::STATUS_CANCELLED]);
        
        return redirect()->route('orders.index')
            ->with('status', 'order-cancelled');
    }

    /**
     * Remove the specified order from storage.
     */
    public function destroy(Order $order)
    {
        // Only allow users to delete their own orders
        if ($order->user_id !== Auth::id()) {
            abort(403);
        }
        
        $order->delete();
        
        return redirect()->route('orders.index')
            ->with('status', 'order-deleted');
    }
}
