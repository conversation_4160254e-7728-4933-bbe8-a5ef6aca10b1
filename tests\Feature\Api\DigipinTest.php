<?php

namespace Tests\Feature\Api;

use App\Services\DigipinService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DigipinTest extends TestCase
{
    protected DigipinService $digipinService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->digipinService = new DigipinService();
    }

    /**
     * Test DIGIPIN generation from valid coordinates
     */
    public function test_generate_digipin_from_valid_coordinates()
    {
        $response = $this->postJson('/api/digipin/generate', [
            'latitude' => 28.6139,
            'longitude' => 77.2090
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'digipin',
                        'latitude',
                        'longitude'
                    ]
                ]);

        $this->assertTrue($response->json('success'));
        $this->assertEquals(28.6139, $response->json('data.latitude'));
        $this->assertEquals(77.2090, $response->json('data.longitude'));
        
        // Verify DIGIPIN format (10 characters with 2 hyphens)
        $digipin = $response->json('data.digipin');
        $this->assertIsString($digipin);
        $this->assertEquals(12, strlen($digipin)); // 10 chars + 2 hyphens
        $this->assertStringContainsString('-', $digipin);
    }

    /**
     * Test DIGIPIN generation with boundary coordinates
     */
    public function test_generate_digipin_with_boundary_coordinates()
    {
        // Test minimum bounds
        $response = $this->postJson('/api/digipin/generate', [
            'latitude' => 2.5,
            'longitude' => 63.5
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'digipin',
                        'latitude',
                        'longitude'
                    ]
                ]);

        // Test maximum bounds
        $response = $this->postJson('/api/digipin/generate', [
            'latitude' => 38.5,
            'longitude' => 99.5
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'digipin',
                        'latitude',
                        'longitude'
                    ]
                ]);
    }

    /**
     * Test DIGIPIN decoding with valid DIGIPIN
     */
    public function test_decode_valid_digipin()
    {
        // First generate a DIGIPIN
        $generateResponse = $this->postJson('/api/digipin/generate', [
            'latitude' => 28.6139,
            'longitude' => 77.2090
        ]);

        $digipin = $generateResponse->json('data.digipin');

        // Then decode it
        $response = $this->postJson('/api/digipin/decode', [
            'digipin' => $digipin
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'digipin',
                        'latitude',
                        'longitude'
                    ]
                ]);

        $this->assertTrue($response->json('success'));
        $this->assertEquals($digipin, $response->json('data.digipin'));
        
        // Verify coordinates are numeric
        $this->assertIsNumeric($response->json('data.latitude'));
        $this->assertIsNumeric($response->json('data.longitude'));
    }

    /**
     * Test DIGIPIN info endpoint
     */
    public function test_get_digipin_info()
    {
        $response = $this->getJson('/api/digipin/info');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'description',
                        'bounds',
                        'grid',
                        'validCharacters',
                        'endpoints'
                    ]
                ]);

        $this->assertTrue($response->json('success'));
        
        // Verify specific data structure
        $data = $response->json('data');
        $this->assertIsString($data['description']);
        $this->assertIsArray($data['bounds']);
        $this->assertIsArray($data['grid']);
        $this->assertIsArray($data['validCharacters']);
        $this->assertIsArray($data['endpoints']);
        
        // Verify bounds structure
        $this->assertArrayHasKey('minLat', $data['bounds']);
        $this->assertArrayHasKey('maxLat', $data['bounds']);
        $this->assertArrayHasKey('minLon', $data['bounds']);
        $this->assertArrayHasKey('maxLon', $data['bounds']);
        
        // Verify endpoints structure
        $this->assertArrayHasKey('generate', $data['endpoints']);
        $this->assertArrayHasKey('decode', $data['endpoints']);
        $this->assertArrayHasKey('info', $data['endpoints']);
    }

    /**
     * Test validation for missing latitude
     */
    public function test_validation_missing_latitude()
    {
        $response = $this->postJson('/api/digipin/generate', [
            'longitude' => 77.2090
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['latitude']);
    }

    /**
     * Test validation for missing longitude
     */
    public function test_validation_missing_longitude()
    {
        $response = $this->postJson('/api/digipin/generate', [
            'latitude' => 28.6139
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['longitude']);
    }

    /**
     * Test validation for non-numeric latitude
     */
    public function test_validation_non_numeric_latitude()
    {
        $response = $this->postJson('/api/digipin/generate', [
            'latitude' => 'invalid',
            'longitude' => 77.2090
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['latitude']);
    }

    /**
     * Test validation for non-numeric longitude
     */
    public function test_validation_non_numeric_longitude()
    {
        $response = $this->postJson('/api/digipin/generate', [
            'latitude' => 28.6139,
            'longitude' => 'invalid'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['longitude']);
    }

    /**
     * Test validation for latitude below minimum bound
     */
    public function test_validation_latitude_below_minimum()
    {
        $response = $this->postJson('/api/digipin/generate', [
            'latitude' => 2.0,
            'longitude' => 77.2090
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['latitude']);
    }

    /**
     * Test validation for latitude above maximum bound
     */
    public function test_validation_latitude_above_maximum()
    {
        $response = $this->postJson('/api/digipin/generate', [
            'latitude' => 40.0,
            'longitude' => 77.2090
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['latitude']);
    }

    /**
     * Test validation for longitude below minimum bound
     */
    public function test_validation_longitude_below_minimum()
    {
        $response = $this->postJson('/api/digipin/generate', [
            'latitude' => 28.6139,
            'longitude' => 60.0
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['longitude']);
    }

    /**
     * Test validation for longitude above maximum bound
     */
    public function test_validation_longitude_above_maximum()
    {
        $response = $this->postJson('/api/digipin/generate', [
            'latitude' => 28.6139,
            'longitude' => 100.0
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['longitude']);
    }

    /**
     * Test validation for missing DIGIPIN
     */
    public function test_validation_missing_digipin()
    {
        $response = $this->postJson('/api/digipin/decode', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['digipin']);
    }

    /**
     * Test validation for non-string DIGIPIN
     */
    public function test_validation_non_string_digipin()
    {
        $response = $this->postJson('/api/digipin/decode', [
            'digipin' => 12345
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['digipin']);
    }

    /**
     * Test validation for DIGIPIN exceeding maximum length
     */
    public function test_validation_digipin_exceeds_max_length()
    {
        $response = $this->postJson('/api/digipin/decode', [
            'digipin' => 'FC9-J38-TJC7-EXTRA'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['digipin']);
    }

    /**
     * Test validation for invalid DIGIPIN format (wrong length)
     */
    public function test_validation_invalid_digipin_length()
    {
        $response = $this->postJson('/api/digipin/decode', [
            'digipin' => 'FC9-J38'
        ]);

        $response->assertStatus(422);
    }

    /**
     * Test validation for invalid DIGIPIN format (invalid characters)
     */
    public function test_validation_invalid_digipin_characters()
    {
        $response = $this->postJson('/api/digipin/decode', [
            'digipin' => 'ABC-DEF-GHIJ'
        ]);

        $response->assertStatus(422);
    }

    /**
     * Test round-trip accuracy (generate -> decode -> compare)
     */
    public function test_round_trip_accuracy()
    {
        $originalLat = 28.6139;
        $originalLon = 77.2090;

        // Generate DIGIPIN
        $generateResponse = $this->postJson('/api/digipin/generate', [
            'latitude' => $originalLat,
            'longitude' => $originalLon
        ]);

        $digipin = $generateResponse->json('data.digipin');

        // Decode DIGIPIN
        $decodeResponse = $this->postJson('/api/digipin/decode', [
            'digipin' => $digipin
        ]);

        $decodedLat = (float) $decodeResponse->json('data.latitude');
        $decodedLon = (float) $decodeResponse->json('data.longitude');

        // Verify accuracy within reasonable tolerance (DIGIPIN has limited precision)
        $this->assertEqualsWithDelta($originalLat, $decodedLat, 0.1);
        $this->assertEqualsWithDelta($originalLon, $decodedLon, 0.1);
    }

    /**
     * Test multiple coordinate pairs for consistency
     */
    public function test_multiple_coordinate_pairs()
    {
        $testCoordinates = [
            ['lat' => 19.0760, 'lon' => 72.8777], // Mumbai
            ['lat' => 12.9716, 'lon' => 77.5946], // Bangalore
            ['lat' => 13.0827, 'lon' => 80.2707], // Chennai
            ['lat' => 22.5726, 'lon' => 88.3639], // Kolkata
        ];

        foreach ($testCoordinates as $coords) {
            $response = $this->postJson('/api/digipin/generate', [
                'latitude' => $coords['lat'],
                'longitude' => $coords['lon']
            ]);

            $response->assertStatus(200);
            $this->assertTrue($response->json('success'));
            
            $digipin = $response->json('data.digipin');
            $this->assertIsString($digipin);
            $this->assertEquals(12, strlen($digipin));
        }
    }

    /**
     * Test service methods directly for edge cases
     */
    public function test_service_edge_cases()
    {
        // Test with exact boundary values
        $digipin = $this->digipinService->encode(2.5, 63.5);
        $this->assertIsString($digipin);
        $this->assertEquals(12, strlen($digipin));

        $coordinates = $this->digipinService->decode($digipin);
        $this->assertIsArray($coordinates);
        $this->assertArrayHasKey('latitude', $coordinates);
        $this->assertArrayHasKey('longitude', $coordinates);
    }

    /**
     * Test service validation with various formats
     */
    public function test_service_validation_formats()
    {
        // Valid formats
        $this->assertTrue($this->digipinService->isValidFormat('FC9-J38-TJC7'));
        $this->assertTrue($this->digipinService->isValidFormat('LMP-T56-K432'));
        
        // Invalid formats
        $this->assertFalse($this->digipinService->isValidFormat('INVALID123'));
        $this->assertFalse($this->digipinService->isValidFormat('FC9'));
        $this->assertFalse($this->digipinService->isValidFormat('FC9-J38-TJC7-EXTRA'));
        $this->assertFalse($this->digipinService->isValidFormat('ABC-DEF-GHIJ'));
    }

    /**
     * Test error handling for invalid DIGIPIN characters
     */
    public function test_error_handling_invalid_characters()
    {
        $this->expectException(\Exception::class);
        $this->digipinService->decode('ABC-DEF-GHIJ');
    }

    /**
     * Test error handling for out of bounds coordinates
     */
    public function test_error_handling_out_of_bounds_coordinates()
    {
        $this->expectException(\Exception::class);
        $this->digipinService->encode(50.0, 77.2090); // Latitude out of bounds
    }

    /**
     * Test error handling for invalid DIGIPIN length
     */
    public function test_error_handling_invalid_digipin_length()
    {
        $this->expectException(\Exception::class);
        $this->digipinService->decode('FC9-J38'); // Too short
    }
} 