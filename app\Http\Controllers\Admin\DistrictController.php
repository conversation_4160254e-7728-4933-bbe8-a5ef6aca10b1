<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\District;
use App\Models\State;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class DistrictController extends Controller
{
    public function index()
    {
        $districts = District::with('state')->orderBy('name')->paginate(10);
        return view('admin.districts.index', compact('districts'));
    }

    public function create()
    {
        $states = State::orderBy('name')->get();
        return view('admin.districts.create', compact('states'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'state_id' => 'required|exists:pin_states,id',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $data = $request->only(['name', 'state_id']);
        
        if ($request->hasFile('featured_image')) {
            $path = $request->file('featured_image')->store('public/districts');
            $data['featured_image'] = $path;
        }

        District::create($data);

        return redirect()->route('admin.districts.index')
            ->with('success', 'District created successfully.');
    }

    public function edit(District $district)
    {
        $states = State::orderBy('name')->get();
        return view('admin.districts.edit', compact('district', 'states'));
    }

    public function update(Request $request, District $district)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'state_id' => 'required|exists:pin_states,id',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $data = $request->only(['name', 'state_id']);
        
        if ($request->hasFile('featured_image')) {
            // Delete old image if exists
            if ($district->featured_image) {
                Storage::delete($district->featured_image);
            }
            $path = $request->file('featured_image')->store('public/districts');
            $data['featured_image'] = $path;
        }

        $district->update($data);

        return redirect()->route('admin.districts.index')
            ->with('success', 'District updated successfully.');
    }

    public function destroy(District $district)
    {
        if ($district->featured_image) {
            Storage::delete($district->featured_image);
        }
        $district->delete();

        return redirect()->route('admin.districts.index')
            ->with('success', 'District deleted successfully.');
    }
} 