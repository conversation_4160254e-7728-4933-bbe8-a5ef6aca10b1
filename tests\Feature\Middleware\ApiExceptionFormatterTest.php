<?php

use App\Http\Middleware\ApiExceptionFormatter;
use Illuminate\Http\Request;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

uses()->group('middleware');

beforeEach(function () {
    $this->middleware = new ApiExceptionFormatter();
    $this->next = function ($request) {
        return response('Next middleware called');
    };
    $this->request = Request::create('/api/test');
});

it('passes through requests without exceptions', function () {
    $response = $this->middleware->handle($this->request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});

it('handles throttle requests exception', function () {
    $this->next = function ($request) {
        throw new ThrottleRequestsException('Too Many Requests');
    };

    $response = $this->middleware->handle($this->request, $this->next);

    expect($response->getStatusCode())->toBe(429)
        ->and($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['status'])->toBe(429)
        ->and($content['message'])->toBe('Too Many Requests')
        ->and($content['error'])->toBe('Too Many Requests')
        ->and($content['retry_after'])->toBe(60);
});

it('handles throttle requests exception with custom retry after', function () {
    $exception = new ThrottleRequestsException('Too Many Requests');
    $exception->setHeaders(['Retry-After' => 45]);

    $this->next = function ($request) use ($exception) {
        throw $exception;
    };

    $response = $this->middleware->handle($this->request, $this->next);

    expect($response->getStatusCode())->toBe(429)
        ->and($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['status'])->toBe(429)
        ->and($content['message'])->toBe('Too Many Requests')
        ->and($content['error'])->toBe('Too Many Requests')
        ->and($content['retry_after'])->toBe(45);
});

it('handles http exception with message', function () {
    $this->next = function ($request) {
        throw new HttpException(404, 'Resource not found');
    };

    $response = $this->middleware->handle($this->request, $this->next);

    expect($response->getStatusCode())->toBe(404)
        ->and($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['status'])->toBe(404)
        ->and($content['message'])->toBe('Resource not found')
        ->and($content['error'])->toBe('Resource not found');
});

it('handles http exception without message', function () {
    $this->next = function ($request) {
        throw new HttpException(500);
    };

    $response = $this->middleware->handle($this->request, $this->next);

    expect($response->getStatusCode())->toBe(500)
        ->and($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['status'])->toBe(500)
        ->and($content['message'])->toBe('HTTP error')
        ->and($content['error'])->toBe('HTTP error');
});

it('handles general exception', function () {
    $this->next = function ($request) {
        throw new Exception('Something went wrong');
    };

    $response = $this->middleware->handle($this->request, $this->next);

    expect($response->getStatusCode())->toBe(500)
        ->and($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['status'])->toBe(500)
        ->and($content['message'])->toBe('Internal server error')
        ->and($content['error'])->toBe('Internal server error');
});

it('handles throwable exception', function () {
    $this->next = function ($request) {
        throw new Error('Fatal error occurred');
    };

    $response = $this->middleware->handle($this->request, $this->next);

    expect($response->getStatusCode())->toBe(500)
        ->and($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['status'])->toBe(500)
        ->and($content['message'])->toBe('Internal server error')
        ->and($content['error'])->toBe('Internal server error');
});

it('handles http exception with various status codes', function () {
    $statusCodes = [400, 401, 403, 404, 422, 500, 502, 503];

    foreach ($statusCodes as $statusCode) {
        $this->next = function ($request) use ($statusCode) {
            throw new HttpException($statusCode, "Error {$statusCode}");
        };

        $response = $this->middleware->handle($this->request, $this->next);

        expect($response->getStatusCode())->toBe($statusCode)
            ->and($response->getContent())->toBeJson();
        
        $content = json_decode($response->getContent(), true);
        expect($content['status'])->toBe($statusCode)
            ->and($content['message'])->toBe("Error {$statusCode}")
            ->and($content['error'])->toBe("Error {$statusCode}");
    }
});

it('handles throttle exception with missing retry after header', function () {
    $exception = new ThrottleRequestsException('Too Many Requests');
    $exception->setHeaders([]);

    $this->next = function ($request) use ($exception) {
        throw $exception;
    };

    $response = $this->middleware->handle($this->request, $this->next);

    expect($response->getStatusCode())->toBe(429)
        ->and($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['status'])->toBe(429)
        ->and($content['message'])->toBe('Too Many Requests')
        ->and($content['error'])->toBe('Too Many Requests')
        ->and($content['retry_after'])->toBe(60);
});

it('handles http exception with empty message', function () {
    $this->next = function ($request) {
        throw new HttpException(404, '');
    };

    $response = $this->middleware->handle($this->request, $this->next);

    expect($response->getStatusCode())->toBe(404)
        ->and($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['status'])->toBe(404)
        ->and($content['message'])->toBe('HTTP error')
        ->and($content['error'])->toBe('HTTP error');
});

it('returns consistent json structure for all exceptions', function () {
    $exceptions = [
        new ThrottleRequestsException('Too Many Requests'),
        new HttpException(404, 'Not Found'),
        new Exception('General Error')
    ];

    foreach ($exceptions as $exception) {
        $this->next = function ($request) use ($exception) {
            throw $exception;
        };

        $response = $this->middleware->handle($this->request, $this->next);

        expect($response->getContent())->toBeJson();
        
        $content = json_decode($response->getContent(), true);
        expect($content)->toHaveKeys(['status', 'message', 'error']);
    }
});

it('handles nested exceptions', function () {
    $this->next = function ($request) {
        try {
            throw new Exception('Inner exception');
        } catch (Exception $e) {
            throw new HttpException(500, 'Outer exception', $e);
        }
    };

    $response = $this->middleware->handle($this->request, $this->next);

    expect($response->getStatusCode())->toBe(500)
        ->and($response->getContent())->toBeJson();
    
    $content = json_decode($response->getContent(), true);
    expect($content['status'])->toBe(500)
        ->and($content['message'])->toBe('Outer exception')
        ->and($content['error'])->toBe('Outer exception');
});