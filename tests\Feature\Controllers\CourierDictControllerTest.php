<?php

use App\Models\CourierDict;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

function createTestTerms()
{
    CourierDict::factory()->create([
        'term' => 'Express Delivery',
        'description' => 'Fast delivery service',
        'long_description' => 'A premium delivery service that ensures quick delivery of packages',
        'tag' => 'delivery',
        'slug' => 'express-delivery',
    ]);

    CourierDict::factory()->create([
        'term' => 'Standard Delivery',
        'description' => 'Regular delivery service',
        'long_description' => 'Standard delivery service with normal delivery times',
        'tag' => 'delivery',
        'slug' => 'standard-delivery',
    ]);
}

test('index page displays all dictionary terms', function () {
    createTestTerms();

    $response = $this->get(route('courier_dict.index'));

    $response->assertStatus(200)
        ->assertViewIs('dict.index')
        ->assertViewHas('terms')
        ->assertSee('Express Delivery')
        ->assertSee('Standard Delivery');
});

test('show page displays a specific term', function () {
    createTestTerms();

    $response = $this->get(route('courier_dict.show', 'express-delivery'));

    $response->assertStatus(200)
        ->assertViewIs('dict.show')
        ->assertViewHas('term')
        ->assertViewHas('relatedTerms')
        ->assertViewHas('randomRelatedTerms')
        ->assertSee('Express Delivery')
        ->assertSee('Fast delivery service');
});

test('show page returns 404 for non existent term', function () {
    $response = $this->get(route('courier_dict.show', 'non-existent-term'));

    $response->assertStatus(404);
});

test('search returns matching terms', function () {
    createTestTerms();

    $response = $this->get(route('courier_dict.search', ['search' => 'Express']));

    $response->assertStatus(200)
        ->assertViewIs('dict.index')
        ->assertViewHas('terms')
        ->assertViewHas('search')
        ->assertSee('Express Delivery')
        ->assertDontSee('Standard Delivery');
});

test('search returns all terms when search is empty', function () {
    createTestTerms();

    $response = $this->get(route('courier_dict.search', ['search' => '']));

    $response->assertStatus(200)
        ->assertViewIs('dict.index')
        ->assertViewHas('terms')
        ->assertViewHas('search')
        ->assertSee('Express Delivery')
        ->assertSee('Standard Delivery');
});

test('autocomplete returns matching suggestions', function () {
    createTestTerms();

    $response = $this->get(route('courier_dict.autocomplete', ['query' => 'Exp']));

    $response->assertStatus(200)
        ->assertJsonCount(1)
        ->assertJson([
            [
                'term' => 'Express Delivery',
                'slug' => 'express-delivery',
            ],
        ]);
});

test('autocomplete returns empty array when no matches found', function () {
    createTestTerms();

    $response = $this->get(route('courier_dict.autocomplete', ['query' => 'xyz']));

    $response->assertStatus(200)
        ->assertJsonCount(0);
});

test('autocomplete limits results to 5 suggestions', function () {
    CourierDict::factory()->count(10)->create(['term' => 'Test Term']);

    $response = $this->get(route('courier_dict.autocomplete', ['query' => 'Test']));

    $response->assertStatus(200)
        ->assertJsonCount(5);
}); 