<?php $__env->startSection('title', 'Edit Landing Page Section'); ?>

<?php $__env->startSection('content'); ?>
<div class="container px-6 mx-auto grid text-text-primary-light dark:text-text-primary-dark">
    <div class="flex justify-between items-center">
        <h2 class="my-6 text-2xl font-semibold">
            Edit Section: <?php echo e($section->name); ?>

        </h2>
        <a href="<?php echo e(route('admin.landing-page.index')); ?>" class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-primary-light dark:bg-primary-dark border border-transparent rounded-lg active:bg-primary-light/90 hover:bg-primary-light/90 dark:active:bg-primary-dark/90 dark:hover:bg-primary-dark/90 focus:outline-none focus:shadow-outline-purple">
            Back to Sections
        </a>
    </div>

    <?php if(session('success')): ?>
    <div class="mb-4 px-4 py-3 leading-normal text-green-700 bg-green-100 dark:text-green-100 dark:bg-green-700/30 rounded-lg" role="alert">
        <?php echo e(session('success')); ?>

    </div>
    <?php endif; ?>

    <div class="px-4 py-3 mb-8 bg-white dark:bg-bg-dark rounded-lg shadow-md">
        <form action="<?php echo e(route('admin.landing-page.update', $section)); ?>" method="POST" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>

            <div class="mb-6">
                <label for="name" class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-2">Section Name</label>
                <input type="text" name="name" id="name" value="<?php echo e(old('name', $section->name)); ?>" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md" required>
                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="mb-6">
                <label for="sort_order" class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-2">Sort Order</label>
                <input type="number" name="sort_order" id="sort_order" value="<?php echo e(old('sort_order', $section->sort_order)); ?>" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md" required>
                <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="mb-6">
                <label class="flex items-center">
                    <input type="checkbox" name="is_active" class="text-primary-light dark:text-primary-dark form-checkbox focus:border-primary-light dark:focus:border-primary-dark focus:outline-none focus:ring-primary-light/20 dark:focus:ring-primary-dark/20" <?php echo e($section->is_active ? 'checked' : ''); ?>>
                    <span class="ml-2 text-sm">Active</span>
                </label>
            </div>

            <div class="border-t border-border-light dark:border-border-dark pt-6 mb-6">
                <h3 class="text-lg font-medium mb-4">Section Content</h3>

                <?php $__currentLoopData = $section->contents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $content): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="mb-8 p-4 border border-border-light dark:border-border-dark rounded-lg">
                    <h4 class="font-medium mb-3"><?php echo e(Str::title(str_replace('_', ' ', $content->key))); ?></h4>
                    <input type="hidden" name="contents[<?php echo e($loop->index); ?>][id]" value="<?php echo e($content->id); ?>">

                    <?php if($content->type === 'text'): ?>
                        <input type="text" name="contents[<?php echo e($loop->index); ?>][value]" value="<?php echo e(old('contents.'.$loop->index.'.value', $content->value)); ?>" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md">
                    
                    <?php elseif($content->type === 'textarea'): ?>
                        <textarea name="contents[<?php echo e($loop->index); ?>][value]" rows="4" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md"><?php echo e(old('contents.'.$loop->index.'.value', $content->value)); ?></textarea>
                    
                    <?php elseif($content->type === 'image'): ?>
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0">
                                <?php if($content->value): ?>
                                <img src="<?php echo e(asset('storage/' . $content->value)); ?>" alt="<?php echo e($content->key); ?>" class="h-24 w-auto object-cover rounded">
                                <?php else: ?>
                                <div class="h-24 w-24 bg-bg-light dark:bg-gray-800 flex items-center justify-center rounded">
                                    <span class="text-text-secondary-light dark:text-text-secondary-dark">No image</span>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-grow">
                                <input type="file" name="contents[<?php echo e($loop->index); ?>][image]" class="block w-full text-sm text-text-secondary-light dark:text-text-secondary-dark file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-light/10 file:text-primary-light dark:file:bg-primary-dark/20 dark:file:text-primary-dark hover:file:bg-primary-light/20 dark:hover:file:bg-primary-dark/30">
                                <input type="hidden" name="contents[<?php echo e($loop->index); ?>][value]" value="<?php echo e($content->value); ?>">
                                <p class="text-xs text-text-secondary-light dark:text-text-secondary-dark mt-1">Leave empty to keep the current image</p>
                            </div>
                        </div>
                    
                    <?php elseif($content->type === 'color'): ?>
                        <div class="flex items-center space-x-2">
                            <input type="color" name="contents[<?php echo e($loop->index); ?>][value]" value="<?php echo e(old('contents.'.$loop->index.'.value', $content->value)); ?>" class="h-8 w-8 rounded">
                            <input type="text" value="<?php echo e(old('contents.'.$loop->index.'.value', $content->value)); ?>" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md" readonly>
                        </div>
                    
                    <?php elseif($content->type === 'boolean'): ?>
                        <label class="flex items-center">
                            <input type="checkbox" name="contents[<?php echo e($loop->index); ?>][value]" value="true" class="text-primary-light dark:text-primary-dark form-checkbox focus:border-primary-light dark:focus:border-primary-dark focus:outline-none focus:ring-primary-light/20 dark:focus:ring-primary-dark/20" <?php echo e($content->value === 'true' ? 'checked' : ''); ?>>
                            <span class="ml-2 text-sm">Enabled</span>
                        </label>
                    
                    <?php elseif($content->type === 'repeater'): ?>
                        <?php 
                            $repeaterItems = is_array($content->value) ? $content->value : json_decode($content->value, true);
                            $repeaterItems = $repeaterItems ?: [];
                        ?>
                        
                        <div class="repeater-container" data-index="<?php echo e($loop->index); ?>">
                            <div class="repeater-items">
                                <?php $__currentLoopData = $repeaterItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $itemIndex => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="repeater-item mb-4 p-3 border border-border-light dark:border-border-dark rounded-lg">
                                    <div class="flex justify-between items-center mb-2">
                                        <h5 class="font-medium">Item <?php echo e($itemIndex + 1); ?></h5>
                                        <button type="button" class="remove-item text-red-500 hover:text-red-700 dark:hover:text-red-400">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                    </div>
                                    
                                    <?php $__currentLoopData = $item; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fieldKey => $fieldValue): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="mb-3">
                                        <label class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1"><?php echo e(Str::title(str_replace('_', ' ', $fieldKey))); ?></label>
                                        
                                        <?php if(is_array($fieldValue)): ?>
                                            <!-- Handle nested arrays if needed -->
                                            <textarea name="contents[<?php echo e($loop->parent->parent->index); ?>][value][<?php echo e($itemIndex); ?>][<?php echo e($fieldKey); ?>]" rows="2" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md"><?php echo e(json_encode($fieldValue)); ?></textarea>
                                        <?php elseif(Str::contains($fieldKey, 'image') || Str::contains($fieldKey, 'logo') || Str::contains($fieldKey, 'avatar')): ?>
                                            <div class="flex items-start space-x-4">
                                                <div class="flex-shrink-0">
                                                    <?php if($fieldValue): ?>
                                                    <img src="<?php echo e(asset('storage/' . $fieldValue)); ?>" alt="<?php echo e($fieldKey); ?>" class="h-16 w-auto object-cover rounded">
                                                    <?php endif; ?>
                                                </div>
                                                <input type="text" name="contents[<?php echo e($loop->parent->parent->index); ?>][value][<?php echo e($itemIndex); ?>][<?php echo e($fieldKey); ?>]" value="<?php echo e($fieldValue); ?>" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md">
                                            </div>
                                        <?php elseif(Str::contains($fieldKey, 'icon')): ?>
                                            <textarea name="contents[<?php echo e($loop->parent->parent->index); ?>][value][<?php echo e($itemIndex); ?>][<?php echo e($fieldKey); ?>]" rows="2" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md"><?php echo e($fieldValue); ?></textarea>
                                        <?php elseif(Str::contains($fieldKey, 'description') || strlen($fieldValue) > 100): ?>
                                            <textarea name="contents[<?php echo e($loop->parent->parent->index); ?>][value][<?php echo e($itemIndex); ?>][<?php echo e($fieldKey); ?>]" rows="3" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md"><?php echo e($fieldValue); ?></textarea>
                                        <?php else: ?>
                                            <input type="text" name="contents[<?php echo e($loop->parent->parent->index); ?>][value][<?php echo e($itemIndex); ?>][<?php echo e($fieldKey); ?>]" value="<?php echo e($fieldValue); ?>" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md">
                                        <?php endif; ?>
                                    </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            
                            <button type="button" class="add-item mt-2 px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-primary-light dark:bg-primary-dark border border-transparent rounded-lg active:bg-primary-light/90 hover:bg-primary-light/90 dark:active:bg-primary-dark/90 dark:hover:bg-primary-dark/90 focus:outline-none focus:shadow-outline-purple">
                                Add Item
                            </button>
                            
                            <template class="repeater-template" data-content-index="<?php echo e($loop->index); ?>">
                                <div class="repeater-item mb-4 p-3 border border-border-light dark:border-border-dark rounded-lg">
                                    <div class="flex justify-between items-center mb-2">
                                        <h5 class="font-medium">New Item</h5>
                                        <button type="button" class="remove-item text-red-500 hover:text-red-700 dark:hover:text-red-400">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                    </div>
                                    
                                    <?php if(!empty($repeaterItems)): ?>
                                        <?php 
                                            $templateItem = reset($repeaterItems);
                                            $contentIndex = $loop->index;
                                        ?>
                                        <?php $__currentLoopData = $templateItem; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fieldKey => $fieldValue): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="mb-3">
                                            <label class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1"><?php echo e(Str::title(str_replace('_', ' ', $fieldKey))); ?></label>
                                            
                                            <?php if(is_array($fieldValue)): ?>
                                                <textarea name="contents[<?php echo e($contentIndex); ?>][value][__INDEX__][<?php echo e($fieldKey); ?>]" rows="2" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md"></textarea>
                                            <?php elseif(Str::contains($fieldKey, 'image') || Str::contains($fieldKey, 'logo') || Str::contains($fieldKey, 'avatar')): ?>
                                                <input type="text" name="contents[<?php echo e($contentIndex); ?>][value][__INDEX__][<?php echo e($fieldKey); ?>]" value="" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md">
                                            <?php elseif(Str::contains($fieldKey, 'icon')): ?>
                                                <textarea name="contents[<?php echo e($contentIndex); ?>][value][__INDEX__][<?php echo e($fieldKey); ?>]" rows="2" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md"></textarea>
                                            <?php elseif(Str::contains($fieldKey, 'description') || strlen($fieldValue) > 100): ?>
                                                <textarea name="contents[<?php echo e($contentIndex); ?>][value][__INDEX__][<?php echo e($fieldKey); ?>]" rows="3" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md"></textarea>
                                            <?php else: ?>
                                                <input type="text" name="contents[<?php echo e($contentIndex); ?>][value][__INDEX__][<?php echo e($fieldKey); ?>]" value="" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md">
                                            <?php endif; ?>
                                        </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                                </div>
                            </template>
                        </div>
                    <?php else: ?>
                        <input type="text" name="contents[<?php echo e($loop->index); ?>][value]" value="<?php echo e(old('contents.'.$loop->index.'.value', $content->value)); ?>" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md">
                    <?php endif; ?>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <div class="flex justify-end">
                <button type="submit" class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-primary-light dark:bg-primary-dark border border-transparent rounded-lg active:bg-primary-light/90 hover:bg-primary-light/90 dark:active:bg-primary-dark/90 dark:hover:bg-primary-dark/90 focus:outline-none focus:shadow-outline-purple">
                    Save Changes
                </button>
            </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle repeater fields
        document.querySelectorAll('.repeater-container').forEach(container => {
            const contentIndex = container.dataset.index;
            const itemsContainer = container.querySelector('.repeater-items');
            const template = container.querySelector('.repeater-template').innerHTML;
            const addButton = container.querySelector('.add-item');
            
            // Add new item
            addButton.addEventListener('click', function() {
                const newIndex = itemsContainer.children.length;
                const newItem = template.replace(/__INDEX__/g, newIndex);
                
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = newItem;
                const itemElement = tempDiv.firstElementChild;
                
                itemsContainer.appendChild(itemElement);
                
                // Update item title
                const itemTitle = itemElement.querySelector('h5');
                if (itemTitle) {
                    itemTitle.textContent = `Item ${newIndex + 1}`;
                }
                
                // Add remove event listener
                setupRemoveButton(itemElement.querySelector('.remove-item'));
            });
            
            // Setup existing remove buttons
            container.querySelectorAll('.remove-item').forEach(button => {
                setupRemoveButton(button);
            });
            
            function setupRemoveButton(button) {
                button.addEventListener('click', function() {
                    const item = this.closest('.repeater-item');
                    item.remove();
                    
                    // Update indices
                    updateIndices(itemsContainer, contentIndex);
                });
            }
            
            function updateIndices(container, contentIndex) {
                Array.from(container.children).forEach((item, idx) => {
                    // Update item title
                    const itemTitle = item.querySelector('h5');
                    if (itemTitle) {
                        itemTitle.textContent = `Item ${idx + 1}`;
                    }
                    
                    // Update input names
                    item.querySelectorAll('input, textarea').forEach(input => {
                        const name = input.getAttribute('name');
                        if (name) {
                            const newName = name.replace(/\[(\d+)\]/, `[${idx}]`);
                            input.setAttribute('name', newName);
                        }
                    });
                });
            }
        });
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/admin/landing-page/edit.blade.php ENDPATH**/ ?>