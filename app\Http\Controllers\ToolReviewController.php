<?php

namespace App\Http\Controllers;

use App\Models\ToolReview;
use Illuminate\Http\Request;
use App\Models\Tool;
use Illuminate\Support\Facades\Auth;

class ToolReviewController extends Controller
{

    public function store(Request $request, $toolId)
    {
        $validationRules = [
            'review' => 'required|string|min:10|max:1000',
            'rating' => 'nullable|integer|min:1|max:5',
        ];

        // Only require name and email for guests
        if (!Auth::check()) {
            $validationRules['name'] = 'required|string|max:255';
            $validationRules['email'] = 'required|email|max:255';
        }

        $request->validate($validationRules);

        $tool = Tool::findOrFail($toolId);

        $review = new ToolReview();
        $review->tool_id = $tool->id;

        if (Auth::check()) {
            $review->user_id = Auth::id();
        } else {
            $review->name = $request->name;
            $review->email = $request->email;
        }

        $review->review = $request->review;
        $review->rating = $request->rating;
        $review->save();

        return redirect()->back()->with('success', 'Review submitted successfully and is pending approval.');
    }

    public function viewReviews($slug)
    {
        $tool = Tool::where('slug', $slug)->firstOrFail();

        // Get all approved reviews with pagination
        $reviews = ToolReview::where('tool_id', $tool->id)
            ->where('is_approved', true)
            ->with('user')
            ->latest()
            // ->orderBy('created_at', 'desc')
            ->paginate(50);

        // Handle AJAX requests
        if (request()->ajax()) {
            $html = '';
            foreach ($reviews as $review) {
                $html .= view('partials.review-item', compact('review'))->render();
            }

            return response()->json([
                'html' => $html,
                'next_page_url' => $reviews->nextPageUrl(),
                'has_more_pages' => $reviews->hasMorePages()
            ]);
        }

        $pageTitle = $tool->name . ' - All Reviews';
        $metaDescription = "Read all reviews for {$tool->name}. See what users think about this tool.";

        // Set SEO
        $imgPath = "assets/images/tools/" . ($tool->thumbnail ?? 'default.webp');
        setSEO($pageTitle, $metaDescription, null, $imgPath);

        // Generate breadcrumbs
        $path = "tools/{$slug}/reviews";
        $breadcrumbController = new BreadcrumbController();
        $breadcrumbs = $breadcrumbController->generateBreadcrumb($path);

        return view('tools.tool-reviews', compact('tool', 'pageTitle', 'reviews', 'metaDescription', 'breadcrumbs'));
    }
}
