<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ContactFormMail extends Mailable
{
    use Queueable, SerializesModels;

    public $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function envelope(): Envelope
    {
        $subject = $this->data['subject'] ?? '';
        return new Envelope(
            subject: "New Contact Form Submission: {$subject}",
            tags: ['contact-form'],
            metadata: [
                'contact_form_id' => uniqid('contact_'),
            ],
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'emails.contact-form',
            with: [
                'name' => $this->data['name'] ?? '',
                'email' => $this->data['email'] ?? '',
                'subject' => $this->data['subject'] ?? '',
                'message' => $this->data['message'] ?? '',
                'siteName' => \App\Models\Setting::get('site_name', 'Test Site Name'),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
