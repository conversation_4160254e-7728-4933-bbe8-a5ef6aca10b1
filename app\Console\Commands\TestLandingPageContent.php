<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\LandingPageTestSeeder;

class TestLandingPageContent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'landing-page:test-content {--reset : Reset to original content}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update landing page content with test data to verify dynamic content functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('reset')) {
            $this->resetToOriginalContent();
        } else {
            $this->updateWithTestContent();
        }

        return 0;
    }

    /**
     * Update landing page content with test data
     */
    private function updateWithTestContent()
    {
        $this->info('🧪 Updating Landing Page Content with Test Data...');
        $this->info('This will add [TEST-DYNAMIC] prefixes to all content to verify dynamic functionality.');
        
        if (!$this->confirm('Do you want to continue?')) {
            $this->info('Operation cancelled.');
            return;
        }

        // Run the test seeder
        $seeder = new LandingPageTestSeeder();
        $seeder->setCommand($this);
        $seeder->run();

        $this->newLine();
        $this->info('✅ Test content update completed!');
        $this->info('🔍 Visit your landing page to verify all content shows [TEST-DYNAMIC] prefixes.');
        $this->info('📝 This confirms that all content is properly dynamic and manageable via admin panel.');
        $this->newLine();
        $this->warn('💡 To reset back to original content, run: php artisan landing-page:test-content --reset');
    }

    /**
     * Reset landing page content to original data
     */
    private function resetToOriginalContent()
    {
        $this->info('🔄 Resetting Landing Page Content to Original Data...');
        
        if (!$this->confirm('This will restore original landing page content. Continue?')) {
            $this->info('Operation cancelled.');
            return;
        }

        // Run the original seeder
        $this->call('db:seed', ['--class' => 'LandingPageSeeder']);

        $this->newLine();
        $this->info('✅ Landing page content has been reset to original data!');
        $this->info('🏠 Your landing page now shows the original content.');
    }
}
