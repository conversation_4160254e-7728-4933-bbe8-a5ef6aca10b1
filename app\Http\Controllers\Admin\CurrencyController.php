<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CurrencyRate;
use App\Models\PaymentGateway;
use App\Services\Payment\CurrencyConversionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\Rule;

class CurrencyController extends Controller
{
    protected $currencyService;

    public function __construct(CurrencyConversionService $currencyService)
    {
        $this->currencyService = $currencyService;
    }

    /**
     * Display currency management dashboard
     */
    public function index()
    {
        $rates = CurrencyRate::orderBy('from_currency')
            ->orderBy('to_currency')
            ->paginate(20);
            
        $supportedCurrencies = $this->getSupportedCurrencies();
        $gatewayConfigs = $this->getGatewayCurrencyConfigs();
        
        return view('admin.currency.index', compact('rates', 'supportedCurrencies', 'gatewayConfigs'));
    }

    /**
     * Store new currency rate
     */
    public function store(Request $request)
    {
        $request->validate([
            'from_currency' => 'required|string|size:3',
            'to_currency' => 'required|string|size:3|different:from_currency',
            'rate' => 'required|numeric|min:0.000001',
            'source' => 'required|in:manual,api'
        ]);

        CurrencyRate::updateOrCreate(
            [
                'from_currency' => strtoupper($request->from_currency),
                'to_currency' => strtoupper($request->to_currency)
            ],
            [
                'rate' => $request->rate,
                'source' => $request->source
            ]
        );

        return redirect()->route('admin.currency.index')
            ->with('success', 'Currency rate updated successfully');
    }

    /**
     * Update existing currency rate
     */
    public function update(Request $request, CurrencyRate $currencyRate)
    {
        $request->validate([
            'rate' => 'required|numeric|min:0.000001',
            'source' => 'required|in:manual,api'
        ]);

        $currencyRate->update([
            'rate' => $request->rate,
            'source' => $request->source
        ]);

        return redirect()->route('admin.currency.index')
            ->with('success', 'Currency rate updated successfully');
    }

    /**
     * Delete currency rate
     */
    public function destroy(CurrencyRate $currencyRate)
    {
        $currencyRate->delete();
        
        return redirect()->route('admin.currency.index')
            ->with('success', 'Currency rate deleted successfully');
    }

    /**
     * Fetch rates from external API
     */
    public function fetchRates(Request $request)
    {
        $request->validate([
            'base_currency' => 'required|string|size:3',
            'target_currencies' => 'required|array',
            'target_currencies.*' => 'string|size:3'
        ]);

        try {
            $baseCurrency = strtoupper($request->base_currency);
            $targetCurrencies = array_map('strtoupper', $request->target_currencies);
            
            $rates = $this->fetchExchangeRates($baseCurrency, $targetCurrencies);
            
            $updated = 0;
            foreach ($rates as $currency => $rate) {
                CurrencyRate::updateOrCreate(
                    [
                        'from_currency' => $baseCurrency,
                        'to_currency' => $currency
                    ],
                    [
                        'rate' => $rate,
                        'source' => 'api'
                    ]
                );
                $updated++;
            }
            
            return response()->json([
                'success' => true,
                'message' => "Updated {$updated} currency rates successfully",
                'rates' => $rates
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch currency rates: ' . $e->getMessage()
            ], 500);
        }
    }    /**

     * Test currency conversion
     */
    public function testConversion(Request $request)
    {
        $request->validate([
            'from_currency' => 'required|string|size:3',
            'to_currency' => 'required|string|size:3',
            'amount' => 'required|numeric|min:0.01'
        ]);

        try {
            $convertedAmount = $this->currencyService->convert(
                $request->amount,
                $request->from_currency,
                $request->to_currency
            );
            
            $rate = $this->currencyService->getExchangeRate(
                $request->from_currency,
                $request->to_currency
            );
            
            return response()->json([
                'success' => true,
                'original_amount' => $request->amount,
                'converted_amount' => $convertedAmount,
                'from_currency' => strtoupper($request->from_currency),
                'to_currency' => strtoupper($request->to_currency),
                'exchange_rate' => $rate
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Conversion failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Configure gateway currencies
     */
    public function configureGateway(Request $request, PaymentGateway $gateway)
    {
        $request->validate([
            'supported_currencies' => 'required|array',
            'supported_currencies.*' => 'string|size:3',
            'default_currency' => 'required|string|size:3'
        ]);

        $supportedCurrencies = array_map('strtoupper', $request->supported_currencies);
        $defaultCurrency = strtoupper($request->default_currency);
        
        // Ensure default currency is in supported currencies
        if (!in_array($defaultCurrency, $supportedCurrencies)) {
            $supportedCurrencies[] = $defaultCurrency;
        }

        $configuration = $gateway->configuration;
        $configuration['supported_currencies'] = $supportedCurrencies;
        $configuration['default_currency'] = $defaultCurrency;

        $gateway->update([
            'configuration' => $configuration,
            'supported_currencies' => $supportedCurrencies
        ]);

        return redirect()->route('admin.currency.index')
            ->with('success', "Currency configuration updated for {$gateway->display_name}");
    }

    /**
     * Get supported currencies list
     */
    private function getSupportedCurrencies()
    {
        return [
            'USD' => 'US Dollar',
            'EUR' => 'Euro',
            'GBP' => 'British Pound',
            'INR' => 'Indian Rupee',
            'JPY' => 'Japanese Yen',
            'AUD' => 'Australian Dollar',
            'CAD' => 'Canadian Dollar',
            'CHF' => 'Swiss Franc',
            'CNY' => 'Chinese Yuan',
            'SGD' => 'Singapore Dollar',
            'HKD' => 'Hong Kong Dollar',
            'NZD' => 'New Zealand Dollar',
            'SEK' => 'Swedish Krona',
            'NOK' => 'Norwegian Krone',
            'MXN' => 'Mexican Peso',
            'ZAR' => 'South African Rand',
            'BRL' => 'Brazilian Real',
            'RUB' => 'Russian Ruble',
            'KRW' => 'South Korean Won',
            'TRY' => 'Turkish Lira'
        ];
    }

    /**
     * Get gateway currency configurations
     */
    private function getGatewayCurrencyConfigs()
    {
        return PaymentGateway::select('id', 'name', 'display_name', 'configuration', 'supported_currencies')
            ->where('is_active', true)
            ->get()
            ->map(function ($gateway) {
                $config = $gateway->configuration;
                return [
                    'id' => $gateway->id,
                    'name' => $gateway->name,
                    'display_name' => $gateway->display_name,
                    'supported_currencies' => $gateway->supported_currencies ?? [],
                    'default_currency' => $config['default_currency'] ?? 'USD'
                ];
            });
    }

    /**
     * Fetch exchange rates from external API
     */
    private function fetchExchangeRates($baseCurrency, $targetCurrencies)
    {
        $cacheKey = "exchange_rates_{$baseCurrency}_" . implode('_', $targetCurrencies);
        
        return Cache::remember($cacheKey, 3600, function () use ($baseCurrency, $targetCurrencies) {
            // Using a free exchange rate API (you can replace with your preferred service)
            $response = Http::timeout(10)->get('https://api.exchangerate-api.com/v4/latest/' . $baseCurrency);
            
            if (!$response->successful()) {
                throw new \Exception('Failed to fetch exchange rates from API');
            }
            
            $data = $response->json();
            $rates = [];
            
            foreach ($targetCurrencies as $currency) {
                if (isset($data['rates'][$currency])) {
                    $rates[$currency] = $data['rates'][$currency];
                }
            }
            
            return $rates;
        });
    }

    /**
     * Clear currency rate cache
     */
    public function clearCache()
    {
        Cache::flush(); // You might want to be more specific about which cache keys to clear
        
        return redirect()->route('admin.currency.index')
            ->with('success', 'Currency rate cache cleared successfully');
    }

    /**
     * Bulk update rates
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'rates' => 'required|array',
            'rates.*.id' => 'required|exists:currency_rates,id',
            'rates.*.rate' => 'required|numeric|min:0.000001'
        ]);

        $updated = 0;
        foreach ($request->rates as $rateData) {
            CurrencyRate::where('id', $rateData['id'])
                ->update(['rate' => $rateData['rate']]);
            $updated++;
        }

        return response()->json([
            'success' => true,
            'message' => "Updated {$updated} currency rates successfully"
        ]);
    }
}