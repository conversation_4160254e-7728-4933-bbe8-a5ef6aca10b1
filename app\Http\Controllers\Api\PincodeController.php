<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PinCode;
use App\Models\State;
use App\Models\District;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use App\Traits\TracksApiRequests;

class PincodeController extends Controller
{
    use TracksApiRequests;

    public function getPincode(Request $request, $pincode)
    {
        $this->applyRateLimit($request);

        $pincodeData = PinCode::where('pincode', $pincode)->first();

        if ($pincodeData) {
            $transformedData = [
                'pincode' => $pincodeData->pincode,
                'state' => $pincodeData->state,
                'district' => $pincodeData->district,
                'post_office' => $pincodeData->name,
            ];

            $response = response()->json([
                'status' => 'success',
                'data' => $transformedData
            ]);
        } else {
            $response = response()->json([
                'status' => 'error',
                'message' => 'Pincode not found!'
            ], 404);
        }

        $this->trackApiRequest($request, $response);
        return $response;
    }

    public function getStateNames(Request $request, $stateName)
    {
        $states = State::where('name', 'LIKE', "%{$stateName}%")->get(['id', 'name']);
        
        $response = response()->json(['data' => $states]);

        $this->trackApiRequest($request, $response);
        return $response;
    }

    public function getDistrictNames(Request $request, $stateID, $districtName)
    {
        $districts = District::where('name', 'LIKE', "%{$districtName}%")->where('state_id', 'LIKE', "%{$stateID}%")->pluck('name');
        $response = response()->json($districts);
        $this->trackApiRequest($request, $response);
        return $response;
    }

    public function getTehsilNames(Request $request, $stateName, $districtName, $tehsilName)
    {

        $tehsil = PinCode::where('branch_type', 'LIKE', '%PO%')
            ->where('state', 'LIKE', "%{$stateName}%")
            ->where('district', 'LIKE', "%{$districtName}%")
            ->where('name', 'LIKE', "%{$tehsilName}%")
            ->pluck('name');

        $response = response()->json($tehsil);
        $this->trackApiRequest($request, $response);
        return $response;
    }
    public function getPincodesByPostName(Request $request, $stateName, $districtName, $postOfficeName)
    {

        $po_offices = PinCode::where('state', 'LIKE', "%{$stateName}%")
            ->where('district', 'LIKE', "%{$districtName}%")
            ->where('name', 'LIKE', "%{$postOfficeName}%")
            ->pluck('name');

        $response = response()->json($po_offices);
        $this->trackApiRequest($request, $response);
        return $response;
    }

    public function getPincodeDetails(Request $request, $stateName, $districtName, $postOfficeName)
    {
        $this->applyRateLimit($request);

        // $pincodeData = PinCode::where('name', $postOfficeName)->first();
        $pincodeData = PinCode::where('state', 'LIKE', "%{$stateName}%")
            ->where('district', 'LIKE', "%{$districtName}%")
            ->where('name', $postOfficeName)
            ->first();

        if ($pincodeData) {
            $response = response()->json([
                'status' => 'success',
                'data' => [
                    // 'postOfficeId' => $pincodeData->id,
                    'postalCircle' => $pincodeData->circle,
                    'postalCode' => $pincodeData->pincode,
                    'postalRegion' => $pincodeData->region,
                    'postalDivision' => $pincodeData->division,
                    'postOfficeName' => $pincodeData->name,
                    'branchType' => $pincodeData->branch_type,
                    'deliveryStatus' => $pincodeData->delivery_status,
                    'districtName' => $pincodeData->district,
                    'stateName' => $pincodeData->state,
                    'location' => [
                        'latitude' => $pincodeData->latitude,
                        'longitude' => $pincodeData->longitude,
                    ],
                ]
            ]);
        } else {
            $response = response()->json([
                'status' => 'error',
                'message' => 'Pincode not found!'
            ], 404);
        }

        $this->trackApiRequest($request, $response);
        return $response;
    }



    private function applyRateLimit(Request $request)
    {
        $user = $request->user();
        $limiterKey = $user->hasActiveSubscription() ? 'premium:' . $user->id : 'regular:' . $user->id;
        $limit = $user->hasActiveSubscription() ? 120 : 60;

        RateLimiter::hit($limiterKey, 60);

        if (RateLimiter::tooManyAttempts($limiterKey, $limit)) {
            abort(429, 'Too many requests');
        }
    }
}