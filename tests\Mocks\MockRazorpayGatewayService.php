<?php

namespace Tests\Mocks;

use App\Models\Order;
use App\Models\Payment;
use App\Services\Payment\PaymentGatewayServiceInterface;
use App\Services\Payment\Responses\PaymentResponse;
use App\Services\Payment\Responses\WebhookResponse;
use App\Services\Payment\Responses\RefundResponse;
use Illuminate\Http\Request;

class MockRazorpayGatewayService implements PaymentGatewayServiceInterface
{
    private array $mockResponses = [];
    private array $callLog = [];
    private bool $shouldFail = false;
    private string $failureReason = 'Mock failure';

    public function createPayment(Order $order): PaymentResponse
    {
        $this->logCall('createPayment', ['order_id' => $order->id]);

        if ($this->shouldFail) {
            return PaymentResponse::error($this->failureReason, 'MOCK_ERROR');
        }

        $mockOrderId = 'order_mock_' . uniqid();
        $mockPaymentId = 'pay_mock_' . uniqid();

        return PaymentResponse::success([
            'payment_id' => $mockPaymentId,
            'order_id' => $mockOrderId,
            'checkout_url' => 'https://checkout.razorpay.com/v1/checkout.js',
            'status' => 'created',
            'amount' => $order->amount * 100, // Convert to paise
            'currency' => $order->currency,
            'metadata' => [
                'key' => 'rzp_test_mock',
                'amount' => $order->amount * 100,
                'currency' => $order->currency,
                'order_id' => $mockOrderId,
                'name' => 'Test Company',
                'description' => 'Payment for Order #' . $order->order_number,
                'prefill' => [
                    'name' => $order->user->name,
                    'email' => $order->user->email,
                    'contact' => $order->user->phone ?? ''
                ],
                'theme' => [
                    'color' => '#3399cc'
                ]
            ]
        ]);
    }

    public function verifyPayment(string $paymentId): PaymentResponse
    {
        $this->logCall('verifyPayment', ['payment_id' => $paymentId]);

        if ($this->shouldFail) {
            return PaymentResponse::error($this->failureReason, 'VERIFICATION_FAILED');
        }

        // Mock successful verification
        return PaymentResponse::success([
            'payment_id' => $paymentId,
            'status' => 'captured',
            'amount' => 100000, // Mock amount in paise
            'currency' => 'INR',
            'method' => 'card',
            'captured' => true,
            'verified_at' => now()
        ]);
    }

    public function handleWebhook(Request $request): WebhookResponse
    {
        $this->logCall('handleWebhook', [
            'headers' => $request->headers->all(),
            'payload' => $request->all()
        ]);

        if ($this->shouldFail) {
            return WebhookResponse::error($this->failureReason);
        }

        $payload = $request->all();
        $eventType = $payload['event'] ?? 'unknown';

        return WebhookResponse::success([
            'event_type' => $eventType,
            'payment_id' => $payload['payload']['payment']['entity']['id'] ?? null,
            'status' => $payload['payload']['payment']['entity']['status'] ?? 'unknown',
            'processed_at' => now()
        ]);
    }

    public function refundPayment(string $paymentId, float $amount): RefundResponse
    {
        $this->logCall('refundPayment', [
            'payment_id' => $paymentId,
            'amount' => $amount
        ]);

        if ($this->shouldFail) {
            return RefundResponse::error($this->failureReason, 'REFUND_FAILED');
        }

        $mockRefundId = 'rfnd_mock_' . uniqid();

        return RefundResponse::success([
            'refund_id' => $mockRefundId,
            'payment_id' => $paymentId,
            'amount' => $amount * 100, // Convert to paise
            'currency' => 'INR',
            'status' => 'processed',
            'created_at' => now()
        ]);
    }

    public function getPaymentStatus(string $paymentId): PaymentResponse
    {
        $this->logCall('getPaymentStatus', ['payment_id' => $paymentId]);

        if ($this->shouldFail) {
            return PaymentResponse::error($this->failureReason, 'STATUS_CHECK_FAILED');
        }

        // Return mock status based on payment ID pattern
        $status = str_contains($paymentId, 'failed') ? 'failed' : 'captured';

        return PaymentResponse::success([
            'payment_id' => $paymentId,
            'status' => $status,
            'amount' => 100000,
            'currency' => 'INR',
            'method' => 'card',
            'captured' => $status === 'captured',
            'created_at' => now()->subMinutes(5)
        ]);
    }

    // Mock-specific methods for testing

    public function setMockResponse(string $method, array $response): void
    {
        $this->mockResponses[$method] = $response;
    }

    public function setShouldFail(bool $shouldFail, string $reason = 'Mock failure'): void
    {
        $this->shouldFail = $shouldFail;
        $this->failureReason = $reason;
    }

    public function getCallLog(): array
    {
        return $this->callLog;
    }

    public function getCallCount(string $method = null): int
    {
        if ($method === null) {
            return count($this->callLog);
        }

        return count(array_filter($this->callLog, function ($call) use ($method) {
            return $call['method'] === $method;
        }));
    }

    public function wasMethodCalled(string $method): bool
    {
        return $this->getCallCount($method) > 0;
    }

    public function getLastCall(string $method = null): ?array
    {
        $calls = $method ? array_filter($this->callLog, function ($call) use ($method) {
            return $call['method'] === $method;
        }) : $this->callLog;

        return empty($calls) ? null : end($calls);
    }

    public function clearCallLog(): void
    {
        $this->callLog = [];
    }

    public function simulateWebhookSignature(array $payload, string $secret = 'test_secret'): string
    {
        return hash_hmac('sha256', json_encode($payload), $secret);
    }

    public function simulatePaymentCapture(string $paymentId): array
    {
        return [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => $paymentId,
                        'amount' => 100000,
                        'currency' => 'INR',
                        'status' => 'captured',
                        'method' => 'card',
                        'captured' => true,
                        'created_at' => now()->timestamp
                    ]
                ]
            ]
        ];
    }

    public function simulatePaymentFailure(string $paymentId, string $errorCode = 'BAD_REQUEST_ERROR'): array
    {
        return [
            'event' => 'payment.failed',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => $paymentId,
                        'amount' => 100000,
                        'currency' => 'INR',
                        'status' => 'failed',
                        'method' => 'card',
                        'error_code' => $errorCode,
                        'error_description' => 'Mock payment failure',
                        'created_at' => now()->timestamp
                    ]
                ]
            ]
        ];
    }

    private function logCall(string $method, array $parameters = []): void
    {
        $this->callLog[] = [
            'method' => $method,
            'parameters' => $parameters,
            'timestamp' => now(),
            'call_id' => uniqid()
        ];
    }

    // Additional helper methods for testing specific scenarios

    public function simulateNetworkTimeout(): void
    {
        $this->setShouldFail(true, 'Network timeout - connection failed');
    }

    public function simulateInvalidCredentials(): void
    {
        $this->setShouldFail(true, 'Invalid API credentials');
    }

    public function simulateInsufficientFunds(): void
    {
        $this->setShouldFail(true, 'Payment failed due to insufficient funds');
    }

    public function simulateCardDeclined(): void
    {
        $this->setShouldFail(true, 'Card declined by issuing bank');
    }

    public function reset(): void
    {
        $this->mockResponses = [];
        $this->callLog = [];
        $this->shouldFail = false;
        $this->failureReason = 'Mock failure';
    }
}