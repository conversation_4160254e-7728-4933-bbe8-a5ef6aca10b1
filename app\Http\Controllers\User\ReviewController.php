<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Review;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReviewController extends Controller
{
    /**
     * Display a listing of the user's reviews.
     */
    public function index()
    {
        $reviews = Auth::user()->reviews()
            ->with('pincode')
            ->latest()
            ->paginate(10);

        return view('user.reviews.index', compact('reviews'));
    }

    /**
     * Show the form for editing the specified review.
     */
    public function edit(Review $review)
    {
        // Ensure the review belongs to the authenticated user
        if ($review->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        // Only allow editing of pending reviews
        if ($review->status !== 'pending') {
            return redirect()->route('user.reviews.index')
                ->with('error', 'Only pending reviews can be edited.');
        }

        return view('user.reviews.edit', compact('review'));
    }

    /**
     * Update the specified review in storage.
     */
    public function update(Request $request, Review $review)
    {
        // Ensure the review belongs to the authenticated user
        if ($review->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        // Only allow updating of pending reviews
        if ($review->status !== 'pending') {
            return redirect()->route('user.reviews.index')
                ->with('error', 'Only pending reviews can be updated.');
        }

        $validated = $request->validate([
            'review' => 'required|string|min:10|max:1000',
        ]);

        $review->update($validated);

        return redirect()->route('user.reviews.index')
            ->with('status', 'Review updated successfully.');
    }

    /**
     * Remove the specified review from storage.
     */
    public function destroy(Review $review)
    {
        // Ensure the review belongs to the authenticated user
        if ($review->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        // Only allow deletion of pending reviews
        if ($review->status !== 'pending') {
            return redirect()->route('user.reviews.index')
                ->with('error', 'Only pending reviews can be deleted.');
        }

        $review->delete();

        return redirect()->route('user.reviews.index')
            ->with('status', 'Review deleted successfully.');
    }
} 