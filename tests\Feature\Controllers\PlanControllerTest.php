<?php

use App\Models\Plan;
use App\Models\User;
use App\Models\Order;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create a test user
    $this->user = User::factory()->create();
});

test('authenticated user can view plans in dashboard', function () {
    // Create some active plans
    $plans = Plan::factory()->count(3)->create(['is_active' => true]);
    
    // Create an inactive plan that shouldn't be visible
    Plan::factory()->create(['is_active' => false]);

    // Act as the authenticated user
    $response = $this->actingAs($this->user)
        ->get(route('user.plans.index'));

    // Assert response
    $response->assertStatus(200)
        ->assertViewIs('user.plans.index')
        ->assertViewHas('plans')
        ->assertViewHas('currentPlanId');

    // Get the plans from the view
    $viewPlans = $response->viewData('plans');
    
    // Ensure we have a collection
    expect($viewPlans)->toBeInstanceOf(Collection::class);
    
    // Assert only active plans are shown
    expect($viewPlans)->toHaveCount(3);
    
    // Check each plan
    $viewPlans->each(function ($plan) {
        expect($plan)->toBeInstanceOf(Plan::class)
            ->and($plan->is_active)->toBeTrue();
    });
});

test('unauthenticated user cannot access plans dashboard', function () {
    $response = $this->get(route('user.plans.index'));
    
    $response->assertRedirect(route('login'));
});

test('public plans page shows all active plans', function () {
    // Create some active plans
    $plans = Plan::factory()->count(3)->create(['is_active' => true]);
    
    // Create an inactive plan that shouldn't be visible
    Plan::factory()->create(['is_active' => false]);

    $response = $this->get(route('plans.public'));

    $response->assertStatus(200)
        ->assertViewIs('plans.public-view')
        ->assertViewHas('plans');

    // Get the plans from the view
    $viewPlans = $response->viewData('plans');
    
    // Ensure we have a collection
    expect($viewPlans)->toBeInstanceOf(Collection::class);
    
    // Assert only active plans are shown
    expect($viewPlans)->toHaveCount(3);
    
    // Check each plan
    $viewPlans->each(function ($plan) {
        expect($plan)->toBeInstanceOf(Plan::class)
            ->and($plan->is_active)->toBeTrue();
    });
});

test('user can see their current plan id in dashboard', function () {
    // Create a plan and assign it to the user
    $plan = Plan::factory()->create(['is_active' => true]);
    
    // Create a completed order for the user
    Order::factory()->completed()->create([
        'user_id' => $this->user->id,
        'plan_id' => $plan->id,
    ]);

    $response = $this->actingAs($this->user)
        ->get(route('user.plans.index'));

    $response->assertStatus(200)
        ->assertViewHas('currentPlanId', (string)$plan->id);
});

test('plan features are properly cast to array', function () {
    $plan = Plan::factory()->create([
        'features' => [
            'feature1' => 'Test Feature 1',
            'feature2' => 'Test Feature 2'
        ]
    ]);

    $response = $this->actingAs($this->user)
        ->get(route('user.plans.index'));

    $viewPlans = $response->viewData('plans');
    $viewPlan = $viewPlans->first();

    expect($viewPlan->features)->toBeArray()
        ->toHaveKey('feature1', 'Test Feature 1')
        ->toHaveKey('feature2', 'Test Feature 2');
});

test('plans are ordered by price ascending', function () {
    $plans = [
        Plan::factory()->create(['price' => 100, 'is_active' => true]),
        Plan::factory()->create(['price' => 50, 'is_active' => true]),
        Plan::factory()->create(['price' => 75, 'is_active' => true]),
    ];

    $response = $this->actingAs($this->user)
        ->get(route('user.plans.index'));

    $viewPlans = $response->viewData('plans');
    
    // Get prices and convert to float for comparison
    $prices = $viewPlans->pluck('price')->map(function ($price) {
        return (float) $price;
    })->toArray();
    
    expect($prices)->toBe([50.0, 75.0, 100.0]);
});

test('plans page shows empty state when no active plans exist', function () {
    // Create only inactive plans
    Plan::factory()->count(3)->create(['is_active' => false]);

    $response = $this->actingAs($this->user)
        ->get(route('user.plans.index'));

    $viewPlans = $response->viewData('plans');
    expect($viewPlans)->toBeEmpty();
});

test('plan has required attributes', function () {
    $plan = Plan::factory()->create();

    expect($plan)->toHaveKeys([
        'name',
        'slug',
        'price',
        'request_limit',
        'description',
        'is_active',
        'features'
    ]);
});

test('plan price is cast to decimal', function () {
    $plan = Plan::factory()->create(['price' => 99.99]);

    expect((float) $plan->price)->toBe(99.99);
    expect((string) $plan->getRawOriginal('price'))->toBe('99.99');
});

test('plan request limit is cast to integer', function () {
    $plan = Plan::factory()->create(['request_limit' => '100']);

    expect($plan->request_limit)->toBe(100);
    expect($plan->getRawOriginal('request_limit'))->toBe('100');
});