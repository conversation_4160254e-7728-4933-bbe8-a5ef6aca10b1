@extends('layouts.user')

@section('title', 'Subscription Plans')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="text-center mb-12">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">Choose Your Plan</h1>
        <p class="text-lg text-gray-600">Select the plan that best fits your needs</p>
    </div>

    <div class="grid md:grid-cols-3 gap-8 max-w-7xl mx-auto">
        @foreach($plans as $plan)
            <div class="bg-white rounded-lg shadow-lg overflow-hidden {{ $currentPlanId === $plan->id ? 'ring-2 ring-blue-500' : '' }}">
                <div class="p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">{{ $plan->name }}</h2>
                    <div class="text-4xl font-bold text-gray-900 mb-4">
                        ₹{{ number_format($plan->price, 2) }}
                        <span class="text-lg font-normal text-gray-500">/month</span>
                    </div>
                    <p class="text-gray-600 mb-6">{{ $plan->description }}</p>
                    
                    <ul class="space-y-3 mb-8">
                        @foreach($plan->features as $key => $feature)
                            <li class="flex items-center">
                                <svg class="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                {{ $feature }}
                            </li>
                        @endforeach
                    </ul>

                    @if($currentPlanId === $plan->id)
                        <button disabled class="w-full bg-gray-300 text-gray-500 px-4 py-2 rounded font-medium">
                            Current Plan
                        </button>
                    @else
                        <form action="{{ route('orders.store') }}" method="POST">
                            @csrf
                            <input type="hidden" name="plan_id" value="{{ $plan->id }}">
                            <input type="hidden" name="amount" value="{{ $plan->price }}">
                            <input type="hidden" name="request_limit" value="{{ $plan->request_limit }}">
                            <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded font-medium">
                                {{ $currentPlanId === 'Free' ? 'Subscribe' : 'Change Plan' }}
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        @endforeach
    </div>
</div>
@endsection