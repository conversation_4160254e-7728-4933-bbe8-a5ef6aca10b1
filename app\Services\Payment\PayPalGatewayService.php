<?php

namespace App\Services\Payment;

use App\Models\Order;
use App\Models\Payment;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PayPalGatewayService extends BasePaymentGatewayService
{
    private string $clientId;
    private string $clientSecret;
    private string $mode;
    private string $baseUrl;

    public function __construct($gateway)
    {
        parent::__construct($gateway);
        
        $this->clientId = $this->config['client_id'];
        $this->clientSecret = $this->config['client_secret'];
        $this->mode = $this->config['mode'] ?? 'sandbox';
        
        $this->baseUrl = $this->mode === 'sandbox' 
            ? 'https://api-m.sandbox.paypal.com'
            : 'https://api-m.paypal.com';
    }

    /**
     * Create a payment for the given order.
     */
    public function createPayment(Order $order): PaymentResponse
    {
        try {
            $this->validateOrder($order);

            // Create payment record first
            $payment = $this->createPaymentRecord($order);

            // Get access token
            $accessToken = $this->getAccessToken();

            // Create PayPal order
            $paypalOrder = $this->createPayPalOrder($order, $payment, $accessToken);

            if (!$paypalOrder) {
                $this->handleFailedPayment($payment, 'Failed to create PayPal order');
                return PaymentResponse::error('Failed to create payment order');
            }

            // Update payment record with PayPal order ID
            $this->updatePaymentRecord($payment, [
                'gateway_order_id' => $paypalOrder['id'],
                'payment_details' => $paypalOrder,
            ]);

            // Find approval URL
            $approvalUrl = null;
            foreach ($paypalOrder['links'] as $link) {
                if ($link['rel'] === 'approve') {
                    $approvalUrl = $link['href'];
                    break;
                }
            }

            $this->logGatewayInteraction('create_payment', [
                'order_id' => $order->id,
                'payment_id' => $payment->id,
                'paypal_order_id' => $paypalOrder['id'],
            ]);

            return PaymentResponse::success([
                'payment_id' => $payment->id,
                'order_id' => $paypalOrder['id'],
                'checkout_url' => $approvalUrl,
                'status' => 'pending',
                'message' => 'PayPal order created successfully',
                'gateway_response' => $paypalOrder,
            ]);

        } catch (\Exception $e) {
            if (isset($payment)) {
                $this->handleFailedPayment($payment, $e->getMessage());
            }
            throw $this->handleGatewayException($e, 'create_payment');
        }
    }

    /**
     * Verify a payment by its ID.
     */
    public function verifyPayment(string $paymentId): PaymentResponse
    {
        try {
            // Find payment record
            $payment = Payment::where('gateway_payment_id', $paymentId)
                            ->orWhere('gateway_order_id', $paymentId)
                            ->orWhere('id', $paymentId)
                            ->first();

            if (!$payment) {
                return PaymentResponse::error('Payment not found');
            }

            // Get access token
            $accessToken = $this->getAccessToken();

            // Get PayPal order details
            $paypalOrder = $this->getPayPalOrder($payment->gateway_order_id, $accessToken);

            if (!$paypalOrder) {
                return PaymentResponse::error('PayPal order not found');
            }

            // Map PayPal status to internal status
            $status = $this->mapPayPalStatus($paypalOrder['status']);
            
            // Update payment record
            $updateData = [
                'payment_status' => $status,
                'payment_details' => array_merge($payment->payment_details ?? [], $paypalOrder),
            ];

            if ($status === Payment::STATUS_COMPLETED) {
                $updateData['paid_at'] = now();
                $this->handleSuccessfulPayment($payment, $paypalOrder);
            } elseif ($status === Payment::STATUS_FAILED) {
                $updateData['failed_reason'] = 'PayPal payment failed';
                $this->handleFailedPayment($payment, $updateData['failed_reason'], $paypalOrder);
            }

            $this->updatePaymentRecord($payment, $updateData);

            $this->logGatewayInteraction('verify_payment', [
                'payment_id' => $paymentId,
                'status' => $status,
            ]);

            return PaymentResponse::success([
                'payment_id' => $payment->id,
                'order_id' => $payment->gateway_order_id,
                'status' => $status,
                'message' => 'Payment verification completed',
                'gateway_response' => $paypalOrder,
            ]);

        } catch (\Exception $e) {
            throw $this->handleGatewayException($e, 'verify_payment');
        }
    }

    /**
     * Handle webhook notifications from PayPal.
     */
    public function handleWebhook(Request $request): WebhookResponse
    {
        try {
            // Log webhook event
            $webhookLog = $this->logWebhookEvent($request, $request->input('event_type', 'unknown'));

            // PayPal webhook verification would go here
            // For now, we'll process the webhook without signature verification
            
            $payload = $request->all();
            $eventType = $payload['event_type'] ?? 'unknown';
            $resource = $payload['resource'] ?? null;

            if (!$resource) {
                $webhookLog->markAsFailed('Invalid webhook payload - no resource');
                return WebhookResponse::error('Invalid webhook payload');
            }

            // Find payment record based on PayPal order ID or payment ID
            $payment = null;
            if (isset($resource['id'])) {
                $payment = Payment::where('gateway_order_id', $resource['id'])
                                ->orWhere('gateway_payment_id', $resource['id'])
                                ->first();
            }

            if (!$payment) {
                $webhookLog->markAsFailed('Payment not found');
                return WebhookResponse::error('Payment not found');
            }

            // Update webhook log with payment
            $webhookLog->update(['payment_id' => $payment->id]);

            // Process webhook event
            $response = $this->processPayPalWebhookEvent($eventType, $resource, $payment);

            $webhookLog->markAsProcessed();

            return $response;

        } catch (\Exception $e) {
            if (isset($webhookLog)) {
                $webhookLog->markAsFailed($e->getMessage());
            }
            
            Log::error('PayPal webhook processing failed', [
                'error' => $e->getMessage(),
                'payload' => $request->all(),
            ]);

            return WebhookResponse::error('Webhook processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Refund a payment.
     */
    public function refundPayment(string $paymentId, float $amount): RefundResponse
    {
        try {
            $payment = Payment::where('gateway_payment_id', $paymentId)
                            ->orWhere('id', $paymentId)
                            ->first();

            if (!$payment || !$payment->canBeRefunded()) {
                return RefundResponse::error('Payment cannot be refunded');
            }

            // Get access token
            $accessToken = $this->getAccessToken();

            // Create refund in PayPal
            $refundData = $this->createPayPalRefund($payment->gateway_payment_id, $amount, $accessToken);

            if (!$refundData) {
                return RefundResponse::error('Failed to create refund');
            }

            // Update payment record
            $this->updatePaymentRecord($payment, [
                'refund_id' => $refundData['id'],
                'payment_status' => Payment::STATUS_REFUNDED,
            ]);

            $this->logGatewayInteraction('refund_payment', [
                'payment_id' => $paymentId,
                'refund_amount' => $amount,
                'refund_id' => $refundData['id'],
            ]);

            return RefundResponse::success([
                'refund_id' => $refundData['id'],
                'payment_id' => $payment->id,
                'refund_amount' => $amount,
                'currency' => $payment->currency,
                'status' => 'processed',
                'message' => 'Refund processed successfully',
                'gateway_response' => $refundData,
            ]);

        } catch (\Exception $e) {
            throw $this->handleGatewayException($e, 'refund_payment');
        }
    }

    /**
     * Get the current status of a payment.
     */
    public function getPaymentStatus(string $paymentId): PaymentStatus
    {
        try {
            // Get access token
            $accessToken = $this->getAccessToken();

            // Get PayPal order details
            $paypalOrder = $this->getPayPalOrder($paymentId, $accessToken);

            if (!$paypalOrder) {
                return PaymentStatus::error('Payment not found');
            }

            $status = $this->mapPayPalStatus($paypalOrder['status']);

            return PaymentStatus::success([
                'payment_id' => $paypalOrder['id'],
                'status' => $status,
                'amount' => $this->parseAmountFromGateway($paypalOrder['purchase_units'][0]['amount']['value'] ?? 0, $paypalOrder['purchase_units'][0]['amount']['currency_code'] ?? 'USD'),
                'currency' => $paypalOrder['purchase_units'][0]['amount']['currency_code'] ?? 'USD',
                'gateway_response' => $paypalOrder,
                'updated_at' => isset($paypalOrder['update_time']) ? new \DateTime($paypalOrder['update_time']) : null,
            ]);

        } catch (\Exception $e) {
            throw $this->handleGatewayException($e, 'get_payment_status');
        }
    }

    /**
     * Test the gateway connection and credentials.
     */
    public function testConnection(): bool
    {
        try {
            $accessToken = $this->getAccessToken();
            return !empty($accessToken);
        } catch (\Exception $e) {
            Log::error('PayPal connection test failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Calculate gateway fee for PayPal.
     */
    public function calculateFee(float $amount, string $currency): float
    {
        // PayPal charges approximately 2.9% + $0.30 for domestic transactions
        return round(($amount * 0.029) + 0.30, 2);
    }

    /**
     * Get PayPal access token.
     */
    private function getAccessToken(): string
    {
        $response = Http::withBasicAuth($this->clientId, $this->clientSecret)
                      ->asForm()
                      ->post($this->baseUrl . '/v1/oauth2/token', [
                          'grant_type' => 'client_credentials'
                      ]);

        if ($response->successful()) {
            $data = $response->json();
            return $data['access_token'] ?? '';
        }

        throw new PaymentGatewayException('Failed to get PayPal access token: ' . $response->body());
    }

    /**
     * Create PayPal order.
     */
    private function createPayPalOrder(Order $order, Payment $payment, string $accessToken): ?array
    {
        $orderData = [
            'intent' => 'CAPTURE',
            'purchase_units' => [
                [
                    'reference_id' => $order->order_number,
                    'amount' => [
                        'currency_code' => strtoupper($order->currency ?? 'USD'),
                        'value' => number_format($order->amount, 2, '.', '')
                    ],
                    'description' => "Payment for Order #{$order->id}"
                ]
            ],
            'application_context' => [
                'return_url' => route('payment.success', ['order_id' => $order->id]),
                'cancel_url' => route('payment.cancel', ['order_id' => $order->id]),
                'brand_name' => config('app.name'),
                'user_action' => 'PAY_NOW',
                'shipping_preference' => 'NO_SHIPPING'
            ]
        ];

        $response = Http::withToken($accessToken)
                      ->post($this->baseUrl . '/v2/checkout/orders', $orderData);

        if ($response->successful()) {
            return $response->json();
        }

        Log::error('Failed to create PayPal order', [
            'response' => $response->json(),
            'order_data' => $orderData,
        ]);

        return null;
    }

    /**
     * Get PayPal order details.
     */
    private function getPayPalOrder(string $orderId, string $accessToken): ?array
    {
        $response = Http::withToken($accessToken)
                      ->get($this->baseUrl . '/v2/checkout/orders/' . $orderId);

        if ($response->successful()) {
            return $response->json();
        }

        return null;
    }

    /**
     * Create refund in PayPal.
     */
    private function createPayPalRefund(string $captureId, float $amount, string $accessToken): ?array
    {
        $refundData = [
            'amount' => [
                'value' => number_format($amount, 2, '.', ''),
                'currency_code' => 'USD' // This should be dynamic based on original payment
            ]
        ];

        $response = Http::withToken($accessToken)
                      ->post($this->baseUrl . '/v2/payments/captures/' . $captureId . '/refund', $refundData);

        if ($response->successful()) {
            return $response->json();
        }

        return null;
    }

    /**
     * Process PayPal webhook event.
     */
    private function processPayPalWebhookEvent(string $eventType, array $resource, Payment $payment): WebhookResponse
    {
        $response = WebhookResponse::success([
            'event_type' => $eventType,
            'payment_id' => $payment->id,
            'order_id' => $payment->gateway_order_id,
        ]);

        switch ($eventType) {
            case 'PAYMENT.CAPTURE.COMPLETED':
                $this->handleSuccessfulPayment($payment, $resource);
                $response->setStatus('completed')
                        ->setMessage('Payment captured successfully')
                        ->addAction('send_confirmation_email', ['payment_id' => $payment->id]);
                break;

            case 'PAYMENT.CAPTURE.DENIED':
            case 'PAYMENT.CAPTURE.DECLINED':
                $reason = $resource['status_details']['reason'] ?? 'Payment declined';
                $this->handleFailedPayment($payment, $reason, $resource);
                $response->setStatus('failed')
                        ->setMessage('Payment failed')
                        ->addAction('send_failure_notification', ['payment_id' => $payment->id]);
                break;

            case 'PAYMENT.CAPTURE.PENDING':
                $this->updatePaymentRecord($payment, [
                    'payment_status' => Payment::STATUS_PENDING,
                    'payment_details' => array_merge($payment->payment_details ?? [], $resource),
                ]);
                $response->setStatus('pending')
                        ->setMessage('Payment pending');
                break;

            default:
                $response->setMessage('Webhook event processed');
        }

        return $response->setPayload($resource);
    }

    /**
     * Map PayPal status to internal status.
     */
    private function mapPayPalStatus(string $paypalStatus): string
    {
        return match($paypalStatus) {
            'COMPLETED' => Payment::STATUS_COMPLETED,
            'APPROVED' => Payment::STATUS_PENDING,
            'CREATED' => Payment::STATUS_PENDING,
            'SAVED' => Payment::STATUS_PENDING,
            'VOIDED' => Payment::STATUS_FAILED,
            'PAYER_ACTION_REQUIRED' => Payment::STATUS_PENDING,
            default => Payment::STATUS_PENDING,
        };
    }
}