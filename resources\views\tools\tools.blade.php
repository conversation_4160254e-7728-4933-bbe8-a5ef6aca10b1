@extends('layouts.app')

@section('json-ld')
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "{{ $pageTitle }}",
            "description": "{{ $metaDescription }}",
            "publisher": {
                "@type": "Organization",
                "name": "NSK Multiservices Kosbi"
            },
            "url": "{{ url()->current() }}",
            "datePublished": "2024-07-04",
            "dateModified": "2024-07-04"
        }
    </script>

    @if (!empty($breadcrumbs))
        <script type="application/ld+json">
            {
                "@context": "https://schema.org",
                "@type": "BreadcrumbList",
                "itemListElement": [
                    @foreach ($breadcrumbs as $index => $breadcrumb)
                        {
                            "@type": "ListItem",
                            "position": {{ $index + 1 }},
                            "name": "{{ $breadcrumb['name'] }}",
                            "item": "{{ $breadcrumb['url'] }}"
                        } @if (!$loop->last) , @endif
                    @endforeach
                ]
            }
        </script>
    @endif

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "NSK Multiservices Kosbi",
            "url": "{{ url()->current() }}",
            "logo": "{{ asset('assets/images/nsk/nsk-multiservices-logo.webp') }}",
            "contactPoint": {
                "@type": "ContactPoint",
                "telephone": "+91-9834754391",
                "contactType": "Customer Service"
            },
            "sameAs": [
                "https://www.linkedin.com/in/nsk-multiservices/",
                "https://www.instagram.com/nskmultiservices/",
                "https://x.com/digi_nsk",
                "https://www.facebook.com/nskmultiservices/"
            ]
        }
    </script>
@endsection

@section('content')
    <x-breadcrumb :segments="$breadcrumbs" :pageTitle="$pageTitle" />
    
    <div class="container max-w-6xl mx-auto px-4">
        <!-- Hero Section -->
        <div class="relative bg-gradient-to-r from-primary-light to-primary-dark dark:from-primary-dark dark:to-primary-light rounded-3xl overflow-hidden mb-12 border border-border-light dark:border-border-dark shadow-sm">
            <div class="absolute inset-0 bg-grid-white/20 [mask-image:linear-gradient(0deg,white,transparent)]"></div>
            <div class="relative px-6 py-16 sm:py-24 sm:px-12">
                <div class="text-center max-w-3xl mx-auto">
                    <h2 class="text-4xl sm:text-5xl font-bold text-white mb-6">{{ $pageTitle }}</h2>
                    <p class="text-xl text-white/90">{{ $metaDescription }}</p>
                </div>
            </div>
            <div class="absolute inset-0 bg-gradient-to-t from-white/30 dark:from-bg-dark/30 to-transparent"></div>
        </div>

        <!-- Tools Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @forelse ($tools as $item)
                <div class="group relative bg-white dark:bg-bg-dark rounded-2xl shadow-sm hover:shadow-md dark:hover:shadow-lg border border-border-light dark:border-border-dark transition-all duration-300 overflow-hidden">
                    <!-- Tool Image with Overlay -->
                    <div class="relative aspect-[4/3] overflow-hidden">
                        <img class="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300" 
                             src="{{ uploads_url($item->image_path) }}"
                             alt="{{ $item->name }}">
                        <div class="absolute inset-0 bg-gradient-to-t from-gray-800/40 dark:from-gray-900/60 to-transparent"></div>
                    </div>

                    <!-- Content -->
                    <div class="relative p-6">
                        <!-- Tool Category Badge -->
                        <div class="absolute -top-4 left-6">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-accent-light/20 dark:bg-accent-dark/20 text-accent-light dark:text-accent-dark border border-accent-light/30 dark:border-accent-dark/30">
                                {{ $item->category ?? 'Tool' }}
                            </span>
                        </div>

                        <!-- Tool Info -->
                        <div class="mt-2">
                            <h3 class="text-xl font-bold text-text-primary-light dark:text-text-primary-dark mb-3">
                                {{ Str::limit(__($item->name), 40) }}
                            </h3>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark mb-6 line-clamp-2">
                                {{ Str::limit(strip_tags(__($item->meta_description)), 90) }}
                            </p>

                            <!-- Action Buttons -->
                            <div class="flex items-center justify-between">
                                <a href="{{ url("/tools/{$item->slug}") }}" 
                                   class="inline-flex items-center px-4 py-2 bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-white font-medium rounded-lg transition-colors duration-200 group">
                                    Try Now
                                    <svg class="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform" 
                                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                              d="M14 5l7 7m0 0l-7 7m7-7H3"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-span-full">
                    <div class="text-center py-12 bg-bg-light dark:bg-bg-dark rounded-2xl border border-border-light dark:border-border-dark">
                        <svg class="mx-auto h-12 w-12 text-text-secondary-light dark:text-text-secondary-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <h2 class="mt-4 text-2xl font-bold text-text-primary-light dark:text-text-primary-dark">{{ __($emptyMessage) }}</h2>
                    </div>
                </div>
            @endforelse
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Add smooth hover effect for tool cards
        document.querySelectorAll('.group').forEach(card => {
            card.addEventListener('mousemove', e => {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                card.style.transform = `perspective(1000px) rotateX(${(y - rect.height/2)/20}deg) rotateY(${-(x - rect.width/2)/20}deg)`;
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0)';
            });
        });
    </script>
@endpush