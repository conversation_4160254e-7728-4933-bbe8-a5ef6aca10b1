<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    const TOKEN_MAX_PER_USER = 5;
    const TOKEN_EXPIRY_DAYS = 365;

    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    /**
     * User roles
     */
    const ROLE_ADMIN = 'admin';
    const ROLE_USER = 'user';

    /**
     * User statuses
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'status',
        'two_factor_enabled',
        'plan_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'two_factor_enabled' => 'boolean',
        ];
    }

    /**
     * Check if the user is an admin.
     *
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->role === self::ROLE_ADMIN;
    }

    /**
     * Check if the user is active.
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Get the orders for the user.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the API requests for the user.
     */
    public function apiRequests(): HasMany
    {
        return $this->hasMany(ApiRequest::class);
    }

    /**
     * Get the reviews for the user.
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the current active plan for the user.
     * 
     * @return string
     */
    public function getCurrentPlan(): string
    {
        $latestOrder = $this->orders()
            ->where('status', 'completed')
            ->latest('paid_at')
            ->first();

        return $latestOrder ? $latestOrder->plan_id : 'Free';
    }

    /**
     * Get the total API requests made by the user.
     * 
     * @return int
     */
    public function getTotalRequests(): int
    {
        return $this->apiRequests()->count();
    }

    /**
     * Get the remaining API requests for the user.
     * 
     * @return int
     */
    public function getRemainingRequests(): int
    {
        $latestOrder = $this->orders()
            ->where('status', 'completed')
            ->latest('paid_at')
            ->first();

        $limit = $latestOrder ? $latestOrder->request_limit : 100; // Default to 100 for free plan
        $used = $this->getTotalRequests();

        return max(0, $limit - $used);
    }

    public function hasActiveSubscription(): bool
    {
        return $this->orders()
            ->where('status', Order::STATUS_COMPLETED)
            ->where(function ($query) {
                $query->whereNull('paid_at')
                    ->orWhere('paid_at', '>=', now()->subDays(30));
            })
            ->exists();
    }

    public function getActiveOrder()
    {
        return $this->orders()
            ->where('status', Order::STATUS_COMPLETED)
            ->latest('paid_at')
            ->first();
    }

    /**
     * Check if user can create more tokens
     */
    public function canCreateToken(): bool
    {
        return $this->tokens()->count() < self::TOKEN_MAX_PER_USER;
    }

    /**
     * Get active tokens count
     */
    public function getActiveTokensCount(): int
    {
        return $this->tokens()
            ->where('created_at', '>=', now()->subDays(self::TOKEN_EXPIRY_DAYS))
            ->count();
    }

    /**
     * Create a new API token with expiration
     */
    public function createApiToken(string $name): \Laravel\Sanctum\NewAccessToken
    {
        return $this->createToken($name, ['*'], now()->addDays(self::TOKEN_EXPIRY_DAYS));
    }

    /**
     * Get the user's current plan.
     */
    public function plan()
    {
        return $this->belongsTo(Plan::class);
    }
}
