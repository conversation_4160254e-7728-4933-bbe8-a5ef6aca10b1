#!/usr/bin/env python3
import mysql.connector
import json
from datetime import datetime

# Database connection configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'pincodes-new'
}

def migrate_posts_to_blog_posts():
    """
    Migrates data from the 'posts' table to the 'blog_posts' table.
    
    Field mappings:
    - posts.id → blog_posts.id (maintain IDs)
    - posts.title → blog_posts.title
    - posts.content → blog_posts.content
    - posts.slug → blog_posts.slug
    - posts.description → blog_posts.excerpt
    - posts.image → blog_posts.featured_image
    - posts.author_id → blog_posts.user_id
    - posts.status → blog_posts.is_published (convert 'published' to 1, 'draft' to 0)
    - posts.created_at → blog_posts.created_at
    - posts.updated_at → blog_posts.updated_at
    - posts.description → blog_posts.meta_description
    - posts.keywords → blog_posts.meta_keywords (convert string to JSON)
    - posts.title → blog_posts.meta_title (using the same title)
    - Default values for other fields
    """
    try:
        # Connect to the database
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor(dictionary=True)
        
        # Get all posts from the source table
        cursor.execute("SELECT * FROM posts")
        posts = cursor.fetchall()
        
        print(f"Found {len(posts)} posts to migrate.")
        
        # Prepare the insert query
        insert_query = """
        INSERT INTO blog_posts (
            id, title, content, slug, excerpt, featured_image, 
            user_id, is_published, published_at, 
            meta_title, meta_description, meta_keywords,
            created_at, updated_at
        ) VALUES (
            %s, %s, %s, %s, %s, %s, 
            %s, %s, %s, 
            %s, %s, %s,
            %s, %s
        )
        """
        
        # Process and insert each post
        for post in posts:
            # Convert status to is_published boolean
            is_published = 1 if post['status'] == 'published' else 0
            
            # Set published_at date if post is published
            published_at = datetime.now() if is_published else None
            
            # Convert keywords string to JSON array if exists
            meta_keywords = None
            if post['keywords']:
                # Split by comma and remove whitespace
                keywords_list = [k.strip() for k in post['keywords'].split(',')]
                meta_keywords = json.dumps(keywords_list)
            
            # Prepare data for insertion
            post_data = (
                post['id'],                 # id
                post['title'],              # title
                post['content'],            # content
                post['slug'],               # slug
                post['description'],        # excerpt
                post['image'],              # featured_image
                post['author_id'],          # user_id
                is_published,               # is_published
                published_at,               # published_at
                post['title'],              # meta_title
                post['description'],        # meta_description
                meta_keywords,              # meta_keywords
                post['created_at'],         # created_at
                post['updated_at']          # updated_at
            )
            
            try:
                cursor.execute(insert_query, post_data)
                print(f"Migrated post ID: {post['id']} - '{post['title']}'")
            except mysql.connector.Error as err:
                print(f"Error migrating post ID {post['id']}: {err}")
        
        # Commit the changes
        conn.commit()
        print(f"Migration completed successfully. Migrated {cursor.rowcount} posts.")
        
    except mysql.connector.Error as err:
        print(f"Database error: {err}")
    finally:
        if 'conn' in locals() and conn.is_connected():
            cursor.close()
            conn.close()
            print("Database connection closed.")

if __name__ == "__main__":
    migrate_posts_to_blog_posts()