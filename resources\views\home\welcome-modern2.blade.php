@extends('layouts.app')

@section('content')
    <div>
        <div class="hidden">{{ $pageTitle ?? 'Welcome to BharatPostal Info' }}</div>
        <!-- Hero Section -->
        <section id="hero" class="pt-24 relative overflow-hidden bg-gray-50">
            <div class="particles-container" id="particles-js"></div>
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div class="relative z-10">
                        <div class="mb-8">
                            <span
                                class="inline-block px-4 py-1 rounded-full bg-primary-100 text-primary-800 text-xs font-semibold tracking-wider uppercase">Trusted
                                by 5,000+ businesses</span>
                        </div>
                        <h1 class="text-4xl md:text-5xl lg:text-6xl font-extrabold tracking-tight mb-4">
                            <span>India's Most</span>
                            <span class="gradient-text">Comprehensive</span>
                            <span class="block">Pincode Directory</span>
                        </h1>
                        <p class="text-lg md:text-xl text-gray-600 mb-8 max-w-2xl">
                            Find detailed information about any pincode in India. Our comprehensive database covers all
                            155,000+ postal codes with accurate locality data.
                        </p>
                        <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                            <a href="#search-section"
                                class="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-300 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl">
                                <span>Search Pincode</span>
                                <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                    stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </a>
                            <a href="#tools"
                                class="px-6 py-3 bg-white text-primary-600 rounded-lg hover:bg-gray-100 transition-colors duration-300 flex items-center justify-center space-x-2 border border-gray-200 shadow-md hover:shadow-lg">
                                <span>Explore Tools</span>
                                <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                    stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17 8l4 4m0 0l-4 4m4-4H3" />
                                </svg>
                            </a>
                        </div>
                    </div>
                    <div class="relative">

                        <div class="relative z-10 animate-float">
                            <img src="{{ uploads_url('images/india-map.webp') }}" alt="India Pincode Map"
                                class="rounded-2xl shadow-2xl">
                            <div class="absolute -bottom-5 -right-5 bg-white rounded-2xl shadow-lg p-4 max-w-xs">
                                <div class="flex items-center space-x-2">
                                    <div class="bg-green-500 rounded-full p-2 text-white">
                                        <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                    <span class="font-medium">Verified Data</span>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">All pincodes are verified with India Post records</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12 text-center">
                    <div class="flex flex-col items-center">
                        <div class="text-4xl font-bold text-primary-600" id="counter-pincodes">155,000+</div>
                        <div class="text-gray-600 mt-2">Total Pincodes</div>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="text-4xl font-bold text-primary-600" id="counter-states">36+</div>
                        <div class="text-gray-600 mt-2">States & UTs</div>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="text-4xl font-bold text-primary-600" id="counter-districts">700+</div>
                        <div class="text-gray-600 mt-2">Districts</div>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="text-4xl font-bold text-primary-600" id="counter-searches">10M+</div>
                        <div class="text-gray-600 mt-2">Monthly Searches</div>
                    </div>
                </div>
            </div>

            <div class="absolute bottom-0 left-0 w-full overflow-hidden">
                <svg class="w-full h-auto" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 100"
                    preserveAspectRatio="none">
                    <path fill="#ffffff" fill-opacity="1"
                        d="M0,32L80,42.7C160,53,320,75,480,74.7C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,100L1360,100C1280,100,1120,100,960,100C800,100,640,100,480,100C320,100,160,100,80,100L0,100Z">
                    </path>
                </svg>
            </div>
        </section>

        <!-- Search Section -->
        <section id="search-section" class="py-16 bg-primary-light">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="max-w-3xl mx-auto">
                    <div class="text-center mb-10">
                        <h2 class="text-3xl font-bold mb-4 text-primary-dark">Find Any Pincode In India</h2>
                        <p class="text-secondary">Enter a pincode, locality, district, or state to find detailed information
                        </p>
                    </div>

                    <div class="card p-6 md:p-8">
                        <form id="searchForm" class="relative">
                            <div class="flex flex-col md:flex-row gap-4">
                                <div class="flex-1">
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                            </svg>
                                        </div>
                                        <input type="text" name="query" id="search"
                                            class="form-input block w-full pl-10 pr-3 py-4 border border-primary rounded-lg bg-primary-light focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-theme"
                                            placeholder="Enter pincode or locality name">
                                    </div>
                                </div>
                                <div>
                                    <button type="submit"
                                        class="btn btn-primary w-full md:w-auto py-4 px-8 rounded-lg font-medium flex items-center justify-center transition-theme">
                                        <span>Search</span>
                                        <svg class="ml-2 w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <div class="mt-6 flex items-center justify-center md:justify-start gap-6">
                                <div class="flex items-center">
                                    <input type="radio" id="type-pincode" name="type" value="pincode" checked
                                        class="h-4 w-4 text-primary focus:ring-primary">
                                    <label for="type-pincode" class="ml-2 text-sm text-primary-dark">Pincode</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="radio" id="type-name" name="type" value="name"
                                        class="h-4 w-4 text-primary focus:ring-primary">
                                    <label for="type-name" class="ml-2 text-sm text-primary-dark">Post Office</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="radio" id="type-district" name="type" value="district"
                                        class="h-4 w-4 text-primary focus:ring-primary">
                                    <label for="type-district" class="ml-2 text-sm text-primary-dark">District</label>
                                </div>
                            </div>
                        </form>

                        <div class="mt-8 pt-6 border-t border-primary">
                            <div class="text-center">
                                <h3 class="text-sm font-medium text-secondary uppercase tracking-wider mb-3">Popular
                                    Searches</h3>
                                <div class="flex flex-wrap justify-center gap-2">
                                    <a href="/search?query=delhi&type=name"
                                        class="px-3 py-1 bg-secondary hover:bg-secondary-100 rounded-full text-sm text-primary-dark transition-theme">Delhi</a>
                                    <a href="/search?query=mumbai&type=name"
                                        class="px-3 py-1 bg-secondary hover:bg-secondary-100 rounded-full text-sm text-primary-dark transition-theme">Mumbai</a>
                                    <a href="/search?query=bangalore&type=name"
                                        class="px-3 py-1 bg-secondary hover:bg-secondary-100 rounded-full text-sm text-primary-dark transition-theme">Bangalore</a>
                                    <a href="/search?query=hyderabad&type=name"
                                        class="px-3 py-1 bg-secondary hover:bg-secondary-100 rounded-full text-sm text-primary-dark transition-theme">Hyderabad</a>
                                    <a href="/search?query=chennai&type=name"
                                        class="px-3 py-1 bg-secondary hover:bg-secondary-100 rounded-full text-sm text-primary-dark transition-theme">Chennai</a>
                                    <a href="/search?query=kolkata&type=name"
                                        class="px-3 py-1 bg-secondary hover:bg-secondary-100 rounded-full text-sm text-primary-dark transition-theme">Kolkata</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="py-20 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center max-w-3xl mx-auto mb-16">
                    <span
                        class="inline-block px-3 py-1 rounded-full bg-primary-100 text-primary-800 text-xs font-semibold tracking-wider uppercase mb-3">Features</span>
                    <h2 class="text-3xl md:text-4xl font-extrabold mb-4">Everything you need for postal code information
                    </h2>
                    <p class="text-lg text-gray-600">Our comprehensive pincode directory provides accurate and up-to-date
                        information for all your needs.</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                    <div class="bg-white rounded-xl shadow-lg p-6 card-hover">
                        <div
                            class="w-12 h-12 bg-primary-100 text-primary-600 rounded-xl flex items-center justify-center mb-5">
                            <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-3">Complete Coverage</h3>
                        <p class="text-gray-600 mb-4">Comprehensive database covering all 155,000+ pincodes across India
                            with detailed locality information.</p>
                        <a href="#"
                            class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center">
                            Learn more
                            <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>

                    <div class="bg-white rounded-xl shadow-lg p-6 card-hover">
                        <div
                            class="w-12 h-12 bg-primary-100 text-primary-600 rounded-xl flex items-center justify-center mb-5">
                            <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-3">Verified Data</h3>
                        <p class="text-gray-600 mb-4">All information is verified with official India Post records for
                            maximum accuracy and reliability.</p>
                        <a href="#"
                            class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center">
                            Learn more
                            <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>

                    <div class="bg-white rounded-xl shadow-lg p-6 card-hover">
                        <div
                            class="w-12 h-12 bg-primary-100 text-primary-600 rounded-xl flex items-center justify-center mb-5">
                            <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-3">Regular Updates</h3>
                        <p class="text-gray-600 mb-4">We regularly update our database to ensure you have access to the
                            most current postal information available in India.</p>
                        <a href="#"
                            class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center">
                            Learn more
                            <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>

                    <div class="bg-white rounded-xl shadow-lg p-6 card-hover">
                        <div
                            class="w-12 h-12 bg-primary-100 text-primary-600 rounded-xl flex items-center justify-center mb-5">
                            <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-3">User Friendly</h3>
                        <p class="text-gray-600 mb-4">Our easy-to-use interface makes finding pincode information simple
                            and convenient for everyone.</p>
                        <a href="#"
                            class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center">
                            Learn more
                            <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>

                    <div class="bg-white rounded-xl shadow-lg p-6 card-hover">
                        <div
                            class="w-12 h-12 bg-primary-100 text-primary-600 rounded-xl flex items-center justify-center mb-5">
                            <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-3">Rich Data</h3>
                        <p class="text-gray-600 mb-4">Get detailed information including district, state, post office type,
                            delivery status and more for each pincode.</p>
                        <a href="#"
                            class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center">
                            Learn more
                            <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>

                    <div class="bg-white rounded-xl shadow-lg p-6 card-hover">
                        <div
                            class="w-12 h-12 bg-primary-100 text-primary-600 rounded-xl flex items-center justify-center mb-5">
                            <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-3">API Access</h3>
                        <p class="text-gray-600 mb-4">Integrate our pincode data directly into your applications with our
                            reliable and fast API solutions.</p>
                        <a href="#"
                            class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center">
                            Learn more
                            <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section id="stats" class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center max-w-3xl mx-auto mb-16">
                    <span
                        class="inline-block px-3 py-1 rounded-full bg-primary-100 text-primary-800 text-xs font-semibold tracking-wider uppercase mb-3">Coverage</span>
                    <h2 class="text-3xl md:text-4xl font-extrabold mb-4">Complete Coverage Across India</h2>
                    <p class="text-lg text-gray-600">Our comprehensive database covers postal codes across all states and
                        districts</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100 text-center card-hover">
                        <div
                            class="w-16 h-16 mx-auto bg-primary-100 text-primary-600 rounded-full flex items-center justify-center mb-4">
                            <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <span class="text-4xl font-extrabold text-primary-600">36</span>
                        <p class="text-lg font-medium text-gray-900 mt-2">States & Union Territories</p>
                        <p class="text-gray-600 mt-2">Complete coverage across all states and union territories in India
                        </p>
                    </div>

                    <div
                        class="bg-white rounded-xl shadow-lg p-6 border border-gray-100 text-center card-hover transform translate-y-4">
                        <div
                            class="w-16 h-16 mx-auto bg-primary-100 text-primary-600 rounded-full flex items-center justify-center mb-4">
                            <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <span class="text-4xl font-extrabold text-primary-600">700+</span>
                        <p class="text-lg font-medium text-gray-900 mt-2">Districts Mapped</p>
                        <p class="text-gray-600 mt-2">Detailed mapping of all districts with their corresponding pincodes
                        </p>
                    </div>

                    <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100 text-center card-hover">
                        <div
                            class="w-16 h-16 mx-auto bg-primary-100 text-primary-600 rounded-full flex items-center justify-center mb-4">
                            <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <span class="text-4xl font-extrabold text-primary-600">155,000+</span>
                        <p class="text-lg font-medium text-gray-900 mt-2">Delivery Offices</p>
                        <p class="text-gray-600 mt-2">Information on post offices and their delivery status across India
                        </p>
                    </div>
                </div>

                <!-- India Map Visualization -->
                <div class="mt-20">
                    <div class="bg-white rounded-xl overflow-hidden shadow-lg">
                        <div class="p-6 bg-gradient-to-r from-primary-600 to-primary-800 text-white">
                            <h3 class="text-xl font-bold">Pincode Distribution Across India</h3>
                            <p class="text-primary-100">Visual representation of postal code density by state</p>
                        </div>
                        <div class="p-6">
                            <div class="aspect-w-16 aspect-h-9">
                                <img src="{{ uploads_url('images/india-map.webp') }}" alt="India Pincode Distribution Map"
                                    class="rounded-lg">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Tools Section -->
        <section id="tools" class="py-20 bg-gradient-to-b from-gray-50 to-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center max-w-3xl mx-auto mb-16">
                    <span
                        class="inline-block px-3 py-1 rounded-full bg-primary-100 text-primary-800 text-xs font-semibold tracking-wider uppercase mb-3">Tools</span>
                    <h2 class="text-3xl md:text-4xl font-extrabold mb-4">Quick Pincode Tools</h2>
                    <p class="text-lg text-gray-600">Access our most popular pincode search and verification tools</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- Tool 1: Browse by State -->
                    <div
                        class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 card-hover">
                        <div class="h-3 bg-blue-500"></div>
                        <div class="p-6">
                            <div
                                class="flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 text-blue-600 mb-5">
                                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-2">Browse by State</h3>
                            <p class="text-gray-600 mb-6">Explore pincodes organized by states and districts across India.
                                Navigate through our hierarchical directory.</p>
                            <a href="/pincodes" class="inline-flex items-center text-blue-600 hover:text-blue-800">
                                View States
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- Tool 2: All Pincodes List -->
                    <div
                        class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 card-hover">
                        <div class="h-3 bg-green-500"></div>
                        <div class="p-6">
                            <div
                                class="flex items-center justify-center h-12 w-12 rounded-full bg-green-100 text-green-600 mb-5">
                                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-2">Complete Pincode List</h3>
                            <p class="text-gray-600 mb-6">Access the comprehensive list of all postal codes in India.
                                Search, filter, and find any pincode instantly.</p>
                            <a href="/india-postal-code-list"
                                class="inline-flex items-center text-green-600 hover:text-green-800">
                                View All Pincodes
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- Tool 3: Download Pincodes -->
                    <div
                        class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 card-hover">
                        <div class="h-3 bg-purple-500"></div>
                        <div class="p-6">
                            <div
                                class="flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 text-purple-600 mb-5">
                                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-2">Download Pincodes</h3>
                            <p class="text-gray-600 mb-6">Download pincode data for your business or personal use.
                                Available in multiple formats for easy integration.</p>
                            <a href="/tools/district-wise-pincode-download"
                                class="inline-flex items-center text-purple-600 hover:text-purple-800">
                                Download Options
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Latest Blog Posts -->
        <section class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center max-w-3xl mx-auto mb-16">
                    <span
                        class="inline-block px-3 py-1 rounded-full bg-primary-100 text-primary-800 text-xs font-semibold tracking-wider uppercase mb-3">Blog</span>
                    <h2 class="text-3xl md:text-4xl font-extrabold mb-4">Latest from our blog</h2>
                    <p class="text-lg text-gray-600">Learn more about postal codes, address verification, and logistics in
                        India</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Blog Post 1 -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden card-hover">
                        <div class="relative">
                            <img src="/api/placeholder/600/400" alt="Blog Post Image" class="w-full h-48 object-cover">
                            <div
                                class="absolute top-4 left-4 bg-primary-600 text-white text-xs font-bold px-2 py-1 rounded">
                                Logistics</div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">Understanding India's PIN Code System</h3>
                            <p class="text-gray-600 mb-4">Learn about the history and structure of India's postal index
                                number system and how it helps in efficient mail delivery.</p>
                            <div class="flex items-center text-sm text-gray-500 mb-4">
                                <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <span>Apr 10, 2025</span>
                                <span class="mx-2">•</span>
                                <span>5 min read</span>
                            </div>
                            <div class="flex items-center">
                                <img src="/api/placeholder/40/40" alt="Author Avatar" class="w-8 h-8 rounded-full mr-3">
                                <span class="text-sm font-medium text-gray-900">Rahul Sharma</span>
                            </div>
                        </div>
                    </div>

                    <!-- Blog Post 2 -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden card-hover">
                        <div class="relative">
                            <img src="/api/placeholder/600/400" alt="Blog Post Image" class="w-full h-48 object-cover">
                            <div class="absolute top-4 left-4 bg-green-600 text-white text-xs font-bold px-2 py-1 rounded">
                                Guides</div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">How to Verify Addresses Using Pincodes</h3>
                            <p class="text-gray-600 mb-4">A comprehensive guide on using postal codes for address
                                verification in e-commerce and logistics businesses.</p>
                            <div class="flex items-center text-sm text-gray-500 mb-4">
                                <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <span>Apr 5, 2025</span>
                                <span class="mx-2">•</span>
                                <span>7 min read</span>
                            </div>
                            <div class="flex items-center">
                                <img src="/api/placeholder/40/40" alt="Author Avatar" class="w-8 h-8 rounded-full mr-3">
                                <span class="text-sm font-medium text-gray-900">Priya Patel</span>
                            </div>
                        </div>
                    </div>

                    <!-- Blog Post 3 -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden card-hover">
                        <div class="relative">
                            <img src="/api/placeholder/600/400" alt="Blog Post Image" class="w-full h-48 object-cover">
                            <div class="absolute top-4 left-4 bg-blue-600 text-white text-xs font-bold px-2 py-1 rounded">
                                Updates</div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">Recent Changes in Postal Codes Across India
                            </h3>
                            <p class="text-gray-600 mb-4">Stay updated with the latest changes in India's postal code
                                system and how it affects mail delivery and logistics.</p>
                            <div class="flex items-center text-sm text-gray-500 mb-4">
                                <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <span>Mar 28, 2025</span>
                                <span class="mx-2">•</span>
                                <span>4 min read</span>
                            </div>
                            <div class="flex items-center">
                                <img src="/api/placeholder/40/40" alt="Author Avatar" class="w-8 h-8 rounded-full mr-3">
                                <span class="text-sm font-medium text-gray-900">Vikram Singh</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-12 text-center">
                    <a href="/blog"
                        class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 transition-colors duration-300 shadow-md hover:shadow-lg">
                        View all articles
                        <svg class="ml-2 w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                    </a>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-20 bg-gradient-to-r from-primary-600 to-primary-800 text-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="relative rounded-2xl bg-white/10 backdrop-blur-lg p-8 md:p-12 overflow-hidden shadow-2xl">
                    <!-- Background design elements -->
                    <div class="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
                        <div class="absolute -top-24 -left-24 w-64 h-64 rounded-full bg-white/5"></div>
                        <div class="absolute -bottom-32 -right-32 w-96 h-96 rounded-full bg-white/5"></div>
                    </div>

                    <div class="relative z-10 md:flex md:items-center md:justify-between">
                        <div class="md:max-w-lg mb-8 md:mb-0">
                            <h2 class="text-3xl md:text-4xl font-extrabold mb-4">Ready to get started?</h2>
                            <p class="text-xl text-white/80 mb-6">Get full access to our pincode directory with features to
                                help you find and verify addresses across India.</p>
                            <ul class="space-y-3 mb-8">
                                <li class="flex items-center">
                                    <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M5 13l4 4L19 7" />
                                    </svg>
                                    <span>Access to all 155,000+ pincodes</span>
                                </li>
                                <li class="flex items-center">
                                    <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M5 13l4 4L19 7" />
                                    </svg>
                                    <span>Bulk pincode verification</span>
                                </li>
                                <li class="flex items-center">
                                    <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M5 13l4 4L19 7" />
                                    </svg>
                                    <span>Real-time pincode availability check</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection
@push('styles')
    <style>
        .particles-container {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            z-index: 0;
            overflow: hidden;
        }

        .gradient-text {
            background: linear-gradient(45deg, #4f46e5, #6366f1, #818cf8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .pulse-border {
            animation: pulse-border 2s infinite;
        }

        @keyframes pulse-border {
            0% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
            }

            70% {
                box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
            }

            100% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
            }
        }

        .blur-overlay {
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
        }

        .search-input:focus {
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
        }

        .grid-mask {
            mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
            -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
        }
    </style>
@endpush


@push('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/alpinejs/3.10.3/cdn.min.js" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.3/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.3/ScrollTrigger.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/countup.js/2.0.8/countUp.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/particlesjs/2.2.3/particles.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchForm = document.getElementById('searchForm');
            const searchInput = document.getElementById('search');
            const typePincode = document.getElementById('type-pincode');
            const typeName = document.getElementById('type-name');
            const typeDistrict = document.getElementById('type-district');

            searchForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                let selectedType;
                if (typePincode.checked) {
                    selectedType = 'pincode';
                } else if (typeName.checked) {
                    selectedType = 'name';
                } else if (typeDistrict.checked) {
                    selectedType = 'district';
                } else {
                    selectedType = 'pincode'; // default
                }
                
                const query = searchInput.value.trim();
                if (!query) return;

                let searchUrl = `/search?query=${encodeURIComponent(query)}&type=${selectedType}`;
                
                // Add district parameter if type is district
                if (selectedType === 'district') {
                    searchUrl += `&district=${encodeURIComponent(query)}`;
                }

                window.location.href = searchUrl;
            });

            // Update placeholder text based on selected type
            [typePincode, typeName, typeDistrict].forEach(radio => {
                radio.addEventListener('change', function() {
                    const placeholder = this.value === 'pincode' ? 'Enter pincode' :
                                     this.value === 'name' ? 'Enter post office name' :
                                     'Enter district name';
                    searchInput.placeholder = placeholder;
                });
            });
        });
    </script>
@endpush
