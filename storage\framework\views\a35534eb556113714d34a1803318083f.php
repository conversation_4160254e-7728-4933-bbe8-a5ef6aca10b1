<?php $__env->startSection('title', 'Manage Pincodes'); ?>
<?php $__env->startSection('page-title', 'Pincodes Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="bg-white dark:bg-bg-dark shadow-md rounded-lg overflow-hidden border border-border-light dark:border-border-dark">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center px-4 sm:px-6 py-4 border-b border-border-light dark:border-border-dark space-y-3 sm:space-y-0">
        <h2 class="text-lg sm:text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">All Pincodes</h2>
        <a href="<?php echo e(route('admin.pincodes.create')); ?>" class="w-full sm:w-auto bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-white px-4 py-2 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-bg-dark transition-colors">
            <div class="flex items-center justify-center sm:justify-start">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add New Pincode
            </div>
        </a>
    </div>

    <div class="p-4 sm:p-6">
        <div class="flex flex-col lg:flex-row justify-between mb-4 space-y-4 lg:space-y-0 lg:space-x-4">
            <div class="w-full lg:w-1/3">
                <div class="relative">
                    <input type="text" id="searchInput" placeholder="Search pincodes..." class="w-full px-4 py-2 border border-border-light dark:border-border-dark rounded-md text-text-primary-light dark:text-text-primary-dark bg-white dark:bg-bg-dark focus:outline-none focus:border-primary-light dark:focus:border-primary-dark">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <svg class="w-5 h-5 text-text-secondary-light dark:text-text-secondary-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="flex space-x-2">
                <select id="stateFilter" class="w-full border border-border-light dark:border-border-dark rounded-md text-text-primary-light dark:text-text-primary-dark bg-white dark:bg-bg-dark px-4 py-2 focus:outline-none focus:border-primary-light dark:focus:border-primary-dark">
                    <option value="">All States</option>
                    <?php $__currentLoopData = $states; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $state): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($state->name); ?>"><?php echo e($state->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
        </div>

        <!-- Mobile Card View (hidden on desktop) -->
        <div class="block lg:hidden space-y-4">
            <?php $__empty_1 = true; $__currentLoopData = $pincodes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pincode): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-border-light dark:border-border-dark">
                <div class="flex justify-between items-start mb-3">
                    <div class="font-medium text-text-primary-light dark:text-text-primary-dark text-lg">
                        <?php echo e($pincode->pincode); ?>

                    </div>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e($pincode->delivery_status === 'Delivery' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400'); ?>">
                        <?php echo e(ucfirst($pincode->delivery_status ?? 'N/A')); ?>

                    </span>
                </div>
                <div class="space-y-2 text-sm">
                    <div>
                        <span class="text-text-secondary-light dark:text-text-secondary-dark font-medium">Area:</span>
                        <span class="text-text-primary-light dark:text-text-primary-dark ml-2"><?php echo e($pincode->name); ?></span>
                    </div>
                    <div>
                        <span class="text-text-secondary-light dark:text-text-secondary-dark font-medium">District:</span>
                        <span class="text-text-primary-light dark:text-text-primary-dark ml-2"><?php echo e($pincode->district); ?></span>
                    </div>
                    <div>
                        <span class="text-text-secondary-light dark:text-text-secondary-dark font-medium">State:</span>
                        <span class="text-text-primary-light dark:text-text-primary-dark ml-2"><?php echo e($pincode->state); ?></span>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-4 pt-3 border-t border-border-light dark:border-border-dark">
                    <a href="<?php echo e(route('admin.pincodes.edit', $pincode->id)); ?>" class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light p-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                    </a>
                    <form method="POST" action="<?php echo e(route('admin.pincodes.destroy', $pincode->id)); ?>" class="inline-block" onsubmit="return confirm('Are you sure you want to delete this pincode?');">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </form>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="text-center py-8 text-text-secondary-light dark:text-text-secondary-dark">
                No pincodes found.
            </div>
            <?php endif; ?>
        </div>

        <!-- Desktop Table View (hidden on mobile) -->
        <div class="hidden lg:block overflow-x-auto">
            <table class="min-w-full divide-y divide-border-light dark:divide-border-dark">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                            Pincode
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                            Area Name
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                            District
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                            State
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                            Delivery Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                    <?php $__empty_1 = true; $__currentLoopData = $pincodes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pincode): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-text-primary-light dark:text-text-primary-dark">
                            <?php echo e($pincode->pincode); ?>

                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                            <?php echo e($pincode->name); ?>

                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                            <?php echo e($pincode->district); ?>

                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                            <?php echo e($pincode->state); ?>

                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e($pincode->delivery_status === 'Delivery' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400'); ?>">
                                <?php echo e(ucfirst($pincode->delivery_status ?? 'N/A')); ?>

                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex justify-end space-x-2">
                                <a href="<?php echo e(route('admin.pincodes.edit', $pincode->id)); ?>" class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </a>
                                <form method="POST" action="<?php echo e(route('admin.pincodes.destroy', $pincode->id)); ?>" class="inline-block" onsubmit="return confirm('Are you sure you want to delete this pincode?');">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="6" class="px-6 py-4 whitespace-nowrap text-center text-text-secondary-light dark:text-text-secondary-dark">
                            No pincodes found.
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <div class="mt-4">
            <?php echo e($pincodes->links()); ?>

        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const stateFilter = document.getElementById('stateFilter');
        
        // Add event listeners for search and filters
        searchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                applyFilters();
            }
        });
        
        stateFilter.addEventListener('change', applyFilters);
        
        function applyFilters() {
            const searchTerm = searchInput.value;
            const state = stateFilter.value;
            
            let url = new URL(window.location.href);
            
            if (searchTerm) {
                url.searchParams.set('search', searchTerm);
            } else {
                url.searchParams.delete('search');
            }
            
            if (state) {
                url.searchParams.set('state', state);
            } else {
                url.searchParams.delete('state');
            }
            
            window.location.href = url.toString();
        }
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/admin/pincodes/index.blade.php ENDPATH**/ ?>