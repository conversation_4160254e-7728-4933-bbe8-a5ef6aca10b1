<?php

namespace App\Services\Payment;

use App\Models\Order;
use Illuminate\Http\Request;

interface PaymentGatewayServiceInterface
{
    /**
     * Create a payment for the given order.
     */
    public function createPayment(Order $order): PaymentResponse;

    /**
     * Verify a payment by its ID.
     */
    public function verifyPayment(string $paymentId): PaymentResponse;

    /**
     * Handle webhook notifications from the payment gateway.
     */
    public function handleWebhook(Request $request): WebhookResponse;

    /**
     * Refund a payment.
     */
    public function refundPayment(string $paymentId, float $amount): RefundResponse;

    /**
     * Get the current status of a payment.
     */
    public function getPaymentStatus(string $paymentId): PaymentStatus;

    /**
     * Get the gateway configuration.
     */
    public function getGatewayConfig(): array;

    /**
     * Test the gateway connection and credentials.
     */
    public function testConnection(): bool;

    /**
     * Get supported currencies for this gateway.
     */
    public function getSupportedCurrencies(): array;

    /**
     * Calculate gateway fee for the given amount.
     */
    public function calculateFee(float $amount, string $currency): float;
}