@extends('admin.layouts.admin')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark">Review Contact Number
                    Change Request</h1>
                <a href="{{ route('admin.contact-number-changes.index') }}"
                    class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light transition-colors">
                    ← Back to List
                </a>
            </div>

            <div
                class="bg-white dark:bg-bg-dark rounded-lg shadow-lg dark:shadow-xl overflow-hidden border border-border-light dark:border-border-dark">
                <div class="p-6">
                    <!-- Post Office Details -->
                    <div class="mb-6">
                        <h2 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark mb-4">Post Office
                            Details</h2>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Name</p>
                                <p class="text-base font-medium text-text-primary-light dark:text-text-primary-dark">
                                    {{ $change->pincode->name }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Pincode</p>
                                <p class="text-base font-medium text-text-primary-light dark:text-text-primary-dark">
                                    {{ $change->pincode->pincode }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">District</p>
                                <p class="text-base font-medium text-text-primary-light dark:text-text-primary-dark">
                                    {{ $change->pincode->district }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">State</p>
                                <p class="text-base font-medium text-text-primary-light dark:text-text-primary-dark">
                                    {{ $change->pincode->state }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Change Request Details -->
                    <div class="mb-6">
                        <h2 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark mb-4">Change
                            Request Details</h2>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Current Number
                                </p>
                                <p class="text-base font-medium text-text-primary-light dark:text-text-primary-dark">
                                    {{ $change->old_number ?? 'N/A' }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Requested Number
                                </p>
                                <p class="text-base font-medium text-text-primary-light dark:text-text-primary-dark">
                                    {{ $change->new_number }}</p>
                            </div>
                            <div class="col-span-2">
                                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Reason for Change
                                </p>
                                <p class="text-base text-text-primary-light dark:text-text-primary-dark">
                                    {{ $change->reason }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Request Information -->
                    <div class="mb-6">
                        <h2 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark mb-4">Request
                            Information</h2>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Requested By</p>
                                <p class="text-base font-medium text-text-primary-light dark:text-text-primary-dark">
                                    {{ $change->user ? $change->user->name : 'Anonymous' }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Request Date</p>
                                <p class="text-base font-medium text-text-primary-light dark:text-text-primary-dark">
                                    {{ $change->created_at->format('M d, Y H:i') }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Status</p>
                                <span
                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                @if ($change->status === 'pending') bg-accent-light/20 dark:bg-accent-dark/20 text-accent-light dark:text-accent-dark border border-accent-light/30 dark:border-accent-dark/30
                                @elseif($change->status === 'approved') bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 border border-green-200 dark:border-green-700
                                @else bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400 border border-red-200 dark:border-red-700 @endif">
                                    {{ ucfirst($change->status) }}
                                </span>
                            </div>
                            @if ($change->reviewed_at)
                                <div>
                                    <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Reviewed At
                                    </p>
                                    <p class="text-base font-medium text-text-primary-light dark:text-text-primary-dark">
                                        {{ $change->reviewed_at->format('M d, Y H:i') }}</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Related Changes -->
                    @if ($relatedChanges->isNotEmpty())
                        <div class="mb-6">
                            <h2 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark mb-4">Related
                                Changes</h2>
                            <div
                                class="bg-bg-light dark:bg-slate-800/50 rounded-lg p-4 border border-border-light dark:border-border-dark">
                                <div class="space-y-4">
                                    @foreach ($relatedChanges as $relatedChange)
                                        <div
                                            class="border-b border-border-light dark:border-border-dark pb-4 last:border-0 last:pb-0">
                                            <div class="flex justify-between items-start">
                                                <div>
                                                    <p
                                                        class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark">
                                                        {{ $relatedChange->created_at->format('M d, Y H:i') }}
                                                    </p>
                                                    <p
                                                        class="text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                                        Changed from {{ $relatedChange->old_number ?? 'N/A' }} to
                                                        {{ $relatedChange->new_number }}
                                                    </p>
                                                </div>
                                                <span
                                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        @if ($relatedChange->status === 'pending') bg-accent-light/20 dark:bg-accent-dark/20 text-accent-light dark:text-accent-dark border border-accent-light/30 dark:border-accent-dark/30
                                        @elseif($relatedChange->status === 'approved') bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 border border-green-200 dark:border-green-700
                                        @else bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400 border border-red-200 dark:border-red-700 @endif">
                                                    {{ ucfirst($relatedChange->status) }}
                                                </span>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Review Form -->
                    @if ($change->status === 'pending')
                        <div class="mt-8">
                            <h2 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark mb-4">Review
                                Request</h2>
                            <form id="reviewForm" class="space-y-4">
                                @csrf
                                <div>
                                    <label for="status"
                                        class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Decision</label>
                                    <select id="status" name="status"
                                        class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-1">
                                        <option value="approved">Approve</option>
                                        <option value="rejected">Reject</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="admin_notes"
                                        class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Admin
                                        Notes</label>
                                    <textarea id="admin_notes" name="admin_notes" rows="4"
                                        class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-1 placeholder-text-secondary-light dark:placeholder-text-secondary-dark"
                                        placeholder="Enter your notes about this decision..." required></textarea>
                                </div>
                                <div class="flex justify-end">
                                    <button type="submit"
                                        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-white dark:focus:ring-offset-bg-dark transition-colors">
                                        Submit Review
                                    </button>
                                </div>
                            </form>
                        </div>
                    @else
                        <div class="mt-8">
                            <h2 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark mb-4">Review
                                Information</h2>
                            <div
                                class="bg-bg-light dark:bg-slate-800/50 rounded-lg p-4 border border-border-light dark:border-border-dark">
                                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Admin Notes</p>
                                <p class="text-base text-text-primary-light dark:text-text-primary-dark">
                                    {{ $change->admin_notes }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @if ($change->status === 'pending')
        @push('scripts')
            <script>
                document.getElementById('reviewForm').addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const submitButton = this.querySelector('button[type="submit"]');
                    const originalButtonText = submitButton.innerHTML;

                    // Disable submit button and show loading state
                    submitButton.disabled = true;
                    submitButton.innerHTML = `
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Processing...
    `;

                    fetch('{{ route('admin.contact-number-changes.update', $change) }}', {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                                    'content'),
                                'Accept': 'application/json'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Show success message
                                const successAlert = document.createElement('div');
                                successAlert.className =
                                    'fixed top-4 right-4 bg-green-100 dark:bg-green-900/30 border border-green-400 dark:border-green-700 text-green-700 dark:text-green-400 px-4 py-3 rounded shadow-lg z-50';
                                successAlert.innerHTML = data.message;
                                document.body.appendChild(successAlert);

                                // Redirect after a short delay
                                setTimeout(() => {
                                    window.location.href = '{{ route('admin.contact-number-changes.index') }}';
                                }, 1500);
                            } else {
                                throw new Error(data.message || 'An error occurred while submitting the review.');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);

                            // Show error message
                            const errorAlert = document.createElement('div');
                            errorAlert.className =
                                'fixed top-4 right-4 bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-400 px-4 py-3 rounded shadow-lg z-50';
                            errorAlert.innerHTML = error.message;
                            document.body.appendChild(errorAlert);

                            // Remove error message after 5 seconds
                            setTimeout(() => {
                                errorAlert.remove();
                            }, 5000);

                            // Reset button state
                            submitButton.disabled = false;
                            submitButton.innerHTML = originalButtonText;
                        });
                });
            </script>
        @endpush
    @endif
@endsection
