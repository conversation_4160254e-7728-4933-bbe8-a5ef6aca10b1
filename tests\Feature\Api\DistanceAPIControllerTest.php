<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

// Returns correct distance for valid coordinates
test('returns correct distance for valid coordinates', function () {
    $user = User::factory()->create();
    // Delhi (28.6139, 77.2090) to Mumbai (19.0760, 72.8777)
    $response = $this->actingAs($user)->getJson('/api/calculate-distance-between-two-coordinates/28.6139/77.2090/19.0760/72.8777');
    $response->assertStatus(200)
        ->assertJsonStructure([
            'distance' => [
                'km',
                'miles',
            ]
        ]);
    $data = $response->json('distance');
    expect($data['km'])->toBeGreaterThan(1000);
    expect($data['miles'])->toBeGreaterThan(600);
});

// Returns 0 for identical coordinates
test('returns 0 for identical coordinates', function () {
    $user = User::factory()->create();
    $response = $this->actingAs($user)->getJson('/api/calculate-distance-between-two-coordinates/10/20/10/20');
    $response->assertStatus(200)
        ->assertJson([
            'distance' => [
                'km' => 0.0,
                'miles' => 0.0,
            ]
        ]);
});

// Returns validation error for out-of-range latitude/longitude
test('returns validation error for out-of-range latitude', function () {
    $user = User::factory()->create();
    $response = $this->actingAs($user)->getJson('/api/calculate-distance-between-two-coordinates/100/20/10/20');
    $response->assertStatus(400)
        ->assertJsonStructure(['error']);
});

test('returns validation error for out-of-range longitude', function () {
    $user = User::factory()->create();
    $response = $this->actingAs($user)->getJson('/api/calculate-distance-between-two-coordinates/10/200/10/20');
    $response->assertStatus(400)
        ->assertJsonStructure(['error']);
});

// Returns validation error for non-numeric input
test('returns validation error for non-numeric input', function () {
    $user = User::factory()->create();
    $response = $this->actingAs($user)->getJson('/api/calculate-distance-between-two-coordinates/foo/bar/10/20');
    $response->assertStatus(400)
        ->assertJsonStructure(['error']);
}); 