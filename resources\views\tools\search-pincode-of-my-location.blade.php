@extends('layouts.app')

@include('layouts.partials.tools-json-ld')

@section('content')
    <x-breadcrumb :segments="$breadcrumbs" :pageTitle="$pageTitle" />
    <div class="container max-w-6xl mx-auto px-4 py-8">
        <div class="flex flex-wrap -mx-4">
            <div class="w-full lg:w-2/3 px-4">
                <h2 class="text-3xl font-bold mb-6 text-text-primary-light dark:text-text-primary-dark">Pincode of Current
                    Location</h2>
                <p class="text-text-secondary-light dark:text-text-secondary-dark mb-6">
                    Finding the pincode of your current location is essential for many reasons, like booking a
                    delivery or filling out an address form. You may have tried searching for the "pincode of my
                    current location" on Google, but it might not have provided the correct address or pincode.
                </p>

                <div id="nearestPostOfficeInfo" class="mb-8">
                    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-lg overflow-hidden">
                        <div
                            class="bg-gradient-to-r from-primary-light to-indigo-600 dark:from-primary-dark dark:to-indigo-500 px-6 py-4">
                            <h2 class="text-xl text-white font-semibold flex items-center">
                                <svg class="h-6 w-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                Nearest Post Office Information
                            </h2>
                        </div>
                        <div id="postOfficeContent" class="p-6">
                            <div class="flex justify-center items-center h-32">
                                <button id="getLocationBtn"
                                    class="inline-flex items-center px-6 py-3 bg-primary-light dark:bg-primary-dark hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-medium rounded-lg transition duration-150 ease-in-out">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20"
                                        fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    Find Nearest Post Office
                                </button>
                            </div>
                        </div>
                    </div>
                </div>



                <div id="map-container" class="h-96 bg-bg-light dark:bg-gray-800 rounded-lg mb-6 hidden"></div>

                <div class="bg-white dark:bg-bg-dark rounded-lg shadow-md mb-6">
                    <div
                        class="border-b border-border-light dark:border-border-dark bg-bg-light dark:bg-gray-800 px-6 py-4">
                        <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">How to Find
                            the Pincode of Your Current Location</h2>
                    </div>
                    <div class="p-6">
                        <ol
                            class="list-decimal list-inside space-y-2 text-text-secondary-light dark:text-text-secondary-dark">
                            <li>Download our Android App - <a
                                    href="https://play.google.com/store/apps/details?id=com.nskmultiservices.currentlocationpincode"
                                    class="text-primary-light dark:text-primary-dark hover:text-blue-800 dark:hover:text-blue-400 underline"
                                    target="_blank">
                                    Pincode of Current Location App
                                </a></li>
                            <li>Open the app</li>
                            <li>Allow location permission (this is important)</li>
                            <li>Click on the "Show Pincode of My Current Location" button</li>
                            <li>Wait for the result</li>
                            <li>In just a few seconds, you'll see the pincode of your current location</li>
                            <li>You can also view the distance of your location from the pincode</li>
                            <li>The app will display the name of the post office, district, state, and distance for the
                                pincode</li>
                            <li>You can read more details about the pincode and the post office</li>
                        </ol>
                    </div>
                </div>
                <div class="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500 dark:border-blue-400 p-4 mb-6">
                    <h4 class="text-xl font-semibold text-blue-700 dark:text-blue-300 mb-2">Why you might not find the
                        correct pincode:</h4>
                    <p class="text-blue-600 dark:text-blue-200">
                        Sometimes, relying on your device's location services, especially when using an IP address,
                        doesn't give an accurate pincode. For the most precise result, we recommend using Mobile Browser or
                        try our
                        Android app, which can easily helps you to find pincode (Postal Code) of your area.
                    </p>
                </div>

                <div class="bg-white dark:bg-bg-dark rounded-lg shadow-md mb-6">
                    <div
                        class="border-b border-border-light dark:border-border-dark bg-bg-light dark:bg-gray-800 px-6 py-4">
                        <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">FAQs on
                            Finding the Pincode of Your Current Location</h2>
                    </div>
                    <div class="p-6 space-y-6">
                        <div>
                            <h3 class="text-lg font-semibold mb-2 text-text-primary-light dark:text-text-primary-dark">1.
                                What is the pincode of my current location?</h3>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark">The pincode is a postal code
                                that identifies your current area. You can
                                find it using your
                                phone's GPS or check it using an online service based on your IP address.</p>
                        </div>

                        <!-- Repeat pattern for other FAQs -->
                        <div>
                            <h3 class="text-lg font-semibold mb-2 text-text-primary-light dark:text-text-primary-dark">2.
                                How do I find the current pincode of my location?</h3>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark">You can find your current
                                location's pincode by using location-based
                                services on your phone or by
                                visiting a website that fetches your location via GPS or IP address.</p>
                        </div>
                    </div>
                </div>

                <div class="flex space-x-4 mb-6">
                    <button
                        class="inline-flex items-center px-4 py-2 border border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark font-medium rounded-md hover:bg-bg-light dark:hover:bg-gray-800 transition duration-150 ease-in-out">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20"
                            fill="currentColor">
                            <path
                                d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
                        </svg>
                        Share
                    </button>
                    <a href="https://form.jotform.com/241740272022444" target="_blank"
                        class="inline-flex items-center px-4 py-2 border border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark font-medium rounded-md hover:bg-bg-light dark:hover:bg-gray-800 transition duration-150 ease-in-out">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20"
                            fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                                clip-rule="evenodd" />
                        </svg>
                        Feedback
                    </a>
                </div>
            </div>

            <div class="w-full lg:w-1/3 px-4">
                <div class="sticky top-20">
                    @include('pincodes.partials.sidebar')
                </div>
            </div>
        </div>

        <!-- Reviews Section -->
        <div class="mt-8">
            <x-tool-reviews
                :reviews="$reviews"
                :tool="$tool"
                :hasMoreReviews="$hasMoreReviews ?? false"
                :totalReviewsCount="$totalReviewsCount ?? 0"
            />
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Global coordinates handler
        const LocationService = {
            coordinates: null,

            init() {
                console.debug('[LocationService] Initializing...');
                this.loadSavedCoordinates();
                this.setupEventListeners();
                console.debug('[LocationService] Initialization complete');
            },

            loadSavedCoordinates() {
                console.debug('[LocationService] Loading saved coordinates from localStorage');
                try {
                    const saved = localStorage.getItem('visitorCoordinates');
                    console.debug('[LocationService] Saved coordinates found:', saved);
                    if (saved) {
                        this.coordinates = JSON.parse(saved);
                        console.debug('[LocationService] Parsed coordinates:', this.coordinates);
                    } else {
                        console.debug('[LocationService] No saved coordinates found');
                    }
                } catch (e) {
                    console.error('[LocationService] Error loading saved coordinates:', e);
                }
            },

            getVisitorCoordinates() {
                console.debug('[LocationService] Getting visitor coordinates...');
                return new Promise((resolve, reject) => {
                    if (this.coordinates) {
                        console.debug('[LocationService] Using cached coordinates:', this.coordinates);
                        resolve(this.coordinates);
                        return;
                    }

                    if (!navigator.geolocation) {
                        console.error('[LocationService] Geolocation not supported by browser');
                        reject(new Error("Geolocation is not supported by your browser"));
                        return;
                    }

                    console.debug('[LocationService] Requesting geolocation from browser...');
                    navigator.geolocation.getCurrentPosition(
                        (position) => {
                            console.debug('[LocationService] Successfully received position:', position);
                            const coordinates = {
                                latitude: position.coords.latitude,
                                longitude: position.coords.longitude,
                                accuracy: position.coords.accuracy
                            };

                            console.debug('[LocationService] Extracted coordinates:', coordinates);
                            this.coordinates = coordinates;
                            localStorage.setItem('visitorCoordinates', JSON.stringify(coordinates));
                            console.debug('[LocationService] Saved coordinates to localStorage');
                            this.sendCoordinatesToServer(coordinates);
                            resolve(coordinates);
                        },
                        (error) => {
                            console.error('[LocationService] Geolocation error:', error);
                            const errorMessages = {
                                1: "User denied the request for geolocation",
                                2: "Location information is unavailable",
                                3: "The request to get user location timed out"
                            };
                            const errorMessage = errorMessages[error.code] || "An unknown error occurred";
                            console.error('[LocationService] Geolocation error message:', errorMessage);
                            reject(new Error(errorMessage));
                        }, {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 0
                    }
                    );
                    console.debug('[LocationService] Geolocation request sent with options:', {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 0
                    });
                });
            },

            sendCoordinatesToServer(coordinates) {
                console.debug('[LocationService] Sending coordinates to server:', coordinates);
                try {
                    const csrfToken = document.querySelector('meta[name="csrf-token"]');
                    if (!csrfToken) {
                        console.error('[LocationService] CSRF token not found in document');
                        return;
                    }

                    console.debug('[LocationService] CSRF token found:', csrfToken.content);

                    fetch('/api/save-coordinates', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken.content
                        },
                        body: JSON.stringify(coordinates)
                    })
                        .then(response => {
                            console.debug('[LocationService] Server response status:', response.status);
                            return response.json();
                        })
                        .then(data => {
                            console.debug('[LocationService] Coordinates saved successfully:', data);
                        })
                        .catch(error => {
                            console.error('[LocationService] Error saving coordinates to server:', error);
                        });
                } catch (error) {
                    console.error('[LocationService] Exception while sending coordinates to server:', error);
                }
            },

            setupEventListeners() {
                console.debug('[LocationService] Setting up event listeners');
                this.getVisitorCoordinates()
                    .then(coordinates => {
                        console.debug('[LocationService] Coordinates available, dispatching event:', coordinates);
                        document.dispatchEvent(new CustomEvent('coordinatesAvailable', {
                            detail: coordinates
                        }));
                    })
                    .catch(error => {
                        console.error('[LocationService] Error during setup:', error.message);
                    });
            }
        };

        document.addEventListener('DOMContentLoaded', function () {
            console.debug('[Main] DOM content loaded, initializing UI components');

            const postOfficeContent = document.getElementById('postOfficeContent');
            const mapContainer = document.getElementById('map-container');
            const getLocationBtn = document.getElementById('getLocationBtn');

            if (!postOfficeContent) console.error('[Main] Element #postOfficeContent not found');
            if (!mapContainer) console.error('[Main] Element #map-container not found');
            if (!getLocationBtn) console.error('[Main] Element #getLocationBtn not found');

            // Initialize LocationService
            console.debug('[Main] Initializing LocationService');
            LocationService.init();

            getLocationBtn.addEventListener('click', async function () {
                console.debug('[Main] Location button clicked');
                try {
                    // Show loading state
                    console.debug('[Main] Showing loading state');
                    postOfficeContent.innerHTML = '';
                    const loadingDiv = document.createElement('div');
                    loadingDiv.className = 'flex flex-col items-center justify-center h-32';
                    loadingDiv.innerHTML = `
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-light dark:border-primary-dark"></div>
                        <p class="mt-4 text-text-secondary-light dark:text-text-secondary-dark">Finding nearest post office...</p>
                    `;
                    postOfficeContent.appendChild(loadingDiv);

                    // Get coordinates using LocationService
                    // console.debug('[Main] Requesting visitor coordinates');
                    const coordinates = await LocationService.getVisitorCoordinates();
                    console.debug('[Main] Coordinates received:', coordinates);

                    // Show map
                    console.debug('[Main] Setting up map with coordinates');
                    mapContainer.classList.remove('hidden');
                    const iframe = document.createElement('iframe');
                    iframe.width = '100%';
                    iframe.height = '100%';
                    iframe.frameBorder = '0';
                    iframe.style.border = '0';
                    const mapUrl =
                        `https://maps.google.com/maps?q=${coordinates.latitude},${coordinates.longitude}&hl=es;z=14&output=embed`;
                    console.debug('[Main] Map URL:', mapUrl);
                    iframe.src = mapUrl;
                    iframe.allowFullscreen = true;
                    mapContainer.innerHTML = '';
                    mapContainer.appendChild(iframe);

                    // Fetch and display post office data
                    const apiUrl =
                        `/nearest-post-offices/${coordinates.latitude}/${coordinates.longitude}`;
                    console.debug('[Main] Fetching post office data from:', apiUrl);
                    const response = await fetch(apiUrl);
                    console.debug('[Main] API response status:', response.status);

                    if (!response.ok) {
                        console.error('[Main] Failed API response:', response);
                        throw new Error('Failed to fetch post office data');
                    }

                    const data = await response.json();
                    console.debug('[Main] Post office data received:', data);

                    // Create content container
                    console.debug('[Main] Creating UI for post office data');
                    const container = document.createElement('div');
                    container.className = 'space-y-4';

                    // Create grid container for pincode, district, state info
                    const grid = document.createElement('div');
                    grid.className = 'grid grid-cols-1 md:grid-cols-3 gap-4';

                    // Use property names that match the controller response
                    const infoBoxes = [
                        {
                            title: 'Pincode',
                            value: data.postalCode
                        },
                        {
                            title: 'District',
                            value: data.city
                        },
                        {
                            title: 'State',
                            value: data.state
                        }
                    ];

                    console.debug('[Main] Creating info boxes with data:', infoBoxes);

                    infoBoxes.forEach(box => {
                        const div = document.createElement('div');
                        div.className = 'bg-bg-light dark:bg-gray-800 p-4 rounded-lg';
                        div.innerHTML = `
                            <div class="flex items-center space-x-2 mb-2">
                                <span class="font-medium text-text-secondary-light dark:text-text-secondary-dark">${box.title}</span>
                            </div>
                            <p class="text-text-primary-light dark:text-text-primary-dark">${box.value || ''}</p>
                        `;
                        grid.appendChild(div);
                    });

                    container.appendChild(grid);

                    // Add heading for post offices
                    const postOfficesHeading = document.createElement('h3');
                    postOfficesHeading.className = 'text-xl font-semibold mt-6 mb-3 text-text-primary-light dark:text-text-primary-dark';
                    postOfficesHeading.textContent = 'Post Offices with Pincode ' + data.postalCode;
                    container.appendChild(postOfficesHeading);

                    // Create post offices list
                    if (data.postOffices && data.postOffices.length > 0) {
                        const postOfficesList = document.createElement('div');
                        postOfficesList.className = 'space-y-3';

                        data.postOffices.forEach((office, index) => {
                            const officeCard = document.createElement('div');
                            officeCard.className = 'bg-white dark:bg-bg-dark border border-border-light dark:border-border-dark rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-200';

                            officeCard.innerHTML = `
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <h4 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">${office.name}</h4>
                                            <div class="mt-2 flex items-center text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                                </svg>
                                                Distance: ${Math.round(office.distance * 100) / 100} km
                                            </div>
                                        </div>
                                        <a href="https://maps.google.com/?q=${office.latitude},${office.longitude}" target="_blank" class="text-primary-light dark:text-primary-dark hover:text-blue-800 dark:hover:text-blue-400 text-sm flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                            </svg>
                                            View on Map
                                        </a>
                                    </div>
                                `;

                            postOfficesList.appendChild(officeCard);
                        });

                        container.appendChild(postOfficesList);
                    } else {
                        const noPostOffices = document.createElement('div');
                        noPostOffices.className = 'bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-400 dark:border-yellow-500 p-4';
                        noPostOffices.innerHTML = `
                                <div class="flex">
                                    <div class="ml-3">
                                        <p class="text-sm text-yellow-700 dark:text-yellow-300">No post offices found with this pincode.</p>
                                    </div>
                                </div>
                            `;
                        container.appendChild(noPostOffices);
                    }

                    // Add nearest distance info
                    console.debug('[Main] Adding distance info:', data.nearestDistance);
                    const distanceDiv = document.createElement('div');
                    distanceDiv.className = 'bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mt-4';
                    distanceDiv.innerHTML = `
                        <div class="flex items-center space-x-2">
                            <span class="font-medium text-blue-700 dark:text-blue-300">Nearest post office is:</span>
                            <span class="text-blue-900 dark:text-blue-200">${Math.round(data.nearestDistance * 100) / 100} km away</span>
                        </div>
                    `;
                    container.appendChild(distanceDiv);

                    // Update content
                    console.debug('[Main] Updating content with post office information');
                    postOfficeContent.innerHTML = '';
                    postOfficeContent.appendChild(container);
                } catch (error) {
                    console.error('[Main] Error in location handler:', error);
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 dark:border-red-400 p-4';
                    errorDiv.innerHTML = `
                        <div class="flex">
                            <div class="ml-3">
                                <p class="text-sm text-red-700 dark:text-red-300">${error.message || 'Error finding nearest post office. Please try again.'}</p>
                            </div>
                        </div>
                    `;
                    postOfficeContent.innerHTML = '';
                    postOfficeContent.appendChild(errorDiv);
                }
            });

            console.debug('[Main] Setup complete');
        });
    </script>
@endpush