@if(isset($landingPage['pricing']) && $landingPage['pricing']['active'])
<section class="py-20 bg-white dark:bg-bg-dark">
    <div class="max-w-6xl mx-auto px-4">
        <div class="text-center mb-16" data-aos="fade-up">
            <span
                class="inline-block px-4 py-1 rounded-full bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark text-xs font-semibold tracking-wider uppercase mb-3">
                {{ $landingPage['pricing']['content']['badge_text'] ?? 'Pricing' }}
            </span>
            <h2 class="text-3xl md:text-4xl font-extrabold mb-4 text-primary-light dark:text-primary-dark">
                {{ $landingPage['pricing']['content']['heading'] ?? 'Choose Your Plan' }}
            </h2>
            <p class="text-lg text-text-secondary-light dark:text-text-secondary-dark">
                {{ $landingPage['pricing']['content']['subheading'] ?? 'Flexible plans for individuals, businesses, and enterprises' }}
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
            @forelse($landingPage['pricing']['content']['plans'] ?? [] as $index => $plan)
                <div class="{{ $plan['popular'] ? 'bg-white dark:bg-bg-dark rounded-xl shadow-2xl p-8 flex flex-col items-center border-4 border-primary-light dark:border-primary-dark relative' : 'bg-primary-50 dark:bg-bg-dark rounded-xl shadow-lg p-8 flex flex-col items-center border border-border-light dark:border-border-dark' }}"
                    data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                    
                    @if($plan['popular'])
                        <div class="absolute -top-5 left-1/2 -translate-x-1/2 bg-primary-light dark:bg-primary-dark text-white text-xs font-bold px-4 py-1 rounded-full shadow">
                            {{ $landingPage['pricing']['content']['popular_badge_text'] ?? 'Most Popular' }}
                        </div>
                    @endif
                    
                    <h3 class="font-bold text-xl mb-2 text-primary-light dark:text-primary-dark">
                        {{ $plan['name'] }}
                    </h3>
                    
                    <div class="text-4xl font-extrabold text-primary-light dark:text-primary-dark mb-4">
                        @if($plan['price'] === null)
                            {{ $landingPage['pricing']['content']['custom_price_text'] ?? 'Custom' }}
                        @elseif($plan['price'] == 0)
                            {{ $plan['currency'] }}0
                        @else
                            {{ $plan['currency'] }}{{ number_format($plan['price'], 0) }}
                            <span class="text-base font-normal text-text-secondary-light dark:text-text-secondary-dark">
                                /{{ $plan['billing_period'] }}
                            </span>
                        @endif
                    </div>
                    
                    <p class="text-text-secondary-light dark:text-text-secondary-dark mb-6 text-center">
                        {{ $plan['description'] }}
                    </p>
                    
                    <ul class="mb-8 space-y-3 text-text-primary-light dark:text-text-primary-dark flex-grow">
                        @foreach($plan['features'] as $feature)
                            <li class="flex items-start">
                                <i class="fa-solid fa-check text-green-500 mr-3 mt-1 flex-shrink-0"></i>
                                <span>{{ $feature }}</span>
                            </li>
                        @endforeach
                    </ul>
                    
                    <a href="{{ $plan['cta_link'] }}" 
                        class="w-full text-center px-6 py-3 {{ $plan['popular'] ? 'bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light' : ($plan['color'] === 'accent' ? 'bg-accent-light dark:bg-accent-dark hover:bg-accent-dark dark:hover:bg-accent-light' : 'bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light') }} text-white rounded-lg font-semibold shadow transition-colors duration-300">
                        {{ $plan['cta_text'] }}
                    </a>
                </div>
            @empty
                <div class="col-span-3 text-center text-text-secondary-light dark:text-text-secondary-dark">
                    {{ $landingPage['pricing']['content']['no_plans_text'] ?? 'No plans available at the moment. Please check back later.' }}
                </div>
            @endforelse
        </div>
        
        @if(isset($landingPage['pricing']['content']['free_trial_text']))
            <div class="text-center mt-12">
                <p class="text-text-secondary-light dark:text-text-secondary-dark mb-2">
                    {{ $landingPage['pricing']['content']['free_trial_text'] }}
                </p>
                @if(isset($landingPage['pricing']['content']['money_back_guarantee']))
                    <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">
                        {{ $landingPage['pricing']['content']['money_back_guarantee'] }}
                    </p>
                @endif
            </div>
        @endif
    </div>
</section>
@endif