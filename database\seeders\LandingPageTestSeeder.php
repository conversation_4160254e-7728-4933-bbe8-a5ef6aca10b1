<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\LandingPageSection;
use App\Models\LandingPageContent;

class LandingPageTestSeeder extends Seeder
{
    /**
     * Run the database seeds for testing landing page dynamic content.
     * This seeder updates all landing page content with identifiable test text
     * to verify that all content is properly dynamic and manageable via admin panel.
     */
    public function run()
    {
        $this->command->info('🧪 Starting Landing Page Test Content Update...');

        // Update Hero Section
        $this->updateHeroSection();
        
        // Update Features Section
        $this->updateFeaturesSection();
        
        // Update Stats Section
        $this->updateStatsSection();
        
        // Update Search Section
        $this->updateSearchSection();
        
        // Update Tools Section
        $this->updateToolsSection();
        
        // Update Pricing Section
        $this->updatePricingSection();
        
        // Update Testimonials Section
        $this->updateTestimonialsSection();
        
        // Update CTA Section
        $this->updateCtaSection();
        
        // Update FAQ Section
        $this->updateFaqSection();
        
        // Update Blog Section
        $this->updateBlogSection();

        $this->command->info('✅ Landing Page Test Content Update Completed!');
        $this->command->info('🔍 All content now has [X-TEST] prefixes for easy identification.');
        $this->command->info('📝 Check your landing page to verify all content is dynamic.');
    }

    private function updateHeroSection()
    {
        $heroSection = LandingPageSection::where('slug', 'hero')->first();
        if (!$heroSection) return;

        $this->command->info('📝 Updating Hero Section...');

        $updates = [
            'badge_text' => '[X-TEST] India\'s #1 Postal Directory',
            'heading' => '<span class="block mb-2 text-text-primary-light dark:text-text-primary-dark animate-slide-in-left">[X-TEST] Discover</span><span class="enhanced-gradient-text typing-animation block animate-slide-in-right">[X-TEST] Every Pincode</span><span class="block text-text-primary-light dark:text-text-primary-dark animate-slide-in-left">[X-TEST] in India</span>',
            'subheading' => '[X-TEST] Instantly search, verify, and explore 155,000+ Indian pincodes with rich, verified data and interactive tools.',
            'cta_text' => '[X-TEST] Start Searching',
            'cta_link' => '#search-section',
            'second_cta_text' => '[X-TEST] See Features',
            'second_cta_link' => '#features',
            'floating_txt_1' => '[X-TEST] 167K+ Post Offices',
            'floating_txt_2' => '[X-TEST] 28 States & 8 UTs',
            'verified_by_1' => '[X-TEST] Verified by',
            'verified_by_2' => '[X-TEST] India Post',
        ];

        foreach ($updates as $key => $value) {
            LandingPageContent::where('section_id', $heroSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }
    }

    private function updateFeaturesSection()
    {
        $featuresSection = LandingPageSection::where('slug', 'features')->first();
        if (!$featuresSection) return;

        $this->command->info('📝 Updating Features Section...');

        $updates = [
            'badge_text' => '[X-TEST] Features',
            'heading' => '[X-TEST] Everything you need for postal code information',
            'subheading' => '[X-TEST] Our comprehensive pincode directory provides accurate and up-to-date information for all your needs.',
            'learn_more_text' => '[X-TEST] Learn more',
            'features' => json_encode([
                [
                    'title' => '[X-TEST] Complete Coverage',
                    'description' => '[X-TEST] Comprehensive database covering all 155,000+ pincodes across India with detailed locality information.',
                    'icon' => '<svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 919-9" /></svg>'
                ],
                [
                    'title' => '[X-TEST] Verified Data',
                    'description' => '[X-TEST] All information is verified with official India Post records for maximum accuracy and reliability.',
                    'icon' => '<svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" /></svg>'
                ],
                [
                    'title' => '[X-TEST] Regular Updates',
                    'description' => '[X-TEST] We regularly update our database to ensure you have access to the most current postal information available in India.',
                    'icon' => '<svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" /></svg>'
                ]
            ])
        ];

        foreach ($updates as $key => $value) {
            LandingPageContent::where('section_id', $featuresSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }
    }

    private function updateStatsSection()
    {
        $statsSection = LandingPageSection::where('slug', 'stats')->first();
        if (!$statsSection) return;

        $this->command->info('📝 Updating Stats Section...');

        $updates = [
            'badge_text' => '[X-TEST] Coverage',
            'heading' => '[X-TEST] Complete Coverage Across India',
            'subheading' => '[X-TEST] Our comprehensive database covers postal codes across all states and districts',
            'states_count' => '[X-TEST] 36',
            'states_label' => '[X-TEST] States & Union Territories',
            'states_description' => '[X-TEST] Complete coverage across all states and union territories in India',
            'districts_count' => '[X-TEST] 700+',
            'districts_label' => '[X-TEST] Districts Mapped',
            'districts_description' => '[X-TEST] Detailed mapping of all districts with their corresponding pincodes',
            'delivery_offices_count' => '[X-TEST] 155,000+',
            'delivery_offices_label' => '[X-TEST] Delivery Offices',
            'delivery_offices_description' => '[X-TEST] Information on post offices and their delivery status across India',
        ];

        foreach ($updates as $key => $value) {
            LandingPageContent::where('section_id', $statsSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }
    }

    private function updateSearchSection()
    {
        $searchSection = LandingPageSection::where('slug', 'search')->first();
        if (!$searchSection) return;

        $this->command->info('📝 Updating Search Section...');

        $updates = [
            'badge_text' => '[X-TEST] Search Tool',
            'heading' => '[X-TEST] Find Any Pincode In India',
            'subheading' => '[X-TEST] Enter a pincode, locality, district, or state to find detailed information',
            'search_placeholder' => '[X-TEST] Enter pincode or locality name',
            'search_button_text' => '[X-TEST] Search',
            'popular_searches_heading' => '[X-TEST] Popular Searches',
            'popular_searches' => json_encode([
                ['name' => '[X-TEST] Delhi', 'link' => '/search?query=delhi&type=name'],
                ['name' => '[X-TEST] Mumbai', 'link' => '/search?query=mumbai&type=name'],
                ['name' => '[X-TEST] Bengaluru', 'link' => '/search?query=bengaluru&type=name'],
            ])
        ];

        foreach ($updates as $key => $value) {
            LandingPageContent::where('section_id', $searchSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }
    }

    private function updateToolsSection()
    {
        $toolsSection = LandingPageSection::where('slug', 'tools')->first();
        if (!$toolsSection) return;

        $this->command->info('📝 Updating Tools Section...');

        $updates = [
            'badge_text' => '[X-TEST] Tools',
            'heading' => '[X-TEST] Quick Pincode Tools',
            'subheading' => '[X-TEST] Access our most popular pincode search and verification tools',
            'tools' => json_encode([
                [
                    'title' => '[X-TEST] Browse by State',
                    'description' => '[X-TEST] Explore pincodes organized by states and districts across India.',
                    'link' => '/pincodes',
                    'link_text' => '[X-TEST] View States',
                    'color' => 'primary',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />'
                ],
                [
                    'title' => '[X-TEST] Pincode Finder',
                    'description' => '[X-TEST] Find pincodes by entering locality, district, or state name.',
                    'link' => '/search',
                    'link_text' => '[X-TEST] Start Search',
                    'color' => 'accent',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />'
                ]
            ])
        ];

        foreach ($updates as $key => $value) {
            LandingPageContent::where('section_id', $toolsSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }
    }

    private function updatePricingSection()
    {
        $pricingSection = LandingPageSection::where('slug', 'pricing')->first();
        if (!$pricingSection) return;

        $this->command->info('📝 Updating Pricing Section...');

        $updates = [
            'badge_text' => '[X-TEST] Pricing',
            'heading' => '[X-TEST] Choose Your Plan',
            'subheading' => '[X-TEST] Flexible plans for individuals, businesses, and enterprises',
            'popular_badge_text' => '[X-TEST] Most Popular',
            'custom_price_text' => '[X-TEST] Custom',
            'no_plans_text' => '[X-TEST] No plans available at the moment. Please check back later.',
            'free_trial_text' => '[X-TEST] Start with a 14-day free trial. No credit card required.',
            'money_back_guarantee' => '[X-TEST] 30-day money-back guarantee on all paid plans',
            'plans' => json_encode([
                [
                    'name' => '[X-TEST] Free Plan',
                    'price' => 0,
                    'currency' => '₹',
                    'billing_period' => 'forever',
                    'description' => '[X-TEST] Perfect for personal use and small projects',
                    'features' => [
                        '[X-TEST] 100 API calls per month',
                        '[X-TEST] Basic pincode search',
                        '[X-TEST] Standard support'
                    ],
                    'cta_text' => '[X-TEST] Get Started',
                    'cta_link' => '/register',
                    'popular' => false,
                    'color' => 'gray'
                ],
                [
                    'name' => '[X-TEST] Professional Plan',
                    'price' => 999,
                    'currency' => '₹',
                    'billing_period' => 'month',
                    'description' => '[X-TEST] Ideal for growing businesses and developers',
                    'features' => [
                        '[X-TEST] 10,000 API calls per month',
                        '[X-TEST] Priority support',
                        '[X-TEST] Bulk data export'
                    ],
                    'cta_text' => '[X-TEST] Choose Plan',
                    'cta_link' => '/register',
                    'popular' => true,
                    'color' => 'primary'
                ]
            ])
        ];

        foreach ($updates as $key => $value) {
            LandingPageContent::where('section_id', $pricingSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }
    }

    private function updateTestimonialsSection()
    {
        $testimonialsSection = LandingPageSection::where('slug', 'testimonials')->first();
        if (!$testimonialsSection) return;

        $this->command->info('📝 Updating Testimonials Section...');

        $updates = [
            'heading' => '[X-TEST] What our users say',
            'subheading' => '[X-TEST] Trusted by thousands of users across India for accurate pincode information',
            'cta_text' => '[X-TEST] Join thousands of satisfied customers who trust our pincode service',
            'cta_button_text' => '[X-TEST] Get Started Today',
            'cta_button_link' => '#contact',
            'testimonials' => json_encode([
                [
                    'name' => '[X-TEST] Rajesh Kumar',
                    'designation' => '[X-TEST] E-commerce Business Owner',
                    'location' => '[X-TEST] Mumbai',
                    'content' => '[X-TEST] This pincode service has been a game-changer for our online delivery business. The accuracy and completeness of data helped us streamline our logistics operations significantly.',
                    'rating' => 5,
                    'avatar' => '/images/testimonials/rajesh.jpg'
                ],
                [
                    'name' => '[X-TEST] Priya Sharma',
                    'designation' => '[X-TEST] Logistics Manager',
                    'location' => '[X-TEST] Delhi',
                    'content' => '[X-TEST] Excellent service with comprehensive pincode database. The API integration was smooth and the support team is very responsive.',
                    'rating' => 5,
                    'avatar' => '/images/testimonials/priya.jpg'
                ]
            ])
        ];

        foreach ($updates as $key => $value) {
            LandingPageContent::where('section_id', $testimonialsSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }
    }

    private function updateCtaSection()
    {
        $ctaSection = LandingPageSection::where('slug', 'cta')->first();
        if (!$ctaSection) return;

        $this->command->info('📝 Updating CTA Section...');

        $updates = [
            'heading' => '[X-TEST] Ready to get started?',
            'subheading' => '[X-TEST] Get full access to our pincode directory with features to help you find and verify addresses across India.',
            'cta_text' => '[X-TEST] Get Started Now',
            'cta_link' => '#register',
            'secondary_cta_text' => '[X-TEST] Learn More',
            'secondary_cta_link' => '#features',
            'features' => json_encode([
                '[X-TEST] Access to all 155,000+ pincodes',
                '[X-TEST] Bulk pincode verification',
                '[X-TEST] Real-time pincode availability check'
            ])
        ];

        foreach ($updates as $key => $value) {
            LandingPageContent::where('section_id', $ctaSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }
    }

    private function updateFaqSection()
    {
        $faqSection = LandingPageSection::where('slug', 'faq')->first();
        if (!$faqSection) return;

        $this->command->info('📝 Updating FAQ Section...');

        $updates = [
            'badge_text' => '[X-TEST] FAQ',
            'heading' => '[X-TEST] Frequently Asked Questions',
            'faqs' => json_encode([
                [
                    'question' => '[X-TEST] How accurate is your pincode data?',
                    'answer' => '[X-TEST] All data is verified with India Post and updated regularly for maximum accuracy.'
                ],
                [
                    'question' => '[X-TEST] Can I download pincode data for my business?',
                    'answer' => '[X-TEST] Yes, you can download data in multiple formats or use our API for integration.'
                ],
                [
                    'question' => '[X-TEST] Is there a free plan available?',
                    'answer' => '[X-TEST] Yes, we offer a free plan with basic features. Upgrade for advanced access and API.'
                ]
            ])
        ];

        foreach ($updates as $key => $value) {
            LandingPageContent::where('section_id', $faqSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }
    }

    private function updateBlogSection()
    {
        $blogSection = LandingPageSection::where('slug', 'latest-blog-posts')->first();
        if (!$blogSection) return;

        $this->command->info('📝 Updating Blog Section...');

        $updates = [
            'badge_text' => '[X-TEST] Blog',
            'heading' => '[X-TEST] Latest from Our Blog',
            'subheading' => '[X-TEST] Stay updated with the latest news, tips, and insights about postal services in India',
            'view_all_text' => '[X-TEST] View all articles',
            'view_all_link' => '/blog',
            'show_count' => '3'
        ];

        foreach ($updates as $key => $value) {
            LandingPageContent::where('section_id', $blogSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }
    }
}
