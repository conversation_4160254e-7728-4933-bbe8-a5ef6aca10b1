<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\LandingPageSection;
use App\Models\LandingPageContent;

class LandingPageTestSeeder extends Seeder
{
    /**
     * Run the database seeds for testing landing page dynamic content.
     * This seeder updates all landing page content with identifiable test text
     * to verify that all content is properly dynamic and manageable via admin panel.
     */
    public function run()
    {
        $this->command->info('🧪 Starting Landing Page Test Content Update...');

        // Update Hero Section
        $this->updateHeroSection();
        
        // Update Features Section
        $this->updateFeaturesSection();
        
        // Update Stats Section
        $this->updateStatsSection();
        
        // Update Search Section
        $this->updateSearchSection();
        
        // Update Tools Section
        $this->updateToolsSection();
        
        // Update Pricing Section
        $this->updatePricingSection();
        
        // Update Testimonials Section
        $this->updateTestimonialsSection();
        
        // Update CTA Section
        $this->updateCtaSection();
        
        // Update FAQ Section
        $this->updateFaqSection();
        
        // Update Blog Section
        $this->updateBlogSection();

        $this->command->info('✅ Landing Page Test Content Update Completed!');
        $this->command->info('🔍 All content now has [X-TEST] prefixes for easy identification.');
        $this->command->info('📝 Check your landing page to verify all content is dynamic.');
    }

    private function updateHeroSection()
    {
        $heroSection = LandingPageSection::where('slug', 'hero')->first();
        if (!$heroSection) return;

        $this->command->info('📝 Updating Hero Section...');

        $updates = [
            'badge_text' => '[X-TEST] India\'s #1 Postal Directory',
            'heading' => '<span class="block mb-2 text-text-primary-light dark:text-text-primary-dark animate-slide-in-left">[X-TEST] Discover</span><span class="enhanced-gradient-text typing-animation block animate-slide-in-right">[X-TEST] Every Pincode</span><span class="block text-text-primary-light dark:text-text-primary-dark animate-slide-in-left">[X-TEST] in India</span>',
            'subheading' => '[X-TEST] Instantly search, verify, and explore 155,000+ Indian pincodes with rich, verified data and interactive tools.',
            'cta_text' => '[X-TEST] Start Searching',
            'cta_link' => '#search-section',
            'second_cta_text' => '[X-TEST] See Features',
            'second_cta_link' => '#features',
            'floating_txt_1' => '[X-TEST] 167K+ Post Offices',
            'floating_txt_2' => '[X-TEST] 28 States & 8 UTs',
            'verified_by_1' => '[X-TEST] Verified by',
            'verified_by_2' => '[X-TEST] India Post',
        ];

        foreach ($updates as $key => $value) {
            LandingPageContent::where('section_id', $heroSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }
    }

    private function updateFeaturesSection()
    {
        $featuresSection = LandingPageSection::where('slug', 'features')->first();
        if (!$featuresSection) return;

        $this->command->info('📝 Updating Features Section...');

        // Update simple text fields
        $textUpdates = [
            'badge_text' => '[X-TEST] Features',
            'heading' => '[X-TEST] Everything you need for postal code information',
            'subheading' => '[X-TEST] Our comprehensive pincode directory provides accurate and up-to-date information for all your needs.',
            'learn_more_text' => '[X-TEST] Learn more',
        ];

        foreach ($textUpdates as $key => $value) {
            LandingPageContent::where('section_id', $featuresSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }

        // Update features array while preserving ALL existing features
        $featuresContent = LandingPageContent::where('section_id', $featuresSection->id)
            ->where('key', 'features')
            ->first();

        if ($featuresContent) {
            // Handle both JSON string and array formats
            $features = is_string($featuresContent->value)
                ? json_decode($featuresContent->value, true) ?? []
                : (is_array($featuresContent->value) ? $featuresContent->value : []);

            // Add [X-TEST] prefix to all existing features
            foreach ($features as &$feature) {
                if (isset($feature['title']) && !str_starts_with($feature['title'], '[X-TEST]')) {
                    $feature['title'] = '[X-TEST] ' . $feature['title'];
                }
                if (isset($feature['description']) && !str_starts_with($feature['description'], '[X-TEST]')) {
                    $feature['description'] = '[X-TEST] ' . $feature['description'];
                }
            }

            $featuresContent->update(['value' => json_encode($features)]);
        }
    }

    private function updateStatsSection()
    {
        $statsSection = LandingPageSection::where('slug', 'stats')->first();
        if (!$statsSection) return;

        $this->command->info('📝 Updating Stats Section...');

        $updates = [
            'badge_text' => '[X-TEST] Coverage',
            'heading' => '[X-TEST] Complete Coverage Across India',
            'subheading' => '[X-TEST] Our comprehensive database covers postal codes across all states and districts',
            'states_count' => '[X-TEST] 36',
            'states_label' => '[X-TEST] States & Union Territories',
            'states_description' => '[X-TEST] Complete coverage across all states and union territories in India',
            'districts_count' => '[X-TEST] 700+',
            'districts_label' => '[X-TEST] Districts Mapped',
            'districts_description' => '[X-TEST] Detailed mapping of all districts with their corresponding pincodes',
            'delivery_offices_count' => '[X-TEST] 155,000+',
            'delivery_offices_label' => '[X-TEST] Delivery Offices',
            'delivery_offices_description' => '[X-TEST] Information on post offices and their delivery status across India',
        ];

        foreach ($updates as $key => $value) {
            LandingPageContent::where('section_id', $statsSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }
    }

    private function updateSearchSection()
    {
        $searchSection = LandingPageSection::where('slug', 'search')->first();
        if (!$searchSection) return;

        $this->command->info('📝 Updating Search Section...');

        $updates = [
            'badge_text' => '[X-TEST] Search Tool',
            'heading' => '[X-TEST] Find Any Pincode In India',
            'subheading' => '[X-TEST] Enter a pincode, locality, district, or state to find detailed information',
            'search_placeholder' => '[X-TEST] Enter pincode or locality name',
            'search_button_text' => '[X-TEST] Search',
            'popular_searches_heading' => '[X-TEST] Popular Searches',
            'popular_searches' => json_encode([
                ['name' => '[X-TEST] Delhi', 'link' => '/search?query=delhi&type=name'],
                ['name' => '[X-TEST] Mumbai', 'link' => '/search?query=mumbai&type=name'],
                ['name' => '[X-TEST] Bengaluru', 'link' => '/search?query=bengaluru&type=name'],
            ])
        ];

        foreach ($updates as $key => $value) {
            LandingPageContent::where('section_id', $searchSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }
    }

    private function updateToolsSection()
    {
        $toolsSection = LandingPageSection::where('slug', 'tools')->first();
        if (!$toolsSection) return;

        $this->command->info('📝 Updating Tools Section...');

        // Update simple text fields
        $textUpdates = [
            'badge_text' => '[X-TEST] Tools',
            'heading' => '[X-TEST] Quick Pincode Tools',
            'subheading' => '[X-TEST] Access our most popular pincode search and verification tools',
        ];

        foreach ($textUpdates as $key => $value) {
            LandingPageContent::where('section_id', $toolsSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }

        // Update tools array while preserving ALL existing tools
        $toolsContent = LandingPageContent::where('section_id', $toolsSection->id)
            ->where('key', 'tools')
            ->first();

        if ($toolsContent) {
            // Handle both JSON string and array formats
            $tools = is_string($toolsContent->value)
                ? json_decode($toolsContent->value, true) ?? []
                : (is_array($toolsContent->value) ? $toolsContent->value : []);

            // Add [X-TEST] prefix to all existing tools
            foreach ($tools as &$tool) {
                if (isset($tool['title']) && !str_starts_with($tool['title'], '[X-TEST]')) {
                    $tool['title'] = '[X-TEST] ' . $tool['title'];
                }
                if (isset($tool['description']) && !str_starts_with($tool['description'], '[X-TEST]')) {
                    $tool['description'] = '[X-TEST] ' . $tool['description'];
                }
                if (isset($tool['link_text']) && !str_starts_with($tool['link_text'], '[X-TEST]')) {
                    $tool['link_text'] = '[X-TEST] ' . $tool['link_text'];
                }
            }

            $toolsContent->update(['value' => json_encode($tools)]);
        }
    }

    private function updatePricingSection()
    {
        $pricingSection = LandingPageSection::where('slug', 'pricing')->first();
        if (!$pricingSection) return;

        $this->command->info('📝 Updating Pricing Section...');

        // Update simple text fields
        $textUpdates = [
            'badge_text' => '[X-TEST] Pricing',
            'heading' => '[X-TEST] Choose Your Plan',
            'subheading' => '[X-TEST] Flexible plans for individuals, businesses, and enterprises',
            'popular_badge_text' => '[X-TEST] Most Popular',
            'custom_price_text' => '[X-TEST] Custom',
            'no_plans_text' => '[X-TEST] No plans available at the moment. Please check back later.',
            'free_trial_text' => '[X-TEST] Start with a 14-day free trial. No credit card required.',
            'money_back_guarantee' => '[X-TEST] 30-day money-back guarantee on all paid plans',
        ];

        foreach ($textUpdates as $key => $value) {
            LandingPageContent::where('section_id', $pricingSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }

        // NOTE: Plans data is now managed through the Plan model
        // No need to update plans content as it's fetched from the Plan model directly
    }

    private function updateTestimonialsSection()
    {
        $testimonialsSection = LandingPageSection::where('slug', 'testimonials')->first();
        if (!$testimonialsSection) return;

        $this->command->info('📝 Updating Testimonials Section...');

        // Update simple text fields
        $textUpdates = [
            'heading' => '[X-TEST] What our users say',
            'subheading' => '[X-TEST] Trusted by thousands of users across India for accurate pincode information',
            'cta_text' => '[X-TEST] Join thousands of satisfied customers who trust our pincode service',
            'cta_button_text' => '[X-TEST] Get Started Today',
            'cta_button_link' => '#contact',
        ];

        foreach ($textUpdates as $key => $value) {
            LandingPageContent::where('section_id', $testimonialsSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }

        // Update testimonials array while preserving ALL existing testimonials
        $testimonialsContent = LandingPageContent::where('section_id', $testimonialsSection->id)
            ->where('key', 'testimonials')
            ->first();

        if ($testimonialsContent) {
            // Handle both JSON string and array formats
            $testimonials = is_string($testimonialsContent->value)
                ? json_decode($testimonialsContent->value, true) ?? []
                : (is_array($testimonialsContent->value) ? $testimonialsContent->value : []);

            // Add [X-TEST] prefix to all existing testimonials
            foreach ($testimonials as &$testimonial) {
                if (isset($testimonial['name']) && !str_starts_with($testimonial['name'], '[X-TEST]')) {
                    $testimonial['name'] = '[X-TEST] ' . $testimonial['name'];
                }
                if (isset($testimonial['designation']) && !str_starts_with($testimonial['designation'], '[X-TEST]')) {
                    $testimonial['designation'] = '[X-TEST] ' . $testimonial['designation'];
                }
                if (isset($testimonial['location']) && !str_starts_with($testimonial['location'], '[X-TEST]')) {
                    $testimonial['location'] = '[X-TEST] ' . $testimonial['location'];
                }
                if (isset($testimonial['content']) && !str_starts_with($testimonial['content'], '[X-TEST]')) {
                    $testimonial['content'] = '[X-TEST] ' . $testimonial['content'];
                }
            }

            $testimonialsContent->update(['value' => json_encode($testimonials)]);
        }
    }

    private function updateCtaSection()
    {
        $ctaSection = LandingPageSection::where('slug', 'cta')->first();
        if (!$ctaSection) return;

        $this->command->info('📝 Updating CTA Section...');

        // Update simple text fields
        $textUpdates = [
            'heading' => '[X-TEST] Ready to get started?',
            'subheading' => '[X-TEST] Get full access to our pincode directory with features to help you find and verify addresses across India.',
            'cta_text' => '[X-TEST] Get Started Now',
            'cta_link' => '#register',
            'secondary_cta_text' => '[X-TEST] Learn More',
            'secondary_cta_link' => '#features',
        ];

        foreach ($textUpdates as $key => $value) {
            LandingPageContent::where('section_id', $ctaSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }

        // Update features array while preserving ALL existing features
        $featuresContent = LandingPageContent::where('section_id', $ctaSection->id)
            ->where('key', 'features')
            ->first();

        if ($featuresContent) {
            // Handle both JSON string and array formats
            $features = is_string($featuresContent->value)
                ? json_decode($featuresContent->value, true) ?? []
                : (is_array($featuresContent->value) ? $featuresContent->value : []);

            // Add [X-TEST] prefix to all existing features
            foreach ($features as &$feature) {
                if (!str_starts_with($feature, '[X-TEST]')) {
                    $feature = '[X-TEST] ' . $feature;
                }
            }

            $featuresContent->update(['value' => json_encode($features)]);
        }
    }

    private function updateFaqSection()
    {
        $faqSection = LandingPageSection::where('slug', 'faq')->first();
        if (!$faqSection) return;

        $this->command->info('📝 Updating FAQ Section...');

        // Update simple text fields
        $textUpdates = [
            'badge_text' => '[X-TEST] FAQ',
            'heading' => '[X-TEST] Frequently Asked Questions',
        ];

        foreach ($textUpdates as $key => $value) {
            LandingPageContent::where('section_id', $faqSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }

        // Update FAQs array while preserving ALL existing FAQs
        $faqsContent = LandingPageContent::where('section_id', $faqSection->id)
            ->where('key', 'faqs')
            ->first();

        if ($faqsContent) {
            // Handle both JSON string and array formats
            $faqs = is_string($faqsContent->value)
                ? json_decode($faqsContent->value, true) ?? []
                : (is_array($faqsContent->value) ? $faqsContent->value : []);

            // Add [X-TEST] prefix to all existing FAQs
            foreach ($faqs as &$faq) {
                if (isset($faq['question']) && !str_starts_with($faq['question'], '[X-TEST]')) {
                    $faq['question'] = '[X-TEST] ' . $faq['question'];
                }
                if (isset($faq['answer']) && !str_starts_with($faq['answer'], '[X-TEST]')) {
                    $faq['answer'] = '[X-TEST] ' . $faq['answer'];
                }
            }

            $faqsContent->update(['value' => json_encode($faqs)]);
        }
    }

    private function updateBlogSection()
    {
        $blogSection = LandingPageSection::where('slug', 'latest-blog-posts')->first();
        if (!$blogSection) return;

        $this->command->info('📝 Updating Blog Section...');

        $updates = [
            'badge_text' => '[X-TEST] Blog',
            'heading' => '[X-TEST] Latest from Our Blog',
            'subheading' => '[X-TEST] Stay updated with the latest news, tips, and insights about postal services in India',
            'view_all_text' => '[X-TEST] View all articles',
            'view_all_link' => '/blog',
            'show_count' => '3'
        ];

        foreach ($updates as $key => $value) {
            LandingPageContent::where('section_id', $blogSection->id)
                ->where('key', $key)
                ->update(['value' => $value]);
        }
    }
}
