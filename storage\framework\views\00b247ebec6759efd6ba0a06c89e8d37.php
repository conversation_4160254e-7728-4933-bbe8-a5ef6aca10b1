<?php
    use App\Models\Setting;
    use Illuminate\Support\Facades\Crypt;
    $settings = Setting::all()->groupBy('group');
?>

<?php $__env->startSection('title', 'Website Settings'); ?>

<?php $__env->startSection('content'); ?>
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white dark:bg-bg-dark rounded-lg shadow-md p-6 border border-border-light dark:border-border-dark">
            <h1 class="text-2xl font-bold mb-6 text-text-primary-light dark:text-text-primary-dark">Website Settings</h1>

            <form action="<?php echo e(route('admin.settings.update')); ?>" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                

                <?php $__currentLoopData = $settings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group => $groupSettings): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-6 border border-gray-200 dark:border-gray-700">
                        <div class="border-b border-gray-200 dark:border-gray-600 pb-3 mb-6">
                            <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark flex items-center">
                                <span class="w-1 h-6 bg-primary-light dark:bg-primary-dark rounded-full mr-3"></span>
                                <?php echo e(ucfirst($group)); ?> Settings
                            </h2>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                Configure <?php echo e(strtolower($group)); ?> related settings
                            </p>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <?php $__currentLoopData = $groupSettings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $setting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="bg-white dark:bg-bg-dark rounded-md p-4 border border-border-light dark:border-border-dark">
                                    <label class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                                        <?php echo e($setting->description); ?>

                                    </label>
                                    
                                    <?php switch($setting->type):
                                        case ('text'): ?>
                                            <input type="text" name="settings[<?php echo e($setting->key); ?>]" 
                                                value="<?php echo e($setting->value); ?>"
                                                class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                                            <?php break; ?>

                                        <?php case ('textarea'): ?>
                                            <textarea name="settings[<?php echo e($setting->key); ?>]" rows="3"
                                                class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark"><?php echo e($setting->value); ?></textarea>
                                            <?php break; ?>

                                        <?php case ('boolean'): ?>
                                            <div class="flex items-center">
                                                <input type="hidden" name="settings[<?php echo e($setting->key); ?>]" value="0">
                                                <input type="checkbox" id="<?php echo e($setting->key); ?>" 
                                                    name="settings[<?php echo e($setting->key); ?>]" value="1"
                                                    <?php echo e($setting->value ? 'checked' : ''); ?>

                                                    class="rounded border border-border-light dark:border-border-dark text-primary-light dark:text-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark bg-white dark:bg-bg-dark">
                                                <label for="<?php echo e($setting->key); ?>" class="ml-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">
                                                    Enable
                                                </label>
                                            </div>
                                            <?php break; ?>

                                        <?php case ('image'): ?>
                                            <input type="file" name="settings[<?php echo e($setting->key); ?>]" accept="image/*"
                                                class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                                            <?php if($setting->value): ?>
                                                <img src="<?php echo e(uploads_url($setting->value)); ?>" 
                                                    alt="<?php echo e($setting->description); ?>"
                                                    class="mt-2 h-12 rounded border border-gray-200 dark:border-gray-600">
                                            <?php endif; ?>
                                            <?php break; ?>

                                        <?php case ('json'): ?>
                                            <textarea name="settings[<?php echo e($setting->key); ?>]" rows="3"
                                                class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:ring-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark font-mono text-sm"><?php echo e(is_string($setting->value) ? $setting->value : json_encode($setting->value, JSON_PRETTY_PRINT)); ?></textarea>
                                            <?php break; ?>

                                        <?php default: ?>
                                            <input type="text" name="settings[<?php echo e($setting->key); ?>]" 
                                                value="<?php echo e($setting->value); ?>"
                                                class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                                    <?php endswitch; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <div class="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-600">
                    <button type="submit"
                        class="bg-primary-light dark:bg-primary-dark text-white px-6 py-3 rounded-md hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 font-medium transition-colors duration-200">
                        Save Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/admin/settings/index.blade.php ENDPATH**/ ?>