<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;

class ApiDocController extends Controller
{
    public function index(): View
    {
        $apiVersion = '1.0';
        $pageTitle = 'API Documentation';
        $metaDescription = 'API Documentation';
        $metaKeywords = "postal pincode api, api for pincode, free pincode api, all india pincode directory api";
        $metaImage = '/doc-image.png';

        setSEO($pageTitle, $metaDescription, $metaKeywords, $metaImage);

        return view('api-docs.index', compact('apiVersion', 'pageTitle', 'metaDescription', 'metaKeywords', 'metaImage'));
    }

    // public function registreationGuide(): View
    // {
    //     $pageTitle = 'API Registration Guide';
    //     $metaDescription = 'API Registration Guide';
    //     $metaKeywords = "API Registration Guide";
    //     $metaImage = '/doc-image.png';

    //     setSEO($pageTitle, $metaDescription, $metaKeywords, $metaImage);

    //     return view('api-docs.registration-guide', compact('pageTitle', 'metaDescription', 'metaKeywords', 'metaImage'));
    // }

    // public function distanceBetweenTwoCoordinates(): View
    // {
    //     $pageTitle = 'Distance Between Two Coordinates';
    //     $metaDescription = 'Distance Between Two Coordinates';
    //     $metaKeywords = "Distance Between Two Coordinates";
    //     $metaImage = '/doc-image.png';

    //     setSEO($pageTitle, $metaDescription, $metaKeywords, $metaImage);

    //     return view( 'api-docs.distance-between-two-coordinates', compact('pageTitle', 'metaDescription', 'metaKeywords', 'metaImage'));
    // }
}