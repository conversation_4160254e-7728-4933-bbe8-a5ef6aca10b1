@if (isset($landingPage['faq']) && $landingPage['faq']['active'])
        <section class="py-20 bg-bg-light dark:bg-bg-dark">
            <div class="max-w-4xl mx-auto px-4">
                <div class="text-center mb-12" data-aos="fade-up">
                    <span
                        class="inline-block px-4 py-1 rounded-full bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark text-xs font-semibold tracking-wider uppercase mb-3 animate-pulse">FAQ</span>
                    <h2
                        class="text-3xl md:text-4xl font-extrabold mb-4 text-text-primary-light dark:text-text-primary-dark">
                        {{ $landingPage['faq']['content']['heading'] ?? 'Frequently Asked Questions' }}
                    </h2>
                </div>
                <div x-data="{ open: null }" class="space-y-4">
                    @foreach ($landingPage['faq']['content']['faqs'] ?? [] as $i => $faq)
                        <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg dark:shadow-slate-700/50 p-5 border border-border-light dark:border-border-dark"
                            data-aos="fade-up" data-aos-delay="{{ $i * 100 }}">
                            <button @click="open === {{ $i + 1 }} ? open = null : open = {{ $i + 1 }}"
                                class="flex justify-between items-center w-full text-left font-semibold text-primary-light dark:text-primary-dark text-lg focus:outline-none hover:text-accent-light dark:hover:text-accent-dark transition-colors">
                                {{ $faq['question'] ?? '' }}
                                <i :class="open === {{ $i + 1 }} ? 'fa-solid fa-chevron-up' : 'fa-solid fa-chevron-down'"
                                    class="text-primary-light dark:text-primary-dark"></i>
                            </button>
                            <div x-show="open === {{ $i + 1 }}" x-transition
                                class="mt-3 text-text-secondary-light dark:text-text-secondary-dark">
                                {{ $faq['answer'] ?? '' }}
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif