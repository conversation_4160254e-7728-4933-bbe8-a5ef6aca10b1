<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PincodeImport extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'pin_code_imports';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'filename',
        'file_path',
        'status',
        'has_header',
        'update_existing',
        'total_records',
        'successful_records',
        'failed_records',
        'has_errors',
        'error_details',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'has_header' => 'boolean',
        'update_existing' => 'boolean',
        'has_errors' => 'boolean',
        'error_details' => 'array',
    ];

    /**
     * Get the user that created the import.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
