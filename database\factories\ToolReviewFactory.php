<?php

namespace Database\Factories;

use App\Models\Tool;
use App\Models\ToolReview;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ToolReview>
 */
class ToolReviewFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ToolReview::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'tool_id' => Tool::factory(),
            'user_id' => User::factory(),
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'review' => $this->faker->paragraph(),
            'is_approved' => $this->faker->boolean(70), // 70% chance of being approved
            'rating' => $this->faker->numberBetween(1, 5),
        ];
    }

    /**
     * Indicate that the review is approved.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function approved()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_approved' => true,
            ];
        });
    }

    /**
     * Indicate that the review is not approved.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function unapproved()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_approved' => false,
            ];
        });
    }

    /**
     * Indicate that the review has a specific rating.
     *
     * @param int $rating
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function rating(int $rating)
    {
        return $this->state(function (array $attributes) use ($rating) {
            return [
                'rating' => $rating,
            ];
        });
    }
} 