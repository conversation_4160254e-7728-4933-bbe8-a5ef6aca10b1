<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Card;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use App\Traits\TracksApiRequests;

class CardAPIController extends Controller
{
    use TracksApiRequests;

    public function getCardData(Request $request): JsonResponse
    {
        // Log::info('getCardData method called');

        try {
            // Log::info('Attempting to fetch all cards');
            $cards = Card::select(['id', 'title', 'line2', 'line3', 'imageUrl', 'link', 'linkText'])->get();
            // Log::info('Cards fetched successfully', ['count' => $cards->count()]);

            $response = response()->json(['data' => $cards]);
        } catch (\Exception $e) {
            Log::error('Error in getCardData method', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $response = response()->json(['error' => 'An error occurred while fetching card data'], 500);
        }

        $this->trackApiRequest($request, $response);
        return $response;
    }

    public function show(Request $request, Card $card): JsonResponse
    {
        $response = response()->json($card);
        $this->trackApiRequest($request, $response);
        return $response;
    }
}