<?php

use App\Models\User;
use App\Models\BlogPost;
use App\Models\BlogPostCategory;
use App\Models\BlogPostTag;
use App\Models\Comment;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

beforeEach(function () {
    $this->admin = User::factory()->create([
        'role' => 'admin',
        'status' => 'active'
    ]);
    
    $this->actingAs($this->admin);
    Storage::fake('public');
});

describe('Blog Management Integration', function () {
    
    describe('Blog Post Management', function () {
        it('displays blog posts index', function () {
            BlogPost::factory()->count(5)->create();

            $response = $this->get(route('admin.blog.posts.index'));

            $response->assertStatus(200);
            $response->assertViewIs('admin.blog.posts.index');
            $response->assertViewHas('posts');
        });

        it('creates new blog post successfully', function () {
            $category = BlogPostCategory::factory()->create();
            $tag = BlogPostTag::factory()->create();

            $postData = [
                'title' => 'Test Blog Post',
                'slug' => 'test-blog-post',
                'content' => 'This is test content for the blog post.',
                'excerpt' => 'Test excerpt',
                'category_id' => $category->id,
                'tags' => [$tag->id],
                'status' => 'published',
                'featured' => true,
                'meta_title' => 'Test Meta Title',
                'meta_description' => 'Test meta description'
            ];

            $response = $this->post(route('admin.blog.posts.store'), $postData);

            $response->assertRedirect(route('admin.blog.posts.index'));
            $response->assertSessionHas('success');
            
            $this->assertDatabaseHas('blog_posts', [
                'title' => 'Test Blog Post',
                'slug' => 'test-blog-post',
                'status' => 'published',
                'featured' => true
            ]);
        });

        it('validates required fields for blog post creation', function () {
            $response = $this->post(route('admin.blog.posts.store'), []);

            $response->assertSessionHasErrors([
                'title', 'content', 'category_id', 'status'
            ]);
        });

        it('generates slug automatically if not provided', function () {
            $category = BlogPostCategory::factory()->create();

            $postData = [
                'title' => 'Auto Generated Slug Post',
                'content' => 'Content for auto slug post',
                'category_id' => $category->id,
                'status' => 'published'
            ];

            $response = $this->post(route('admin.blog.posts.store'), $postData);

            $response->assertRedirect(route('admin.blog.posts.index'));
            
            $this->assertDatabaseHas('blog_posts', [
                'title' => 'Auto Generated Slug Post',
                'slug' => 'auto-generated-slug-post'
            ]);
        });

        it('uploads featured image successfully', function () {
            $category = BlogPostCategory::factory()->create();
            $image = UploadedFile::fake()->image('featured.jpg');

            $postData = [
                'title' => 'Post with Image',
                'content' => 'Content with featured image',
                'category_id' => $category->id,
                'status' => 'published',
                'featured_image' => $image
            ];

            $response = $this->post(route('admin.blog.posts.store'), $postData);

            $response->assertRedirect(route('admin.blog.posts.index'));
            
            $post = BlogPost::where('title', 'Post with Image')->first();
            expect($post->featured_image)->not->toBeNull();
            Storage::disk('public')->assertExists($post->featured_image);
        });

        it('updates blog post successfully', function () {
            $post = BlogPost::factory()->create();
            $category = BlogPostCategory::factory()->create();

            $updateData = [
                'title' => 'Updated Blog Post',
                'content' => 'Updated content',
                'category_id' => $category->id,
                'status' => 'draft'
            ];

            $response = $this->put(route('admin.blog.posts.update', $post), $updateData);

            $response->assertRedirect(route('admin.blog.posts.index'));
            $response->assertSessionHas('success');
            
            $post->refresh();
            expect($post->title)->toBe('Updated Blog Post');
            expect($post->status)->toBe('draft');
        });

        it('deletes blog post successfully', function () {
            $post = BlogPost::factory()->create();

            $response = $this->delete(route('admin.blog.posts.destroy', $post));

            $response->assertRedirect(route('admin.blog.posts.index'));
            $response->assertSessionHas('success');
            
            $this->assertDatabaseMissing('blog_posts', ['id' => $post->id]);
        });
    });

    describe('Blog Category Management', function () {
        it('displays categories index', function () {
            BlogPostCategory::factory()->count(3)->create();

            $response = $this->get(route('admin.blog.categories.index'));

            $response->assertStatus(200);
            $response->assertViewIs('admin.blog.categories.index');
            $response->assertViewHas('categories');
        });

        it('creates new category successfully', function () {
            $categoryData = [
                'name' => 'Technology',
                'slug' => 'technology',
                'description' => 'Technology related posts',
                'is_active' => true
            ];

            $response = $this->post(route('admin.blog.categories.store'), $categoryData);

            $response->assertRedirect(route('admin.blog.categories.index'));
            $response->assertSessionHas('success');
            
            $this->assertDatabaseHas('blog_post_categories', [
                'name' => 'Technology',
                'slug' => 'technology',
                'is_active' => true
            ]);
        });

        it('validates unique slug for categories', function () {
            BlogPostCategory::factory()->create(['slug' => 'existing-slug']);

            $response = $this->post(route('admin.blog.categories.store'), [
                'name' => 'New Category',
                'slug' => 'existing-slug',
                'is_active' => true
            ]);

            $response->assertSessionHasErrors(['slug']);
        });

        it('updates category successfully', function () {
            $category = BlogPostCategory::factory()->create();

            $updateData = [
                'name' => 'Updated Category',
                'slug' => 'updated-category',
                'description' => 'Updated description',
                'is_active' => false
            ];

            $response = $this->put(route('admin.blog.categories.update', $category), $updateData);

            $response->assertRedirect(route('admin.blog.categories.index'));
            
            $category->refresh();
            expect($category->name)->toBe('Updated Category');
            expect($category->is_active)->toBe(false);
        });

        it('prevents deletion of category with posts', function () {
            $category = BlogPostCategory::factory()->create();
            BlogPost::factory()->create(['category_id' => $category->id]);

            $response = $this->delete(route('admin.blog.categories.destroy', $category));

            $response->assertRedirect(route('admin.blog.categories.index'));
            $response->assertSessionHas('error');
            
            $this->assertDatabaseHas('blog_post_categories', ['id' => $category->id]);
        });
    });

    describe('Blog Tag Management', function () {
        it('displays tags index', function () {
            BlogPostTag::factory()->count(5)->create();

            $response = $this->get(route('admin.blog.tags.index'));

            $response->assertStatus(200);
            $response->assertViewIs('admin.blog.tags.index');
            $response->assertViewHas('tags');
        });

        it('creates new tag successfully', function () {
            $tagData = [
                'name' => 'Laravel',
                'slug' => 'laravel',
                'color' => '#ff0000'
            ];

            $response = $this->post(route('admin.blog.tags.store'), $tagData);

            $response->assertRedirect(route('admin.blog.tags.index'));
            $response->assertSessionHas('success');
            
            $this->assertDatabaseHas('blog_post_tags', [
                'name' => 'Laravel',
                'slug' => 'laravel',
                'color' => '#ff0000'
            ]);
        });

        it('validates tag color format', function () {
            $response = $this->post(route('admin.blog.tags.store'), [
                'name' => 'Test Tag',
                'slug' => 'test-tag',
                'color' => 'invalid-color'
            ]);

            $response->assertSessionHasErrors(['color']);
        });

        it('updates tag successfully', function () {
            $tag = BlogPostTag::factory()->create();

            $updateData = [
                'name' => 'Updated Tag',
                'slug' => 'updated-tag',
                'color' => '#00ff00'
            ];

            $response = $this->put(route('admin.blog.tags.update', $tag), $updateData);

            $response->assertRedirect(route('admin.blog.tags.index'));
            
            $tag->refresh();
            expect($tag->name)->toBe('Updated Tag');
            expect($tag->color)->toBe('#00ff00');
        });

        it('deletes tag successfully', function () {
            $tag = BlogPostTag::factory()->create();

            $response = $this->delete(route('admin.blog.tags.destroy', $tag));

            $response->assertRedirect(route('admin.blog.tags.index'));
            $response->assertSessionHas('success');
            
            $this->assertDatabaseMissing('blog_post_tags', ['id' => $tag->id]);
        });
    });

    describe('Comment Management', function () {
        it('displays comments index', function () {
            Comment::factory()->count(10)->create();

            $response = $this->get(route('admin.comments.index'));

            $response->assertStatus(200);
            $response->assertViewIs('admin.comments.index');
            $response->assertViewHas('comments');
        });

        it('approves comment successfully', function () {
            $comment = Comment::factory()->create(['status' => 'pending']);

            $response = $this->patch(route('admin.comments.approve', $comment));

            $response->assertRedirect(route('admin.comments.index'));
            $response->assertSessionHas('success');
            
            $comment->refresh();
            expect($comment->status)->toBe('approved');
        });

        it('rejects comment successfully', function () {
            $comment = Comment::factory()->create(['status' => 'pending']);

            $response = $this->patch(route('admin.comments.reject', $comment));

            $response->assertRedirect(route('admin.comments.index'));
            $response->assertSessionHas('success');
            
            $comment->refresh();
            expect($comment->status)->toBe('rejected');
        });

        it('deletes comment successfully', function () {
            $comment = Comment::factory()->create();

            $response = $this->delete(route('admin.comments.destroy', $comment));

            $response->assertRedirect(route('admin.comments.index'));
            $response->assertSessionHas('success');
            
            $this->assertDatabaseMissing('comments', ['id' => $comment->id]);
        });

        it('filters comments by status', function () {
            Comment::factory()->count(3)->create(['status' => 'approved']);
            Comment::factory()->count(2)->create(['status' => 'pending']);

            $response = $this->get(route('admin.comments.index', ['status' => 'pending']));

            $response->assertStatus(200);
        });
    });

    describe('Image Upload', function () {
        it('uploads blog image successfully', function () {
            $image = UploadedFile::fake()->image('blog-image.jpg');

            $response = $this->post(route('admin.blog.upload-image'), [
                'image' => $image
            ]);

            $response->assertStatus(200);
            $response->assertJsonStructure(['url']);
            
            $data = $response->json();
            Storage::disk('public')->assertExists($data['url']);
        });

        it('validates image file type', function () {
            $file = UploadedFile::fake()->create('document.pdf', 100);

            $response = $this->post(route('admin.blog.upload-image'), [
                'image' => $file
            ]);

            $response->assertStatus(422);
            $response->assertJsonValidationErrors(['image']);
        });

        it('validates image file size', function () {
            $image = UploadedFile::fake()->image('large-image.jpg')->size(5000); // 5MB

            $response = $this->post(route('admin.blog.upload-image'), [
                'image' => $image
            ]);

            $response->assertStatus(422);
            $response->assertJsonValidationErrors(['image']);
        });
    });

    describe('API Endpoints', function () {
        it('returns categories as JSON', function () {
            BlogPostCategory::factory()->count(3)->create(['is_active' => true]);

            $response = $this->get(route('admin.blog.categories.api'));

            $response->assertStatus(200);
            $response->assertJsonStructure([
                '*' => ['id', 'name', 'slug']
            ]);
        });

        it('returns tags as JSON', function () {
            BlogPostTag::factory()->count(5)->create();

            $response = $this->get(route('admin.blog.tags.api'));

            $response->assertStatus(200);
            $response->assertJsonStructure([
                '*' => ['id', 'name', 'slug', 'color']
            ]);
        });
    });

    describe('Authorization', function () {
        it('prevents non-admin from accessing blog management', function () {
            $regularUser = User::factory()->create(['role' => 'user']);
            $this->actingAs($regularUser);

            $response = $this->get(route('admin.blog.posts.index'));

            $response->assertStatus(403);
        });

        it('prevents guest from accessing blog management', function () {
            auth()->logout();

            $response = $this->get(route('admin.blog.posts.index'));

            $response->assertRedirect(route('admin.login'));
        });
    });
});