@props(['tokens'])

<div class="bg-white dark:bg-bg-dark overflow-hidden shadow-sm dark:shadow-md dark:shadow-gray-800/30 sm:rounded-lg border border-border-light dark:border-border-dark">
    <div class="p-6">
        <!-- Success Messages -->
        @if (session('status'))
            <div class="mb-4" id="status-message">
                @php
                    $statusMessages = [
                        'api-token-created' => 'API token created successfully.',
                        'api-token-deleted' => 'API token deleted successfully.',
                        'all-tokens-revoked' => 'All API tokens have been revoked.',
                    ];
                    $message = $statusMessages[session('status')] ?? 'Operation completed successfully.';
                @endphp
                <div class="bg-green-50 dark:bg-green-900/20 border-l-4 border-green-400 dark:border-green-500 p-4 rounded-r-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400 dark:text-green-500" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3 flex-grow">
                            <p class="text-sm text-green-700 dark:text-green-300">{{ __($message) }}</p>
                        </div>
                        <div class="ml-3">
                            <button type="button" onclick="dismissMessage('status-message')"
                                class="text-green-400 hover:text-green-600 dark:text-green-500 dark:hover:text-green-400 focus:outline-none">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Header Section -->
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
            <div>
                <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">
                    {{ __('API Tokens') }}
                </h3>
                <p class="mt-1 text-sm text-text-secondary-light dark:text-text-secondary-dark">
                    {{ __('Manage your API tokens for accessing the API. Tokens expire automatically for security.') }}
                </p>
            </div>
            <div class="flex gap-2">
                @if ($tokens->count() > 0)
                    <x-secondary-button x-data=""
                        x-on:click.prevent="$dispatch('open-modal', 'revoke-all-tokens')"
                        class="text-red-600 hover:text-red-700 border-red-300 hover:border-red-400">
                        {{ __('Revoke All') }}
                    </x-secondary-button>
                @endif
                <a href="{{ route('user.api-tokens.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-light hover:bg-primary-light/90 dark:bg-primary-dark dark:hover:bg-primary-dark/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark dark:focus:ring-offset-bg-dark">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    {{ __('Create New Token') }}
                </a>
            </div>
        </div>

        <!-- Tokens Table -->
        @if ($tokens->count())
            <div class="overflow-x-auto shadow dark:shadow-md dark:shadow-gray-800/30 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 rounded-lg">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                <div class="flex items-center">
                                    {{ __('Token Name') }}
                                    <svg class="w-4 h-4 ml-1 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z">
                                        </path>
                                    </svg>
                                </div>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                {{ __('Created') }}
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                {{ __('Last Used') }}
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                {{ __('Status') }}
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                {{ __('Expires') }}
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                {{ __('Actions') }}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-bg-dark divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach ($tokens as $token)
                            @php
                                $isExpired = $token->expires_at && $token->expires_at->isPast();
                                $isExpiringSoon = $token->expires_at && $token->expires_at->diffInDays() <= 7;
                            @endphp
                            <tr id="token-row-{{ $token->id }}" class="hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark">{{ $token->name }}</div>
                                        @if ($token->abilities && count($token->abilities) > 0)
                                            <div class="ml-2">
                                                <span
                                                    class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                                    {{ count($token->abilities) }} {{ __('abilities') }}
                                                </span>
                                            </div>
                                        @endif
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                    <div class="flex flex-col">
                                        <span>{{ $token->created_at->format('M d, Y') }}</span>
                                        <span
                                            class="text-xs text-gray-400 dark:text-gray-500">{{ $token->created_at->format('H:i') }}</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                    @if ($token->last_used_at)
                                        <div class="flex items-center">
                                            <div class="w-2 h-2 bg-green-400 dark:bg-green-500 rounded-full mr-2"></div>
                                            <span>{{ $token->last_used_at->diffForHumans() }}</span>
                                        </div>
                                    @else
                                        <div class="flex items-center">
                                            <div class="w-2 h-2 bg-gray-300 dark:bg-gray-600 rounded-full mr-2"></div>
                                            <span>{{ __('Never used') }}</span>
                                        </div>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if ($isExpired)
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                            {{ __('Expired') }}
                                        </span>
                                    @elseif($isExpiringSoon)
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                                            {{ __('Expiring Soon') }}
                                        </span>
                                    @else
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                            {{ __('Active') }}
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                    @if ($token->expires_at)
                                        <div class="flex flex-col">
                                            <span
                                                class="{{ $isExpired ? 'text-red-600 dark:text-red-400' : ($isExpiringSoon ? 'text-yellow-600 dark:text-yellow-400' : '') }}">
                                                {{ $token->expires_at->format('M d, Y') }}
                                            </span>
                                            <span
                                                class="text-xs text-gray-400 dark:text-gray-500">{{ $token->expires_at->diffForHumans() }}</span>
                                        </div>
                                    @else
                                        <span class="text-gray-400 dark:text-gray-500">{{ __('Never') }}</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-3">
                                        @unless ($isExpired)
                                            <button class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 regenerate-token-btn"
                                                data-token-id="{{ $token->id }}" data-token-name="{{ $token->name }}"
                                                title="{{ __('Regenerate Token') }}">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                                                    </path>
                                                </svg>
                                            </button>
                                        @endunless
                                        <button class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 delete-token-btn"
                                            data-token-id="{{ $token->id }}" data-token-name="{{ $token->name }}"
                                            title="{{ __('Delete Token') }}">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                                </path>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Token Statistics -->
            <div class="mt-6 grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-border-light dark:border-border-dark">
                    <div class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark">{{ $tokens->count() }}</div>
                    <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark">{{ __('Total Tokens') }}</div>
                </div>
                <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        {{ $tokens->filter(fn($t) => $t->last_used_at !== null)->count() }}
                    </div>
                    <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark">{{ __('Active Tokens') }}</div>
                </div>
                <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
                    <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                        {{ $tokens->filter(fn($t) => $t->expires_at && $t->expires_at->diffInDays() <= 7)->count() }}
                    </div>
                    <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark">{{ __('Expiring Soon') }}</div>
                </div>
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <svg class="mx-auto h-16 w-16 text-gray-300 dark:text-gray-600" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
                        d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                </svg>
                <h3 class="mt-4 text-lg font-medium text-text-primary-light dark:text-text-primary-dark">{{ __('No API tokens') }}</h3>
                <p class="mt-2 text-sm text-text-secondary-light dark:text-text-secondary-dark">
                    {{ __('Get started by creating your first API token to access the API.') }}</p>
                <div class="mt-6">
                    <a href="{{ route('user.api-tokens.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-light hover:bg-primary-light/90 dark:bg-primary-dark dark:hover:bg-primary-dark/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark dark:focus:ring-offset-bg-dark">
                        {{ __('Create Your First Token') }}
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>

<!-- Delete Token Confirmation Modal -->
<x-modal name="delete-token-confirmation" :show="false">
    <div class="p-6 bg-white dark:bg-bg-dark">
        <div class="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 dark:bg-red-900/30 rounded-full">
            <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
        </div>

        <div class="mt-4 text-center">
            <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">{{ __('Delete API Token') }}</h3>
            <p class="mt-2 text-sm text-text-secondary-light dark:text-text-secondary-dark">
                {{ __('Are you sure you want to delete this API token? This action cannot be undone and all applications using this token will lose access immediately.') }}
            </p>
            <p class="mt-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark bg-gray-100 dark:bg-gray-800 p-2 rounded" id="delete-token-name"></p>
        </div>

        <div class="mt-6 flex justify-end space-x-3">
            <x-secondary-button x-on:click="$dispatch('close')">
                {{ __('Cancel') }}
            </x-secondary-button>
            <x-danger-button id="confirm-delete-token">
                <span class="delete-btn-text">{{ __('Delete Token') }}</span>
                <span class="delete-btn-loading hidden">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                            stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                        </path>
                    </svg>
                    {{ __('Deleting...') }}
                </span>
            </x-danger-button>
        </div>
    </div>
</x-modal>

<!-- Revoke All Tokens Modal -->
<x-modal name="revoke-all-tokens" :show="false">
    <div class="p-6 bg-white dark:bg-bg-dark">
        <div class="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 dark:bg-red-900/30 rounded-full">
            <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
        </div>

        <div class="mt-4 text-center">
            <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">{{ __('Revoke All API Tokens') }}</h3>
            <p class="mt-2 text-sm text-text-secondary-light dark:text-text-secondary-dark">
                {{ __('This will permanently delete all your API tokens. All applications using these tokens will lose access immediately.') }}
            </p>
            <p class="mt-2 text-sm font-bold text-red-600">{{ __('This action cannot be undone.') }}</p>
        </div>

        <div class="mt-6 flex justify-end space-x-3">
            <x-secondary-button x-on:click="$dispatch('close')">
                {{ __('Cancel') }}
            </x-secondary-button>
            <x-danger-button id="confirm-revoke-all">
                {{ __('Revoke All Tokens') }}
            </x-danger-button>
        </div>
    </div>
</x-modal>

<!-- Token Created Modal -->
<x-modal name="token-created" :show="false">
    <div class="p-6">
        <div class="flex items-center justify-center w-12 h-12 mx-auto bg-green-100 rounded-full">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
        </div>
        <div class="mt-4 text-center">
            <h3 class="text-lg font-medium text-gray-900">{{ __('API Token Created Successfully') }}</h3>
            <p class="mt-2 text-sm text-gray-500">
                {{ __('Please copy your API token now. For security reasons, you won\'t be able to see it again!') }}
            </p>
        </div>
        <div class="mt-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('Your API Token') }}</label>
            <div class="relative">
                <input type="text" id="token-value" readonly
                    class="block w-full px-4 py-3 pr-20 text-sm font-mono border border-gray-300 rounded-md bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    value="{{ session('plain_text_token') }}">
                <button type="button" onclick="copyToken()"
                    class="absolute inset-y-0 right-0 flex items-center px-4 text-sm font-medium text-indigo-600 hover:text-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-r-md"
                    id="copy-token-btn">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z">
                        </path>
                    </svg>
                    <span class="copy-text">{{ __('Copy') }}</span>
                </button>
            </div>
            @if (session('token_expiry'))
                <div class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <div class="flex">
                        <svg class="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                clip-rule="evenodd"></path>
                        </svg>
                        <div>
                            <p class="text-sm text-yellow-800">
                                <strong>{{ __('Expiration:') }}</strong> {{ session('token_expiry') }}
                            </p>
                            <p class="text-xs text-yellow-600 mt-1">
                                {{ __('Make sure to set up token renewal before this date.') }}
                            </p>
                        </div>
                    </div>
                </div>
            @endif
        </div>
        <div class="mt-6 flex justify-end">
            <x-secondary-button x-on:click="$dispatch('close')" id="close-token-modal">
                {{ __('I\'ve Saved My Token') }}
            </x-secondary-button>
        </div>
    </div>
</x-modal>

@push('scripts')
    <script>
        // Global variables
        let tokenToDelete = null;
        let isProcessing = false;

        // Utility functions
        function dismissMessage(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.transition = 'opacity 0.3s ease-out';
                element.style.opacity = '0';
                setTimeout(() => element.remove(), 300);
            }
        }

        function showNotification(message, type = 'success') {
            const colors = {
                success: {
                    bg: 'bg-green-50',
                    border: 'border-green-400',
                    text: 'text-green-700',
                    icon: 'text-green-400'
                },
                error: {
                    bg: 'bg-red-50',
                    border: 'border-red-400',
                    text: 'text-red-700',
                    icon: 'text-red-400'
                },
                warning: {
                    bg: 'bg-yellow-50',
                    border: 'border-yellow-400',
                    text: 'text-yellow-700',
                    icon: 'text-yellow-400'
                }
            };

            const color = colors[type] || colors.success;
            const iconPath = type === 'error' ?
                "M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" :
                "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z";

            const notification = document.createElement('div');
            notification.className = `mb-4 ${color.bg} border-l-4 ${color.border} p-4 rounded-r-md`;
            notification.innerHTML = `
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 ${color.icon}" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="${iconPath}" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3 flex-grow">
                        <p class="text-sm ${color.text}">${message}</p>
                    </div>
                    <div class="ml-3">
                        <button type="button" onclick="this.parentElement.parentElement.parentElement.remove()" 
                            class="${color.icon} hover:opacity-75 focus:outline-none">
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            `;

            const container = document.querySelector('.p-6');
            container.insertBefore(notification, container.firstChild);

            // Auto dismiss after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.transition = 'opacity 0.3s ease-out';
                    notification.style.opacity = '0';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 5000);
        }

        function setButtonLoading(button, isLoading, loadingText = 'Processing...') {
            const textSpan = button.querySelector('.delete-btn-text');
            const loadingSpan = button.querySelector('.delete-btn-loading');

            if (textSpan && loadingSpan) {
                if (isLoading) {
                    textSpan.classList.add('hidden');
                    loadingSpan.classList.remove('hidden');
                } else {
                    textSpan.classList.remove('hidden');
                    loadingSpan.classList.add('hidden');
                }
            } else {
                button.innerHTML = isLoading ? loadingText : button.getAttribute('data-original-text') || 'Submit';
            }

            button.disabled = isLoading;
        }

        function copyToken() {
            const tokenInput = document.getElementById('token-value');
            const copyButton = document.getElementById('copy-token-btn');
            const copyText = copyButton.querySelector('.copy-text');

            if (!tokenInput || !copyText) return;

            // Use modern clipboard API if available
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(tokenInput.value).then(() => {
                    showCopyFeedback(copyText);
                }).catch(() => {
                    fallbackCopyTextToClipboard(tokenInput, copyText);
                });
            } else {
                fallbackCopyTextToClipboard(tokenInput, copyText);
            }
        }

        function fallbackCopyTextToClipboard(tokenInput, copyText) {
            tokenInput.select();
            tokenInput.setSelectionRange(0, 99999); // For mobile devices

            try {
                document.execCommand('copy');
                showCopyFeedback(copyText);
            } catch (err) {
                console.error('Failed to copy text:', err);
                showNotification('Failed to copy token. Please select and copy manually.', 'error');
            }
        }

        function showCopyFeedback(copyText) {
            const originalText = copyText.textContent;
            copyText.textContent = '{{ __('Copied!') }}';
            copyText.classList.add('text-green-600');

            setTimeout(() => {
                copyText.textContent = originalText;
                copyText.classList.remove('text-green-600');
            }, 2000);
        }

        // API request helper
        async function makeApiRequest(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                        '',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                credentials: 'same-origin'
            };

            const mergedOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };

            const response = await fetch(url, mergedOptions);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }

            return response.json();
        }

        // Token deletion functionality
        async function deleteToken(tokenId) {
            if (isProcessing) return;
            isProcessing = true;

            const confirmButton = document.getElementById('confirm-delete-token');
            setButtonLoading(confirmButton, true);

            try {
                const url = `{{ route('tokens.destroy', ['token_id' => ':token_id']) }}`.replace(':token_id', tokenId);
                const data = await makeApiRequest(url, {
                    method: 'DELETE'
                });

                if (data.success) {
                    // Remove the token row with animation
                    const row = document.getElementById(`token-row-${tokenId}`);
                    if (row) {
                        row.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
                        row.style.opacity = '0';
                        row.style.transform = 'translateX(-10px)';
                        setTimeout(() => row.remove(), 300);
                    }

                    // Close modal
                    window.dispatchEvent(new CustomEvent('close-modal', {
                        detail: 'delete-token-confirmation'
                    }));

                    showNotification('{{ __('API token deleted successfully.') }}');

                    // Check if no tokens left and show empty state
                    setTimeout(() => {
                        const remainingRows = document.querySelectorAll('[id^="token-row-"]');
                        if (remainingRows.length === 0) {
                            location.reload(); // Reload to show empty state
                        }
                    }, 400);
                } else {
                    throw new Error(data.message || 'Failed to delete token');
                }
            } catch (error) {
                console.error('Delete token error:', error);
                showNotification(error.message || '{{ __('An error occurred while deleting the token.') }}', 'error');
            } finally {
                setButtonLoading(confirmButton, false);
                isProcessing = false;
                tokenToDelete = null;
            }
        }

        // Token regeneration functionality
        async function regenerateToken(tokenId, tokenName) {
            if (isProcessing) return;

            if (!confirm(
                    `{{ __('Are you sure you want to regenerate the token') }} "${tokenName}"? {{ __('The old token will stop working immediately.') }}`
                )) {
                return;
            }

            isProcessing = true;
            const button = document.querySelector(`[data-token-id="${tokenId}"].regenerate-token-btn`);
            const originalHtml = button.innerHTML;
            button.innerHTML =
                '<svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>';
            button.disabled = true;

            try {
                const url = `{{ route('tokens.regenerate', ['tokenId' => ':tokenId']) }}`.replace(':tokenId', tokenId);
                const data = await makeApiRequest(url, {
                    method: 'POST'
                });

                if (data.success && data.token) {
                    // Update the token value in the modal
                    document.getElementById('token-value').value = data.token;

                    // Show the token created modal
                    window.dispatchEvent(new CustomEvent('open-modal', {
                        detail: 'token-created'
                    }));

                    showNotification(
                        `{{ __('Token') }} "${tokenName}" {{ __('has been regenerated successfully.') }}`);
                } else {
                    throw new Error(data.message || 'Failed to regenerate token');
                }
            } catch (error) {
                console.error('Regenerate token error:', error);
                showNotification(error.message || '{{ __('An error occurred while regenerating the token.') }}',
                    'error');
            } finally {
                button.innerHTML = originalHtml;
                button.disabled = false;
                isProcessing = false;
            }
        }

        // Revoke all tokens functionality
        async function revokeAllTokens() {
            if (isProcessing) return;
            isProcessing = true;

            const confirmButton = document.getElementById('confirm-revoke-all');
            const originalText = confirmButton.textContent;
            confirmButton.textContent = '{{ __('Revoking...') }}';
            confirmButton.disabled = true;

            try {
                const url = '{{ route('tokens.revoke-all') }}';
                const data = await makeApiRequest(url, {
                    method: 'POST'
                });

                if (data.success) {
                    window.dispatchEvent(new CustomEvent('close-modal', {
                        detail: 'revoke-all-tokens'
                    }));
                    showNotification('{{ __('All API tokens have been revoked successfully.') }}');

                    // Reload page to show empty state
                    setTimeout(() => location.reload(), 1000);
                } else {
                    throw new Error(data.message || 'Failed to revoke all tokens');
                }
            } catch (error) {
                console.error('Revoke all tokens error:', error);
                showNotification(error.message || '{{ __('An error occurred while revoking tokens.') }}', 'error');
            } finally {
                confirmButton.textContent = originalText;
                confirmButton.disabled = false;
                isProcessing = false;
            }
        }

        // DOM Content Loaded Event Handler
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-dismiss status messages
            setTimeout(() => {
                const statusMessage = document.getElementById('status-message');
                if (statusMessage) {
                    dismissMessage('status-message');
                }
            }, 5000);

            // Show token created modal if there's a new token
            @if (session('plain_text_token'))
                setTimeout(() => {
                    window.dispatchEvent(new CustomEvent('open-modal', {
                        detail: 'token-created'
                    }));
                }, 100);
            @endif

            // Delete token button handlers
            document.addEventListener('click', function(e) {
                if (e.target.closest('.delete-token-btn')) {
                    const button = e.target.closest('.delete-token-btn');
                    const tokenId = button.dataset.tokenId;
                    const tokenName = button.dataset.tokenName;

                    tokenToDelete = tokenId;
                    document.getElementById('delete-token-name').textContent = tokenName;
                    window.dispatchEvent(new CustomEvent('open-modal', {
                        detail: 'delete-token-confirmation'
                    }));
                }

                // Regenerate token button handlers
                if (e.target.closest('.regenerate-token-btn')) {
                    const button = e.target.closest('.regenerate-token-btn');
                    const tokenId = button.dataset.tokenId;
                    const tokenName = button.dataset.tokenName;

                    regenerateToken(tokenId, tokenName);
                }
            });

            // Confirm delete token
            document.getElementById('confirm-delete-token')?.addEventListener('click', function() {
                if (tokenToDelete) {
                    deleteToken(tokenToDelete);
                }
            });

            // Confirm revoke all tokens
            document.getElementById('confirm-revoke-all')?.addEventListener('click', function() {
                revokeAllTokens();
            });

            // Close token modal handler with page reload
            document.getElementById('close-token-modal')?.addEventListener('click', function() {
                setTimeout(() => location.reload(), 200);
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Escape key to close modals
                if (e.key === 'Escape') {
                    const openModals = document.querySelectorAll('[x-show="show"]');
                    openModals.forEach(modal => {
                        window.dispatchEvent(new CustomEvent('close-modal'));
                    });
                }
            });

            // Add tooltips for action buttons
            const actionButtons = document.querySelectorAll('[title]');
            actionButtons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    // You can implement custom tooltip logic here if needed
                });
            });

            // Auto-refresh token last used times every 30 seconds
            setInterval(() => {
                const lastUsedCells = document.querySelectorAll('td:nth-child(3)');
                // You can implement live updates here if needed
            }, 30000);
        });

        // Export functions for potential external use
        window.TokenManager = {
            copyToken,
            deleteToken,
            regenerateToken,
            revokeAllTokens,
            showNotification,
            dismissMessage
        };
    </script>
@endpush
