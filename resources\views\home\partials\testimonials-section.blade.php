@php
        $testimonialsData = $landingPage['testimonials'] ?? null;
        $heading = $testimonialsData['content']['heading'] ?? 'What our users say';
        $subheading = $testimonialsData['content']['subheading'] ?? '';
        $testimonials = $testimonialsData['content']['testimonials'] ?? [];
    @endphp

    @if ($testimonialsData && $testimonialsData['active'] && !empty($testimonials))
        <section id="testimonials" class="py-20 bg-bg-light dark:bg-bg-dark transition-colors duration-300">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Section Header -->
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-text-primary-light dark:text-text-primary-dark mb-4">
                        {{ $heading }}
                    </h2>
                    @if ($subheading)
                        <p class="text-lg text-text-secondary-light dark:text-text-secondary-dark max-w-3xl mx-auto">
                            {{ $subheading }}
                        </p>
                    @endif
                </div>

                <!-- Testimonials Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach ($testimonials as $testimonial)
                        <div
                            class="bg-white dark:bg-bg-dark rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 p-6 border border-border-light dark:border-border-dark">
                            <!-- Rating Stars -->
                            <div class="flex items-center mb-4">
                                @for ($i = 1; $i <= 5; $i++)
                                    <svg class="w-5 h-5 {{ $i <= ($testimonial['rating'] ?? 5) ? 'text-yellow-400' : 'text-border-light dark:text-border-dark' }}"
                                        fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                @endfor
                            </div>

                            <!-- Testimonial Content -->
                            <blockquote
                                class="text-text-secondary-light dark:text-text-secondary-dark mb-6 leading-relaxed">
                                "{{ $testimonial['content'] ?? '' }}"
                            </blockquote>

                            <!-- User Info -->
                            <div class="flex items-center">
                                <div class="flex-shrink-0 mr-4">
                                    <img class="h-12 w-12 rounded-full object-cover border-2 border-border-light dark:border-border-dark"
                                        src="{{ $testimonial['avatar'] ?? '/images/testimonials/default-avatar.jpg' }}"
                                        alt="{{ $testimonial['name'] ?? 'User' }}" loading="lazy">
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">
                                        {{ $testimonial['name'] ?? 'Anonymous' }}
                                    </h4>
                                    @if (!empty($testimonial['designation']))
                                        <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                            {{ $testimonial['designation'] }}
                                        </p>
                                    @endif
                                    @if (!empty($testimonial['location']))
                                        <p class="text-sm text-border-light dark:text-border-dark">
                                            {{ $testimonial['location'] }}
                                        </p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Call to Action -->
                <div class="text-center mt-12">
                    <p class="text-lg text-text-secondary-light dark:text-text-secondary-dark mb-6">
                        {{ $landingPage['testimonials']['content']['cta_text'] ?? 'Join thousands of satisfied customers who trust our pincode service' }}
                    </p>
                    <a href="{{ $landingPage['testimonials']['content']['cta_button_link'] ?? '#contact' }}"
                        class="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light transition-colors duration-200">
                        {{ $landingPage['testimonials']['content']['cta_button_text'] ?? 'Get Started Today' }}
                        <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                                clip-rule="evenodd" />
                        </svg>
                    </a>
                </div>
            </div>
        </section>
    @endif