<?php

use App\Models\LandingPageContent;
use App\Models\LandingPageSection;
use Illuminate\Support\Facades\Storage;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    Storage::fake('public');
});

test('landing page content can be created', function () {
    $content = LandingPageContent::factory()->create([
        'key' => 'test_key',
        'value' => 'Test Value',
        'type' => 'text',
    ]);

    expect($content)->toBeInstanceOf(LandingPageContent::class)
        ->and($content->key)->toBe('test_key')
        ->and($content->value)->toBe('Test Value')
        ->and($content->type)->toBe('text');
});

test('landing page content belongs to a section', function () {
    $section = LandingPageSection::factory()->create();
    $content = LandingPageContent::factory()->create([
        'section_id' => $section->id
    ]);

    expect($content->section)->toBeInstanceOf(LandingPageSection::class)
        ->and($content->section->id)->toBe($section->id);
});

test('landing page content can be ordered by section sort order', function () {
    // Create sections with different sort orders
    $section1 = LandingPageSection::factory()->create(['sort_order' => 2]);
    $section2 = LandingPageSection::factory()->create(['sort_order' => 1]);
    
    // Create content for each section
    $content1 = LandingPageContent::factory()->create(['section_id' => $section1->id]);
    $content2 = LandingPageContent::factory()->create(['section_id' => $section2->id]);
    
    // Get ordered content
    $orderedContents = LandingPageContent::ordered()->get();
    
    // The first content should be from section2 (sort_order 1)
    expect($orderedContents->first()->id)->toBe($content2->id);
});

test('get config value returns correct value', function () {
    // Create a config section
    $configSection = LandingPageSection::factory()->create([
        'slug' => 'config'
    ]);
    
    // Create a config content
    LandingPageContent::factory()->create([
        'section_id' => $configSection->id,
        'key' => 'test_config',
        'value' => 'config_value',
        'type' => 'text'
    ]);
    
    // Test the getConfigValue method
    $value = LandingPageContent::getConfigValue('test_config');
    $defaultValue = LandingPageContent::getConfigValue('non_existent', 'default');
    
    expect($value)->toBe('config_value')
        ->and($defaultValue)->toBe('default');
});

test('value attribute handles repeater type correctly', function () {
    $repeaterData = [
        ['title' => 'Item 1', 'description' => 'Description 1'],
        ['title' => 'Item 2', 'description' => 'Description 2']
    ];
    
    // Create content with repeater type
    $content = LandingPageContent::factory()->create([
        'key' => 'repeater_test',
        'value' => json_encode($repeaterData),
        'type' => 'repeater'
    ]);
    
    // Refresh to test the accessor
    $content->refresh();
    
    expect($content->value)->toBeArray()
        ->and($content->value)->toHaveCount(2)
        ->and($content->value[0]['title'])->toBe('Item 1');
    
    // Test the mutator by updating the value
    $newRepeaterData = [
        ['title' => 'New Item 1', 'description' => 'New Description 1']
    ];
    
    $content->value = $newRepeaterData;
    $content->save();
    $content->refresh();
    
    expect($content->value)->toBeArray()
        ->and($content->value)->toHaveCount(1)
        ->and($content->value[0]['title'])->toBe('New Item 1');
});

test('deleting content with image type deletes the file', function () {
    // Create a fake image file
    $imagePath = 'test-image.jpg';
    Storage::disk('public')->put($imagePath, 'fake image content');
    
    // Create content with image type
    $content = LandingPageContent::factory()->create([
        'key' => 'image_test',
        'value' => $imagePath,
        'type' => 'image'
    ]);
    
    // Verify the file exists
    expect(Storage::disk('public')->exists($imagePath))->toBeTrue();
    
    // Delete the content
    $content->delete();
    
    // Verify the file was deleted
    expect(Storage::disk('public')->exists($imagePath))->toBeFalse();
});

test('updating content with image type deletes the old file', function () {
    // Create fake image files
    $oldImagePath = 'old-image.jpg';
    $newImagePath = 'new-image.jpg';
    Storage::disk('public')->put($oldImagePath, 'old fake image content');
    Storage::disk('public')->put($newImagePath, 'new fake image content');
    
    // Create content with image type
    $content = LandingPageContent::factory()->create([
        'key' => 'image_update_test',
        'value' => $oldImagePath,
        'type' => 'image'
    ]);
    
    // Verify the old file exists
    expect(Storage::disk('public')->exists($oldImagePath))->toBeTrue();
    
    // Update the content with a new image
    $content->value = $newImagePath;
    $content->save();
    
    // Verify the old file was deleted and the new file exists
    expect(Storage::disk('public')->exists($oldImagePath))->toBeFalse()
        ->and(Storage::disk('public')->exists($newImagePath))->toBeTrue();
});