<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_proofs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('payment_id')->constrained('payments')->onDelete('cascade')->comment('Associated payment record');
            
            // File information
            $table->string('file_path', 500)->comment('Storage path of the uploaded file');
            $table->string('file_name')->comment('Original filename');
            $table->unsignedInteger('file_size')->comment('File size in bytes');
            $table->string('mime_type', 100)->comment('File MIME type');
            
            // Verification status
            $table->enum('verification_status', ['pending', 'approved', 'rejected'])->default('pending')->comment('Verification status');
            $table->text('verification_notes')->nullable()->comment('Admin notes for verification');
            $table->foreignId('verified_by')->nullable()->constrained('users')->onDelete('set null')->comment('Admin who verified');
            $table->timestamp('verified_at')->nullable()->comment('When verification was completed');
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index('verification_status', 'idx_payment_proofs_verification_status');
            $table->index(['payment_id', 'verification_status'], 'idx_payment_proofs_payment_status');
            $table->index('created_at', 'idx_payment_proofs_created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_proofs');
    }
};
