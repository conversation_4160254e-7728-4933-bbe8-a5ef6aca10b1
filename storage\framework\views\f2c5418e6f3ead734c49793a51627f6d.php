<?php $__env->startSection('title', 'Manage Landing Page'); ?>

<?php $__env->startSection('content'); ?>
<div class="container px-6 mx-auto grid text-text-primary-light dark:text-text-primary-dark">
    <div class="flex justify-between items-center">
        <h2 class="my-6 text-2xl font-semibold">
            Manage Landing Page Sections
        </h2>
    </div>

    <?php if(session('success')): ?>
    <div class="mb-4 px-4 py-3 leading-normal text-green-700 bg-green-100 dark:text-green-100 dark:bg-green-700/30 rounded-lg" role="alert">
        <?php echo e(session('success')); ?>

    </div>
    <?php endif; ?>

    <!-- Sections List -->
    <div class="w-full overflow-hidden rounded-lg shadow-xs mb-8">
        <div class="w-full overflow-x-auto">
            <table class="w-full whitespace-no-wrap" id="sortable-sections">
                <thead>
                    <tr class="text-xs font-semibold tracking-wide text-left text-text-secondary-light dark:text-text-secondary-dark uppercase border-b border-border-light dark:border-border-dark bg-bg-light dark:bg-bg-dark">
                        <th class="px-4 py-3">Order</th>
                        <th class="px-4 py-3">Name</th>
                        <th class="px-4 py-3">Slug</th>
                        <th class="px-4 py-3">Status</th>
                        <th class="px-4 py-3">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                    <?php $__currentLoopData = $sections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr data-id="<?php echo e($section->id); ?>">
                        <td class="px-4 py-3 cursor-move">
                            <span class="handle">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                                </svg>
                            </span>
                            <span class="ml-2"><?php echo e($section->sort_order); ?></span>
                        </td>
                        <td class="px-4 py-3"><?php echo e($section->name); ?></td>
                        <td class="px-4 py-3"><?php echo e($section->slug); ?></td>
                        <td class="px-4 py-3">
                            <span class="px-2 py-1 font-semibold leading-tight rounded-full <?php echo e($section->is_active ? 'text-green-700 bg-green-100 dark:text-green-100 dark:bg-green-700/30' : 'text-red-700 bg-red-100 dark:text-red-100 dark:bg-red-700/30'); ?>">
                                <?php echo e($section->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                        </td>
                        <td class="px-4 py-3 flex">
                            <a href="<?php echo e(route('admin.landing-page.edit', $section)); ?>" class="flex items-center justify-between px-2 py-2 text-sm font-medium leading-5 text-primary-light dark:text-primary-dark rounded-lg focus:outline-none focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20">
                                <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                                </svg>
                            </a>
                            <form action="<?php echo e(route('admin.landing-page.toggle-active', $section)); ?>" method="POST" class="inline">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PATCH'); ?>
                                <button type="submit" class="flex items-center justify-between px-2 py-2 text-sm font-medium leading-5 <?php echo e($section->is_active ? 'text-red-600' : 'text-green-600'); ?> rounded-lg focus:outline-none focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20">
                                    <?php if($section->is_active): ?>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd" />
                                    </svg>
                                    <?php else: ?>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                    <?php endif; ?>
                                </button>
                            </form>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const el = document.getElementById('sortable-sections').querySelector('tbody');
        const sortable = new Sortable(el, {
            handle: '.handle',
            animation: 150,
            onEnd: function() {
                const sections = [];
                document.querySelectorAll('#sortable-sections tbody tr').forEach(row => {
                    sections.push(row.getAttribute('data-id'));
                });
                
                fetch('<?php echo e(route("admin.landing-page.reorder")); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    body: JSON.stringify({ sections })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update the order numbers displayed
                        document.querySelectorAll('#sortable-sections tbody tr').forEach((row, index) => {
                            row.querySelector('td:first-child span:last-child').textContent = index + 1;
                        });
                    }
                });
            }
        });
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/admin/landing-page/index.blade.php ENDPATH**/ ?>