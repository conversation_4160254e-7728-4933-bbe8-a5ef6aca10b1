<?php

use App\Mail\ContactFormMail;
use App\Models\BlogPost;
use App\Models\Page;
use App\Models\State;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create test states
    State::factory()->count(3)->create();

    // Create test blog posts
    BlogPost::factory()->count(6)->create([
        'is_published' => true
    ]);

    // Create test pages
    Page::factory()->create([
        'title' => 'Test Page',
        'slug' => 'test-page',
        'content' => 'Test content',
        'template' => 'default',
        'is_active' => true
    ]);

    Page::factory()->create([
        'title' => 'Inactive Page',
        'slug' => 'inactive-page',
        'content' => 'Inactive content',
        'template' => 'default',
        'is_active' => false
    ]);
});

test('index page displays states and blog posts', function () {
    $response = $this->get(route('home'));

    $response->assertStatus(200)
        ->assertViewIs('home.welcome-modern-animated')
        ->assertViewHas('stateList')
        ->assertViewHas('pageTitle')
        ->assertViewHas('posts')
        ->assertSee('Pincode');
});

test('index page paginates blog posts', function () {
    // Create more blog posts to test pagination
    BlogPost::factory()->count(10)->create([
        'is_published' => true
    ]);

    $response = $this->get(route('home'));

    $response->assertStatus(200)
        ->assertViewHas('posts')
        ->assertViewIs('home.welcome-modern-animated');
});

test('page show displays active page', function () {
    $response = $this->get(route('pages.show', 'test-page'));

    $response->assertStatus(200)
        ->assertViewIs('pages.default')
        ->assertViewHas('page')
        ->assertSee('Test Page')
        ->assertSee('Test content');
});

test('page show returns 404 for inactive page', function () {
    $response = $this->get(route('pages.show', 'inactive-page'));

    $response->assertStatus(404);
});

test('page show returns 404 for non-existent page', function () {
    $response = $this->get(route('pages.show', 'non-existent-page'));

    $response->assertStatus(404);
});

test('contact page displays correctly', function () {
    $response = $this->get(route('pages.contact'));

    $response->assertStatus(200)
        ->assertViewIs('pages.contact-us')
        ->assertViewHas('title')
        ->assertSee('Welcome to BharatPostal Info');
});

test('contact form submission sends email', function () {
    Mail::fake();

    $response = $this->post(route('contact.submit'), [
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'subject' => 'Test Subject',
        'message' => 'Test Message'
    ]);

    $response->assertRedirect()
        ->assertSessionHas('success');

    Mail::assertQueued(ContactFormMail::class, function ($mail) {
        return $mail->data['name'] === 'John Doe' &&
            $mail->data['email'] === '<EMAIL>' &&
            $mail->data['subject'] === 'Test Subject' &&
            $mail->data['message'] === 'Test Message';
    });
});

test('contact form validates required fields', function () {
    $response = $this->post(route('contact.submit'), []);

    $response->assertSessionHasErrors(['name', 'email', 'subject', 'message']);
});

test('contact form validates email format', function () {
    $response = $this->post(route('contact.submit'), [
        'name' => 'John Doe',
        'email' => 'invalid-email',
        'subject' => 'Test Subject',
        'message' => 'Test Message'
    ]);

    $response->assertSessionHasErrors(['email']);
});

test('contact form handles email sending failure gracefully', function () {
    Mail::fake();
    Mail::shouldReceive('to')
        ->once()
        ->andThrow(new \Exception('Mail server error'));

    $response = $this->post(route('contact.submit'), [
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'subject' => 'Test Subject',
        'message' => 'Test Message'
    ]);

    $response->assertRedirect()
        ->assertSessionHas('error')
        ->assertSessionMissing('success');
});

test('about page displays correctly', function () {
    $response = $this->get(route('pages.about'));

    $response->assertStatus(200)
        ->assertViewIs('pages.about-us')
        ->assertViewHas('title')
        ->assertSee('Welcome to BharatPostal Info');
});