@extends('admin.layouts.admin')

@section('title', 'Create New Plan')

@section('content')
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-text-primary-light dark:text-text-primary-dark">Create New Plan</h1>
        </div>

        <div class="bg-white dark:bg-bg-dark shadow-sm rounded-lg border border-border-light dark:border-border-dark">
            <div class="px-6 py-4 border-b border-border-light dark:border-border-dark bg-gray-50 dark:bg-gray-800 rounded-t-lg">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-text-secondary-light dark:text-text-secondary-dark mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">Plan Details</h3>
                </div>
            </div>

            <div class="p-6">
                <form action="{{ route('admin.plans.store') }}" method="POST" class="space-y-6">
                    @csrf

                    <!-- Plan Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                            Plan Name
                        </label>
                        <input type="text"
                            class="w-full px-3 py-2 border border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark @error('name') border-red-500 focus:ring-red-500 focus:border-red-500 @enderror"
                            id="name" name="name" value="{{ old('name') }}" required>
                        @error('name')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Price -->
                    <div>
                        <label for="price" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                            Price ( 9)
                        </label>
                        <input type="number"
                            class="w-full px-3 py-2 border border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark @error('price') border-red-500 focus:ring-red-500 focus:border-red-500 @enderror"
                            id="price" name="price" value="{{ old('price') }}" step="0.01" min="0"
                            required>
                        @error('price')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Request Limit -->
                    <div>
                        <label for="request_limit" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                            Request Limit
                        </label>
                        <input type="number"
                            class="w-full px-3 py-2 border border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark @error('request_limit') border-red-500 focus:ring-red-500 focus:border-red-500 @enderror"
                            id="request_limit" name="request_limit" value="{{ old('request_limit') }}" min="1"
                            required>
                        @error('request_limit')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                            Description
                        </label>
                        <textarea
                            class="w-full px-3 py-2 border border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark @error('description') border-red-500 focus:ring-red-500 focus:border-red-500 @enderror"
                            id="description" name="description" rows="3" required>{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Features -->
                    <div>
                        <label class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                            Features
                        </label>
                        <div id="features-container" class="space-y-2">
                            @if (old('features'))
                                @foreach (old('features') as $feature)
                                    <div class="flex gap-2">
                                        <input type="text"
                                            class="flex-1 px-3 py-2 border border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark @error('features.*') border-red-500 focus:ring-red-500 focus:border-red-500 @enderror"
                                            name="features[]" value="{{ $feature }}" required>
                                        <button type="button"
                                            class="px-3 py-2 bg-red-600 dark:bg-red-500 text-white rounded-md hover:bg-red-700 dark:hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 dark:focus:ring-red-400 focus:ring-offset-2 dark:focus:ring-offset-bg-dark transition-colors remove-feature">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </button>
                                    </div>
                                @endforeach
                            @else
                                <div class="flex gap-2">
                                    <input type="text"
                                        class="flex-1 px-3 py-2 border border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark"
                                        name="features[]" required>
                                    <button type="button"
                                        class="px-3 py-2 bg-red-600 dark:bg-red-500 text-white rounded-md hover:bg-red-700 dark:hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 dark:focus:ring-red-400 focus:ring-offset-2 dark:focus:ring-offset-bg-dark transition-colors remove-feature">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                            @endif
                        </div>
                        <button type="button"
                            class="mt-3 inline-flex items-center px-3 py-2 border border-border-light dark:border-border-dark shadow-sm text-sm leading-4 font-medium rounded-md text-text-primary-light dark:text-text-primary-dark bg-white dark:bg-bg-dark hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-2 dark:focus:ring-offset-bg-dark transition-colors"
                            id="add-feature">
                            <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            Add Feature
                        </button>
                        @error('features')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Active Status -->
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox"
                                class="h-4 w-4 text-primary-light dark:text-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark border-border-light dark:border-border-dark rounded @error('is_active') border-red-500 @enderror"
                                id="is_active" name="is_active" value="1" {{ old('is_active') ? 'checked' : '' }}>
                            <label class="ml-2 block text-sm text-text-primary-light dark:text-text-primary-dark" for="is_active">
                                Active
                            </label>
                        </div>
                        @error('is_active')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-between items-center pt-6 border-t border-border-light dark:border-border-dark">
                        <a href="{{ route('admin.plans.index') }}"
                            class="inline-flex items-center px-4 py-2 border border-border-light dark:border-border-dark shadow-sm text-sm font-medium rounded-md text-text-primary-light dark:text-text-primary-dark bg-white dark:bg-bg-dark hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-2 dark:focus:ring-offset-bg-dark transition-colors">
                            Cancel
                        </a>
                        <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-2 dark:focus:ring-offset-bg-dark transition-colors">
                            Create Plan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const featuresContainer = document.getElementById('features-container');
            const addFeatureBtn = document.getElementById('add-feature');

            // Add new feature input
            addFeatureBtn.addEventListener('click', function() {
                const featureInput = document.createElement('div');
                featureInput.className = 'flex gap-2';
                featureInput.innerHTML = `
            <input type="text" class="flex-1 px-3 py-2 border border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark" name="features[]" required>
            <button type="button" class="px-3 py-2 bg-red-600 dark:bg-red-500 text-white rounded-md hover:bg-red-700 dark:hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 dark:focus:ring-red-400 focus:ring-offset-2 dark:focus:ring-offset-bg-dark transition-colors remove-feature">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        `;
                featuresContainer.appendChild(featureInput);
            });

            // Remove feature input
            featuresContainer.addEventListener('click', function(e) {
                if (e.target.closest('.remove-feature')) {
                    const featureInput = e.target.closest('.flex');
                    if (featuresContainer.children.length > 1) {
                        featureInput.remove();
                    }
                }
            });
        });
    </script>
@endpush
