<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\PaymentProof;
use App\Services\Payment\QRBankTransferService;
use App\Services\Payment\PaymentGatewayFactory;
use App\Services\Payment\FileUploadSecurityService;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class PaymentProofController extends Controller
{
    /**
     * Show payment proof upload form
     */
    public function show(Payment $payment)
    {
        // Ensure user can only access their own payments
        if ($payment->order->user_id !== Auth::id()) {
            abort(403);
        }

        // Only allow proof upload for QR bank transfer payments
        if ($payment->payment_method !== Payment::METHOD_QR_BANK_TRANSFER) {
            abort(404, 'Payment proof upload not available for this payment method');
        }

        // Get payment instructions from the service
        try {
            $service = PaymentGatewayFactory::create($payment->gateway);
            
            if ($service instanceof QRBankTransferService) {
                $instructions = $service->getPaymentInstructions($payment);
                $bankDetails = $service->getBankDetails();
            } else {
                throw new PaymentGatewayException('Invalid service type for QR bank transfer');
            }
        } catch (\Exception $e) {
            Log::error('Failed to get payment instructions', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);

            $instructions = [];
            $bankDetails = [];
        }

        $existingProofs = $payment->paymentProofs()->latest()->get();

        return view('user.payment.proof-upload', compact(
            'payment', 'instructions', 'bankDetails', 'existingProofs'
        ));
    }

    /**
     * Upload payment proof (for QR payment interface)
     */
    public function upload(Request $request)
    {
        // Validate the request
        $request->validate([
            'payment_id' => 'required|exists:payments,id',
            'proof_file' => 'required|file|mimes:jpeg,png,jpg,pdf|max:5120', // 5MB max
            'transaction_id' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:500',
        ]);

        $payment = Payment::findOrFail($request->payment_id);

        // Ensure user can only upload proof for their own payments
        if ($payment->order->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        // Only allow proof upload for QR bank transfer payments
        if ($payment->payment_method !== Payment::METHOD_QR_BANK_TRANSFER) {
            return response()->json([
                'success' => false,
                'message' => 'Payment proof upload not available for this payment method',
            ], 400);
        }

        // Validate the request
        $request->validate([
            'proof_file' => 'required|file|mimes:jpeg,png,jpg,pdf|max:5120', // 5MB max
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            // Get the QR bank transfer service
            $service = PaymentGatewayFactory::create($payment->gateway);
            
            if (!$service instanceof QRBankTransferService) {
                throw new PaymentGatewayException('Invalid service type for QR bank transfer');
            }

            // Upload the proof using the service
            $paymentProof = $service->uploadPaymentProof(
                $payment, 
                $request->file('proof_file'),
                ['notes' => $request->notes]
            );

            Log::info('Payment proof uploaded', [
                'payment_id' => $payment->id,
                'proof_id' => $paymentProof->id,
                'user_id' => Auth::id(),
                'file_name' => $paymentProof->file_name,
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment proof uploaded successfully. It will be reviewed by our team.',
                    'proof_id' => $paymentProof->id,
                    'status' => $paymentProof->verification_status,
                ]);
            }

            return redirect()->back()
                ->with('success', 'Payment proof uploaded successfully. It will be reviewed by our team.');

        } catch (PaymentGatewayException $e) {
            Log::error('Payment proof upload failed', [
                'payment_id' => $payment->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage(),
                ], 400);
            }

            return redirect()->back()
                ->with('error', $e->getMessage());

        } catch (\Exception $e) {
            Log::error('Payment proof upload error', [
                'payment_id' => $payment->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to upload payment proof. Please try again.',
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to upload payment proof. Please try again.');
        }
    }

    /**
     * Get payment proof status
     */
    public function status(Payment $payment)
    {
        // Ensure user can only check their own payments
        if ($payment->order->user_id !== Auth::id()) {
            abort(403);
        }

        $latestProof = $payment->latestPaymentProof;

        $response = [
            'payment_id' => $payment->id,
            'payment_status' => $payment->payment_status,
            'payment_status_display' => $payment->getPaymentStatusDisplayName(),
            'has_proof' => $latestProof !== null,
        ];

        if ($latestProof) {
            $response['proof'] = [
                'id' => $latestProof->id,
                'status' => $latestProof->verification_status,
                'status_display' => $latestProof->getVerificationStatusDisplayName(),
                'uploaded_at' => $latestProof->created_at->toISOString(),
                'verified_at' => $latestProof->verified_at?->toISOString(),
                'notes' => $latestProof->verification_notes,
            ];
        }

        return response()->json($response);
    }

    /**
     * Download payment proof (for user to view their own uploaded proof)
     */
    public function download(PaymentProof $proof)
    {
        // Ensure user can only download their own payment proofs
        if ($proof->payment->order->user_id !== Auth::id()) {
            abort(403);
        }

        if (!Storage::disk('private')->exists($proof->file_path)) {
            abort(404, 'File not found');
        }

        return response()->download(
            Storage::disk('private')->path($proof->file_path),
            $proof->file_name
        );
    }

    /**
     * Delete payment proof (allow user to delete and re-upload)
     */
    public function delete(PaymentProof $proof)
    {
        // Ensure user can only delete their own payment proofs
        if ($proof->payment->order->user_id !== Auth::id()) {
            abort(403);
        }

        // Only allow deletion of pending proofs
        if ($proof->verification_status !== PaymentProof::STATUS_PENDING) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete proof that has already been reviewed',
            ], 400);
        }

        try {
            // Delete the file from storage
            if (Storage::disk('private')->exists($proof->file_path)) {
                Storage::disk('private')->delete($proof->file_path);
            }

            // Delete the database record
            $proof->delete();

            Log::info('Payment proof deleted', [
                'proof_id' => $proof->id,
                'payment_id' => $proof->payment_id,
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment proof deleted successfully',
            ]);

        } catch (\Exception $e) {
            Log::error('Payment proof deletion error', [
                'proof_id' => $proof->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete payment proof',
            ], 500);
        }
    }

    /**
     * Get upload progress (for AJAX uploads)
     */
    public function uploadProgress(Request $request)
    {
        $sessionId = $request->input('session_id');
        
        if (!$sessionId) {
            return response()->json(['error' => 'Session ID required'], 400);
        }

        // This would typically check upload progress from session or cache
        // For now, return a simple response
        return response()->json([
            'uploaded' => 0,
            'total' => 0,
            'percentage' => 0,
        ]);
    }

    /**
     * Validate file before upload (AJAX validation)
     */
    public function validateFile(Request $request)
    {
        try {
            $request->validate([
                'file' => 'required|file|mimes:jpeg,png,jpg,pdf|max:5120',
            ]);

            $file = $request->file('file');
            
            return response()->json([
                'success' => true,
                'message' => 'File is valid',
                'file_info' => [
                    'name' => $file->getClientOriginalName(),
                    'size' => $file->getSize(),
                    'type' => $file->getMimeType(),
                    'size_formatted' => $this->formatFileSize($file->getSize()),
                ],
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'File validation failed',
                'errors' => $e->errors(),
            ], 422);
        }
    }

    /**
     * Get payment proof history
     */
    public function history(Payment $payment)
    {
        // Ensure user can only view their own payment history
        if ($payment->order->user_id !== Auth::id()) {
            abort(403);
        }

        $proofs = $payment->paymentProofs()
                         ->with('verifiedBy:id,name')
                         ->latest()
                         ->get()
                         ->map(function ($proof) {
                             return [
                                 'id' => $proof->id,
                                 'file_name' => $proof->file_name,
                                 'file_size' => $proof->getFormattedFileSize(),
                                 'status' => $proof->verification_status,
                                 'status_display' => $proof->getVerificationStatusDisplayName(),
                                 'status_color' => $proof->getVerificationStatusColor(),
                                 'uploaded_at' => $proof->created_at->format('M d, Y H:i'),
                                 'verified_at' => $proof->verified_at?->format('M d, Y H:i'),
                                 'verified_by' => $proof->verifiedBy?->name,
                                 'notes' => $proof->verification_notes,
                                 'can_delete' => $proof->isPending(),
                             ];
                         });

        return response()->json([
            'success' => true,
            'proofs' => $proofs,
        ]);
    }

    /**
     * Format file size for display
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
}