<?php

use App\Models\User;
use App\Models\Plan;
use App\Models\Order;

beforeEach(function () {
    // Create necessary settings to avoid cache issues
    \App\Models\Setting::create([
        'key' => 'site_favicon',
        'value' => 'favicon.ico',
        'type' => 'string',
        'group' => 'general',
        'is_public' => true,
        'is_encrypted' => false
    ]);

    \App\Models\Setting::create([
        'key' => 'site_logo',
        'value' => 'logo.png',
        'type' => 'string',
        'group' => 'general',
        'is_public' => true,
        'is_encrypted' => false
    ]);

    \App\Models\Setting::create([
        'key' => 'site_name',
        'value' => 'Test Site',
        'type' => 'string',
        'group' => 'general',
        'is_public' => true,
        'is_encrypted' => false
    ]);

    // Clear any existing cache to avoid conflicts
    \Illuminate\Support\Facades\Cache::flush();

    $this->admin = User::factory()->create([
        'role' => 'admin',
        'status' => 'active'
    ]);

    $this->actingAs($this->admin);
});

describe('Plan Management Integration', function () {
    
    describe('Plan Listing', function () {
        it('displays all plans with status', function () {
            Plan::factory()->count(3)->create(['is_active' => true]);
            Plan::factory()->count(2)->create(['is_active' => false]);

            // Test controller directly to avoid view cache issues
            $controller = new \App\Http\Controllers\Admin\PlanController();
            $view = $controller->index();

            expect($view)->toBeInstanceOf(\Illuminate\View\View::class);
            expect($view->getName())->toBe('admin.plans.index');
            expect($view->getData())->toHaveKey('plans');
            expect($view->getData()['plans'])->toHaveCount(5);
        });

        it('shows plan statistics', function () {
            $plan = Plan::factory()->create(['price' => 99.99]);
            Order::factory()->count(5)->create([
                'plan_id' => $plan->id,
                'status' => 'completed'
            ]);

            $response = $this->get(route('admin.plans.index'));

            $response->assertStatus(200);
        });
    });

    describe('Plan Creation', function () {
        it('creates new plan successfully', function () {
            $planData = [
                'name' => 'Premium Plan',
                'slug' => 'premium-plan',
                'description' => 'Premium features for power users',
                'price' => 29.99,
                'billing_cycle' => 'monthly',
                'features' => json_encode([
                    'Unlimited API calls',
                    'Priority support',
                    'Advanced analytics'
                ]),
                'request_limit' => 10000,
                'is_active' => true,
                'sort_order' => 1
            ];

            $response = $this->post(route('admin.plans.store'), $planData);

            $response->assertRedirect(route('admin.plans.index'));
            $response->assertSessionHas('success');
            
            $this->assertDatabaseHas('plans', [
                'name' => 'Premium Plan',
                'slug' => 'premium-plan',
                'price' => 29.99,
                'is_active' => true
            ]);
        });

        it('validates required fields for plan creation', function () {
            $response = $this->post(route('admin.plans.store'), []);

            $response->assertSessionHasErrors([
                'name', 'price', 'billing_cycle', 'request_limit'
            ]);
        });

        it('validates unique slug for plans', function () {
            Plan::factory()->create(['slug' => 'existing-plan']);

            $response = $this->post(route('admin.plans.store'), [
                'name' => 'New Plan',
                'slug' => 'existing-plan',
                'price' => 19.99,
                'billing_cycle' => 'monthly',
                'request_limit' => 5000,
                'is_active' => true
            ]);

            $response->assertSessionHasErrors(['slug']);
        });

        it('validates price format', function () {
            $response = $this->post(route('admin.plans.store'), [
                'name' => 'Test Plan',
                'slug' => 'test-plan',
                'price' => 'invalid-price',
                'billing_cycle' => 'monthly',
                'request_limit' => 5000,
                'is_active' => true
            ]);

            $response->assertSessionHasErrors(['price']);
        });

        it('validates request limit is numeric', function () {
            $response = $this->post(route('admin.plans.store'), [
                'name' => 'Test Plan',
                'slug' => 'test-plan',
                'price' => 19.99,
                'billing_cycle' => 'monthly',
                'request_limit' => 'unlimited',
                'is_active' => true
            ]);

            $response->assertSessionHasErrors(['request_limit']);
        });
    });

    describe('Plan Editing', function () {
        it('displays plan edit form', function () {
            $plan = Plan::factory()->create();

            $response = $this->get(route('admin.plans.edit', $plan));

            $response->assertStatus(200);
            $response->assertViewIs('admin.plans.edit');
            $response->assertViewHas('plan', $plan);
        });

        it('updates plan successfully', function () {
            $plan = Plan::factory()->create();

            $updateData = [
                'name' => 'Updated Plan Name',
                'slug' => 'updated-plan-name',
                'description' => 'Updated description',
                'price' => 39.99,
                'billing_cycle' => 'yearly',
                'request_limit' => 20000,
                'is_active' => false,
                'sort_order' => 5
            ];

            $response = $this->put(route('admin.plans.update', $plan), $updateData);

            $response->assertRedirect(route('admin.plans.index'));
            $response->assertSessionHas('success');
            
            $plan->refresh();
            expect($plan->name)->toBe('Updated Plan Name');
            expect((float) $plan->price)->toBe(39.99);
            expect($plan->is_active)->toBe(false);
        });

        it('validates unique slug when updating', function () {
            $plan1 = Plan::factory()->create(['slug' => 'plan-one']);
            $plan2 = Plan::factory()->create(['slug' => 'plan-two']);

            $response = $this->put(route('admin.plans.update', $plan1), [
                'name' => $plan1->name,
                'slug' => 'plan-two',
                'price' => $plan1->price,
                'billing_cycle' => $plan1->billing_cycle,
                'request_limit' => $plan1->request_limit,
                'is_active' => $plan1->is_active
            ]);

            $response->assertSessionHasErrors(['slug']);
        });

        it('allows keeping same slug when updating', function () {
            $plan = Plan::factory()->create();

            $response = $this->put(route('admin.plans.update', $plan), [
                'name' => 'Updated Name',
                'slug' => $plan->slug,
                'price' => $plan->price,
                'billing_cycle' => $plan->billing_cycle,
                'request_limit' => $plan->request_limit,
                'is_active' => $plan->is_active
            ]);

            $response->assertRedirect(route('admin.plans.index'));
            $response->assertSessionHasNoErrors();
        });
    });

    describe('Plan Status Management', function () {
        it('toggles plan status successfully', function () {
            $plan = Plan::factory()->create(['is_active' => true]);

            $response = $this->patch(route('admin.plans.toggle-status', $plan));

            $response->assertRedirect(route('admin.plans.index'));
            $response->assertSessionHas('success');
            
            $plan->refresh();
            expect($plan->is_active)->toBe(false);
        });

        it('activates inactive plan', function () {
            $plan = Plan::factory()->create(['is_active' => false]);

            $response = $this->patch(route('admin.plans.toggle-status', $plan));

            $response->assertRedirect(route('admin.plans.index'));
            
            $plan->refresh();
            expect($plan->is_active)->toBe(true);
        });
    });

    describe('Plan Deletion', function () {
        it('deletes plan without orders', function () {
            $plan = Plan::factory()->create();

            $response = $this->delete(route('admin.plans.destroy', $plan));

            $response->assertRedirect(route('admin.plans.index'));
            $response->assertSessionHas('success');
            
            $this->assertDatabaseMissing('plans', ['id' => $plan->id]);
        });

        it('prevents deletion of plan with orders', function () {
            $plan = Plan::factory()->create();
            Order::factory()->pending()->create(['plan_id' => $plan->id]);

            $response = $this->delete(route('admin.plans.destroy', $plan));

            $response->assertRedirect(route('admin.plans.index'));
            $response->assertSessionHas('error');

            $this->assertDatabaseHas('plans', ['id' => $plan->id]);
        });

        it('soft deletes plan with completed orders', function () {
            $plan = Plan::factory()->create();
            Order::factory()->create([
                'plan_id' => $plan->id,
                'status' => 'completed'
            ]);

            $response = $this->delete(route('admin.plans.destroy', $plan));

            $response->assertRedirect(route('admin.plans.index'));
            
            // Should soft delete instead of hard delete
            $this->assertSoftDeleted('plans', ['id' => $plan->id]);
        });
    });

    describe('Plan Features Management', function () {
        it('updates plan features as JSON', function () {
            $plan = Plan::factory()->create();

            $features = [
                'API Access',
                'Email Support',
                'Monthly Reports',
                'Custom Integrations'
            ];

            $response = $this->put(route('admin.plans.update', $plan), [
                'name' => $plan->name,
                'slug' => $plan->slug,
                'price' => $plan->price,
                'billing_cycle' => $plan->billing_cycle,
                'request_limit' => $plan->request_limit,
                'features' => json_encode($features),
                'is_active' => $plan->is_active
            ]);

            $response->assertRedirect(route('admin.plans.index'));
            
            $plan->refresh();
            expect($plan->features)->toBe($features);
        });

        it('handles empty features array', function () {
            $plan = Plan::factory()->create();

            $response = $this->put(route('admin.plans.update', $plan), [
                'name' => $plan->name,
                'slug' => $plan->slug,
                'price' => $plan->price,
                'billing_cycle' => $plan->billing_cycle,
                'request_limit' => $plan->request_limit,
                'features' => json_encode([]),
                'is_active' => $plan->is_active
            ]);

            $response->assertRedirect(route('admin.plans.index'));
            $response->assertSessionHasNoErrors();
        });
    });

    describe('Plan Analytics', function () {
        it('shows plan subscription statistics', function () {
            $plan = Plan::factory()->create();
            
            // Create orders for different statuses
            Order::factory()->count(5)->create([
                'plan_id' => $plan->id,
                'status' => 'completed'
            ]);
            Order::factory()->count(2)->create([
                'plan_id' => $plan->id,
                'status' => 'pending'
            ]);

            $response = $this->get(route('admin.plans.index'));

            $response->assertStatus(200);
        });

        it('calculates plan revenue correctly', function () {
            $plan = Plan::factory()->create(['price' => 50.00]);
            
            Order::factory()->count(3)->create([
                'plan_id' => $plan->id,
                'amount' => 50.00,
                'status' => 'completed'
            ]);

            $response = $this->get(route('admin.plans.index'));

            $response->assertStatus(200);
        });
    });

    describe('Plan Sorting', function () {
        it('updates plan sort order', function () {
            $plan1 = Plan::factory()->create(['sort_order' => 1]);
            $plan2 = Plan::factory()->create(['sort_order' => 2]);

            $response = $this->put(route('admin.plans.update', $plan1), [
                'name' => $plan1->name,
                'slug' => $plan1->slug,
                'price' => $plan1->price,
                'billing_cycle' => $plan1->billing_cycle,
                'request_limit' => $plan1->request_limit,
                'sort_order' => 5,
                'is_active' => $plan1->is_active
            ]);

            $response->assertRedirect(route('admin.plans.index'));
            
            $plan1->refresh();
            expect($plan1->sort_order)->toBe(5);
        });

        it('validates sort order is numeric', function () {
            $plan = Plan::factory()->create();

            $response = $this->put(route('admin.plans.update', $plan), [
                'name' => $plan->name,
                'slug' => $plan->slug,
                'price' => $plan->price,
                'billing_cycle' => $plan->billing_cycle,
                'request_limit' => $plan->request_limit,
                'sort_order' => 'first',
                'is_active' => $plan->is_active
            ]);

            $response->assertSessionHasErrors(['sort_order']);
        });
    });

    describe('Authorization', function () {
        it('prevents non-admin from accessing plan management', function () {
            $regularUser = User::factory()->create(['role' => 'user']);
            $this->actingAs($regularUser);

            $response = $this->get(route('admin.plans.index'));

            $response->assertStatus(403);
        });

        it('prevents guest from accessing plan management', function () {
            auth()->logout();

            $response = $this->get(route('admin.plans.index'));

            $response->assertRedirect(route('admin.login'));
        });
    });
});