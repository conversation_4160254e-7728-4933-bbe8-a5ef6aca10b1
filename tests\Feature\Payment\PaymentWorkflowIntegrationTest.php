<?php

namespace Tests\Feature\Payment;

use App\Models\User;
use App\Models\Order;
use App\Models\Payment;
use App\Models\PaymentGateway;
use App\Models\PaymentProof;
use App\Models\Plan;
use App\Services\Payment\PaymentGatewayManager;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class PaymentWorkflowIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Plan $plan;
    protected PaymentGateway $razorpayGateway;
    protected PaymentGateway $qrGateway;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->plan = Plan::factory()->create([
            'price' => 1000.00,
            'currency' => 'INR',
            'is_active' => true
        ]);

        // Create payment gateways
        $this->razorpayGateway = PaymentGateway::factory()->create([
            'name' => 'razorpay',
            'display_name' => 'Razorpay',
            'is_active' => true,
            'configuration' => [
                'key_id' => 'rzp_test_**********',
                'key_secret' => 'test_secret_key',
                'webhook_secret' => 'test_webhook_secret'
            ],
            'supported_currencies' => ['INR']
        ]);

        $this->qrGateway = PaymentGateway::factory()->create([
            'name' => 'qr_bank_transfer',
            'display_name' => 'Bank Transfer (QR)',
            'is_active' => true,
            'configuration' => [
                'bank_name' => 'Test Bank',
                'account_name' => 'Test Company Ltd',
                'account_number' => '**********',
                'ifsc_code' => 'TEST0001234',
                'upi_id' => 'testcompany@testbank'
            ],
            'supported_currencies' => ['INR']
        ]);

        Storage::fake('private');
    }

    public function test_complete_razorpay_payment_workflow()
    {
        $this->actingAs($this->user);

        // Step 1: Create order
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->plan->id,
            'amount' => $this->plan->price,
            'currency' => 'INR',
            'status' => Order::STATUS_PENDING
        ]);

        // Step 2: Initiate payment
        $response = $this->post(route('payment.create'), [
            'order_id' => $order->id,
            'gateway_id' => $this->razorpayGateway->id,
            'amount' => $order->amount,
            'currency' => $order->currency,
            'csrf_token' => 'test_token' // Would be generated in real scenario
        ]);

        // Should redirect to Razorpay checkout or show checkout page
        $this->assertTrue(
            $response->isRedirection() || 
            $response->isSuccessful()
        );

        // Step 3: Verify payment record was created
        $payment = Payment::where('order_id', $order->id)->first();
        $this->assertNotNull($payment);
        $this->assertEquals($this->razorpayGateway->id, $payment->gateway_id);
        $this->assertEquals('razorpay', $payment->payment_method);
        $this->assertEquals(Payment::STATUS_PENDING, $payment->payment_status);

        // Step 4: Simulate successful payment callback
        $response = $this->post(route('user.payment.razorpay.verify'), [
            'order_id' => $order->id,
            'razorpay_payment_id' => 'pay_test123',
            'razorpay_order_id' => 'order_test123',
            'razorpay_signature' => 'test_signature',
            'customer_details' => [
                'name' => $this->user->name,
                'email' => $this->user->email
            ]
        ]);

        $response->assertJson(['success' => true]);

        // Step 5: Verify payment and order status updated
        $payment->refresh();
        $order->refresh();

        $this->assertEquals(Payment::STATUS_COMPLETED, $payment->payment_status);
        $this->assertEquals(Order::STATUS_COMPLETED, $order->status);
        $this->assertNotNull($payment->paid_at);
    }

    public function test_complete_qr_bank_transfer_workflow()
    {
        $this->actingAs($this->user);

        // Step 1: Create order
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->plan->id,
            'amount' => $this->plan->price,
            'currency' => 'INR',
            'status' => Order::STATUS_PENDING
        ]);

        // Step 2: Initiate QR payment
        $response = $this->post(route('payment.create'), [
            'order_id' => $order->id,
            'gateway_id' => $this->qrGateway->id,
            'amount' => $order->amount,
            'currency' => $order->currency,
            'csrf_token' => 'test_token'
        ]);

        $this->assertTrue(
            $response->isRedirection() || 
            $response->isSuccessful()
        );

        // Step 3: Verify payment record was created
        $payment = Payment::where('order_id', $order->id)->first();
        $this->assertNotNull($payment);
        $this->assertEquals($this->qrGateway->id, $payment->gateway_id);
        $this->assertEquals('qr_bank_transfer', $payment->payment_method);
        $this->assertEquals(Payment::STATUS_PENDING, $payment->payment_status);

        // Step 4: Upload payment proof
        $file = UploadedFile::fake()->image('payment_proof.jpg', 800, 600);
        
        $response = $this->post(route('user.payment.qr.upload-proof'), [
            'payment_id' => $payment->id,
            'payment_proof' => $file,
            'csrf_token' => 'test_token'
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Step 5: Verify payment proof was created
        $paymentProof = PaymentProof::where('payment_id', $payment->id)->first();
        $this->assertNotNull($paymentProof);
        $this->assertEquals(PaymentProof::STATUS_PENDING, $paymentProof->verification_status);

        // Step 6: Admin approves payment
        $admin = User::factory()->create(['is_admin' => true]);
        $this->actingAs($admin);

        $response = $this->post(route('admin.payment-verification.approve', $payment), [
            'admin_notes' => 'Payment verified successfully'
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Step 7: Verify payment and order status updated
        $payment->refresh();
        $order->refresh();
        $paymentProof->refresh();

        $this->assertEquals(Payment::STATUS_COMPLETED, $payment->payment_status);
        $this->assertEquals(Order::STATUS_COMPLETED, $order->status);
        $this->assertEquals(PaymentProof::STATUS_APPROVED, $paymentProof->verification_status);
        $this->assertNotNull($payment->verified_at);
        $this->assertEquals($admin->id, $payment->verified_by);
    }

    public function test_payment_failure_workflow()
    {
        $this->actingAs($this->user);

        // Step 1: Create order
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->plan->id,
            'amount' => $this->plan->price,
            'currency' => 'INR',
            'status' => Order::STATUS_PENDING
        ]);

        // Step 2: Initiate payment
        $payment = Payment::factory()->create([
            'order_id' => $order->id,
            'gateway_id' => $this->razorpayGateway->id,
            'payment_method' => 'razorpay',
            'payment_status' => Payment::STATUS_PENDING,
            'amount' => $order->amount,
            'currency' => $order->currency
        ]);

        // Step 3: Simulate payment failure
        $response = $this->post(route('user.payment.razorpay.error'), [
            'order_id' => $order->id,
            'error' => [
                'code' => 'BAD_REQUEST_ERROR',
                'description' => 'Payment failed due to insufficient funds'
            ]
        ]);

        $response->assertJson(['success' => true]);

        // Step 4: Verify payment and order status updated
        $payment->refresh();
        $order->refresh();

        $this->assertEquals(Payment::STATUS_FAILED, $payment->payment_status);
        $this->assertEquals(Order::STATUS_FAILED, $order->status);
        $this->assertStringContainsString('Payment failed', $payment->failed_reason);
    }

    public function test_payment_cancellation_workflow()
    {
        $this->actingAs($this->user);

        // Step 1: Create order and payment
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->plan->id,
            'status' => Order::STATUS_PENDING
        ]);

        $payment = Payment::factory()->create([
            'order_id' => $order->id,
            'gateway_id' => $this->razorpayGateway->id,
            'payment_status' => Payment::STATUS_PENDING
        ]);

        // Step 2: Cancel payment
        $response = $this->get(route('payment.cancel', ['order_id' => $order->id]));

        $response->assertRedirect();

        // Step 3: Verify order and payment status
        $order->refresh();
        $payment->refresh();

        $this->assertEquals(Order::STATUS_CANCELLED, $order->status);
        $this->assertEquals(Payment::STATUS_FAILED, $payment->payment_status);
    }

    public function test_qr_payment_rejection_workflow()
    {
        $this->actingAs($this->user);

        // Step 1: Create order and payment with proof
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->plan->id,
            'status' => Order::STATUS_PENDING
        ]);

        $payment = Payment::factory()->create([
            'order_id' => $order->id,
            'gateway_id' => $this->qrGateway->id,
            'payment_method' => 'qr_bank_transfer',
            'payment_status' => Payment::STATUS_PENDING
        ]);

        $paymentProof = PaymentProof::factory()->create([
            'payment_id' => $payment->id,
            'verification_status' => PaymentProof::STATUS_PENDING
        ]);

        // Step 2: Admin rejects payment
        $admin = User::factory()->create(['is_admin' => true]);
        $this->actingAs($admin);

        $response = $this->post(route('admin.payment-verification.reject', $payment), [
            'admin_notes' => 'Invalid payment proof - amount mismatch',
            'rejection_reason' => 'insufficient_amount'
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Step 3: Verify payment and order status updated
        $payment->refresh();
        $order->refresh();
        $paymentProof->refresh();

        $this->assertEquals(Payment::STATUS_FAILED, $payment->payment_status);
        $this->assertEquals(Order::STATUS_FAILED, $order->status);
        $this->assertEquals(PaymentProof::STATUS_REJECTED, $paymentProof->verification_status);
        $this->assertEquals('Invalid payment proof - amount mismatch', $paymentProof->admin_notes);
    }

    public function test_multiple_payment_attempts_workflow()
    {
        $this->actingAs($this->user);

        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->plan->id,
            'status' => Order::STATUS_PENDING
        ]);

        // First payment attempt fails
        $firstPayment = Payment::factory()->create([
            'order_id' => $order->id,
            'gateway_id' => $this->razorpayGateway->id,
            'payment_status' => Payment::STATUS_FAILED
        ]);

        // Second payment attempt succeeds
        $response = $this->post(route('payment.create'), [
            'order_id' => $order->id,
            'gateway_id' => $this->razorpayGateway->id,
            'amount' => $order->amount,
            'currency' => $order->currency,
            'csrf_token' => 'test_token'
        ]);

        $this->assertTrue(
            $response->isRedirection() || 
            $response->isSuccessful()
        );

        // Verify second payment was created
        $secondPayment = Payment::where('order_id', $order->id)
                               ->where('payment_status', Payment::STATUS_PENDING)
                               ->first();
        
        $this->assertNotNull($secondPayment);
        $this->assertNotEquals($firstPayment->id, $secondPayment->id);
    }

    public function test_payment_status_checking_workflow()
    {
        $this->actingAs($this->user);

        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'status' => Order::STATUS_COMPLETED
        ]);

        $payment = Payment::factory()->create([
            'order_id' => $order->id,
            'payment_status' => Payment::STATUS_COMPLETED
        ]);

        // Check payment status
        $response = $this->get(route('user.payment.status', $order));

        $response->assertJson([
            'success' => true,
            'status' => Order::PAYMENT_STATUS_PAID,
            'order_status' => Order::STATUS_COMPLETED
        ]);
    }

    public function test_unauthorized_payment_access_prevention()
    {
        $otherUser = User::factory()->create();
        $this->actingAs($otherUser);

        $order = Order::factory()->create([
            'user_id' => $this->user->id, // Different user
            'status' => Order::STATUS_PENDING
        ]);

        // Try to create payment for another user's order
        $response = $this->post(route('payment.create'), [
            'order_id' => $order->id,
            'gateway_id' => $this->razorpayGateway->id,
            'amount' => $order->amount,
            'currency' => $order->currency,
            'csrf_token' => 'test_token'
        ]);

        $response->assertStatus(403);
    }

    public function test_payment_gateway_unavailable_handling()
    {
        $this->actingAs($this->user);

        // Disable the gateway
        $this->razorpayGateway->update(['is_active' => false]);

        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->plan->id,
            'status' => Order::STATUS_PENDING
        ]);

        $response = $this->post(route('payment.create'), [
            'order_id' => $order->id,
            'gateway_id' => $this->razorpayGateway->id,
            'amount' => $order->amount,
            'currency' => $order->currency,
            'csrf_token' => 'test_token'
        ]);

        // Should handle gracefully (redirect with error or show error page)
        $this->assertTrue(
            $response->isRedirection() || 
            $response->status() >= 400
        );
    }

    public function test_concurrent_payment_attempts_handling()
    {
        $this->actingAs($this->user);

        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->plan->id,
            'status' => Order::STATUS_PENDING
        ]);

        // Simulate concurrent payment attempts
        $responses = [];
        for ($i = 0; $i < 3; $i++) {
            $responses[] = $this->post(route('payment.create'), [
                'order_id' => $order->id,
                'gateway_id' => $this->razorpayGateway->id,
                'amount' => $order->amount,
                'currency' => $order->currency,
                'csrf_token' => 'test_token_' . $i
            ]);
        }

        // Should handle gracefully - either all succeed or some are rejected
        foreach ($responses as $response) {
            $this->assertTrue(
                $response->isSuccessful() || 
                $response->isRedirection() ||
                $response->status() >= 400
            );
        }

        // Verify only reasonable number of payments created
        $paymentCount = Payment::where('order_id', $order->id)->count();
        $this->assertLessThanOrEqual(3, $paymentCount);
    }
}