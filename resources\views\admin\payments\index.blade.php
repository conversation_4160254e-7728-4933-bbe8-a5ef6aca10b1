@extends('admin.layouts.admin')

@section('title', 'Payment Management')

@section('content')
<div class="py-4">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-bg-dark overflow-hidden shadow-sm sm:rounded-lg border border-border-light dark:border-border-dark">
            <div class="p-4 sm:p-6 bg-white dark:bg-bg-dark border-b border-border-light dark:border-border-dark">
                <div class="flex justify-between items-center mb-4 sm:mb-6">
                    <h1 class="text-xl sm:text-2xl font-semibold text-text-primary-light dark:text-text-primary-dark">Payment Management</h1>
                </div>

                <!-- Desktop Table View -->
                <div class="hidden md:block overflow-x-auto">
                    <table class="min-w-full divide-y divide-border-light dark:divide-border-dark">
                        <thead class="bg-gray-50 dark:bg-gray-800">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                    Payment ID
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                    Order
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                    User
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                    Amount
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                    Status
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                    Date
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                            @forelse($payments as $payment)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark">
                                    {{ $payment->payment_id }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark">
                                    {{ $payment->order->order_number }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark">
                                    {{ $payment->order->user->name }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark">
                                    {{ $payment->currency }} {{ number_format($payment->amount, 2) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        @if($payment->payment_status === 'completed') bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400
                                        @elseif($payment->payment_status === 'refunded') bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400
                                        @elseif($payment->payment_status === 'failed') bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400
                                        @else bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200
                                        @endif">
                                        {{ ucfirst($payment->payment_status) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark">
                                    {{ $payment->paid_at ? $payment->paid_at->format('M d, Y H:i:s') : 'N/A' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{{ route('admin.payments.show', $payment) }}" 
                                       class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light">View Details</a>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="px-6 py-12 text-center">
                                    <div class="flex flex-col items-center">
                                        <svg class="w-12 h-12 text-gray-400 dark:text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                        <h3 class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">No payments found</h3>
                                        <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">No payment records are available at the moment.</p>
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Mobile Card View -->
                <div class="md:hidden space-y-4">
                    @forelse($payments as $payment)
                        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-border-light dark:border-border-dark shadow-sm">
                            <!-- Payment Header -->
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center mb-1">
                                        <span class="text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark mr-2">Payment ID:</span>
                                        <span class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark truncate">
                                            {{ $payment->payment_id }}
                                        </span>
                                    </div>
                                    <div class="flex items-center">
                                        <span class="text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark mr-2">Order:</span>
                                        <span class="text-sm text-text-primary-light dark:text-text-primary-dark">
                                            {{ $payment->order->order_number }}
                                        </span>
                                    </div>
                                </div>
                                
                                <!-- Status Badge -->
                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ml-3 
                                    @if($payment->payment_status === 'completed') bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400
                                    @elseif($payment->payment_status === 'refunded') bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400
                                    @elseif($payment->payment_status === 'failed') bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400
                                    @else bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200
                                    @endif">
                                    {{ ucfirst($payment->payment_status) }}
                                </span>
                            </div>
                            
                            <!-- Payment Details -->
                            <div class="grid grid-cols-1 gap-2 text-sm mb-3">
                                <div class="flex justify-between">
                                    <span class="font-medium text-text-secondary-light dark:text-text-secondary-dark">User:</span>
                                    <span class="text-text-primary-light dark:text-text-primary-dark">{{ $payment->order->user->name }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-text-secondary-light dark:text-text-secondary-dark">Amount:</span>
                                    <span class="text-text-primary-light dark:text-text-primary-dark font-semibold">
                                        {{ $payment->currency }} {{ number_format($payment->amount, 2) }}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-text-secondary-light dark:text-text-secondary-dark">Date:</span>
                                    <span class="text-text-secondary-light dark:text-text-secondary-dark text-xs">
                                        {{ $payment->paid_at ? $payment->paid_at->format('M d, Y H:i') : 'N/A' }}
                                    </span>
                                </div>
                            </div>
                            
                            <!-- Mobile Actions -->
                            <div class="pt-3 border-t border-border-light dark:border-border-dark">
                                <a href="{{ route('admin.payments.show', $payment) }}" 
                                   class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-light dark:text-primary-dark bg-primary-light/10 dark:bg-primary-dark/10 hover:bg-primary-light/20 dark:hover:bg-primary-dark/20 focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-2 transition-colors">
                                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                    View Details
                                </a>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-12">
                            <div class="flex flex-col items-center">
                                <svg class="w-12 h-12 text-gray-400 dark:text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                <h3 class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">No payments found</h3>
                                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">No payment records are available at the moment.</p>
                            </div>
                        </div>
                    @endforelse
                </div>
                
                <!-- Pagination -->
                <div class="mt-6">
                    {{ $payments->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection