<?php

namespace Tests\Unit\Payment;

use App\Models\PaymentGateway;
use App\Services\Payment\WebhookSecurityService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class WebhookSecurityTest extends TestCase
{
    use RefreshDatabase;

    protected WebhookSecurityService $service;
    protected PaymentGateway $gateway;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new WebhookSecurityService();
        
        $this->gateway = PaymentGateway::factory()->create([
            'name' => 'razorpay_' . uniqid(),
            'webhook_secret' => 'test_webhook_secret',
            'is_active' => true
        ]);
    }

    public function test_verifies_valid_razorpay_signature()
    {
        $payload = json_encode(['event' => 'payment.captured']);
        $signature = hash_hmac('sha256', $payload, 'test_webhook_secret');

        $request = Request::create('/webhook', 'POST', [], [], [], [
            'HTTP_X_RAZORPAY_SIGNATURE' => $signature,
            'CONTENT_TYPE' => 'application/json'
        ], $payload);

        $result = $this->service->verifyWebhookSignature($request, $this->gateway);

        $this->assertTrue($result['valid']);
    }

    public function test_rejects_invalid_razorpay_signature()
    {
        $payload = json_encode(['event' => 'payment.captured']);
        $invalidSignature = 'invalid_signature';

        $request = Request::create('/webhook', 'POST', [], [], [], [
            'HTTP_X_RAZORPAY_SIGNATURE' => $invalidSignature,
            'CONTENT_TYPE' => 'application/json'
        ], $payload);

        $result = $this->service->verifyWebhookSignature($request, $this->gateway);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Invalid signature', $result['error']);
    }

    public function test_rejects_missing_signature_header()
    {
        $payload = json_encode(['event' => 'payment.captured']);

        $request = Request::create('/webhook', 'POST', [], [], [], [
            'CONTENT_TYPE' => 'application/json'
        ], $payload);

        $result = $this->service->verifyWebhookSignature($request, $this->gateway);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Missing signature header', $result['error']);
    }

    public function test_validates_webhook_source_ip()
    {
        $allowedIps = ['*************', '********'];
        $request = Request::create('/webhook', 'POST');
        $request->server->set('REMOTE_ADDR', '*************');

        $result = $this->service->validateSourceIP($request, $allowedIps);

        $this->assertTrue($result['valid']);
    }

    public function test_rejects_unauthorized_ip()
    {
        $allowedIps = ['*************', '********'];
        $request = Request::create('/webhook', 'POST');
        $request->server->set('REMOTE_ADDR', '*************'); // Not in allowed list

        $result = $this->service->validateSourceIP($request, $allowedIps);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Unauthorized IP', $result['error']);
    }

    public function test_handles_x_forwarded_for_header()
    {
        $allowedIps = ['***********'];
        $request = Request::create('/webhook', 'POST');
        $request->headers->set('X-Forwarded-For', '***********, ***********');

        $result = $this->service->validateSourceIP($request, $allowedIps);

        $this->assertTrue($result['valid']);
    }

    public function test_validates_webhook_payload_structure()
    {
        $validPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'amount' => 100000,
                        'currency' => 'INR'
                    ]
                ]
            ]
        ];

        $result = $this->service->validatePayloadStructure($validPayload, 'razorpay');

        $this->assertTrue($result['valid']);
    }

    public function test_rejects_invalid_payload_structure()
    {
        $invalidPayload = [
            'invalid' => 'structure'
        ];

        $result = $this->service->validatePayloadStructure($invalidPayload, 'razorpay');

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Invalid payload structure', $result['error']);
    }

    public function test_detects_replay_attacks()
    {
        $webhookId = 'whook_test123';
        $timestamp = now()->timestamp;

        // First request should be valid
        $result1 = $this->service->checkReplayAttack($webhookId, $timestamp);
        $this->assertTrue($result1['valid']);

        // Second request with same webhook ID should be rejected
        $result2 = $this->service->checkReplayAttack($webhookId, $timestamp);
        $this->assertFalse($result2['valid']);
        $this->assertStringContainsString('Duplicate webhook', $result2['error']);
    }

    public function test_rejects_old_timestamps()
    {
        $oldTimestamp = now()->subMinutes(10)->timestamp; // 10 minutes old
        $webhookId = 'whook_old_' . uniqid();

        $result = $this->service->checkReplayAttack($webhookId, $oldTimestamp);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Webhook too old', $result['error']);
    }

    public function test_rate_limits_webhook_requests()
    {
        $request = Request::create('/webhook', 'POST');
        $request->server->set('REMOTE_ADDR', '*************');

        // Send requests up to the burst limit (10 requests in 10 seconds)
        for ($i = 0; $i < 10; $i++) {
            $result = $this->service->checkRateLimit($request, $this->gateway);
            $this->assertTrue($result['allowed'], "Request $i should be allowed");
        }

        // Next request should be rate limited due to burst protection
        $result = $this->service->checkRateLimit($request, $this->gateway);
        $this->assertFalse($result['allowed']);
        $this->assertStringContainsString('Rate limit exceeded', $result['error']);
    }

    public function test_logs_security_violations()
    {
        Log::shouldReceive('warning')
           ->once()
           ->with('Webhook security violation', \Mockery::type('array'));

        $payload = json_encode(['event' => 'payment.captured']);
        $invalidSignature = 'invalid_signature';

        $request = Request::create('/webhook', 'POST', [], [], [], [
            'HTTP_X_RAZORPAY_SIGNATURE' => $invalidSignature,
            'CONTENT_TYPE' => 'application/json'
        ], $payload);

        $this->service->verifyWebhookSignature($request, $this->gateway);
    }

    public function test_validates_content_type()
    {
        $request = Request::create('/webhook', 'POST', [], [], [], [
            'CONTENT_TYPE' => 'text/plain'
        ]);

        $result = $this->service->validateContentType($request);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Invalid content type', $result['error']);
    }

    public function test_accepts_valid_content_types()
    {
        $validTypes = ['application/json', 'application/x-www-form-urlencoded'];

        foreach ($validTypes as $contentType) {
            $request = Request::create('/webhook', 'POST', [], [], [], [
                'CONTENT_TYPE' => $contentType
            ]);

            $result = $this->service->validateContentType($request);
            $this->assertTrue($result['valid'], "Content type {$contentType} should be valid");
        }
    }

    public function test_validates_request_method()
    {
        $getRequest = Request::create('/webhook', 'GET');
        $result = $this->service->validateRequestMethod($getRequest);
        $this->assertFalse($result['valid']);

        $postRequest = Request::create('/webhook', 'POST');
        $result = $this->service->validateRequestMethod($postRequest);
        $this->assertTrue($result['valid']);
    }

    public function test_sanitizes_webhook_payload()
    {
        $maliciousPayload = [
            'event' => 'payment.captured',
            'script' => '<script>alert("xss")</script>',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'description' => '<img src=x onerror=alert(1)>'
                    ]
                ]
            ]
        ];

        $sanitized = $this->service->sanitizePayload($maliciousPayload);

        $this->assertArrayNotHasKey('script', $sanitized);
        $this->assertStringNotContainsString('<script>', json_encode($sanitized));
        $this->assertStringNotContainsString('onerror', json_encode($sanitized));
    }

    public function test_validates_webhook_size_limits()
    {
        $largePayload = str_repeat('a', 1024 * 1024 * 2); // 2MB payload

        $request = Request::create('/webhook', 'POST', [], [], [], [
            'CONTENT_LENGTH' => strlen($largePayload)
        ], $largePayload);

        $result = $this->service->validatePayloadSize($request);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Payload too large', $result['error']);
    }

    public function test_handles_malformed_json()
    {
        $malformedJson = '{"event": "payment.captured", "invalid": json}';

        $request = Request::create('/webhook', 'POST', [], [], [], [
            'CONTENT_TYPE' => 'application/json'
        ], $malformedJson);

        $result = $this->service->validateJsonPayload($request);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Invalid JSON', $result['error']);
    }

    public function test_comprehensive_webhook_validation()
    {
        $payload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'amount' => 100000,
                        'currency' => 'INR',
                        'status' => 'captured'
                    ]
                ]
            ]
        ];

        $payloadJson = json_encode($payload);
        $signature = hash_hmac('sha256', $payloadJson, 'test_webhook_secret');

        $request = Request::create('/webhook', 'POST', [], [], [], [
            'HTTP_X_RAZORPAY_SIGNATURE' => $signature,
            'CONTENT_TYPE' => 'application/json',
            'REMOTE_ADDR' => '*************'
        ], $payloadJson);

        $result = $this->service->validateWebhookRequest($request, $this->gateway, ['*************']);

        $this->assertTrue($result['valid']);
        $this->assertArrayHasKey('sanitized_payload', $result);
    }

    public function test_webhook_security_metrics()
    {
        // Simulate various security events
        $this->service->recordSecurityEvent('invalid_signature', '*************');
        $this->service->recordSecurityEvent('unauthorized_ip', '*************');
        $this->service->recordSecurityEvent('rate_limit_exceeded', '*************');

        $metrics = $this->service->getSecurityMetrics();

        $this->assertArrayHasKey('total_violations', $metrics);
        $this->assertArrayHasKey('violation_types', $metrics);
        $this->assertArrayHasKey('top_violating_ips', $metrics);
        $this->assertEquals(3, $metrics['total_violations']);
    }

    public function test_webhook_security_cleanup()
    {
        // Add some old security records
        $this->service->recordSecurityEvent('test_event', '*************');
        
        // Run cleanup
        $cleaned = $this->service->cleanupOldSecurityRecords(1); // 1 day retention

        $this->assertIsInt($cleaned);
        $this->assertGreaterThanOrEqual(0, $cleaned);
    }

    protected function tearDown(): void
    {
        // Clean up any cached data
        $this->service->clearSecurityCache();
        parent::tearDown();
    }
}