<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Page;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class PageController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('admin');
    }

    public function index()
    {
        $pages = Page::ordered()->get();
        return view('admin.pages.index', compact('pages'));
    }

    public function create()
    {
        $templates = Page::getTemplates();
        return view('admin.pages.create', compact('templates'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => ['required', 'string', 'max:255', Rule::unique('pages')],
            'content' => 'required|string',
            'template' => ['required', 'string', Rule::in(array_keys(Page::getTemplates()))],
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'show_in_menu' => 'boolean',
            'order' => 'nullable|integer|min:0',
        ]);

        try {
            $page = Page::create($validated);
            return redirect()->route('admin.pages.index')
                ->with('success', 'Page created successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create page. Please try again.');
        }
    }

    public function edit(Page $page)
    {
        $templates = Page::getTemplates();
        return view('admin.pages.edit', compact('page', 'templates'));
    }

    public function update(Request $request, Page $page)
    {
        $validated = $request->validate([
            'title' => ['required', 'string', 'max:255', Rule::unique('pages')->ignore($page->id)],
            'content' => 'required|string',
            'template' => ['required', 'string', Rule::in(array_keys(Page::getTemplates()))],
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'show_in_menu' => 'boolean',
            'order' => 'nullable|integer|min:0',
        ]);

        try {
            $page->update($validated);
            return redirect()->route('admin.pages.index')
                ->with('success', 'Page updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update page. Please try again.');
        }
    }

    public function destroy(Page $page)
    {
        try {
            $page->delete();
            return redirect()->route('admin.pages.index')
                ->with('success', 'Page deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->route('admin.pages.index')
                ->with('error', 'Failed to delete page. Please try again.');
        }
    }
} 