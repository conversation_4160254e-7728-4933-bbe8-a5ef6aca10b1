<?php

use App\Models\Order;
use App\Models\User;
use App\Models\Plan;
use App\Models\Payment;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('order can be created', function () {
    $order = Order::factory()->create([
        'order_number' => 'ORD-123456',
        'amount' => 99.99,
        'request_limit' => 1000,
        'status' => Order::STATUS_PENDING,
    ]);

    expect($order)->toBeInstanceOf(Order::class)
        ->and($order->order_number)->toBe('ORD-123456')
        ->and((float) $order->amount)->toBe(99.99)
        ->and($order->request_limit)->toBe(1000)
        ->and($order->status)->toBe(Order::STATUS_PENDING);
});

test('order has user relationship', function () {
    $user = User::factory()->create();
    $order = Order::factory()->create([
        'user_id' => $user->id
    ]);

    expect($order->user)->toBeInstanceOf(User::class)
        ->and($order->user->id)->toBe($user->id);
});

test('order has plan relationship', function () {
    $plan = Plan::factory()->create();
    $order = Order::factory()->create([
        'plan_id' => $plan->id
    ]);

    expect($order->plan)->toBeInstanceOf(Plan::class)
        ->and($order->plan->id)->toBe($plan->id);
});

test('order has payment relationship', function () {
    $order = Order::factory()->create();
    
    // Create a payment for the order
    $payment = new Payment([
        'order_id' => $order->id,
        'payment_id' => 'PAY-123456',
        'payer_email' => '<EMAIL>',
        'amount' => $order->amount,
        'currency' => 'USD',
        'payment_status' => Payment::STATUS_COMPLETED,
        'payment_method' => Payment::METHOD_PAYPAL,
        'payment_details' => ['key' => 'value'],
        'paid_at' => now(),
    ]);
    $payment->save();

    expect($order->payment)->toBeInstanceOf(Payment::class)
        ->and($order->payment->payment_id)->toBe('PAY-123456');
});

test('order has correct table name', function () {
    $order = new Order();
    
    expect($order->getTable())->toBe('orders');
});

test('order has correct fillable attributes', function () {
    $order = new Order();
    $fillable = $order->getFillable();
    
    expect($fillable)->toContain('user_id')
        ->toContain('plan_id')
        ->toContain('order_number')
        ->toContain('amount')
        ->toContain('request_limit')
        ->toContain('status')
        ->toContain('notes')
        ->toContain('paid_at')
        ->toContain('paypal_order_id');
});

test('order amount is cast to decimal', function () {
    $order = Order::factory()->create(['amount' => 99.99]);

    expect((float) $order->amount)->toBe(99.99);
});

test('order request_limit is cast to integer', function () {
    $order = Order::factory()->create(['request_limit' => '1000']);

    expect($order->request_limit)->toBe(1000);
});

test('order paid_at is cast to datetime', function () {
    $date = now();
    $order = Order::factory()->create(['paid_at' => $date]);

    expect($order->paid_at)->toBeInstanceOf(\Carbon\Carbon::class);
});

test('isPending returns true for pending orders', function () {
    $order = Order::factory()->pending()->create();
    
    expect($order->isPending())->toBeTrue();
});

test('isCompleted returns true for completed orders', function () {
    $order = Order::factory()->completed()->create();
    
    expect($order->isCompleted())->toBeTrue();
});

test('isFailed returns true for failed orders', function () {
    $order = Order::factory()->failed()->create();
    
    expect($order->isFailed())->toBeTrue();
});

test('isCancelled returns true for cancelled orders', function () {
    $order = Order::factory()->cancelled()->create();
    
    expect($order->isCancelled())->toBeTrue();
});

test('factory states create correct status records', function () {
    $pending = Order::factory()->pending()->create();
    $completed = Order::factory()->completed()->create();
    $failed = Order::factory()->failed()->create();
    $cancelled = Order::factory()->cancelled()->create();

    expect($pending->status)->toBe(Order::STATUS_PENDING)
        ->and($completed->status)->toBe(Order::STATUS_COMPLETED)
        ->and($failed->status)->toBe(Order::STATUS_FAILED)
        ->and($cancelled->status)->toBe(Order::STATUS_CANCELLED);
});

test('completed orders have paid_at date', function () {
    $order = Order::factory()->completed()->create();
    
    expect($order->paid_at)->not->toBeNull()
        ->and($order->paid_at)->toBeInstanceOf(\Carbon\Carbon::class);
});

test('non-completed orders have null paid_at date', function () {
    $pending = Order::factory()->pending()->create();
    $failed = Order::factory()->failed()->create();
    $cancelled = Order::factory()->cancelled()->create();
    
    expect($pending->paid_at)->toBeNull()
        ->and($failed->paid_at)->toBeNull()
        ->and($cancelled->paid_at)->toBeNull();
});