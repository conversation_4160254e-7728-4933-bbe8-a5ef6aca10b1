<?php

use App\Models\PinCode;
use App\Models\State;
use App\Models\District;
use App\Models\PostOffice;
use App\Models\User;
use App\Models\Review;
use App\Models\Like;
use App\Models\VillagePincode;
use App\Models\ContactNumberChange;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

uses(RefreshDatabase::class, WithFaker::class);

// php artisan test tests/Feature/Controllers/PincodeDirectoryControllerPerformanceTest.php

/*
|--------------------------------------------------------------------------
| Performance Tests
|--------------------------------------------------------------------------
*/

test('listofstates uses cache to improve performance', function () {
    // Clear cache to ensure fresh test
    Cache::flush();
    
    // Create test data
    $state = State::factory()->create(['name' => 'Test State']);
    PinCode::factory()->count(10)->create(['state' => $state->name]);
    
    // Measure first request (should populate cache)
    $startTime = microtime(true);
    $response1 = $this->get(route('pincodes.states'));
    $firstRequestTime = microtime(true) - $startTime;
    
    // Verify first request is successful
    $response1->assertStatus(200);
    
    // Measure second request (should use cache)
    $startTime = microtime(true);
    $response2 = $this->get(route('pincodes.states'));
    $secondRequestTime = microtime(true) - $startTime;
    
    // Verify second request is successful
    $response2->assertStatus(200);
    
    // Assert that cached request is significantly faster
    // Allow for some variance but expect meaningful improvement
    $performanceImprovement = $firstRequestTime / $secondRequestTime;
    expect($performanceImprovement)->toBeGreaterThan(1.1);
    
    // Verify cache keys exist
    expect(Cache::has('all_states'))->toBeTrue();
    expect(Cache::has('total_pincodes'))->toBeTrue();
    expect(Cache::has('all_states_model'))->toBeTrue();
});

// test('listofdistricts uses cache to improve performance', function () {
//     Cache::flush();
    
//     // Create test data with explicit relationships
//     $state = State::factory()->create(['name' => 'Test State']);
//     $districts = District::factory()->count(5)->create(['state_id' => $state->id]);
    
//     foreach ($districts as $district) {
//         PinCode::factory()->count(3)->create([
//             'state' => $state->name,
//             'district' => $district->name
//         ]);
//     }
    
//     $routeParams = ['state' => 'Test State'];
    
//     // First request
//     $startTime = microtime(true);
//     $response1 = $this->get(route('pincodes.districts', $routeParams));
//     $firstRequestTime = microtime(true) - $startTime;
    
//     $response1->assertStatus(200);
    
//     // Second request (cached)
//     $startTime = microtime(true);
//     $response2 = $this->get(route('pincodes.districts', $routeParams));
//     $secondRequestTime = microtime(true) - $startTime;
    
//     $response2->assertStatus(200);
    
//     // Performance assertion
//     $performanceImprovement = $firstRequestTime / $secondRequestTime;
//     expect($performanceImprovement)->toBeGreaterThan(1.5);
    
//     // Cache verification
//     expect(Cache::has('districts_Test State'))->toBeTrue();
//     expect(Cache::has('pincode_counts_Test State'))->toBeTrue();
// });

test('listofpostoffices uses cache to improve performance', function () {
    $this->markTestSkipped('Skipping due to uncached sidebar query in controller.');
    Cache::flush();
    
    // Create hierarchical test data
    $state = State::factory()->create(['name' => 'Test State']);
    $district = District::factory()->create([
        'name' => 'Test District',
        'state_id' => $state->id
    ]);
    
    PinCode::factory()->count(10)->create([
        'state' => $state->name,
        'district' => $district->name
    ]);
    
    $routeParams = [
        'state' => 'Test State',
        'district' => 'Test District'
    ];
    
    // First request to warm the cache
    $this->get(route('pincodes.postoffices', $routeParams));

    DB::enableQueryLog();
    // Second request should hit the cache
    $this->get(route('pincodes.postoffices', $routeParams));
    expect(DB::getQueryLog())->toBeEmpty();
});

test('allPincodelist handles pagination efficiently', function () {
    Cache::flush();
    
    // Create controlled test data
    $state = State::factory()->create(['name' => 'Test State']);
    $district = District::factory()->create([
        'state_id' => $state->id,
        'name' => 'Test District'
    ]);
    
    // Create 100 pincodes to test pagination
    $pincodes = [];
    for ($i = 1; $i <= 100; $i++) {
        $pincodes[] = [
            'pincode' => str_pad($i, 6, '0', STR_PAD_LEFT),
            'state' => $state->name,
            'district' => $district->name,
            'name' => "Post Office {$i}",
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
    
    // Bulk insert for better performance
    PinCode::insert($pincodes);
    
    // Test response time
    $startTime = microtime(true);
    $response = $this->get('/india-postal-code-list');
    $responseTime = microtime(true) - $startTime;
    
    // Assertions
    $response->assertStatus(200);
    expect($responseTime)->toBeLessThan(2.0);
    
    // Verify pagination
    $pincodes = $response->viewData('pincodes');
    if ($pincodes) {
        expect($pincodes->count())->toBeLessThanOrEqual(50);
        expect($pincodes->hasPages())->toBeTrue();
    }
});

/*
|--------------------------------------------------------------------------
| Security Tests
|--------------------------------------------------------------------------
*/

test('storeReviews sanitizes user input to prevent XSS', function () {
    $pincode = PinCode::factory()->create();
    
    $maliciousInput = '<script>alert("XSS");</script><img src="x" onerror="alert(1)">';
    $sanitizedInput = 'alert("XSS");';
    
    $response = $this->post(route('reviews.store'), [
        'pincode_id' => $pincode->id,
        'name' => 'Test User',
        'rating' => 5,
        'comment' => $maliciousInput
    ]);
    
    // Should redirect successfully
    $response->assertRedirect();
    
    // Verify review was stored and sanitized
    $storedReview = Review::where('pincode_id', $pincode->id)->first();
    expect($storedReview)->not->toBeNull();
    expect($storedReview->comment)->toBe($sanitizedInput);
    expect($storedReview->comment)->not->toContain('<script>');
    expect($storedReview->comment)->not->toContain('onerror=');
});

test('changeContactNumber validates input properly', function () {
    $pincode = PinCode::factory()->create([
        'name' => 'Test Post Office',
        'contact_number' => '1234567890'
    ]);
    
    // Test invalid contact number format
    $response = $this->postJson(route('contact.change', $pincode->name), [
        'contact_number' => '987-654-3210', // Invalid format
        'reason' => 'Number has changed'
    ]);
    
    $response->assertStatus(422)
        ->assertJsonValidationErrors('contact_number');
    
    // Test valid request
    $response = $this->postJson(route('contact.change', $pincode->name), [
        'contact_number' => '9876543210',
        'reason' => 'Number has changed'
    ]);
    
    $response->assertStatus(200);
    
    // Verify the request was stored
    $changeRequest = ContactNumberChange::where('pincode_id', $pincode->id)->first();
    expect($changeRequest)->not->toBeNull();
    expect($changeRequest->new_number)->toBe('9876543210');
});

test('searchPincode handles malformed queries safely', function () {
    // Create test data
    PinCode::factory()->create([
        'pincode' => '400001',
        'name' => 'Mumbai GPO'
    ]);
    
    // Test potentially malicious queries
    $maliciousQueries = [
        "' OR '1'='1",
        "400001' OR '1'='1",
        "400001; DROP TABLE pincodes; --",
        "400001 UNION SELECT * FROM users",
        "<script>alert('xss')</script>",
        "../../etc/passwd"
    ];
    
    foreach ($maliciousQueries as $query) {
        $response = $this->get(route('search', [
            'query' => $query,
            'type' => 'pincode'
        ]));
        
        // Should return 200 without errors
        $response->assertStatus(200);
        
        // Verify no unexpected data in response
        $content = $response->getContent();
        expect($content)->not->toContain('DROP TABLE');
        expect($content)->not->toContain('UNION SELECT');
        // Check for unescaped script tags that could execute
        expect($content)->not->toContain('<script>alert');
    }
});

test('storeReviews implements rate limiting', function () {
    // This test assumes a simple IP-based rate limit where one review is allowed per pincode.
    $pincode = PinCode::factory()->create();

    $reviewData = [
        'pincode_id' => $pincode->id,
        'name' => 'Test User',
        'rating' => 4,
        'comment' => 'This is the first review, it should be accepted.'
    ];

    // First review should be successful
    $this->post(route('reviews.store'), $reviewData)
         ->assertRedirect()
         ->assertSessionHas('success');

    // Second review from the same IP should be blocked
    $response = $this->post(route('reviews.store'), $reviewData);

    // Should be rate limited (redirects with a 'success' message indicating duplication)
    $response->assertRedirect()
        ->assertSessionHas('success', 'You have already reviewed this pincode.');

    // Verify second review was not created
    $reviewCount = Review::where('pincode_id', $pincode->id)->count();
    expect($reviewCount)->toBe(1);
});

/*
|--------------------------------------------------------------------------
| Integration Tests
|--------------------------------------------------------------------------
*/

test('breadcrumb generation works correctly', function () {
    $state = State::factory()->create(['name' => 'Test State']);
    $district = District::factory()->create([
        'name' => 'Test District',
        'state_id' => $state->id
    ]);
    
    PinCode::factory()->create([
        'pincode' => '123456',
        'state' => $state->name,
        'district' => $district->name,
        'name' => 'Test Post Office'
    ]);
    
    // Test states breadcrumb
    $response = $this->get(route('pincodes.states'));
    $response->assertStatus(200);
    
    $breadcrumbs = $response->viewData('breadcrumbs');
    expect($breadcrumbs)->toHaveCount(1);
    expect($breadcrumbs[0]['name'])->toBe('pincodes');
    
    // Test districts breadcrumb
    $response = $this->get(route('pincodes.districts', ['state' => 'Test State']));
    $response->assertStatus(200);
    
    $breadcrumbs = $response->viewData('breadcrumbs');
    expect($breadcrumbs)->toHaveCount(2);
    expect($breadcrumbs[0]['name'])->toBe('pincodes');
    expect($breadcrumbs[1]['name'])->toBe('test-state');
});

test('SEO metadata is properly set', function () {
    $state = State::factory()->create(['name' => 'Test State']);
    $district = District::factory()->create([
        'name' => 'Test District',
        'state_id' => $state->id
    ]);
    
    // Test states SEO
    $response = $this->get(route('pincodes.states'));
    $response->assertStatus(200);
    
    $pageTitle = $response->viewData('pageTitle');
    $metaDescription = $response->viewData('metaDescription');
    
    expect($pageTitle)->toBe('State Wise Pin Codes');
    expect($metaDescription)->not->toBeEmpty();
    
    // Test districts SEO
    $response = $this->get(route('pincodes.districts', ['state' => 'Test State']));
    $response->assertStatus(200);
    
    $pageTitle = $response->viewData('pageTitle');
    expect($pageTitle)->toContain('Test State');
});

/*
|--------------------------------------------------------------------------
| Error Handling Tests
|--------------------------------------------------------------------------
*/

test('graceful error handling for missing data', function () {
    // Test accessing non-existent state
    $response = $this->get(route('pincodes.districts', ['state' => 'Non Existent State']));
    $response->assertStatus(404);
    
    // Test accessing non-existent district
    $state = State::factory()->create(['name' => 'Test State']);
    $response = $this->get(route('pincodes.postoffices', [
        'state' => 'Test State',
        'district' => 'Non Existent District'
    ]));
    $response->assertStatus(404);
});

test('database connection errors are handled gracefully', function () {
    // Mock Cache to throw exception when accessing states_with_counts
    Cache::shouldReceive('remember')
        ->with('states_with_counts', \Mockery::any(), \Mockery::any())
        ->andThrow(new \Exception('Database connection failed'));
    
    // Allow other Cache calls
    Cache::shouldReceive('forget')->andReturn(true);
    
    Log::shouldReceive('error')->once();
    
    $response = $this->get(route('pincodes.states'));
    
    // Should return error page instead of crashing
    $response->assertStatus(500);
});

/*
|--------------------------------------------------------------------------
| Helper Functions for Testing
|--------------------------------------------------------------------------
*/

function createTestHierarchy(): array
{
    $state = State::factory()->create(['name' => 'Test State']);
    $district = District::factory()->create([
        'name' => 'Test District',
        'state_id' => $state->id
    ]);
    $pincode = PinCode::factory()->create([
        'pincode' => '123456',
        'state' => $state->name,
        'district' => $district->name,
        'name' => 'Test Post Office'
    ]);
    
    return compact('state', 'district', 'pincode');
}

function measurePerformance(callable $callback): float
{
    $startTime = microtime(true);
    $callback();
    return microtime(true) - $startTime;
}