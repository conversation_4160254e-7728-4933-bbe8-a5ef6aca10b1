@extends('layouts.app')

@section('content')
    <div class="min-h-screen flex items-center justify-center bg-bg-light dark:bg-bg-dark py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <h2 class=" text-3xl font-extrabold text-text-primary-light dark:text-text-primary-dark">
                    {{ __('Forgot Password?') }}
                </h2>
                <p class="mt-4 text-sm text-text-secondary-light dark:text-text-secondary-dark leading-relaxed">
                    {{ __('No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one.') }}
                </p>
            </div>

            <!-- Card Container -->
            <div class="bg-bg-light dark:bg-bg-dark shadow-xl rounded-lg border border-border-light dark:border-border-dark p-8">
                <!-- Session Status -->
                <x-auth-session-status class="mb-6" :status="session('status')" />

                <form method="POST" action="{{ route('password.email') }}" class="space-y-6">
                    @csrf

                    <!-- Email Address -->
                    <div>
                        <x-input-label for="email" :value="__('Email Address')"
                            class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-2" />

                        <x-text-input id="email"
                            class="appearance-none rounded-md relative block w-full px-3 py-3 border border-border-light dark:border-border-dark placeholder-text-secondary-light dark:placeholder-text-secondary-dark text-text-primary-light dark:text-text-primary-dark bg-bg-light dark:bg-bg-dark focus:outline-none focus:ring-2 focus:ring-primary-light focus:border-primary-light focus:z-10 sm:text-sm transition-colors duration-200"
                            type="email" name="email" :value="old('email')" required autofocus
                            placeholder="Enter your email address" />

                        <x-input-error :messages="$errors->get('email')" class="mt-2 text-sm text-red-600 dark:text-red-400" />
                    </div>

                    <!-- Submit Button -->
                    <div class="flex items-center justify-between">
                        <a href="{{ route('login') }}"
                            class="text-sm text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-accent-dark transition-colors duration-200">
                            {{ __('Back to Login') }}
                        </a>

                        <x-primary-button
                            class="ml-3 inline-flex justify-center py-3 px-6 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-light hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:bg-primary-dark dark:hover:bg-accent-dark dark:focus:ring-offset-bg-dark transition-colors duration-200">
                            {{ __('Email Password Reset Link') }}
                        </x-primary-button>
                    </div>
                </form>
            </div>

            <!-- Footer -->
            <div class="text-center">
                <p class="text-xs text-text-secondary-light dark:text-text-secondary-dark">
                    {{ __('Remember your password?') }}
                    <a href="{{ route('login') }}"
                        class="font-medium text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-accent-dark transition-colors duration-200">
                        {{ __('Sign in') }}
                    </a>
                </p>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        /* Custom dark mode toggle animation */
        @media (prefers-color-scheme: dark) {
            .dark-mode-transition {
                transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
            }
        }

        /* Focus states for better accessibility */
        .focus-visible:focus {
            outline: 2px solid #3B82F6;
            outline-offset: 2px;
        }

        /* Loading state for form submission */
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }

        /* Smooth transitions for all interactive elements */
        input,
        button,
        a {
            transition: all 0.2s ease-in-out;
        }

        /* Enhanced shadow for dark mode */
        .dark .shadow-xl {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }
    </style>
@endpush

@push('scripts')
    <script>
        // Add loading state to form submission
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const submitButton = form.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;

            form.addEventListener('submit', function() {
                submitButton.textContent = 'Sending...';
                submitButton.disabled = true;
                form.classList.add('loading');
            });

            // Reset if form validation fails
            if (document.querySelector('.text-red-600')) {
                submitButton.textContent = originalText;
                submitButton.disabled = false;
                form.classList.remove('loading');
            }
        });
    </script>
@endpush
