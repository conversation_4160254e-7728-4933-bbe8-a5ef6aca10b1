@extends('admin.layouts.app')

@section('title', 'Payment Gateway Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <div class="d-flex">
                        <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#addGatewayModal">
                            <i class="mdi mdi-plus"></i> Add Gateway
                        </button>
                        <button type="button" class="btn btn-info me-2" id="test-all-btn">
                            <i class="mdi mdi-test-tube"></i> Test All
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="mdi mdi-cog"></i> Actions
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" id="bulk-enable">Enable Selected</a></li>
                                <li><a class="dropdown-item" href="#" id="bulk-disable">Disable Selected</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" id="export-config">Export Configuration</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <h4 class="page-title">Payment Gateway Management</h4>
            </div>
        </div>
    </div>

    <!-- Gateway Statistics -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-muted mb-3 lh-1 d-block text-truncate">Total Gateways</span>
                            <h4 class="mb-3">
                                <span class="counter-value" data-target="{{ $gateways->count() }}">0</span>
                            </h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-primary">
                                <span class="avatar-title bg-primary rounded-circle">
                                    <i class="mdi mdi-credit-card-multiple font-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-muted mb-3 lh-1 d-block text-truncate">Active Gateways</span>
                            <h4 class="mb-3">
                                <span class="counter-value" data-target="{{ $gateways->where('is_active', true)->count() }}">0</span>
                            </h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-success">
                                <span class="avatar-title bg-success rounded-circle">
                                    <i class="mdi mdi-check-circle font-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-muted mb-3 lh-1 d-block text-truncate">This Month</span>
                            <h4 class="mb-3">
                                <span class="counter-value" data-target="{{ $monthlyTransactions ?? 0 }}">0</span>
                            </h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-info">
                                <span class="avatar-title bg-info rounded-circle">
                                    <i class="mdi mdi-chart-line font-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-muted mb-3 lh-1 d-block text-truncate">Success Rate</span>
                            <h4 class="mb-3">
                                <span class="counter-value" data-target="{{ $successRate ?? 0 }}">0</span>%
                            </h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-warning">
                                <span class="avatar-title bg-warning rounded-circle">
                                    <i class="mdi mdi-percent font-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gateway List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="header-title">Payment Gateways</h4>
                        <div class="d-flex align-items-center">
                            <div class="form-check me-3">
                                <input class="form-check-input" type="checkbox" id="select-all">
                                <label class="form-check-label" for="select-all">
                                    Select All
                                </label>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    Sort by
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" data-sort="name">Name</a></li>
                                    <li><a class="dropdown-item" href="#" data-sort="status">Status</a></li>
                                    <li><a class="dropdown-item" href="#" data-sort="created">Created Date</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap mb-0" id="gateways-table">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 20px;">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="customCheck1">
                                        </div>
                                    </th>
                                    <th>Gateway</th>
                                    <th>Status</th>
                                    <th>Supported Currencies</th>
                                    <th>Last Test</th>
                                    <th>Transactions</th>
                                    <th>Success Rate</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($gateways as $gateway)
                                <tr data-gateway-id="{{ $gateway->id }}">
                                    <td>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input gateway-checkbox" value="{{ $gateway->id }}">
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0 me-3">
                                                @if($gateway->logo_url)
                                                    <img src="{{ $gateway->logo_url }}" alt="{{ $gateway->display_name }}" 
                                                         class="rounded" width="32" height="32">
                                                @else
                                                    <div class="avatar-xs">
                                                        <span class="avatar-title rounded-circle bg-primary">
                                                            {{ strtoupper(substr($gateway->display_name, 0, 2)) }}
                                                        </span>
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-0">{{ $gateway->display_name }}</h6>
                                                <small class="text-muted">{{ $gateway->name }}</small>
                                                @if($gateway->is_default)
                                                    <span class="badge bg-info ms-1">Default</span>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input status-toggle" type="checkbox" 
                                                   data-gateway-id="{{ $gateway->id }}"
                                                   {{ $gateway->is_active ? 'checked' : '' }}>
                                            <label class="form-check-label">
                                                <span class="badge bg-{{ $gateway->is_active ? 'success' : 'secondary' }}">
                                                    {{ $gateway->is_active ? 'Active' : 'Inactive' }}
                                                </span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        @if($gateway->supported_currencies && count($gateway->supported_currencies) > 0)
                                            @foreach(array_slice($gateway->supported_currencies, 0, 3) as $currency)
                                                <span class="badge bg-light text-dark me-1">{{ $currency }}</span>
                                            @endforeach
                                            @if(count($gateway->supported_currencies) > 3)
                                                <span class="badge bg-light text-dark">+{{ count($gateway->supported_currencies) - 3 }}</span>
                                            @endif
                                        @else
                                            <span class="text-muted">Not configured</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="text-muted" id="last-test-{{ $gateway->id }}">
                                            {{ $gateway->last_tested_at ? $gateway->last_tested_at->diffForHumans() : 'Never' }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">{{ $gateway->payments_count ?? 0 }}</span>
                                    </td>
                                    <td>
                                        @php
                                            $successRate = $gateway->success_rate ?? 0;
                                        @endphp
                                        <span class="badge bg-{{ $successRate >= 90 ? 'success' : ($successRate >= 70 ? 'warning' : 'danger') }}">
                                            {{ number_format($successRate, 1) }}%
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" 
                                                    onclick="configureGateway({{ $gateway->id }})" title="Configure">
                                                <i class="mdi mdi-cog"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-info test-gateway-btn" 
                                                    data-gateway-id="{{ $gateway->id }}" title="Test Connection">
                                                <i class="mdi mdi-test-tube"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-success" 
                                                    onclick="viewAnalytics({{ $gateway->id }})" title="Analytics">
                                                <i class="mdi mdi-chart-line"></i>
                                            </button>
                                            <div class="dropdown">
                                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="mdi mdi-dots-horizontal"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="setAsDefault({{ $gateway->id }})">Set as Default</a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="duplicateGateway({{ $gateway->id }})">Duplicate</a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteGateway({{ $gateway->id }})">Delete</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="mdi mdi-credit-card-off font-48 text-muted mb-3"></i>
                                            <h5 class="text-muted">No Payment Gateways Found</h5>
                                            <p class="text-muted">Get started by adding your first payment gateway.</p>
                                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addGatewayModal">
                                                <i class="mdi mdi-plus me-1"></i> Add Gateway
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    @if($gateways->hasPages())
                    <div class="d-flex justify-content-center mt-3">
                        {{ $gateways->links() }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Gateway Modal -->
<div class="modal fade" id="addGatewayModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Payment Gateway</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="add-gateway-form" action="{{ route('admin.payment-gateways.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Gateway Type *</label>
                            <select class="form-select" name="name" id="gateway-type" required>
                                <option value="">Select Gateway</option>
                                <option value="paypal">PayPal</option>
                                <option value="razorpay">Razorpay</option>
                                <option value="stripe">Stripe</option>
                                <option value="qr_bank_transfer">QR Bank Transfer</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Display Name *</label>
                            <input type="text" class="form-control" name="display_name" required>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" name="description" rows="2"></textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Sort Order</label>
                            <input type="number" class="form-control" name="sort_order" value="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" name="is_active" value="1" checked>
                                <label class="form-check-label">Active</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_default" value="1">
                                <label class="form-check-label">Set as Default</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Dynamic Configuration Fields -->
                    <div id="gateway-config-fields"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Gateway</button>
                </div>
            </form>
        </div>
    </div>
</div><
!-- Gateway Configuration Modal -->
<div class="modal fade" id="configureGatewayModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Configure Payment Gateway</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="gateway-configuration-content">
                    <!-- Content will be loaded dynamically -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Results Modal -->
<div class="modal fade" id="testResultsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Gateway Test Results</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="test-results-content">
                    <!-- Results will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gateway type change handler
    document.getElementById('gateway-type').addEventListener('change', function() {
        loadGatewayConfigFields(this.value);
    });

    // Status toggle handlers
    document.querySelectorAll('.status-toggle').forEach(toggle => {
        toggle.addEventListener('change', function() {
            toggleGatewayStatus(this.dataset.gatewayId, this.checked);
        });
    });

    // Test gateway buttons
    document.querySelectorAll('.test-gateway-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            testGateway(this.dataset.gatewayId);
        });
    });

    // Test all button
    document.getElementById('test-all-btn').addEventListener('click', function() {
        testAllGateways();
    });

    // Select all checkbox
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.gateway-checkbox');
        checkboxes.forEach(cb => cb.checked = this.checked);
    });

    // Bulk actions
    document.getElementById('bulk-enable').addEventListener('click', function() {
        bulkAction('enable');
    });

    document.getElementById('bulk-disable').addEventListener('click', function() {
        bulkAction('disable');
    });

    // Counter animation
    document.querySelectorAll('.counter-value').forEach(function(counter) {
        const target = parseInt(counter.getAttribute('data-target'));
        const increment = target / 200;
        let current = 0;
        
        const timer = setInterval(function() {
            current += increment;
            if (current >= target) {
                counter.textContent = target.toLocaleString();
                clearInterval(timer);
            } else {
                counter.textContent = Math.ceil(current).toLocaleString();
            }
        }, 10);
    });
});

function loadGatewayConfigFields(gatewayType) {
    const configContainer = document.getElementById('gateway-config-fields');
    
    if (!gatewayType) {
        configContainer.innerHTML = '';
        return;
    }

    // Show loading
    configContainer.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';

    fetch(`{{ route('admin.payment-gateways.config-fields') }}?type=${gatewayType}`)
        .then(response => response.text())
        .then(html => {
            configContainer.innerHTML = html;
        })
        .catch(error => {
            configContainer.innerHTML = '<div class="alert alert-danger">Failed to load configuration fields</div>';
        });
}

function configureGateway(gatewayId) {
    const modal = new bootstrap.Modal(document.getElementById('configureGatewayModal'));
    const content = document.getElementById('gateway-configuration-content');
    
    // Show loading
    content.innerHTML = '<div class="text-center p-4"><div class="spinner-border" role="status"></div></div>';
    modal.show();

    fetch(`{{ route('admin.payment-gateways.configure') }}/${gatewayId}`)
        .then(response => response.text())
        .then(html => {
            content.innerHTML = html;
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">Failed to load gateway configuration</div>';
        });
}

function testGateway(gatewayId) {
    const btn = document.querySelector(`[data-gateway-id="${gatewayId}"].test-gateway-btn`);
    const originalHtml = btn.innerHTML;
    
    // Show loading
    btn.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
    btn.disabled = true;

    fetch(`{{ route('admin.payment-gateways.test') }}/${gatewayId}`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        showTestResults(data);
        updateLastTestTime(gatewayId);
    })
    .catch(error => {
        showAlert('error', 'Failed to test gateway connection');
    })
    .finally(() => {
        btn.innerHTML = originalHtml;
        btn.disabled = false;
    });
}

function testAllGateways() {
    const btn = document.getElementById('test-all-btn');
    const originalHtml = btn.innerHTML;
    
    btn.innerHTML = '<div class="spinner-border spinner-border-sm me-2" role="status"></div>Testing...';
    btn.disabled = true;

    fetch(`{{ route('admin.payment-gateways.test-all') }}`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        showTestResults(data);
        // Update all last test times
        data.results.forEach(result => {
            updateLastTestTime(result.gateway_id);
        });
    })
    .catch(error => {
        showAlert('error', 'Failed to test gateways');
    })
    .finally(() => {
        btn.innerHTML = originalHtml;
        btn.disabled = false;
    });
}

function toggleGatewayStatus(gatewayId, isActive) {
    fetch(`{{ route('admin.payment-gateways.toggle-status') }}/${gatewayId}`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ is_active: isActive })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // Update the status badge
            const row = document.querySelector(`[data-gateway-id="${gatewayId}"]`);
            const badge = row.querySelector('.badge');
            badge.className = `badge bg-${isActive ? 'success' : 'secondary'}`;
            badge.textContent = isActive ? 'Active' : 'Inactive';
        } else {
            showAlert('error', data.message);
            // Revert the toggle
            const toggle = document.querySelector(`[data-gateway-id="${gatewayId}"].status-toggle`);
            toggle.checked = !isActive;
        }
    })
    .catch(error => {
        showAlert('error', 'Failed to update gateway status');
        // Revert the toggle
        const toggle = document.querySelector(`[data-gateway-id="${gatewayId}"].status-toggle`);
        toggle.checked = !isActive;
    });
}

function bulkAction(action) {
    const selectedGateways = Array.from(document.querySelectorAll('.gateway-checkbox:checked'))
        .map(cb => cb.value);

    if (selectedGateways.length === 0) {
        showAlert('warning', 'Please select at least one gateway');
        return;
    }

    if (!confirm(`Are you sure you want to ${action} ${selectedGateways.length} gateway(s)?`)) {
        return;
    }

    fetch(`{{ route('admin.payment-gateways.bulk-action') }}`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: action,
            gateway_ids: selectedGateways
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        showAlert('error', 'Failed to perform bulk action');
    });
}

function setAsDefault(gatewayId) {
    if (!confirm('Are you sure you want to set this as the default gateway?')) {
        return;
    }

    fetch(`{{ route('admin.payment-gateways.set-default') }}/${gatewayId}`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        showAlert('error', 'Failed to set default gateway');
    });
}

function deleteGateway(gatewayId) {
    if (!confirm('Are you sure you want to delete this gateway? This action cannot be undone.')) {
        return;
    }

    fetch(`{{ route('admin.payment-gateways.destroy', '') }}/${gatewayId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // Remove the row
            document.querySelector(`[data-gateway-id="${gatewayId}"]`).remove();
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        showAlert('error', 'Failed to delete gateway');
    });
}

function viewAnalytics(gatewayId) {
    window.open(`{{ route('admin.payment-analytics.index') }}?gateway_id=${gatewayId}`, '_blank');
}

function showTestResults(data) {
    const modal = new bootstrap.Modal(document.getElementById('testResultsModal'));
    const content = document.getElementById('test-results-content');
    
    let html = '';
    if (data.results) {
        // Multiple results
        data.results.forEach(result => {
            html += `
                <div class="test-result mb-3 p-3 border rounded">
                    <div class="d-flex align-items-center mb-2">
                        <i class="mdi mdi-${result.success ? 'check-circle text-success' : 'close-circle text-danger'} me-2"></i>
                        <h6 class="mb-0">${result.gateway_name}</h6>
                    </div>
                    <p class="mb-0 small">${result.message}</p>
                    ${result.details ? `<pre class="small mt-2 bg-light p-2 rounded">${JSON.stringify(result.details, null, 2)}</pre>` : ''}
                </div>
            `;
        });
    } else {
        // Single result
        html = `
            <div class="test-result p-3 border rounded">
                <div class="d-flex align-items-center mb-2">
                    <i class="mdi mdi-${data.success ? 'check-circle text-success' : 'close-circle text-danger'} me-2"></i>
                    <h6 class="mb-0">Test Result</h6>
                </div>
                <p class="mb-0">${data.message}</p>
                ${data.details ? `<pre class="small mt-2 bg-light p-2 rounded">${JSON.stringify(data.details, null, 2)}</pre>` : ''}
            </div>
        `;
    }
    
    content.innerHTML = html;
    modal.show();
}

function updateLastTestTime(gatewayId) {
    const timeElement = document.getElementById(`last-test-${gatewayId}`);
    if (timeElement) {
        timeElement.textContent = 'Just now';
    }
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
@endpush