# India Pincode Directory & Tools

A comprehensive Laravel-based web application providing India's complete postal code directory with advanced search capabilities, distance calculation tools, and API services.

## 🚀 Features

### 📮 Pincode Directory
- **Complete India Pincode Database**: Access to all 19,100+ pincodes covering 154,000+ post offices
- **Hierarchical Navigation**: Browse by State → District → Post Office → Pincode
- **Advanced Search**: Search by pincode, post office name, or location
- **Detailed Information**: Get comprehensive details including circle, division, region, contact numbers
- **Geolocation Data**: Latitude/longitude coordinates for each pincode
- **User Reviews & Ratings**: Community-driven feedback system

### 🛠️ Distance Calculation Tools
- **Pin-to-Pin Distance Calculator**: Calculate distances between any two pincodes
- **Bulk Distance Calculator**: Process multiple pincode pairs via Excel upload
- **Coordinate-based Calculator**: Calculate distances using latitude/longitude
- **Multiple Units**: Results in kilometers, miles, and nautical miles
- **Export Options**: Download results in Excel or PDF format

### 📍 Location Services
- **Nearest Post Office Finder**: Find closest post offices using GPS coordinates
- **District-wise Downloads**: Download pincode lists by district
- **Address Search**: Search post offices by address components

### 🔌 RESTful API
- **Pincode Validation**: Verify pincode authenticity
- **Distance Calculation**: API endpoints for distance calculations
- **Location Services**: Find nearest locations and coordinates
- **Rate Limiting**: Built-in API request throttling
- **Authentication**: Secure API access with Laravel Sanctum

### 📝 Blog System
- **Content Management**: Create and manage blog posts
- **Categories & Tags**: Organize content with categories and tags
- **Comment System**: User comments with moderation
- **SEO Optimization**: Meta tags, structured data, and SEO tools

### 👥 User Management
- **User Registration & Authentication**: Secure user accounts
- **API Token Management**: Generate and manage API access tokens
- **User Dashboard**: Personalized user experience
- **Profile Management**: Update user information and preferences

### 🔧 Admin Panel
- **Comprehensive Dashboard**: Overview of system statistics
- **Content Management**: Manage pincodes, blog posts, users
- **User Management**: Administer user accounts and permissions
- **System Settings**: Configure application settings
- **Data Import/Export**: Bulk data operations

## 🛠️ Technology Stack

- **Backend**: Laravel 11.x (PHP 8.2+)
- **Frontend**: Blade templates with Tailwind CSS
- **Database**: MySQL/PostgreSQL
- **Authentication**: Laravel Breeze with Sanctum
- **File Processing**: PhpSpreadsheet for Excel operations
- **PDF Generation**: DomPDF
- **Testing**: Pest PHP testing framework
- **Payment Integration**: PayPal (optional)

## 📋 Requirements

- PHP 8.2 or higher
- Composer
- MySQL 8.0+ or PostgreSQL 12+
- Node.js & NPM (for asset compilation)
- Web server (Apache/Nginx)

## 🚀 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd pincode-new
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install Node.js dependencies**
   ```bash
   npm install
   ```

4. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Configure database**
   ```bash
   # Edit .env file with your database credentials
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=your_database_name
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

6. **Run migrations and seeders**
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

7. **Import pincode data**
   ```bash
   # Import the SQL files from database/sql/
   mysql -u username -p database_name < database/sql/pin_codes.sql
   mysql -u username -p database_name < database/sql/pin_states.sql
   mysql -u username -p database_name < database/sql/pin_districts.sql
   ```

8. **Compile assets**
   ```bash
   npm run dev
   # or for production
   npm run build
   ```

9. **Start the development server**
   ```bash
   php artisan serve
   ```

## 📖 Usage

### Web Interface

1. **Browse Pincodes**: Navigate through states and districts to find pincodes
2. **Search Functionality**: Use the search bar to find specific pincodes or post offices
3. **Distance Tools**: Access various distance calculation tools from the tools section
4. **API Documentation**: View comprehensive API documentation at `/pincodes/api-documentation`

### API Usage

#### Authentication
```bash
# Get API token
curl -X POST /api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'
```

#### Pincode Validation
```bash
curl -X GET /api/validate-pincode/110001 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Distance Calculation
```bash
curl -X GET /api/calculate-distance-between-two-pincodes/110001/400001 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Get Pincode Details
```bash
curl -X GET /api/pincode/110001 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🗂️ Project Structure

```
pincode-new/
├── app/
│   ├── Http/Controllers/
│   │   ├── Admin/          # Admin panel controllers
│   │   ├── Api/           # API controllers
│   │   ├── Tools/         # Distance calculation tools
│   │   └── User/          # User dashboard controllers
│   ├── Models/            # Eloquent models
│   ├── Services/          # Business logic services
│   └── Traits/            # Reusable traits
├── database/
│   ├── migrations/        # Database migrations
│   ├── seeders/          # Database seeders
│   └── sql/              # SQL data files
├── resources/views/       # Blade templates
├── routes/               # Route definitions
└── tests/                # Test files
```

## 🧪 Testing

Run the test suite using Pest:

```bash
# Run all tests
php artisan test

# Run specific test file
php artisan test tests/Feature/Api/PincodeApiTest.php

# Run tests with coverage
php artisan test --coverage
```

## 🔧 Configuration

### Environment Variables

Key environment variables to configure:

```env
APP_NAME="India Pincode Directory"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password

MAIL_MAILER=smtp
MAIL_HOST=your_smtp_host
MAIL_PORT=587
MAIL_USERNAME=your_email
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"
```

### API Rate Limiting

Configure API rate limiting in `config/api.php`:

```php
'rate_limiting' => [
    'default' => 60, // requests per minute
    'authenticated' => 120, // requests per minute for authenticated users
],
```

## 📊 Database Schema

The application uses several key tables:

- `pin_codes`: Main pincode data with coordinates
- `states`: Indian states information
- `districts`: District data linked to states
- `users`: User accounts and authentication
- `blog_posts`: Blog content management
- `api_requests`: API usage tracking
- `reviews`: User reviews for pincodes
- `comments`: Blog post comments

## 🔒 Security Features

- **CSRF Protection**: Built-in Laravel CSRF protection
- **SQL Injection Prevention**: Eloquent ORM with parameter binding
- **XSS Protection**: Blade template escaping
- **Rate Limiting**: API and web request throttling
- **Authentication**: Secure user authentication with Laravel Breeze
- **Authorization**: Role-based access control
- **Input Validation**: Comprehensive request validation

## 🚀 Deployment

### Production Deployment

1. **Server Requirements**
   - PHP 8.2+ with required extensions
   - MySQL 8.0+ or PostgreSQL 12+
   - Nginx or Apache web server
   - SSL certificate for HTTPS

2. **Deployment Steps**
   ```bash
   # Set production environment
   APP_ENV=production
   APP_DEBUG=false
   
   # Install dependencies
   composer install --optimize-autoloader --no-dev
   npm run build
   
   # Run migrations
   php artisan migrate --force
   
   # Cache configuration
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

3. **Web Server Configuration**
   - Configure web server to point to `public/` directory
   - Set up proper file permissions
   - Configure SSL/TLS certificates

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support and questions:

- **Documentation**: Check the API documentation at `/pincodes/api-documentation`
- **Issues**: Create an issue on GitHub
- **Email**: Contact <EMAIL>

## 🙏 Acknowledgments

- **India Post**: For providing the pincode data
- **Laravel Community**: For the excellent framework
- **Open Source Contributors**: For various packages and tools used

---

**Built with ❤️ using Laravel**
