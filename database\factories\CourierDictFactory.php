<?php

namespace Database\Factories;

use App\Models\CourierDict;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class CourierDictFactory extends Factory
{
    protected $model = CourierDict::class;

    public function definition(): array
    {
        $term = $this->faker->unique()->words(2, true);
        return [
            'id' => Str::uuid()->toString(),
            'term' => $term,
            'description' => $this->faker->sentence(),
            'long_description' => $this->faker->paragraph(),
            'tag' => $this->faker->word(),
            'slug' => Str::slug($term),
        ];
    }
} 