<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Traits\TracksApiRequests;

class DistanceAPIController extends Controller
{
    use TracksApiRequests;

    /**
     * Calculate distance between two coordinates.
     *
     * @param  float  $lat1
     * @param  float  $lon1
     * @param  float  $lat2
     * @param  float  $lon2
     * @return \Illuminate\Http\JsonResponse
     */
    public function calculateDistanceBetweenTwoCoordinates(Request $request, $lat1, $lon1, $lat2, $lon2)
    {
        $validator = Validator::make([
            'lat1' => $lat1,
            'lon1' => $lon1,
            'lat2' => $lat2,
            'lon2' => $lon2,
        ], [
            'lat1' => 'required|numeric|between:-90,90',
            'lon1' => 'required|numeric|between:-180,180',
            'lat2' => 'required|numeric|between:-90,90',
            'lon2' => 'required|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            $response = response()->json(['error' => $validator->errors()], 400);
        } else {
            $distance = $this->haversineDistance($lat1, $lon1, $lat2, $lon2);
            $response = response()->json([
                'distance' => [
                    'km' => $distance,
                    'miles' => $distance * 0.621371
                ]
            ]);
        }

        $this->trackApiRequest($request, $response);
        return $response;
    }

    /**
     * Calculate the great-circle distance between two points using the Haversine formula.
     *
     * @param float $lat1 Latitude of the first point
     * @param float $lon1 Longitude of the first point
     * @param float $lat2 Latitude of the second point
     * @param float $lon2 Longitude of the second point
     * @return float Distance in kilometers
     */
    private function haversineDistance($lat1, $lon1, $lat2, $lon2)
    {
        $earthRadius = 6371; // in kilometers

        $lat1 = deg2rad($lat1);
        $lon1 = deg2rad($lon1);
        $lat2 = deg2rad($lat2);
        $lon2 = deg2rad($lon2);

        $latDelta = $lat2 - $lat1;
        $lonDelta = $lon2 - $lon1;

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
            cos($lat1) * cos($lat2) *
            sin($lonDelta / 2) * sin($lonDelta / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }
}