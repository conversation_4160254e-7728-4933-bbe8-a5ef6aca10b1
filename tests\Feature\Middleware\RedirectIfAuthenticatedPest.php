<?php

namespace Tests\Feature\Middleware;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use App\Http\Middleware\RedirectIfAuthenticated;
use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

uses(RefreshDatabase::class);

describe('RedirectIfAuthenticated Middleware', function () {
    beforeEach(function () {
        $this->middleware = new RedirectIfAuthenticated();
        $this->next = fn() => response('Next middleware called');
    });

    test('it passes through unauthenticated requests', function () {
        $request = Request::create('/login');
        $response = $this->middleware->handle($request, $this->next);
        expect($response->getContent())->toBe('Next middleware called');
    });
});

describe('RedirectIfAuthenticated Middleware Integration Test', function () {

    beforeEach(function () {
        // Define test routes protected by the 'guest' middleware
        Route::middleware('guest')->get('/login', function () {
            return 'Login Page';
        })->name('login');
        
        Route::middleware('guest:admin')->get('/admin/login', function () {
            return 'Admin Login Page';
        })->name('admin.login');

        // Define target routes for redirection
        Route::get('/', function () {
            return 'Home Page';
        })->name('home');

        Route::get('/admin/dashboard', function () {
            return 'Admin Dashboard';
        })->name('admin.dashboard');
    });

    test('it allows unauthenticated users to access guest pages', function () {
        $this->get('/login')->assertSee('Login Page');
        $this->get('/admin/login')->assertSee('Admin Login Page');
    });

    test('it redirects authenticated users from regular guest pages', function () {
        $user = User::factory()->create();
        $this->actingAs($user);
        
        $this->get('/login')->assertRedirect('/');
    });

    test('it redirects authenticated admin users from admin guest pages', function () {
        $adminUser = User::factory()->create(['role' => 'admin']);
        $this->actingAs($adminUser, 'admin');
        
        $this->get('/admin/login')->assertRedirect('/admin');
    });
}); 