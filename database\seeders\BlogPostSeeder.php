<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\BlogPost;
use App\Models\BlogPostCategory;
use App\Models\BlogPostTag;
use App\Models\User;
use Carbon\Carbon;

class BlogPostSeeder extends Seeder
{
    public function run(): void
    {
        $categories = BlogPostCategory::all();
        $tags = BlogPostTag::all();
        $users = User::all();

        // Create real pincode-related blog posts
        $blogPosts = [
            [
                'title' => 'How to Find the Correct Pincode for Your Area',
                'content' => $this->getHowToFindPincodeContent(),
                'excerpt' => 'Learn the most effective methods to find the correct pincode for any area in India. From online tools to offline methods, discover all the ways to locate accurate postal codes.',
                'meta_title' => 'How to Find Correct Pincode for Your Area - Complete Guide 2024',
                'meta_description' => 'Discover multiple ways to find the correct pincode for any area in India. Learn about online tools, mobile apps, and offline methods to locate accurate postal codes.',
                'meta_keywords' => ['pincode finder', 'postal code', 'India pincode', 'area pincode', 'pincode search'],
                'is_published' => true,
                'published_at' => Carbon::now()->subDays(5),
                'featured' => true
            ],
            [
                'title' => 'Difference Between Zip Code and Pincode',
                'content' => $this->getZipCodeVsPincodeContent(),
                'excerpt' => 'Understand the key differences between zip codes and pincodes. Learn about their origins, structure, and how they are used in different countries around the world.',
                'meta_title' => 'Zip Code vs Pincode: Key Differences Explained',
                'meta_description' => 'Learn the fundamental differences between zip codes and pincodes, their structure, usage, and how postal systems work in different countries.',
                'meta_keywords' => ['zip code', 'pincode', 'postal system', 'difference', 'postal code'],
                'is_published' => true,
                'published_at' => Carbon::now()->subDays(3),
                'featured' => false
            ],
            [
                'title' => 'Pincode Trivia: Surprising Facts About India\'s Postal Code System',
                'content' => $this->getPincodeTriviaContent(),
                'excerpt' => 'Discover fascinating facts about India\'s pincode system. From the first pincode to interesting patterns and surprising statistics about postal codes across the country.',
                'meta_title' => 'Amazing Pincode Facts: Surprising Trivia About India\'s Postal System',
                'meta_description' => 'Explore interesting facts and trivia about India\'s pincode system. Learn surprising statistics and fascinating details about postal codes.',
                'meta_keywords' => ['pincode facts', 'India postal system', 'pincode trivia', 'postal code history', 'interesting facts'],
                'is_published' => true,
                'published_at' => Carbon::now()->subDays(1),
                'featured' => true
            ]
        ];

        foreach ($blogPosts as $postData) {
            $post = BlogPost::create([
                'title' => $postData['title'],
                'content' => $postData['content'],
                'excerpt' => $postData['excerpt'],
                'meta_title' => $postData['meta_title'],
                'meta_description' => $postData['meta_description'],
                'meta_keywords' => $postData['meta_keywords'],
                'user_id' => $users->random()->id,
                'blog_post_category_id' => $categories->random()->id,
                'is_published' => $postData['is_published'],
                'published_at' => $postData['published_at'],
                'featured' => $postData['featured']
            ]);

            // Attach relevant tags
            $post->tags()->sync($tags->random(rand(2, 4))->pluck('id')->toArray());
        }

        // Create additional factory posts to reach 20 total
        BlogPost::factory(17)->create()->each(function ($post) use ($categories, $tags) {
            $post->blog_post_category_id = $categories->random()->id;
            $post->save();
            $post->tags()->sync($tags->random(rand(1, 3))->pluck('id')->toArray());
        });
    }

    private function getHowToFindPincodeContent(): string
    {
        return '<div class="blog-content">
            <h2>Why Finding the Correct Pincode Matters</h2>
            <p>Finding the correct pincode for your area is crucial for various reasons - from online shopping deliveries to government documentation, banking services, and official correspondence. An incorrect pincode can lead to delayed deliveries, returned mail, and administrative complications.</p>

            <h2>Online Methods to Find Your Pincode</h2>
            
            <h3>1. India Post Official Website</h3>
            <p>The most reliable source is the <strong>India Post official website</strong>. Here\'s how to use it:</p>
            <ul>
                <li>Visit the India Post website</li>
                <li>Navigate to the "Find Pincode" section</li>
                <li>Enter your state, district, and area name</li>
                <li>Get the accurate 6-digit pincode instantly</li>
            </ul>

            <h3>2. Pincode Finder Websites</h3>
            <p>Several dedicated websites offer pincode search functionality:</p>
            <ul>
                <li><strong>townbox.in</strong> - Comprehensive database with area-wise search</li>
                <li><strong>Indiapost.gov.in</strong> - Official postal department website</li>
                <li><strong>Pincode finder tools</strong> - Various third-party reliable sources</li>
            </ul>

            <h3>3. Google Search</h3>
            <p>Simply search "pincode of [your area name]" on Google. The search results often display the pincode directly in the search results.</p>

            <h2>Mobile Apps for Pincode Search</h2>
            <p>Several mobile applications can help you find pincodes on the go:</p>
            <ul>
                <li><strong>India Post Mobile App</strong> - Official app with pincode finder</li>
                <li><strong>Pincode Finder Apps</strong> - Available on both Android and iOS</li>
                <li><strong>Maps Applications</strong> - Google Maps and other mapping services often show pincodes</li>
            </ul>

            <h2>Offline Methods</h2>
            
            <h3>1. Local Post Office</h3>
            <p>Visit your nearest post office and ask the postal staff. They have comprehensive records of all pincodes in their service area.</p>

            <h3>2. Address Verification</h3>
            <p>Check existing mail, bills, or official documents delivered to your address. The pincode is usually printed on the envelope or document.</p>

            <h3>3. Ask Neighbors or Local Residents</h3>
            <p>Long-time residents of the area typically know the correct pincode for their locality.</p>

            <h2>Understanding Pincode Structure</h2>
            <p>Indian pincodes follow a specific 6-digit structure:</p>
            <ul>
                <li><strong>First digit:</strong> Represents the region (1-9)</li>
                <li><strong>Second digit:</strong> Represents the sub-region</li>
                <li><strong>Third digit:</strong> Represents the sorting district</li>
                <li><strong>Last three digits:</strong> Represent the specific post office</li>
            </ul>

            <h2>Common Mistakes to Avoid</h2>
            <ul>
                <li>Don\'t rely on outdated information - pincodes can change</li>
                <li>Verify the pincode from multiple sources</li>
                <li>Be specific about your exact location, as nearby areas may have different pincodes</li>
                <li>Don\'t confuse similar area names in different states</li>
            </ul>

            <h2>Conclusion</h2>
            <p>Finding the correct pincode is essential for smooth postal services and various administrative processes. Use official sources like India Post website or visit your local post office for the most accurate information. With multiple online and offline methods available, you can easily find the correct pincode for any area in India.</p>
        </div>';
    }

    private function getZipCodeVsPincodeContent(): string
    {
        return '<div class="blog-content">
            <h2>Introduction to Postal Codes</h2>
            <p>Postal codes are essential components of addressing systems worldwide, helping postal services efficiently sort and deliver mail. While the concept is universal, different countries use different terminologies and structures for their postal codes.</p>

            <h2>What is a Zip Code?</h2>
            <p><strong>ZIP Code</strong> stands for "Zone Improvement Plan" and is the postal code system used in the United States. Introduced in 1963, ZIP codes were designed to improve mail delivery efficiency.</p>

            <h3>ZIP Code Structure</h3>
            <ul>
                <li><strong>Basic ZIP Code:</strong> 5 digits (e.g., 90210)</li>
                <li><strong>ZIP+4 Code:</strong> 9 digits with a hyphen (e.g., 90210-1234)</li>
                <li>The first digit represents a group of U.S. states</li>
                <li>The second and third digits represent a region within that group</li>
                <li>The fourth and fifth digits represent specific post offices or delivery areas</li>
            </ul>

            <h2>What is a Pincode?</h2>
            <p><strong>Pincode</strong> stands for "Postal Index Number" and is the postal code system used in India. Introduced in 1972, pincodes help organize mail delivery across the vast Indian subcontinent.</p>

            <h3>Pincode Structure</h3>
            <ul>
                <li><strong>Format:</strong> 6 digits (e.g., 110001)</li>
                <li>The first digit represents one of nine postal regions</li>
                <li>The second digit represents a sub-region or state</li>
                <li>The third digit represents a sorting district</li>
                <li>The last three digits represent the specific post office</li>
            </ul>

            <h2>Key Differences</h2>

            <table class="comparison-table" style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                <thead>
                    <tr style="background-color: #f8f9fa;">
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Aspect</th>
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">ZIP Code (USA)</th>
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Pincode (India)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px;"><strong>Full Form</strong></td>
                        <td style="border: 1px solid #ddd; padding: 12px;">Zone Improvement Plan</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">Postal Index Number</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px;"><strong>Length</strong></td>
                        <td style="border: 1px solid #ddd; padding: 12px;">5 digits (basic) or 9 digits (ZIP+4)</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">6 digits</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px;"><strong>Introduction Year</strong></td>
                        <td style="border: 1px solid #ddd; padding: 12px;">1963</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">1972</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px;"><strong>Coverage</strong></td>
                        <td style="border: 1px solid #ddd; padding: 12px;">United States</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">India</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px;"><strong>Example</strong></td>
                        <td style="border: 1px solid #ddd; padding: 12px;">90210 or 90210-1234</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">110001</td>
                    </tr>
                </tbody>
            </table>

            <h2>Regional Variations</h2>
            
            <h3>Other Countries\' Postal Code Systems</h3>
            <ul>
                <li><strong>Canada:</strong> Uses 6-character postal codes (e.g., K1A 0A6)</li>
                <li><strong>United Kingdom:</strong> Uses alphanumeric postcodes (e.g., SW1A 1AA)</li>
                <li><strong>Germany:</strong> Uses 5-digit postal codes (e.g., 10115)</li>
                <li><strong>Australia:</strong> Uses 4-digit postcodes (e.g., 2000)</li>
                <li><strong>Japan:</strong> Uses 7-digit postal codes (e.g., 100-0001)</li>
            </ul>

            <h2>Usage and Importance</h2>
            
            <h3>ZIP Codes in the USA</h3>
            <ul>
                <li>Essential for mail delivery and package shipping</li>
                <li>Used in demographic analysis and market research</li>
                <li>Required for online purchases and service delivery</li>
                <li>Used by emergency services for location identification</li>
            </ul>

            <h3>Pincodes in India</h3>
            <ul>
                <li>Mandatory for all postal services</li>
                <li>Required for online shopping and e-commerce</li>
                <li>Used in government documentation and forms</li>
                <li>Essential for banking and financial services</li>
                <li>Used for service area determination by businesses</li>
            </ul>

            <h2>Evolution and Future</h2>
            <p>Both ZIP codes and pincodes continue to evolve with changing urban landscapes and technological advances. While the basic structure remains the same, both systems are adapting to handle increased mail volume, e-commerce growth, and changing delivery patterns.</p>

            <h2>Conclusion</h2>
            <p>While ZIP codes and pincodes serve the same fundamental purpose of organizing postal delivery, they differ in structure, length, and regional application. Understanding these differences is important for international communication, shipping, and business operations. Both systems have proven effective in their respective countries and continue to be essential components of modern postal infrastructure.</p>
        </div>';
    }

    private function getPincodeTriviaContent(): string
    {
        return '<div class="blog-content">
            <h2>The Birth of India\'s Pincode System</h2>
            <p>India\'s pincode system has a fascinating history filled with interesting facts and surprising statistics. Let\'s explore some amazing trivia about this essential postal system that connects every corner of our vast nation.</p>

            <h2>Historical Facts</h2>
            
            <h3>🎯 The First Pincode</h3>
            <p><strong>110001</strong> was the first pincode assigned in India, belonging to the Parliament House area in New Delhi. This historic pincode was introduced on August 15, 1972 - India\'s 25th Independence Day!</p>

            <h3>📅 Implementation Timeline</h3>
            <ul>
                <li><strong>1972:</strong> Pincode system officially launched</li>
                <li><strong>1973-1975:</strong> Gradual rollout across major cities</li>
                <li><strong>1980s:</strong> Complete coverage of urban areas</li>
                <li><strong>1990s:</strong> Rural area coverage completed</li>
            </ul>

            <h2>Numerical Wonders</h2>
            
            <h3>📊 Mind-Blowing Statistics</h3>
            <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                <div class="stat-card" style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #007bff; font-size: 2em; margin: 0;">19,300+</h4>
                    <p>Total Pincodes in India</p>
                </div>
                <div class="stat-card" style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #28a745; font-size: 2em; margin: 0;">1,55,000+</h4>
                    <p>Post Offices Covered</p>
                </div>
                <div class="stat-card" style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #dc3545; font-size: 2em; margin: 0;">6,00,000+</h4>
                    <p>Villages Connected</p>
                </div>
                <div class="stat-card" style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                    <h4 style="color: #ffc107; font-size: 2em; margin: 0;">9</h4>
                    <p>Postal Regions</p>
                </div>
            </div>

            <h2>Regional Breakdown</h2>
            
            <h3>🗺️ Pincode Regions and Their Significance</h3>
            <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                <thead>
                    <tr style="background-color: #e9ecef;">
                        <th style="border: 1px solid #ddd; padding: 12px;">First Digit</th>
                        <th style="border: 1px solid #ddd; padding: 12px;">Region</th>
                        <th style="border: 1px solid #ddd; padding: 12px;">States/Areas Covered</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px;"><strong>1</strong></td>
                        <td style="border: 1px solid #ddd; padding: 12px;">Northern</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">Delhi, Punjab, Haryana, Himachal Pradesh, J&K, Ladakh</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px;"><strong>2</strong></td>
                        <td style="border: 1px solid #ddd; padding: 12px;">Northern</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">Uttar Pradesh, Uttarakhand</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px;"><strong>3</strong></td>
                        <td style="border: 1px solid #ddd; padding: 12px;">Western</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">Rajasthan, Gujarat, Maharashtra, Goa, Daman & Diu</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px;"><strong>4</strong></td>
                        <td style="border: 1px solid #ddd; padding: 12px;">Southern</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">Karnataka, Kerala, Tamil Nadu, Andhra Pradesh, Telangana</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px;"><strong>5</strong></td>
                        <td style="border: 1px solid #ddd; padding: 12px;">Eastern</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">West Bengal, Odisha, Bihar, Jharkhand</td>
                    </tr>
                </tbody>
            </table>

            <h2>Fascinating Pincode Facts</h2>
            
            <h3>🏔️ Highest and Lowest Pincodes</h3>
            <ul>
                <li><strong>Highest Altitude Pincode:</strong> 194101 (Leh, Ladakh) - at 11,500+ feet above sea level</li>
                <li><strong>Lowest Altitude Pincode:</strong> 688001 (Alappuzha, Kerala) - near sea level</li>
                <li><strong>Northernmost Pincode:</strong> 194401 (Siachen area)</li>
                <li><strong>Southernmost Pincode:</strong> 629702 (Kanyakumari)</li>
            </ul>

            <h3>🏙️ Metropolitan Marvels</h3>
            <ul>
                <li><strong>Mumbai</strong> has the most pincodes for a single city (over 100)</li>
                <li><strong>Delhi</strong> pincodes all start with 11</li>
                <li><strong>Bangalore</strong> pincodes start with 56</li>
                <li><strong>Chennai</strong> pincodes start with 60</li>
                <li><strong>Kolkata</strong> pincodes start with 70</li>
            </ul>

            <h2>Unique Pincode Patterns</h2>
            
            <h3>🔢 Special Number Sequences</h3>
            <ul>
                <li><strong>111111:</strong> This pincode doesn\'t exist (would be too confusing!)</li>
                <li><strong>123456:</strong> Also doesn\'t exist in the system</li>
                <li><strong>Palindromic Pincodes:</strong> 141141, 151151 exist in some areas</li>
                <li><strong>Repeating Digits:</strong> 444444 doesn\'t exist, but 444001 does</li>
            </ul>

            <h3>🎯 VIP Pincodes</h3>
            <ul>
                <li><strong>110001:</strong> Parliament House, New Delhi</li>
                <li><strong>110011:</strong> President\'s Estate (Rashtrapati Bhavan)</li>
                <li><strong>110021:</strong> Prime Minister\'s Office area</li>
                <li><strong>400001:</strong> Fort area, Mumbai (Financial capital)</li>
            </ul>

            <h2>Technology and Pincodes</h2>
            
            <h3>💻 Digital Age Adaptations</h3>
            <ul>
                <li>Over <strong>2 billion</strong> pincode searches happen online annually</li>
                <li>E-commerce platforms process <strong>50+ million</strong> pincode-based deliveries monthly</li>
                <li>GPS systems now integrate pincode data for better navigation</li>
                <li>Mobile apps can detect your pincode using location services</li>
            </ul>

            <h2>Economic Impact</h2>
            
            <h3>💰 Business and Commerce</h3>
            <ul>
                <li>Pincodes determine delivery charges for most e-commerce platforms</li>
                <li>Insurance premiums often vary by pincode</li>
                <li>Real estate prices are sometimes quoted per pincode area</li>
                <li>Service availability (like food delivery) is pincode-dependent</li>
            </ul>

            <h2>Fun Pincode Trivia</h2>
            
            <h3>🎉 Did You Know?</h3>
            <ul>
                <li>The word "Pincode" is uniquely Indian - other countries don\'t use this term</li>
                <li>Some pincodes serve areas as small as a single large building</li>
                <li>Others cover entire districts in remote areas</li>
                <li>Pincode 110001 receives more mail than any other pincode in India</li>
                <li>The system was inspired by the UK\'s postal code system</li>
                <li>Originally, pincodes were supposed to be 5 digits, but 6 was chosen for better coverage</li>
            </ul>

            <h2>Future of Pincodes</h2>
            <p>As India continues to urbanize and digitize, the pincode system evolves too. New pincodes are regularly added for developing areas, and the system integrates with modern technologies like GPS, AI-powered logistics, and smart city initiatives.</p>

            <h2>Conclusion</h2>
            <p>India\'s pincode system is more than just a postal tool - it\'s a fascinating reflection of our country\'s geography, development, and organizational capabilities. From the historic 110001 to the newest additions, each pincode tells a story of connection, progress, and the incredible diversity of our nation. Next time you write your pincode, remember - you\'re part of one of the world\'s most comprehensive postal systems!</p>
        </div>';
    }
} 

// php artisan db:seed --class=BlogPostSeeder