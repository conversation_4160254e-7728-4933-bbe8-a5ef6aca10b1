<?php

namespace Database\Factories;

use App\Models\Review;
use App\Models\PinCode;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ReviewFactory extends Factory
{
    protected $model = Review::class;

    public function definition()
    {
        return [
            'pincode_id' => PinCode::factory(),
            'user_id' => User::factory(),
            'name' => $this->faker->name,
            'comment' => $this->faker->paragraph,
            'ip_address' => $this->faker->ipv4,
            'user_agent' => $this->faker->userAgent,
            'status' => $this->faker->randomElement(['pending', 'approved', 'rejected'])
        ];
    }

    /**
     * Indicate that the review is approved.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function approved()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'approved',
            ];
        });
    }

    /**
     * Indicate that the review is pending.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function pending()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'pending',
            ];
        });
    }
}