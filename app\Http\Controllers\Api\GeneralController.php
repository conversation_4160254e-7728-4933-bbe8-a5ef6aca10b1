<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class GeneralController extends Controller
{
    public function generalSetting(Request $request): JsonResponse
    {
        return response()->json([
            'settings' => [
                ['key' => 'site_name', 'value' => 'Pincodes API'],
                ['key' => 'support_email', 'value' => '<EMAIL>'],
            ]
        ]);
    }

    public function getCountries(Request $request): JsonResponse
    {
        return response()->json([
            'data' => [
                ['id' => 1, 'name' => 'India', 'code' => 'IN'],
                ['id' => 2, 'name' => 'United States', 'code' => 'US'],
            ]
        ]);
    }
} 