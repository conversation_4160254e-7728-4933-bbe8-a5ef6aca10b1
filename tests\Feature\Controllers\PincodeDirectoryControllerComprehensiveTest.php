<?php

use App\Models\PinCode;
use App\Models\State;
use App\Models\District;
use App\Models\PostOffice;
use App\Models\User;
use App\Models\Review;
use App\Models\Like;
use App\Models\VillagePincode;
use App\Models\ContactNumberChange;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\PincodeDirectoryController;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Reset Faker's unique generator to prevent state leakage between tests
    $this->faker = \Faker\Factory::create();
    $this->faker->unique(true); 
});

afterEach(function () {
    // Mockery::close();
});

/*
|--------------------------------------------------------------------------
| State Listing Tests
|--------------------------------------------------------------------------
*/

test('listofstates displays all states with pincode counts', function () {
    // Arrange
    $state = State::factory()->create(['name' => 'Test State']);
    PinCode::factory()->count(3)->create(['state' => $state->name]);
    
    // Act
    $response = $this->get(route('pincodes.states'));
    
    // Assert
    $response->assertStatus(200)
        ->assertViewIs('pincodes.1-all-state-listing')
        ->assertViewHas('statesData')
        ->assertViewHas('breadcrumbs')
        ->assertViewHas('pageTitle', 'State Wise Pin Codes')
        ->assertViewHas('metaDescription')
        ->assertViewHas('totalPincodes')
        ->assertViewHas('m_states')
        ->assertSee('Test State');
});

// test('listofstates calculates correct percentage of pincodes per state', function () {
//     // Arrange
//     Cache::flush();
//     Cache::forget('states_with_counts');
//     Cache::forget('total_pincodes');
//     $state1 = State::factory()->create(['name' => 'Test State One']);
//     $state2 = State::factory()->create(['name' => 'Test State Two']);
    
//     // Create 75 pincodes for state1 (75%) and 25 for state2 (25%)
//     PinCode::factory()->count(75)->create(['state' => $state1->name]);
//     PinCode::factory()->count(25)->create(['state' => $state2->name]);
    
//     // Act
//     $response = $this->get(route('pincodes.states'));
//     $statesData = $response->viewData('statesData');
    
//     // Assert
//     $this->assertEquals(75, $statesData['Test State One']['count']);
//     $this->assertEquals(25, $statesData['Test State Two']['count']);
//     $this->assertEquals(75, $statesData['Test State One']['percentage']);
//     $this->assertEquals(25, $statesData['Test State Two']['percentage']);
// });

test('listofstates handles empty database gracefully', function () {
    // Arrange
    Cache::flush();
    // Ensure no states exist
    State::query()->delete();
    PinCode::query()->delete();
    
    // Act
    $response = $this->get(route('pincodes.states'));
    
    // Assert
    $response->assertStatus(200)
        ->assertViewIs('pincodes.1-all-state-listing');
    
    $statesData = $response->viewData('statesData');
    $this->assertEmpty($statesData);
    
    $totalPincodes = $response->viewData('totalPincodes');
    $this->assertEquals(0, $totalPincodes);
});

/*
|--------------------------------------------------------------------------
| District Listing Tests
|--------------------------------------------------------------------------
*/

test('listofdistricts displays districts for a valid state', function () {
    // Arrange
    $state = State::factory()->create(['name' => 'Maharashtra']);
    $districts = District::factory()->count(3)->create(['state_id' => $state->id]);
    
    // Create pincodes for each district
    foreach ($districts as $district) {
        PinCode::factory()->count(2)->create([
            'state' => $state->name,
            'district' => $district->name
        ]);
    }
    
    // Act
    $response = $this->get(route('pincodes.districts', ['state' => 'Maharashtra']));
    
    // Assert
    $response->assertStatus(200)
        ->assertViewIs('pincodes.2-pincodes-of-single-state')
        ->assertViewHas('stateList')
        ->assertViewHas('breadcrumbs')
        ->assertViewHas('pageTitle')
        ->assertViewHas('metaDescription')
        ->assertViewHas('m_states')
        ->assertViewHas('m_districts')
        ->assertViewHas('pincode_counts');
    
    // Check that all districts are present
    foreach ($districts as $district) {
        $response->assertSee($district->name);
    }
});

test('listofdistricts returns 404 for non-existent state', function () {
    // Act & Assert
    $this->get(route('pincodes.districts', ['state' => 'NonExistentState']))
        ->assertStatus(404);
});

test('listofdistricts shows correct pincode counts for each district', function () {
    // Arrange
    $state = State::factory()->create(['name' => 'Karnataka']);
    $district1 = District::factory()->create(['name' => 'Bangalore', 'state_id' => $state->id]);
    $district2 = District::factory()->create(['name' => 'Mysore', 'state_id' => $state->id]);
    
    // Create 5 pincodes for Bangalore and 3 for Mysore
    PinCode::factory()->count(5)->create([
        'state' => $state->name,
        'district' => $district1->name
    ]);
    
    PinCode::factory()->count(3)->create([
        'state' => $state->name,
        'district' => $district2->name
    ]);
    
    // Act
    $response = $this->get(route('pincodes.districts', ['state' => 'Karnataka']));
    
    // Assert
    $pincode_counts = $response->viewData('pincode_counts');
    $this->assertEquals(5, $pincode_counts['Bangalore']);
    $this->assertEquals(3, $pincode_counts['Mysore']);
});

/*
|--------------------------------------------------------------------------
| Post Office Listing Tests
|--------------------------------------------------------------------------
*/

test('listofpostoffices displays post offices for a valid district', function () {
    // Arrange
    $state = State::factory()->create(['name' => 'Gujarat']);
    $district = District::factory()->create(['name' => 'Ahmedabad', 'state_id' => $state->id]);
    
    // Create pincodes with different post offices
    $pincode1 = PinCode::factory()->create([
        'pincode' => '380001',
        'state' => $state->name,
        'district' => $district->name,
        'name' => 'Navrangpura'
    ]);
    
    $pincode2 = PinCode::factory()->create([
        'pincode' => '380001',
        'state' => $state->name,
        'district' => $district->name,
        'name' => 'Ambawadi'
    ]);
    
    $pincode3 = PinCode::factory()->create([
        'pincode' => '380015',
        'state' => $state->name,
        'district' => $district->name,
        'name' => 'Satellite'
    ]);
    
    // Act
    $response = $this->get(route('pincodes.postoffices', [
        'state' => 'Gujarat',
        'district' => 'Ahmedabad'
    ]));
    
    // Assert
    $response->assertStatus(200)
        ->assertViewIs('pincodes.3-pincodes-of-single-district')
        ->assertViewHas('breadcrumbs')
        ->assertViewHas('po_counts')
        ->assertViewHas('m_states')
        ->assertViewHas('new_state_name', 'Gujarat')
        ->assertViewHas('new_district_name', 'Ahmedabad')
        ->assertViewHas('m_districts')
        ->assertViewHas('pageTitle')
        ->assertViewHas('metaDescription')
        ->assertViewHas('pin_codes')
        ->assertViewHas('post_offices');
    
    // Check that all pincodes and post offices are present
    $response->assertSee('380001');
    $response->assertSee('380015');
    $response->assertSee('Navrangpura');
    $response->assertSee('Ambawadi');
    $response->assertSee('Satellite');
    
    // Verify post office counts
    $po_counts = $response->viewData('po_counts');
    $this->assertEquals(2, $po_counts['380001']); // Two post offices for this pincode
    $this->assertEquals(1, $po_counts['380015']); // One post office for this pincode
});

test('listofpostoffices returns 404 for non-existent district', function () {
    // Arrange
    $state = State::factory()->create(['name' => 'Tamil Nadu']);
    
    // Act & Assert
    $this->get(route('pincodes.postoffices', [
        'state' => 'Tamil Nadu',
        'district' => 'NonExistentDistrict'
    ]))->assertStatus(404);
});

test('listofpostoffices handles exceptions gracefully', function () {
    // Arrange
    $state = State::factory()->create(['name' => 'Kerala']);
    $district = District::factory()->create(['name' => 'Kochi', 'state_id' => $state->id]);
    
    // Mock the Cache facade to throw an exception
    Cache::shouldReceive('remember')
        ->andThrow(new \Exception('Test exception'));
    
    // Act & Assert
    $response = $this->get(route('pincodes.postoffices', [
        'state' => 'Kerala',
        'district' => 'Kochi'
    ]));
    
    $response->assertStatus(500);
});

/*
|--------------------------------------------------------------------------
| Post Office Detail Tests
|--------------------------------------------------------------------------
*/

test('searchByStateDistrictAndName displays post office details', function () {
    // Arrange
    $state = State::factory()->create(['name' => 'Rajasthan']);
    $district = District::factory()->create(['name' => 'Jaipur', 'state_id' => $state->id]);
    
    $pincode = PinCode::factory()->create([
        'pincode' => '302001',
        'state' => $state->name,
        'district' => $district->name,
        'name' => 'Vaishali Nagar',
        'latitude' => 26.9124,
        'longitude' => 75.7873
    ]);
    
    // Create a district HO for distance calculation
    $districtHO = PinCode::factory()->create([
        'pincode' => '302002',
        'state' => $state->name,
        'district' => 'Jaipur',
        'name' => 'Jaipur',
        'branch_type' => 'HO',
        'latitude' => 26.9239,
        'longitude' => 75.8267
    ]);
    
    // Act
    $response = $this->get(route('pincodes.details-by-name', [
        'state' => 'Rajasthan',
        'district' => 'Jaipur',
        'name' => 'Vaishali Nagar'
    ]));
    
    // Assert
    $response->assertStatus(200)
        ->assertViewIs('pincodes.4-pincodes-by-post-office-name')
        ->assertViewHas('breadcrumbs')
        ->assertViewHas('pincodes')
        ->assertViewHas('pageTitle')
        ->assertViewHas('metaDescription')
        ->assertViewHas('relatedPincodes')
        ->assertViewHas('m_states')
        ->assertViewHas('imgPath')
        ->assertViewHas('dist_url')
        ->assertViewHas('distance')
        ->assertViewHas('reviews')
        ->assertSee('Vaishali Nagar')
        ->assertSee('302001');
    
    // Check that distance calculation works
    $distance = $response->viewData('distance');
    $this->assertNotNull($distance);
    $this->assertIsFloat($distance);
});

test('searchByStateDistrictAndName returns 404 for non-existent post office', function () {
    // Arrange
    $state = State::factory()->create(['name' => 'Bihar']);
    $district = District::factory()->create(['name' => 'Patna', 'state_id' => $state->id]);
    
    // Act & Assert
    $this->get(route('pincodes.details-by-name', [
        'state' => 'Bihar',
        'district' => 'Patna',
        'name' => 'NonExistentPostOffice'
    ]))->assertStatus(404);
});

test('searchByStateDistrictAndName shows related pincodes', function () {
    // Arrange
    $state = State::factory()->create(['name' => 'Uttar Pradesh']);
    $district = District::factory()->create(['name' => 'Lucknow', 'state_id' => $state->id]);
    
    // Create main pincode
    $mainPincode = PinCode::factory()->create([
        'pincode' => '226001',
        'state' => $state->name,
        'district' => $district->name,
        'name' => 'Hazratganj'
    ]);
    
    // Create related pincodes with same pincode
    $relatedPincode1 = PinCode::factory()->create([
        'pincode' => '226001',
        'state' => $state->name,
        'district' => $district->name,
        'name' => 'Chowk'
    ]);
    
    $relatedPincode2 = PinCode::factory()->create([
        'pincode' => '226001',
        'state' => $state->name,
        'district' => $district->name,
        'name' => 'Aminabad'
    ]);
    
    // Act
    $response = $this->get(route('pincodes.details-by-name', [
        'state' => 'Uttar Pradesh',
        'district' => 'Lucknow',
        'name' => 'Hazratganj'
    ]));
    
    // Assert
    $response->assertStatus(200);
    
    $relatedPincodes = $response->viewData('relatedPincodes');
    $this->assertCount(2, $relatedPincodes);
    $this->assertTrue($relatedPincodes->contains('name', 'Chowk'));
    $this->assertTrue($relatedPincodes->contains('name', 'Aminabad'));
});

/*
|--------------------------------------------------------------------------
| Pincode Detail Tests
|--------------------------------------------------------------------------
*/

test('searchByStateDistrictAndPincode displays pincode details', function () {
    // Arrange
    $state = State::factory()->create(['name' => 'Haryana']);
    $district = District::factory()->create(['name' => 'Gurgaon', 'state_id' => $state->id]);
    
    // Create multiple post offices with same pincode
    $pincode1 = PinCode::factory()->create([
        'pincode' => '122001',
        'state' => $state->name,
        'district' => $district->name,
        'name' => 'Sector 14'
    ]);
    
    $pincode2 = PinCode::factory()->create([
        'pincode' => '122001',
        'state' => $state->name,
        'district' => $district->name,
        'name' => 'Sector 15'
    ]);
    
    // Create related pincodes in same district
    $relatedPincode = PinCode::factory()->create([
        'pincode' => '122002',
        'state' => $state->name,
        'district' => $district->name,
        'name' => 'Sector 56'
    ]);
    
    // Create villages with this pincode
    VillagePincode::create([
        'state_id' => $state->id,
        'state_name_en' => $state->name,
        'district_name_en' => $district->name,
        'village_name_en' => 'Sikanderpur',
        'pincode' => '122001'
    ]);
    
    // Act
    $response = $this->get(route('pincodes.pincode', [
        'state' => 'Haryana',
        'district' => 'Gurgaon',
        'pincode' => '122001'
    ]));
    
    // Assert
    $response->assertStatus(200)
        ->assertViewIs('pincodes.5-pincode-wise-post-offices')
        ->assertViewHas('breadcrumbs')
        ->assertViewHas('relatedPincodes')
        ->assertViewHas('pincodes')
        ->assertViewHas('villageList')
        ->assertViewHas('po_count')
        ->assertViewHas('pincode', '122001')
        ->assertViewHas('district', 'Gurgaon')
        ->assertViewHas('state', 'Haryana')
        ->assertViewHas('pageTitle')
        ->assertViewHas('metaDescription')
        ->assertViewHas('m_states')
        ->assertSee('122001')
        ->assertSee('Sector 14')
        ->assertSee('Sector 15')
        ->assertSee('Sikanderpur');
    
    // Check post office count
    $po_count = $response->viewData('po_count');
    $this->assertEquals(2, $po_count);
    
    // Check related pincodes
    $relatedPincodes = $response->viewData('relatedPincodes');
    $this->assertTrue($relatedPincodes->contains('pincode', '122002'));
});

test('searchByStateDistrictAndPincode returns 404 for invalid pincode length', function () {
    // Arrange
    $state = State::factory()->create(['name' => 'Madhya Pradesh']);
    $district = District::factory()->create(['name' => 'Indore', 'state_id' => $state->id]);
    
    // Act & Assert - Pincode with less than 6 digits
    $this->get(route('pincodes.pincode', [
        'state' => 'Madhya Pradesh',
        'district' => 'Indore',
        'pincode' => '12345'
    ]))->assertStatus(404);
    
    // Act & Assert - Pincode with more than 6 digits
    $this->get(route('pincodes.pincode', [
        'state' => 'Madhya Pradesh',
        'district' => 'Indore',
        'pincode' => '1234567'
    ]))->assertStatus(404);
});

test('searchByStateDistrictAndPincode returns 404 for non-existent pincode', function () {
    // Arrange
    $state = State::factory()->create(['name' => 'Assam']);
    $district = District::factory()->create(['name' => 'Guwahati', 'state_id' => $state->id]);
    
    // Act & Assert
    $this->get(route('pincodes.pincode', [
        'state' => 'Assam',
        'district' => 'Guwahati',
        'pincode' => '999999' // Non-existent pincode
    ]))->assertStatus(404);
});

/*
|--------------------------------------------------------------------------
| Search Tests
|--------------------------------------------------------------------------
*/

test('searchPincode returns results for pincode search', function () {
    // Arrange
    PinCode::factory()->create([
        'pincode' => '400001',
        'name' => 'Mumbai GPO'
    ]);
    
    PinCode::factory()->create([
        'pincode' => '400002',
        'name' => 'Colaba'
    ]);
    
    // Act
    $response = $this->get(route('search', [
        'query' => '4000',
        'type' => 'pincode'
    ]));
    
    // Assert
    $response->assertStatus(200)
        ->assertViewIs('pincodes.search-results')
        ->assertViewHas('results')
        ->assertViewHas('query', '4000')
        ->assertViewHas('type', 'pincode')
        ->assertSee('400001')
        ->assertSee('400002');
    
    $results = $response->viewData('results');
    $this->assertEquals(2, $results->count());
});

test('searchPincode returns results for name search', function () {
    // Arrange
    PinCode::factory()->create([
        'pincode' => '500001',
        'name' => 'Hyderabad GPO'
    ]);
    
    PinCode::factory()->create([
        'pincode' => '500036',
        'name' => 'Hyderabad Central'
    ]);
    
    PinCode::factory()->create([
        'pincode' => '600001',
        'name' => 'Chennai GPO'
    ]);
    
    // Act
    $response = $this->get(route('search', [
        'query' => 'Hyderabad',
        'type' => 'name'
    ]));
    
    // Assert
    $response->assertStatus(200)
        ->assertViewIs('pincodes.search-results')
        ->assertViewHas('results')
        ->assertViewHas('query', 'Hyderabad')
        ->assertViewHas('type', 'name')
        ->assertSee('Hyderabad GPO')
        ->assertSee('Hyderabad Central')
        ->assertDontSee('Chennai GPO');
    
    $results = $response->viewData('results');
    $this->assertEquals(2, $results->count());
});

test('searchPincode handles empty query', function () {
    // Act
    $response = $this->get(route('search', [
        'query' => '',
        'type' => 'pincode'
    ]));
    
    // Assert
    $response->assertStatus(200)
        ->assertViewIs('pincodes.search-results')
        ->assertViewHas('results', null)
        ->assertViewHas('query', '')
        ->assertViewHas('type', 'pincode');
});

test('searchPincode returns JSON response for AJAX requests', function () {
    // Arrange
    PinCode::factory()->count(3)->create([
        'pincode' => '700001',
        'name' => 'Kolkata GPO'
    ]);
    
    // Act
    $response = $this->getJson(route('search', [
        'query' => '7000',
        'type' => 'pincode'
    ]));
    
    // Assert
    $response->assertStatus(200)
        ->assertJsonStructure([
            'results',
            'pagination' => [
                'total',
                'per_page',
                'current_page',
                'last_page'
            ]
        ]);
    
    $data = $response->json();
    $this->assertCount(3, $data['results']);
});

/*
|--------------------------------------------------------------------------
| Review Tests
|--------------------------------------------------------------------------
*/

test('storeReviews creates review for authenticated user', function () {
    // Arrange
    $user = User::factory()->create();
    $pincode = PinCode::factory()->create();
    
    // Act
    $response = $this->actingAs($user)->post(route('reviews.store'), [
        'pincode_id' => $pincode->id,
        'rating' => 5,
        'comment' => 'This is a great post office.'
    ]);
    
    // Assert
    $response->assertRedirect()
        ->assertSessionHas('success');
    
    $this->assertDatabaseHas('reviews', [
        'pincode_id' => $pincode->id,
        'user_id' => $user->id,
        'rating' => 5,
        'comment' => 'This is a great post office.',
        'name' => $user->name,
        'status' => 'pending'
    ]);
});

test('storeReviews creates review for guest user', function () {
    // Arrange
    $pincode = PinCode::factory()->create();
    
    // Act
    $response = $this->post(route('reviews.store'), [
        'pincode_id' => $pincode->id,
        'name' => 'Guest User',
        'rating' => 5,
        'comment' => 'Good service.'
    ]);
    
    // Assert
    $response->assertRedirect()
        ->assertSessionHas('success');
    
    $this->assertDatabaseHas('reviews', [
        'pincode_id' => $pincode->id,
        'user_id' => null,
        'name' => 'Guest User',
        'rating' => 5,
        'comment' => 'Good service.',
        'status' => 'pending'
    ]);
});

test('storeReviews validates required fields', function () {
    // Act & Assert - Missing pincode_id
    $response = $this->post(route('reviews.store'), [
        'rating' => 5,
        'comment' => 'This is a great post office.'
    ]);
    
    $response->assertSessionHasErrors('pincode_id');
    
    // Act & Assert - Missing rating
    $pincode = PinCode::factory()->create();
    $response = $this->post(route('reviews.store'), [
        'pincode_id' => $pincode->id,
        'comment' => 'This is a great post office.'
    ]);
    
    $response->assertSessionHasErrors('rating');
    
    // Act & Assert - Missing comment
    $response = $this->post(route('reviews.store'), [
        'pincode_id' => $pincode->id,
        'rating' => 5
    ]);
    
    $response->assertSessionHasErrors('comment');
});

test('storeReviews prevents duplicate reviews', function () {
    // Arrange
    $pincode = PinCode::factory()->create();
    $user = User::factory()->create();
    Review::factory()->create([
        'pincode_id' => $pincode->id,
        'user_id' => $user->id,
        'ip_address' => '127.0.0.1', // Ensure IP is set for consistency
    ]);

    // Act
    $response = $this->actingAs($user) // Authenticate the request
        ->from(route('pincodes.details-by-name', [
            'state' => $pincode->state,
            'district' => $pincode->district,
            'name' => $pincode->name
        ]))->post(route('reviews.store'), [
            'pincode_id' => $pincode->id,
            'rating' => 4,
            'comment' => 'Second review'
        ]);

    // Assert
    $response->assertRedirect()
        ->assertSessionHas('success', 'You have already reviewed this pincode.');

    $this->assertDatabaseCount('reviews', 1);
});

test('storeReviews prevents spam by rate limiting', function () {
    // Arrange
    $pincode = PinCode::factory()->create();
    $ipAddress = '***********';
    Review::factory()->create([
        'pincode_id' => $pincode->id,
        'ip_address' => $ipAddress,
    ]);

    // Act
    $response = $this->withServerVariables(['REMOTE_ADDR' => $ipAddress])
        ->post(route('reviews.store'), [
            'pincode_id' => $pincode->id,
            'name' => 'Spam User',
            'rating' => 1,
            'comment' => 'Spam review'
        ]);

    // Assert
    $response->assertRedirect()
        ->assertSessionHas('success', 'You have already reviewed this pincode.');

    // Verify the second review was not created
    $this->assertDatabaseCount('reviews', 1);
});

test('viewAllReviews displays all reviews for a pincode', function () {
    // Arrange
    $pincode = PinCode::factory()->create([
        'pincode' => '110001',
        'name' => 'New Delhi GPO'
    ]);
    
    // Create some reviews
    Review::factory()->count(3)->create([
        'pincode_id' => $pincode->id,
        'status' => 'approved'
    ]);
    
    // Act
    $response = $this->get(route('reviews.all', $pincode->id));
    
    // Assert
    $response->assertStatus(200)
        ->assertViewIs('pincodes.reviews')
        ->assertViewHas('reviews')
        ->assertViewHas('pincode_id', $pincode->id)
        ->assertViewHas('breadcrumbs')
        ->assertViewHas('pageTitle')
        ->assertViewHas('metaDescription')
        ->assertViewHas('m_states');
    
    $reviews = $response->viewData('reviews');
    $this->assertCount(3, $reviews);
});

/*
|--------------------------------------------------------------------------
| Like Tests
|--------------------------------------------------------------------------
*/

test('storeLikes adds a like for a pincode', function () {
    // Arrange
    $pincode = PinCode::factory()->create();
    
    // Act
    $response = $this->postJson(route('likes.store'), [
        'pincode_id' => $pincode->id
    ]);
    
    // Assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Like added successfully',
            'likes_count' => 1
        ]);
    
    $this->assertDatabaseHas('likes', [
        'pincode_id' => $pincode->id
    ]);
});

test('storeLikes prevents duplicate likes from same IP', function () {
    // Arrange
    $pincode = PinCode::factory()->create();
    $ipAddress = '***********';
    
    // Create an existing like
    Like::create([
        'pincode_id' => $pincode->id,
        'ip_address' => $ipAddress
    ]);
    
    // Mock the request IP
    $this->withServerVariables(['REMOTE_ADDR' => $ipAddress]);
    
    // Act
    $response = $this->postJson(route('likes.store'), [
        'pincode_id' => $pincode->id
    ]);
    
    // Assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => false,
            'message' => 'Already liked'
        ]);
    
    // Verify no additional like was created
    $this->assertDatabaseCount('likes', 1);
});

test('storeLikes handles exceptions gracefully', function () {
    // Arrange
    $pincode = PinCode::factory()->create();
    
    // Mock the Like model to throw an exception
    $this->mock(Like::class, function ($mock) {
        $mock->shouldReceive('where')->andReturnSelf();
        $mock->shouldReceive('first')->andReturn(null);
        $mock->shouldReceive('create')->andThrow(new \Exception('Test exception'));
    });
    
    // Act
    $response = $this->postJson(route('likes.store'), [
        'pincode_id' => $pincode->id
    ]);
    
    // Assert
    $response->assertStatus(500)
        ->assertJson([
            'success' => false,
            'message' => 'Error adding like'
        ]);
});

/*
|--------------------------------------------------------------------------
| Contact Number Change Tests
|--------------------------------------------------------------------------
*/

test('changeContactNumber submits contact number change request', function () {
    // Arrange
    $user = User::factory()->create();
    $pincode = PinCode::factory()->create([
        'name' => 'Test Post Office',
        'contact_number' => '1234567890'
    ]);
    
    // Act
    $response = $this->actingAs($user)->postJson(route('contact.change', $pincode->name), [
        'contact_number' => '9876543210',
        'reason' => 'Number has changed'
    ]);
    
    // Assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Contact number change request submitted successfully.'
        ]);
    
    $this->assertDatabaseHas('contact_number_changes', [
        'pincode_id' => $pincode->id,
        'old_number' => '1234567890',
        'new_number' => '9876543210',
        'reason' => 'Number has changed',
        'status' => 'pending',
        'user_id' => $user->id
    ]);
});

test('changeContactNumber validates required fields', function () {
    // Arrange
    $pincode = PinCode::factory()->create([
        'name' => 'Test Post Office'
    ]);
    
    // Act & Assert - Missing contact_number
    $response = $this->postJson(route('contact.change', $pincode->name), [
        'reason' => 'Number has changed'
    ]);
    
    $response->assertStatus(422)
        ->assertJsonValidationErrors('contact_number');
    
    // Act & Assert - Missing reason
    $response = $this->postJson(route('contact.change', $pincode->name), [
        'contact_number' => '9876543210'
    ]);
    
    $response->assertStatus(422)
        ->assertJsonValidationErrors('reason');
});

test('changeContactNumber returns 404 for non-existent post office', function () {
    // Act & Assert
    $response = $this->postJson(route('contact.change', 'NonExistentPostOffice'), [
        'contact_number' => '9876543210',
        'reason' => 'Number has changed'
    ]);
    
    $response->assertStatus(404);
});

test('changeContactNumber handles exceptions gracefully', function () {
    // Arrange
    $pincode = PinCode::factory()->create([
        'name' => 'Test Post Office'
    ]);
    
    // Mock the ContactNumberChange model to throw an exception
    $this->mock(ContactNumberChange::class, function ($mock) {
        $mock->shouldReceive('create')->andThrow(new \Exception('Test exception'));
    });
    
    // Act
    $response = $this->postJson(route('contact.change', $pincode->name), [
        'contact_number' => '9876543210',
        'reason' => 'Number has changed'
    ]);
    
    // Assert
    $response->assertStatus(500)
        ->assertJson([
            'success' => false,
            'message' => 'Failed to submit contact number change request.'
        ]);
});

/*
|--------------------------------------------------------------------------
| All Pincode List Tests
|--------------------------------------------------------------------------
*/

test('allPincodelist displays paginated list of all pincodes', function () {
    // Arrange
    PinCode::query()->delete();
    PinCode::factory()->count(60)->create(); // Create 60 pincodes to test pagination
    
    // Act
    $response = $this->get(route('pincodes.all'));
    
    // Assert
    $response->assertStatus(200)
        ->assertViewIs('pincodes.allPincodelist')
        ->assertViewHas('breadcrumbs')
        ->assertViewHas('pincodes')
        ->assertViewHas('metaDescription')
        ->assertViewHas('pageTitle', 'India Postal Code List')
        ->assertViewHas('m_states');
    
    $pincodes = $response->viewData('pincodes');
    $this->assertEquals(50, $pincodes->count()); // Default pagination is 50 items
    $this->assertEquals(60, $pincodes->total());
    $this->assertEquals(2, $pincodes->lastPage());
});

test('allPincodelist uses cache for better performance', function () {
    // Arrange
    $cacheMock = \Mockery::mock('Illuminate\Cache\CacheManager');
    $this->app->instance('cache', $cacheMock);

    $states = State::factory()->count(5)->create();
    $cacheMock->shouldReceive('remember')
        ->with('all_states', \Mockery::any(), \Mockery::any())
        ->once()
        ->andReturn($states);
    $cacheMock->shouldReceive('get')->andReturn(null);
    $cacheMock->shouldIgnoreMissing();

    // Act
    $response = $this->get(route('pincodes.all'));
    
    // Assert
    $response->assertStatus(200)
        ->assertViewIs('pincodes.allPincodelist');
});