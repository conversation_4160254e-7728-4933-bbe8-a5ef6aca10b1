<?php

use App\Models\BlogPost;
use App\Models\BlogPostCategory;
use App\Models\BlogPostTag;
use App\Models\CourierDict;
use App\Models\Page;
use App\Models\Pincode;
use App\Models\Plan;
use App\Models\State;
use App\Models\Tool;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

// ============================================================================
// HOME AND SEARCH ROUTES
// ============================================================================

test('home page loads successfully', function () {
    $response = $this->get('/');
    
    $response->assertStatus(200);
    $response->assertViewIs('home.welcome-modern-animated');
});

test('search page loads successfully', function () {
    $response = $this->get('/search');
    
    $response->assertStatus(200);
    $response->assertViewIs('pincodes.search-results');
});

// ============================================================================
// PAGES ROUTES
// ============================================================================

test('contact us page loads successfully', function () {
    $response = $this->get('/pages/contact-us');
    
    $response->assertStatus(200);
    $response->assertViewIs('pages.contact-us');
});

test('about us page loads successfully', function () {
    $response = $this->get('/pages/about-us');
    
    $response->assertStatus(200);
    $response->assertViewIs('pages.about-us');
});

test('dynamic page shows active page', function () {
    $page = Page::factory()->create([
        'slug' => 'test-page',
        'is_active' => true
    ]);
    
    $response = $this->get('/pages/test-page');
    
    $response->assertStatus(200);
    $response->assertViewIs('pages.default');
});

test('dynamic page returns 404 for inactive page', function () {
    $page = Page::factory()->create([
        'slug' => 'inactive-page',
        'is_active' => false
    ]);
    
    $response = $this->get('/pages/inactive-page');
    
    $response->assertStatus(404);
});

test('dynamic page returns 404 for non-existent page', function () {
    $response = $this->get('/pages/non-existent-page');
    
    $response->assertStatus(404);
});

test('contact form submission works', function () {
    $response = $this->post('/contact-submit', [
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'subject' => 'Test Subject',
        'message' => 'Test message content'
    ]);
    
    $response->assertStatus(302); // Redirect after submission
});

test('cookie consent update works', function () {
    $response = $this->post('/cookies-consent', [
        'consent' => true
    ]);
    
    $response->assertStatus(302);
});

// ============================================================================
// API DOCUMENTATION ROUTES
// ============================================================================

test('api documentation page loads successfully', function () {
    $response = $this->get('pincodes/api-documentation');
    
    $response->assertStatus(200);
    $response->assertViewIs('api-docs.index');
});

// ============================================================================
// PLANS ROUTES
// ============================================================================

test('public plans page loads successfully', function () {
    $response = $this->get('/plans');
    
    $response->assertStatus(200);
    $response->assertViewIs('plans.public-view');
});

test('authenticated user can access user plans', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->get('/user/plans');
    
    $response->assertStatus(200);
});

test('unauthenticated user cannot access user plans', function () {
    $response = $this->get('/user/plans');
    
    $response->assertRedirect('/login');
});

// ============================================================================
// PINCODE DIRECTORY ROUTES
// ============================================================================

test('all pincodes list loads successfully', function () {
    $response = $this->get('/all-pincodes');
    
    $response->assertStatus(200);
});

test('states list loads successfully', function () {
    $response = $this->get('/pincodes');
    
    $response->assertStatus(200);
    $response->assertViewIs('pincodes.1-all-state-listing');
});

test('districts list loads for valid state', function () {
    $state = State::factory()->create(['name' => 'Delhi']);
    
    $response = $this->get('/pincodes/Delhi');
    
    $response->assertStatus(200);
    $response->assertViewIs('pincodes.2-pincodes-of-single-state');
});

test('pincode redirect works for PIN format', function () {
    $response = $this->get('/pincodes/Delhi/New Delhi/postal-code/110001');
    
    $response->assertStatus(301);
    $response->assertRedirect('/pincodes/Delhi/New Delhi/postal-code/110001');
});

test('review submission works', function () {
    $pincode = Pincode::factory()->create();
    
    $response = $this->post('/reviews', [
        'pincode_id' => $pincode->id,
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'rating' => 5,
        'comment' => 'Great service!'
    ]);
    
    $response->assertStatus(302);
});

test('likes submission works', function () {
    $pincode = Pincode::factory()->create();
    
    $response = $this->post('/likes', [
        'pincode_id' => $pincode->id
    ]);
    
    $response->assertStatus(302); // Redirect after submission
});

test('view all reviews loads successfully', function () {
    $pincode = Pincode::factory()->create();
    
    $response = $this->get("/reviews/{$pincode->id}");
    
    $response->assertStatus(200);
});

// ============================================================================
// COURIER DICTIONARY ROUTES
// ============================================================================

test('courier dictionary index loads successfully', function () {
    $response = $this->get('/courier-dictionary');
    
    $response->assertStatus(200);
    $response->assertViewIs('dict.index');
});

test('courier dictionary search works', function () {
    $response = $this->get('/dictionary/search?q=test');
    
    $response->assertStatus(200);
});

test('courier dictionary show page loads for valid slug', function () {
    $courier = CourierDict::factory()->create(['term' => 'test-courier']);
    
    $response = $this->get("/courier-dictionary/{$courier->slug}");
    
    $response->assertStatus(200);
    $response->assertViewIs('dict.show');
});

test('courier dictionary autocomplete works', function () {
    $response = $this->get('/api/dict/autocomplete?q=test');
    
    $response->assertStatus(200);
    $response->assertJsonStructure([
        '*' => ['term', 'slug']
    ]);
});

// ============================================================================
// BLOG ROUTES
// ============================================================================

test('blog index loads successfully', function () {
    $response = $this->get('/blog');
    
    $response->assertStatus(200);
    $response->assertViewIs('blog-post.index');
});

test('blog post show loads for valid slug', function () {
    $post = BlogPost::factory()->create(['slug' => 'test-post']);
    
    $response = $this->get('/blog/test-post');
    
    $response->assertStatus(200);
    $response->assertViewIs('blog-post.show');
});

test('blog category view loads for valid slug', function () {
    $category = BlogPostCategory::factory()->create(['slug' => 'test-category']);
    
    $response = $this->get('/blog/category/test-category');
    
    $response->assertStatus(200);
    $response->assertViewIs('blog-post.category');
});

test('blog tag view loads for valid slug', function () {
    $tag = BlogPostTag::factory()->create(['slug' => 'test-tag']);
    
    $response = $this->get('/blog/tag/test-tag');
    
    $response->assertStatus(200);
    $response->assertViewIs('blog-post.tag');
});

test('authenticated user can manage blog post tags', function () {
    $user = User::factory()->create();
    $post = BlogPost::factory()->create();
    
    $response = $this->actingAs($user)->post("/posts/{$post->id}/tags", [
        'tags' => ['tag1', 'tag2']
    ]);
    
    $response->assertStatus(302);
});

test('authenticated user can store comment', function () {
    $user = User::factory()->create();
    $post = BlogPost::factory()->create();
    
    $response = $this->actingAs($user)->post("/posts/{$post->id}/comments", [
        'content' => 'Test comment'
    ]);
    
    $response->assertStatus(302);
});

test('guest user can store comment with name and email', function () {
    $post = BlogPost::factory()->create();
    
    $response = $this->post("/posts/{$post->id}/comments", [
        'content' => 'Test guest comment',
        'guest_name' => 'John Doe',
        'guest_email' => '<EMAIL>'
    ]);
    
    $response->assertStatus(302);
});

test('authenticated user can update comment', function () {
    $user = User::factory()->create();
    $post = BlogPost::factory()->create();
    $comment = $post->comments()->create([
        'user_id' => $user->id,
        'content' => 'Original comment'
    ]);
    
    $response = $this->actingAs($user)->patch("/comments/{$comment->id}", [
        'content' => 'Updated comment'
    ]);
    
    $response->assertStatus(302);
});

test('authenticated user can delete comment', function () {
    $user = User::factory()->create();
    $post = BlogPost::factory()->create();
    $comment = $post->comments()->create([
        'user_id' => $user->id,
        'content' => 'Test comment'
    ]);
    
    $response = $this->actingAs($user)->delete("/comments/{$comment->id}");
    
    $response->assertStatus(302);
});

test('unauthenticated user cannot store comment without guest fields', function () {
    $post = BlogPost::factory()->create();
    
    $response = $this->post("/posts/{$post->id}/comments", [
        'content' => 'Test comment'
    ]);
    
    $response->assertSessionHasErrors(['guest_name', 'guest_email']);
});

// ============================================================================
// LOCATION ROUTES
// ============================================================================

test('get districts for state works', function () {
    $state = State::factory()->create();
    $district = \App\Models\District::factory()->create(['state_id' => $state->id]);
    $response = $this->get("/get-districts/{$state->id}");
    $response->assertStatus(200);
    $response->assertJsonStructure([
        ['id', 'state_id', 'name']
    ]);
});

test('get downloadable data works', function () {
    $response = $this->post('/get-downloadable-data', [
        'state_id' => 1,
        'district_id' => 1
    ]);
    
    $response->assertStatus(200);
});

// ============================================================================
// TOOLS ROUTES
// ============================================================================

test('pincode of my location redirects correctly', function () {
    $response = $this->get('pincode-of-my-location');
    
    $response->assertStatus(301);
    $response->assertRedirect('/tools/pincode-of-my-location');
})->skip();

test('tool review submission works', function () {
    $tool = Tool::factory()->create();
    
    $response = $this->post("/tools/{$tool->id}/reviews", [
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'review' => 'Great tool!'
    ]);
    
    $response->assertStatus(302);
});

test('tool reviews view loads successfully', function () {
    $tool = Tool::factory()->create(['slug' => 'test-tool']);
    
    $response = $this->get('/tools/test-tool/reviews');
    
    $response->assertStatus(200);
});

test('validate pincode works', function () {
    $response = $this->post('/validate-pincode', [
        'pincode' => '110001'
    ]);
    
    $response->assertStatus(200);
});

// ============================================================================
// ERROR HANDLING TESTS
// ============================================================================

test('404 error for non-existent routes', function () {
    $response = $this->get('/non-existent-route');
    
    $response->assertStatus(404);
});

test('method not allowed for wrong HTTP method', function () {
    $response = $this->post('/pincodes'); // Should be GET
    
    $response->assertStatus(405);
});

// ============================================================================
// REDIRECT TESTS
// ============================================================================

test('old pincode format redirects to new format', function () {
    $response = $this->get('/pincodes/Delhi/New Delhi/postal-code/110001');
    
    $response->assertStatus(301);
    $response->assertRedirect('/pincodes/Delhi/New Delhi/postal-code/110001');
});

test('pincode of my location redirects to tools', function () {
    $response = $this->get('pincode-of-my-location');
    
    $response->assertStatus(301);
    $response->assertRedirect('/tools/pincode-of-my-location');
})->skip();

// ============================================================================
// MIDDLEWARE TESTS
// ============================================================================

test('auth middleware protects user routes', function () {
    $response = $this->get('/user/plans');
    
    $response->assertRedirect('/login');
});

test('auth middleware allows authenticated users', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->get('/user/plans');
    
    $response->assertStatus(200);
});

// ============================================================================
// VALIDATION TESTS
// ============================================================================

test('contact form validates required fields', function () {
    $response = $this->post('/contact-submit', []);
    
    $response->assertSessionHasErrors(['name', 'email', 'subject', 'message']);
});

test('contact form validates email format', function () {
    $response = $this->post('/contact-submit', [
        'name' => 'John Doe',
        'email' => 'invalid-email',
        'subject' => 'Test Subject',
        'message' => 'Test message'
    ]);
    
    $response->assertSessionHasErrors(['email']);
});

test('review submission validates required fields', function () {
    $response = $this->post('/reviews', []);
    
    $response->assertSessionHasErrors(['pincode_id', 'name', 'rating', 'comment']);
});

test('tool review submission validates required fields', function () {
    $tool = Tool::factory()->create();
    
    $response = $this->post("/tools/{$tool->id}/reviews", []);
    
    $response->assertSessionHasErrors(['name', 'email', 'review']);
});

// ============================================================================
// PERFORMANCE TESTS
// ============================================================================

test('home page loads within reasonable time', function () {
    $startTime = microtime(true);
    
    $response = $this->get('/');
    
    $endTime = microtime(true);
    $loadTime = $endTime - $startTime;
    
    $response->assertStatus(200);
    expect($loadTime)->toBeLessThan(2.0); // Should load within 2 seconds
});

test('pincodes list loads within reasonable time', function () {
    $startTime = microtime(true);
    
    $response = $this->get('/pincodes');
    
    $endTime = microtime(true);
    $loadTime = $endTime - $startTime;
    
    $response->assertStatus(200);
    expect($loadTime)->toBeLessThan(3.0); // Should load within 3 seconds
});

// ============================================================================
// ROUTE PARAMETER TESTS
// ============================================================================

test('pincode routes handle special characters in state names', function () {
    $state = State::factory()->create(['name' => 'Uttar Pradesh']);
    
    $response = $this->get('/pincodes/Uttar Pradesh');
    
    $response->assertStatus(200);
});

test('pincode routes handle special characters in district names', function () {
    $state = State::factory()->create(['name' => 'Maharashtra']);
    
    $response = $this->get('/pincodes/Maharashtra/Mumbai City');
    
    $response->assertStatus(404); // District doesn't exist in test database
});

test('pincode routes handle special characters in post office names', function () {
    $state = State::factory()->create(['name' => 'Delhi']);
    
    $response = $this->get('/pincodes/Delhi/New Delhi/Connaught Place');
    
    $response->assertStatus(404); // Post office doesn't exist in test database
});

// ============================================================================
// CSRF PROTECTION TESTS
// ============================================================================

test('post routes are protected by CSRF', function () {
    $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class);
    
    $response = $this->post('/contact-submit', [
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'subject' => 'Test Subject',
        'message' => 'Test message content'
    ]);
    
    $response->assertStatus(302);
});

// ============================================================================
// RESPONSE HEADER TESTS
// ============================================================================

test('pages return correct content type headers', function () {
    $response = $this->get('/');
    
    $response->assertStatus(200);
    $response->assertHeader('Content-Type', 'text/html; charset=UTF-8');
});

test('api routes return correct content type headers', function () {
    $response = $this->get('/api/dict/autocomplete?q=test');
    
    $response->assertStatus(200);
    $response->assertHeader('Content-Type', 'application/json');
});

// ============================================================================
// SESSION TESTS
// ============================================================================

test('session is maintained across requests', function () {
    $response = $this->get('/');
    
    $response->assertStatus(200);
    $this->assertNotNull(session()->getId());
});

// ============================================================================
// CACHE TESTS
// ============================================================================

test('static pages can be cached', function () {
    $response = $this->get('/pages/about-us');
    
    $response->assertStatus(200);
    $response->assertHeader('Cache-Control');
});

// ============================================================================
// SEO TESTS
// ============================================================================

test('pages have proper meta tags', function () {
    $response = $this->get('/');
    
    $response->assertStatus(200);
    $response->assertSee('meta');
});

test('blog posts have proper meta tags', function () {
    $post = BlogPost::factory()->create(['slug' => 'test-post']);
    
    $response = $this->get('/blog/test-post');
    
    $response->assertStatus(200);
    $response->assertSee('meta');
}); 