<?php

use App\Http\Controllers\Api\CardAPIController;
use App\Http\Controllers\Api\DigipinController;
use App\Http\Controllers\Api\DistanceAPIController;
use App\Http\Controllers\Api\NearestLocationAPIController;
use App\Http\Controllers\Api\PincodeController;
use App\Http\Controllers\Api\PinToPinAPIController;
use App\Http\Controllers\Api\PinToPinDistanceController;
use App\Http\Controllers\Api\PostAPIController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
 * |--------------------------------------------------------------------------
 * | API Routes
 * |--------------------------------------------------------------------------
 * |
 * | Here is where you can register API routes for your application. These
 * | routes are loaded by the RouteServiceProvider within a group which
 * | is assigned the "api" middleware group. Enjoy building your API!
 * |
 */

// Default Laravel user route
Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::namespace('Api')->name('api.')->group(function () {
    // Public routes without throttling
    Route::get('/getCardData', [CardAPIController::class, 'getCardData']);
    Route::get('/cards/{card}', [CardAPIController::class, 'show']);
    Route::get('/get5Posts', [PostAPIController::class, 'get5Posts']);
    Route::get('posts/{slug}', [PostAPIController::class, 'show']);

    // General routes with default API throttling
    // Route::get('general-setting', 'GeneralController@getGeneralSetting');
    // Route::get('get-countries', 'GeneralController@getCountries');

    // Protected routes - Changed from 'auth:api' to 'auth:sanctum' to avoid the rate limiter issue
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('pincode/{pincode}', [PincodeController::class, 'getPincode']);
        Route::get('pincodes/state/{stateName}', [PincodeController::class, 'getStateNames']);
        Route::get('pincodes/district/{stateID}/{districtName}', [PincodeController::class, 'getDistrictNames']);
        Route::get('pincodes/tehsil/{stateName}/{districtName}/{tehsilName}', [PincodeController::class, 'getTehsilNames']);
        Route::get('pincodes/post/{stateName}/{districtName}/{postOfficeName}', [PincodeController::class, 'getPincodesByPostName']);
        Route::get('pincodes/details/{stateName}/{districtName}/{postOfficeName}', [PincodeController::class, 'getPincodeDetails']);
        Route::get('/calculate-distance-between-two-coordinates/{lat1}/{long1}/{lat2}/{long2}', [DistanceAPIController::class, 'calculateDistanceBetweenTwoCoordinates']);
        Route::get('/get-nearest-location/{latitude}/{longitude}', [NearestLocationAPIController::class, 'findNearestLocation']);
        Route::get('/validate-pincode/{pincode}', [PinToPinAPIController::class, 'validatePincode']);
        Route::get('/calculate-distance-between-two-pincodes/{pincode1}/{pincode2}', [PinToPinAPIController::class, 'calculateDistanceBetweenTwoPincodes']);
        Route::post('/validate-pincode', [PinToPinDistanceController::class, 'validatePincode']);
        Route::post('/calculate-distance', [PinToPinDistanceController::class, 'calculateDistance']);
    });

    // DIGIPIN Routes
    Route::prefix('digipin')->group(function () {
        Route::post('/generate', [DigipinController::class, 'generateDigipin']);
        Route::post('/decode', [DigipinController::class, 'decodeDigipin']);
        Route::get('/info', [DigipinController::class, 'getInfo']);
    });

});

Route::fallback(function (Request $request) {
    return response()->json([
        'status' => 404,
        'message' => 'Resource not found',
        'error' => 'Not Found',
    ], 404);
});
