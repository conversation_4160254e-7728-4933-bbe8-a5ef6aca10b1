<?php

namespace App\Http\Controllers;

use App\Models\PincodeGeo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

class PincodeGeoController extends Controller
{
    public function index()
    {
        return view('pincodes.pincode-geo.index');
    }

    public function search(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'q' => 'required|string|min:1|max:10',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => 'Invalid search query'], 400);
        }

        $query = $request->get('q');

        $pincodes = PincodeGeo::byPincode($query)
            ->select('pincode', 'office_name', 'division', 'region', 'circle')
            ->limit(20)
            ->get();

        return response()->json($pincodes);
    }

    public function getPincodesByBounds(Request $request)
    {
        // Bounds are not supported without lat/lng, so just return a limited set
        $pincodes = PincodeGeo::limit(100)->get();

        $features = $pincodes->map(function ($pincode) {
            return [
                'type' => 'Feature',
                'properties' => [
                    'pincode' => $pincode->pincode,
                    'office_name' => $pincode->office_name,
                    'division' => $pincode->division,
                    'region' => $pincode->region,
                    'circle' => $pincode->circle,
                ],
                'geometry' => $pincode->geometry,
            ];
        });

        return response()->json([
            'type' => 'FeatureCollection',
            'features' => $features,
        ]);
    }

    public function show($pincode)
    {
        $pincodeData = PincodeGeo::where('pincode', $pincode)->first();

        if (!$pincodeData) {
            return response()->json(['error' => 'Pincode not found'], 404);
        }

        return response()->json([
            'type' => 'Feature',
            'properties' => [
                'pincode' => $pincodeData->pincode,
                'office_name' => $pincodeData->office_name,
                'division' => $pincodeData->division,
                'region' => $pincodeData->region,
                'circle' => $pincodeData->circle,
            ],
            'geometry' => $pincodeData->geometry,
        ]);
    }

    // Search post offices by pincode using model PinCode (pincode_geo table does not contain full details about post offices)
    public function searchPostOfficesByPincode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pincode' => 'required|string|min:3|max:10',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => 'Invalid pincode'], 400);
        }

        $pincode = $request->get('pincode');

        // Fetch all post offices for this pincode
        $postOffices = \App\Models\PinCode::where('pincode', $pincode)->get();

        $result = $postOffices->map(function ($office) {
            return [
                'name' => $office->name,
                'branch_type' => $office->branch_type,
                'delivery_status' => $office->delivery_status,
                'district' => $office->district,
                'state' => $office->state,
                'contact_number' => $office->contact_number,
                'url' => route('pincodes.pincode', [
                    'state' => $office->state,
                    'district' => $office->district,
                    'pincode' => $office->pincode,
                ]),
            ];
        });

        return response()->json($result);
    }
}
