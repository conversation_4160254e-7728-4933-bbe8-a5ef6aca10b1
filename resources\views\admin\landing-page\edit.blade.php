@extends('admin.layouts.admin')

@section('title', 'Edit Landing Page Section')

@section('content')
<div class="container px-6 mx-auto grid text-text-primary-light dark:text-text-primary-dark">
    <div class="flex justify-between items-center">
        <h2 class="my-6 text-2xl font-semibold">
            Edit Section: {{ $section->name }}
        </h2>
        <a href="{{ route('admin.landing-page.index') }}" class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-primary-light dark:bg-primary-dark border border-transparent rounded-lg active:bg-primary-light/90 hover:bg-primary-light/90 dark:active:bg-primary-dark/90 dark:hover:bg-primary-dark/90 focus:outline-none focus:shadow-outline-purple">
            Back to Sections
        </a>
    </div>

    @if(session('success'))
    <div class="mb-4 px-4 py-3 leading-normal text-green-700 bg-green-100 dark:text-green-100 dark:bg-green-700/30 rounded-lg" role="alert">
        {{ session('success') }}
    </div>
    @endif

    <div class="px-4 py-3 mb-8 bg-white dark:bg-bg-dark rounded-lg shadow-md">
        <form action="{{ route('admin.landing-page.update', $section) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')

            <div class="mb-6">
                <label for="name" class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-2">Section Name</label>
                <input type="text" name="name" id="name" value="{{ old('name', $section->name) }}" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md" required>
                @error('name')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label for="sort_order" class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-2">Sort Order</label>
                <input type="number" name="sort_order" id="sort_order" value="{{ old('sort_order', $section->sort_order) }}" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md" required>
                @error('sort_order')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label class="flex items-center">
                    <input type="checkbox" name="is_active" class="text-primary-light dark:text-primary-dark form-checkbox focus:border-primary-light dark:focus:border-primary-dark focus:outline-none focus:ring-primary-light/20 dark:focus:ring-primary-dark/20" {{ $section->is_active ? 'checked' : '' }}>
                    <span class="ml-2 text-sm">Active</span>
                </label>
            </div>

            <div class="border-t border-border-light dark:border-border-dark pt-6 mb-6">
                <h3 class="text-lg font-medium mb-4">Section Content</h3>

                @foreach($section->contents as $content)
                <div class="mb-8 p-4 border border-border-light dark:border-border-dark rounded-lg">
                    <h4 class="font-medium mb-3">{{ Str::title(str_replace('_', ' ', $content->key)) }}</h4>
                    <input type="hidden" name="contents[{{ $loop->index }}][id]" value="{{ $content->id }}">

                    @if($content->type === 'text')
                        <input type="text" name="contents[{{ $loop->index }}][value]" value="{{ old('contents.'.$loop->index.'.value', $content->value) }}" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md">
                    
                    @elseif($content->type === 'textarea')
                        <textarea name="contents[{{ $loop->index }}][value]" rows="4" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md">{{ old('contents.'.$loop->index.'.value', $content->value) }}</textarea>
                    
                    @elseif($content->type === 'image')
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0">
                                @if($content->value)
                                <img src="{{ asset('storage/' . $content->value) }}" alt="{{ $content->key }}" class="h-24 w-auto object-cover rounded">
                                @else
                                <div class="h-24 w-24 bg-bg-light dark:bg-gray-800 flex items-center justify-center rounded">
                                    <span class="text-text-secondary-light dark:text-text-secondary-dark">No image</span>
                                </div>
                                @endif
                            </div>
                            <div class="flex-grow">
                                <input type="file" name="contents[{{ $loop->index }}][image]" class="block w-full text-sm text-text-secondary-light dark:text-text-secondary-dark file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-light/10 file:text-primary-light dark:file:bg-primary-dark/20 dark:file:text-primary-dark hover:file:bg-primary-light/20 dark:hover:file:bg-primary-dark/30">
                                <input type="hidden" name="contents[{{ $loop->index }}][value]" value="{{ $content->value }}">
                                <p class="text-xs text-text-secondary-light dark:text-text-secondary-dark mt-1">Leave empty to keep the current image</p>
                            </div>
                        </div>
                    
                    @elseif($content->type === 'color')
                        <div class="flex items-center space-x-2">
                            <input type="color" name="contents[{{ $loop->index }}][value]" value="{{ old('contents.'.$loop->index.'.value', $content->value) }}" class="h-8 w-8 rounded">
                            <input type="text" value="{{ old('contents.'.$loop->index.'.value', $content->value) }}" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md" readonly>
                        </div>
                    
                    @elseif($content->type === 'boolean')
                        <label class="flex items-center">
                            <input type="checkbox" name="contents[{{ $loop->index }}][value]" value="true" class="text-primary-light dark:text-primary-dark form-checkbox focus:border-primary-light dark:focus:border-primary-dark focus:outline-none focus:ring-primary-light/20 dark:focus:ring-primary-dark/20" {{ $content->value === 'true' ? 'checked' : '' }}>
                            <span class="ml-2 text-sm">Enabled</span>
                        </label>
                    
                    @elseif($content->type === 'repeater')
                        @php 
                            $repeaterItems = is_array($content->value) ? $content->value : json_decode($content->value, true);
                            $repeaterItems = $repeaterItems ?: [];
                        @endphp
                        
                        <div class="repeater-container" data-index="{{ $loop->index }}">
                            <div class="repeater-items">
                                @foreach($repeaterItems as $itemIndex => $item)
                                <div class="repeater-item mb-4 p-3 border border-border-light dark:border-border-dark rounded-lg">
                                    <div class="flex justify-between items-center mb-2">
                                        <h5 class="font-medium">Item {{ $itemIndex + 1 }}</h5>
                                        <button type="button" class="remove-item text-red-500 hover:text-red-700 dark:hover:text-red-400">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                    </div>
                                    
                                    @foreach($item as $fieldKey => $fieldValue)
                                    <div class="mb-3">
                                        <label class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">{{ Str::title(str_replace('_', ' ', $fieldKey)) }}</label>
                                        
                                        @if(is_array($fieldValue))
                                            <!-- Handle nested arrays if needed -->
                                            <textarea name="contents[{{ $loop->parent->parent->index }}][value][{{ $itemIndex }}][{{ $fieldKey }}]" rows="2" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md">{{ json_encode($fieldValue) }}</textarea>
                                        @elseif(Str::contains($fieldKey, 'image') || Str::contains($fieldKey, 'logo') || Str::contains($fieldKey, 'avatar'))
                                            <div class="flex items-start space-x-4">
                                                <div class="flex-shrink-0">
                                                    @if($fieldValue)
                                                    <img src="{{ asset('storage/' . $fieldValue) }}" alt="{{ $fieldKey }}" class="h-16 w-auto object-cover rounded">
                                                    @endif
                                                </div>
                                                <input type="text" name="contents[{{ $loop->parent->parent->index }}][value][{{ $itemIndex }}][{{ $fieldKey }}]" value="{{ $fieldValue }}" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md">
                                            </div>
                                        @elseif(Str::contains($fieldKey, 'icon'))
                                            <textarea name="contents[{{ $loop->parent->parent->index }}][value][{{ $itemIndex }}][{{ $fieldKey }}]" rows="2" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md">{{ $fieldValue }}</textarea>
                                        @elseif(Str::contains($fieldKey, 'description') || strlen($fieldValue) > 100)
                                            <textarea name="contents[{{ $loop->parent->parent->index }}][value][{{ $itemIndex }}][{{ $fieldKey }}]" rows="3" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md">{{ $fieldValue }}</textarea>
                                        @else
                                            <input type="text" name="contents[{{ $loop->parent->parent->index }}][value][{{ $itemIndex }}][{{ $fieldKey }}]" value="{{ $fieldValue }}" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md">
                                        @endif
                                    </div>
                                    @endforeach
                                </div>
                                @endforeach
                            </div>
                            
                            <button type="button" class="add-item mt-2 px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-primary-light dark:bg-primary-dark border border-transparent rounded-lg active:bg-primary-light/90 hover:bg-primary-light/90 dark:active:bg-primary-dark/90 dark:hover:bg-primary-dark/90 focus:outline-none focus:shadow-outline-purple">
                                Add Item
                            </button>
                            
                            <template class="repeater-template" data-content-index="{{ $loop->index }}">
                                <div class="repeater-item mb-4 p-3 border border-border-light dark:border-border-dark rounded-lg">
                                    <div class="flex justify-between items-center mb-2">
                                        <h5 class="font-medium">New Item</h5>
                                        <button type="button" class="remove-item text-red-500 hover:text-red-700 dark:hover:text-red-400">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                    </div>
                                    
                                    @if(!empty($repeaterItems))
                                        @php 
                                            $templateItem = reset($repeaterItems);
                                            $contentIndex = $loop->index;
                                        @endphp
                                        @foreach($templateItem as $fieldKey => $fieldValue)
                                        <div class="mb-3">
                                            <label class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">{{ Str::title(str_replace('_', ' ', $fieldKey)) }}</label>
                                            
                                            @if(is_array($fieldValue))
                                                <textarea name="contents[{{ $contentIndex }}][value][__INDEX__][{{ $fieldKey }}]" rows="2" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md"></textarea>
                                            @elseif(Str::contains($fieldKey, 'image') || Str::contains($fieldKey, 'logo') || Str::contains($fieldKey, 'avatar'))
                                                <input type="text" name="contents[{{ $contentIndex }}][value][__INDEX__][{{ $fieldKey }}]" value="" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md">
                                            @elseif(Str::contains($fieldKey, 'icon'))
                                                <textarea name="contents[{{ $contentIndex }}][value][__INDEX__][{{ $fieldKey }}]" rows="2" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md"></textarea>
                                            @elseif(Str::contains($fieldKey, 'description') || strlen($fieldValue) > 100)
                                                <textarea name="contents[{{ $contentIndex }}][value][__INDEX__][{{ $fieldKey }}]" rows="3" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md"></textarea>
                                            @else
                                                <input type="text" name="contents[{{ $contentIndex }}][value][__INDEX__][{{ $fieldKey }}]" value="" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md">
                                            @endif
                                        </div>
                                        @endforeach
                                    @endif
                                </div>
                            </template>
                        </div>
                    @else
                        <input type="text" name="contents[{{ $loop->index }}][value]" value="{{ old('contents.'.$loop->index.'.value', $content->value) }}" class="block w-full mt-1 text-sm bg-transparent border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 rounded-md">
                    @endif
                </div>
                @endforeach
            </div>

            <div class="flex justify-end">
                <button type="submit" class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-primary-light dark:bg-primary-dark border border-transparent rounded-lg active:bg-primary-light/90 hover:bg-primary-light/90 dark:active:bg-primary-dark/90 dark:hover:bg-primary-dark/90 focus:outline-none focus:shadow-outline-purple">
                    Save Changes
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle repeater fields
        document.querySelectorAll('.repeater-container').forEach(container => {
            const contentIndex = container.dataset.index;
            const itemsContainer = container.querySelector('.repeater-items');
            const template = container.querySelector('.repeater-template').innerHTML;
            const addButton = container.querySelector('.add-item');
            
            // Add new item
            addButton.addEventListener('click', function() {
                const newIndex = itemsContainer.children.length;
                const newItem = template.replace(/__INDEX__/g, newIndex);
                
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = newItem;
                const itemElement = tempDiv.firstElementChild;
                
                itemsContainer.appendChild(itemElement);
                
                // Update item title
                const itemTitle = itemElement.querySelector('h5');
                if (itemTitle) {
                    itemTitle.textContent = `Item ${newIndex + 1}`;
                }
                
                // Add remove event listener
                setupRemoveButton(itemElement.querySelector('.remove-item'));
            });
            
            // Setup existing remove buttons
            container.querySelectorAll('.remove-item').forEach(button => {
                setupRemoveButton(button);
            });
            
            function setupRemoveButton(button) {
                button.addEventListener('click', function() {
                    const item = this.closest('.repeater-item');
                    item.remove();
                    
                    // Update indices
                    updateIndices(itemsContainer, contentIndex);
                });
            }
            
            function updateIndices(container, contentIndex) {
                Array.from(container.children).forEach((item, idx) => {
                    // Update item title
                    const itemTitle = item.querySelector('h5');
                    if (itemTitle) {
                        itemTitle.textContent = `Item ${idx + 1}`;
                    }
                    
                    // Update input names
                    item.querySelectorAll('input, textarea').forEach(input => {
                        const name = input.getAttribute('name');
                        if (name) {
                            const newName = name.replace(/\[(\d+)\]/, `[${idx}]`);
                            input.setAttribute('name', newName);
                        }
                    });
                });
            }
        });
    });
</script>
@endpush