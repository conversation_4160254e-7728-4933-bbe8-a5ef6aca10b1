@extends('layouts.app')

@section('title', 'Payment Failed')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Failure Message -->
            <div class="card border-danger shadow-sm">
                <div class="card-body text-center py-5">
                    <div class="failure-icon mb-4">
                        <i class="fas fa-times-circle fa-5x text-danger"></i>
                    </div>
                    <h2 class="text-danger mb-3">Payment Failed</h2>
                    <p class="lead text-muted mb-4">
                        We're sorry, but your payment could not be processed at this time.
                    </p>
                    
                    <!-- Error Details -->
                    @if(isset($error) && $error)
                    <div class="alert alert-danger mb-4">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Error Details:
                        </h6>
                        <p class="mb-0">{{ $error }}</p>
                    </div>
                    @endif
                    
                    <!-- Order Details -->
                    <div class="order-summary bg-light p-4 rounded mb-4">
                        <div class="row">
                            <div class="col-md-6 text-md-start text-center mb-3 mb-md-0">
                                <h6 class="mb-2">Order Information</h6>
                                <p class="mb-1"><strong>Order ID:</strong> {{ $order->order_number }}</p>
                                <p class="mb-1"><strong>Plan:</strong> {{ $order->plan->name ?? 'N/A' }}</p>
                                <p class="mb-1"><strong>Duration:</strong> {{ $order->plan->duration ?? 'N/A' }} {{ $order->plan->duration_type ?? '' }}</p>
                            </div>
                            <div class="col-md-6 text-md-end text-center">
                                <h6 class="mb-2">Payment Details</h6>
                                <p class="mb-1"><strong>Amount:</strong> {{ $order->currency }} {{ number_format($order->amount, 2) }}</p>
                                <p class="mb-1"><strong>Status:</strong> <span class="badge bg-danger">Failed</span></p>
                                @if(isset($payment) && $payment->gateway_payment_id)
                                <p class="mb-1"><strong>Reference:</strong> <code>{{ $payment->gateway_payment_id }}</code></p>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                        <a href="{{ route('user.payment.gateway-selection', $order->id) }}" class="btn btn-primary">
                            <i class="fas fa-redo me-2"></i>
                            Try Again
                        </a>
                        <a href="{{ route('plans.public') }}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Plans
                        </a>
                        <a href="#" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#supportModal">
                            <i class="fas fa-headset me-2"></i>
                            Contact Support
                        </a>
                    </div>
                </div>
            </div>

            <!-- Common Issues and Solutions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Common Issues and Solutions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="troubleshootingAccordion">
                        <!-- Card Declined -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingOne">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                                    <i class="fas fa-credit-card me-2"></i>
                                    Card Declined or Insufficient Funds
                                </button>
                            </h2>
                            <div id="collapseOne" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <ul class="mb-0">
                                        <li>Check if you have sufficient balance in your account</li>
                                        <li>Ensure your card is not expired</li>
                                        <li>Verify that your card supports online transactions</li>
                                        <li>Contact your bank to check if the transaction was blocked</li>
                                        <li>Try using a different card or payment method</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Network Issues -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingTwo">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                                    <i class="fas fa-wifi me-2"></i>
                                    Network or Connection Issues
                                </button>
                            </h2>
                            <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <ul class="mb-0">
                                        <li>Check your internet connection</li>
                                        <li>Try refreshing the page and attempting payment again</li>
                                        <li>Clear your browser cache and cookies</li>
                                        <li>Try using a different browser or device</li>
                                        <li>Disable any VPN or proxy connections</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Bank Security -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingThree">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    Bank Security Restrictions
                                </button>
                            </h2>
                            <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <ul class="mb-0">
                                        <li>Enable international transactions if paying from abroad</li>
                                        <li>Check if your bank has blocked online transactions</li>
                                        <li>Verify your card's daily transaction limits</li>
                                        <li>Contact your bank to whitelist our payment processor</li>
                                        <li>Try using net banking or UPI as an alternative</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Technical Issues -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingFour">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour">
                                    <i class="fas fa-cog me-2"></i>
                                    Technical Issues
                                </button>
                            </h2>
                            <div id="collapseFour" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <ul class="mb-0">
                                        <li>Ensure JavaScript is enabled in your browser</li>
                                        <li>Disable browser extensions that might interfere</li>
                                        <li>Try using an incognito/private browsing window</li>
                                        <li>Update your browser to the latest version</li>
                                        <li>Contact our technical support for assistance</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alternative Payment Methods -->
            <div class="card mt-4">
                <div class="card-body text-center">
                    <h6 class="mb-3">Try Alternative Payment Methods</h6>
                    <p class="text-muted mb-3">If you're still having trouble, try one of these alternative payment options:</p>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="payment-option p-3 border rounded">
                                <i class="fas fa-university fa-2x text-info mb-2"></i>
                                <h6>Net Banking</h6>
                                <small class="text-muted">Direct bank transfer</small>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="payment-option p-3 border rounded">
                                <i class="fas fa-mobile-alt fa-2x text-success mb-2"></i>
                                <h6>UPI Payment</h6>
                                <small class="text-muted">Pay with UPI apps</small>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="payment-option p-3 border rounded">
                                <i class="fas fa-qrcode fa-2x text-dark mb-2"></i>
                                <h6>QR Code</h6>
                                <small class="text-muted">Scan and pay</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Support Modal -->
<div class="modal fade" id="supportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Contact Support</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Our support team is here to help you resolve this payment issue.</p>
                <div class="row">
                    <div class="col-12 mb-3">
                        <div class="d-flex align-items-center p-3 border rounded">
                            <i class="fas fa-envelope fa-2x text-primary me-3"></i>
                            <div>
                                <h6 class="mb-1">Email Support</h6>
                                <p class="mb-0 text-muted"><EMAIL></p>
                                <small class="text-muted">Response within 24 hours</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 mb-3">
                        <div class="d-flex align-items-center p-3 border rounded">
                            <i class="fas fa-phone fa-2x text-success me-3"></i>
                            <div>
                                <h6 class="mb-1">Phone Support</h6>
                                <p class="mb-0 text-muted">+91-XXXXXXXXXX</p>
                                <small class="text-muted">Mon-Fri, 9 AM - 6 PM IST</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="d-flex align-items-center p-3 border rounded">
                            <i class="fas fa-comments fa-2x text-info me-3"></i>
                            <div>
                                <h6 class="mb-1">Live Chat</h6>
                                <p class="mb-0 text-muted">Available on our website</p>
                                <small class="text-muted">Instant support</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Support Form -->
                <form class="mt-4" id="quickSupportForm">
                    <div class="mb-3">
                        <label class="form-label">Quick Message (Optional)</label>
                        <textarea class="form-control" rows="3" placeholder="Describe your issue briefly..."></textarea>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="includeOrderDetails" checked>
                        <label class="form-check-label" for="includeOrderDetails">
                            Include order details in the message
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="sendSupportRequest()">Send Message</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.failure-icon {
    animation: failureShake 0.5s ease-in-out;
}

@keyframes failureShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.order-summary {
    border-left: 4px solid #dc3545;
}

.payment-option {
    transition: all 0.3s ease;
    cursor: pointer;
}

.payment-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.accordion-button:not(.collapsed) {
    background-color: #f8f9fa;
    color: #495057;
}

@media (max-width: 768px) {
    .failure-icon i {
        font-size: 3rem !important;
    }
    
    .lead {
        font-size: 1rem;
    }
    
    .order-summary .row > div {
        text-align: center !important;
        margin-bottom: 1rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-scroll to top
    window.scrollTo(0, 0);
    
    // Track failed payment (for analytics)
    if (typeof gtag !== 'undefined') {
        gtag('event', 'payment_failed', {
            transaction_id: '{{ $order->order_number }}',
            value: {{ $order->amount }},
            currency: '{{ $order->currency }}',
            error_message: '{{ $error ?? "Unknown error" }}'
        });
    }
    
    // Send failure notification to parent window (if in iframe)
    if (window.parent !== window) {
        window.parent.postMessage({
            type: 'payment_failed',
            order_id: '{{ $order->id }}',
            error: '{{ $error ?? "Payment failed" }}'
        }, '*');
    }
    
    // Auto-expand first accordion item if no specific error
    @if(!isset($error) || !$error)
    const firstAccordion = document.querySelector('#collapseOne');
    if (firstAccordion) {
        new bootstrap.Collapse(firstAccordion, { show: true });
    }
    @endif
});

// Send support request
function sendSupportRequest() {
    const message = document.querySelector('#quickSupportForm textarea').value;
    const includeDetails = document.querySelector('#includeOrderDetails').checked;
    
    // Here you would typically send the support request to your backend
    // For now, we'll just show a success message
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('supportModal'));
    modal.hide();
    
    // Show success toast
    showToast('Support request sent successfully! We\'ll get back to you soon.', 'success');
}

// Show toast notification
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container') || createToastContainer();
    
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

// Create toast container if it doesn't exist
function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '1055';
    document.body.appendChild(container);
    return container;
}

// Handle payment option clicks
document.querySelectorAll('.payment-option').forEach(option => {
    option.addEventListener('click', function() {
        // Redirect to payment gateway selection with the specific method
        window.location.href = '{{ route("user.payment.gateway-selection", $order->id) }}';
    });
});
</script>
@endpush