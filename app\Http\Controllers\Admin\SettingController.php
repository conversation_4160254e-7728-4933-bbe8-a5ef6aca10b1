<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;

class SettingController extends Controller
{
    public function index()
    {
        $settings = Setting::all()->groupBy('group');
        $defaultSettings = [
            'site_name' => Setting::get('site_name', 'Pincodes Sale'),
            'site_description' => Setting::get('site_description', 'Find and verify Indian postal codes easily'),
            'site_logo' => Setting::get('site_logo'),
            'site_favicon' => Setting::get('site_favicon'),
            'maintenance_mode' => Setting::get('maintenance_mode', false),
            'login_enabled' => Setting::get('login_enabled', true),
        ];

        return view('admin.settings.index', compact('settings', 'defaultSettings'));
    }

    public function update(Request $request)
    {
        $validated = $request->validate([
            'settings' => 'required|array',
        ]);

        foreach ($validated['settings'] as $key => $value) {

            $setting = Setting::where('key', $key)->first();

            if (!$setting) {
                Log::warning('Setting not found in database', ['key' => $key]);
                continue;
            }
            // Handle file uploads
            if ($request->hasFile("settings.{$key}")) {
                $file = $request->file("settings.{$key}");

                // Delete old file if exists
                $oldFile = $setting->value;
                if ($oldFile && Storage::disk('public')->exists($oldFile)) {
                    Storage::disk('public')->delete($oldFile);
                }

                // Store new file
                $path = $file->store('settings', 'public');
                $value = $path;
            }

            // Handle JSON type
            if ($setting->type === 'json') {

                try {
                    $value = json_encode(json_decode($value));
                } catch (\Exception $e) {
                    Log::error('Invalid JSON format', ['key' => $key, 'error' => $e->getMessage()]);
                    continue; // Skip invalid JSON
                }
            }

            // Handle boolean type
            if ($setting->type === 'boolean') {
                $value = (bool) $value;
            }

            // Handle encrypted values
            if ($setting->is_encrypted) {
                try {
                    $value = Crypt::encryptString($value);
                } catch (\Exception $e) {
                    Log::error('Encryption failed', ['key' => $key, 'error' => $e->getMessage()]);
                    continue; // Skip if encryption fails
                }
            }

            try {
                $result = $setting->update(['value' => $value]);

                // Clear the cache for this setting
                Cache::forget("setting.{$key}");

            } catch (\Exception $e) {
                Log::error('Failed to update setting', ['key' => $key, 'error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            }
        }

        // Clear all settings cache
        Cache::forget('settings.all');

        // Clear Laravel application cache
        \Illuminate\Support\Facades\Artisan::call('cache:clear');
        return redirect()->back()->with('success', 'Settings updated successfully.');
    }

    public function maintenanceMode(Request $request)
    {
        $maintenance = $request->boolean('maintenance_mode');
        Setting::set('maintenance_mode', $maintenance);

        return response()->json(['success' => true]);
    }

    public function toggleLogin(Request $request)
    {
        $enabled = $request->boolean('login_enabled');
        Setting::set('login_enabled', $enabled);

        return response()->json(['success' => true]);
    }

    public function generateApiKey()
    {
        // $apiKey = bin2hex(random_bytes(16));
        $apiKey = Str::random(32);
        Setting::set('api_key', $apiKey);

        return response()->json([
            'success' => true,
            'api_key' => $apiKey
        ]);
    }

    public function clearCache()
    {
        Setting::clearCache();

        // Clear Laravel application cache
        \Illuminate\Support\Facades\Artisan::call('cache:clear');

        return redirect()->back()->with('success', 'Settings cache cleared successfully.');
    }
}
