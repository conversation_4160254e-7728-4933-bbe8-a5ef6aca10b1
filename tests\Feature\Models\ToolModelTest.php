<?php

use App\Models\Tool;
use App\Models\ToolReview;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Tool Model', function () {
    
    describe('Factory and Creation', function () {
        it('can be created using factory', function () {
            $tool = Tool::factory()->create();
            
            expect($tool)->toBeInstanceOf(Tool::class)
                ->and($tool->exists)->toBeTrue();
        });

        it('can be created with custom attributes', function () {
            $tool = Tool::factory()->create([
                'name' => 'Custom Tool',
                'slug' => 'custom-tool',
                'is_featured' => true,
                'is_published' => false,
                'view_count' => 100
            ]);

            expect($tool->name)->toBe('Custom Tool')
                ->and($tool->slug)->toBe('custom-tool')
                ->and($tool->is_featured)->toBeTrue()
                ->and($tool->is_published)->toBeFalse()
                ->and($tool->view_count)->toBe(100);
        });
    });

    describe('Fillable Attributes', function () {
        it('allows mass assignment of fillable attributes', function () {
            $attributes = [
                'name' => 'Test Tool',
                'slug' => 'test-tool',
                'description' => 'A test tool description',
                'content' => 'Tool content here',
                'route_name' => 'tools.test',
                'thumbnail' => 'thumbnail.jpg',
                'usage_instructions' => 'How to use this tool',
                'meta_title' => 'Meta Title',
                'meta_description' => 'Meta Description',
                'is_featured' => true,
                'is_published' => false,
                'view_count' => 50
            ];

            $tool = Tool::create($attributes);

            expect($tool->name)->toBe('Test Tool')
                ->and($tool->slug)->toBe('test-tool')
                ->and($tool->description)->toBe('A test tool description')
                ->and($tool->content)->toBe('Tool content here')
                ->and($tool->route_name)->toBe('tools.test')
                ->and($tool->thumbnail)->toBe('thumbnail.jpg')
                ->and($tool->usage_instructions)->toBe('How to use this tool')
                ->and($tool->meta_title)->toBe('Meta Title')
                ->and($tool->meta_description)->toBe('Meta Description')
                ->and($tool->is_featured)->toBeTrue()
                ->and($tool->is_published)->toBeFalse()
                ->and($tool->view_count)->toBe(50);
        });
    });

    describe('Casts', function () {
        it('casts is_featured to boolean', function () {
            $tool = Tool::factory()->create(['is_featured' => 1]);
            
            expect($tool->is_featured)->toBeTrue()
                ->and($tool->is_featured)->toBeBool();
        });

        it('casts is_published to boolean', function () {
            $tool = Tool::factory()->create(['is_published' => 0]);
            
            expect($tool->is_published)->toBeFalse()
                ->and($tool->is_published)->toBeBool();
        });

        it('casts view_count to integer', function () {
            $tool = Tool::factory()->create(['view_count' => '123']);
            
            expect($tool->view_count)->toBe(123)
                ->and($tool->view_count)->toBeInt();
        });

        it('casts created_at and updated_at to datetime', function () {
            $tool = Tool::factory()->create();
            
            expect($tool->created_at)->toBeInstanceOf(\Illuminate\Support\Carbon::class)
                ->and($tool->updated_at)->toBeInstanceOf(\Illuminate\Support\Carbon::class);
        });
    });

    describe('Route Key', function () {
        it('uses slug as route key', function () {
            $tool = new Tool();
            
            expect($tool->getRouteKeyName())->toBe('slug');
        });

        it('can be found by slug in route model binding', function () {
            $tool = Tool::factory()->create(['slug' => 'unique-tool-slug']);
            
            $foundTool = Tool::where($tool->getRouteKeyName(), 'unique-tool-slug')->first();
            
            expect($foundTool->id)->toBe($tool->id);
        });
    });

    describe('Scopes', function () {
        beforeEach(function () {
            // Create test data
            Tool::factory()->create(['is_published' => true, 'is_featured' => true, 'view_count' => 100]);
            Tool::factory()->create(['is_published' => true, 'is_featured' => false, 'view_count' => 50]);
            Tool::factory()->create(['is_published' => false, 'is_featured' => true, 'view_count' => 200]);
            Tool::factory()->create(['is_published' => false, 'is_featured' => false, 'view_count' => 25]);
        });

        it('filters published tools correctly', function () {
            $publishedTools = Tool::published()->get();
            
            expect($publishedTools->count())->toBe(2)
                ->and($publishedTools->every(fn($tool) => $tool->is_published))->toBeTrue();
        });

        it('filters featured tools correctly', function () {
            $featuredTools = Tool::featured()->get();
            
            expect($featuredTools->count())->toBe(2)
                ->and($featuredTools->every(fn($tool) => $tool->is_featured))->toBeTrue();
        });

        it('orders tools by most viewed correctly', function () {
            $mostViewedTools = Tool::mostViewed()->get();
            
            expect($mostViewedTools->count())->toBe(4)
                ->and($mostViewedTools->first()->view_count)->toBe(200)
                ->and($mostViewedTools->last()->view_count)->toBe(25);
        });

        it('can chain scopes together', function () {
            $publishedFeaturedTools = Tool::published()->featured()->get();
            
            expect($publishedFeaturedTools->count())->toBe(1)
                ->and($publishedFeaturedTools->first()->is_published)->toBeTrue()
                ->and($publishedFeaturedTools->first()->is_featured)->toBeTrue();
        });

        it('can combine scopes with ordering', function () {
            $publishedMostViewed = Tool::published()->mostViewed()->get();
            
            expect($publishedMostViewed->count())->toBe(2)
                ->and($publishedMostViewed->first()->view_count)->toBe(100)
                ->and($publishedMostViewed->last()->view_count)->toBe(50);
        });
    });

    describe('Methods', function () {
        it('increments view count correctly', function () {
            $tool = Tool::factory()->create(['view_count' => 10]);
            
            $result = $tool->incrementViewCount();
            
            expect($result)->toBeTrue()
                ->and($tool->fresh()->view_count)->toBe(11);
        });

        it('increments view count multiple times', function () {
            $tool = Tool::factory()->create(['view_count' => 5]);
            
            $tool->incrementViewCount();
            $tool->incrementViewCount();
            $tool->incrementViewCount();
            
            expect($tool->fresh()->view_count)->toBe(8);
        });
    });

    describe('Relationships', function () {
        it('has many tool reviews', function () {
            $tool = Tool::factory()->create();
            
            expect($tool->tool_reviews())->toBeInstanceOf(\Illuminate\Database\Eloquent\Relations\HasMany::class);
        });

        it('can access tool reviews through relationship', function () {
            $tool = Tool::factory()->create();
            
            // Assuming ToolReview factory exists
            if (class_exists('Database\Factories\ToolReviewFactory')) {
                ToolReview::factory()->count(3)->create(['tool_id' => $tool->id]);
                
                expect($tool->tool_reviews->count())->toBe(3);
            } else {
                // If ToolReview factory doesn't exist, just test the relationship setup
                expect($tool->tool_reviews)->toBeInstanceOf(\Illuminate\Database\Eloquent\Collection::class);
            }
        });
    });

    describe('Database Table', function () {
        it('uses correct table name', function () {
            $tool = new Tool();
            
            expect($tool->getTable())->toBe('tools');
        });
    });

    describe('Model Integration', function () {
        it('can create, update, and delete tools', function () {
            // Create
            $tool = Tool::factory()->create(['name' => 'Original Name']);
            expect($tool->name)->toBe('Original Name');
            
            // Update
            $tool->update(['name' => 'Updated Name']);
            expect($tool->fresh()->name)->toBe('Updated Name');
            
            // Delete
            $toolId = $tool->id;
            $tool->delete();
            expect(Tool::find($toolId))->toBeNull();
        });

        it('handles null values appropriately', function () {
            $tool = Tool::factory()->create([
                'description' => 'Test description', // Changed from null to a string value
                'content' => null,
                'thumbnail' => null,
            ]);

            expect($tool->description)->toBe('Test description')
                ->and($tool->content)->toBeNull()
                ->and($tool->thumbnail)->toBeNull();
        });

        it('maintains data integrity with boolean fields', function () {
            $tool = Tool::factory()->create([
                'is_featured' => false,
                'is_published' => true,
            ]);

            // Test that booleans are properly stored and retrieved
            expect($tool->fresh()->is_featured)->toBeFalse()
                ->and($tool->fresh()->is_published)->toBeTrue();
        });
    });

    describe('Edge Cases', function () {
        it('handles very large view counts', function () {
            $tool = Tool::factory()->create(['view_count' => 999999]);
            $tool->incrementViewCount();
            
            expect($tool->fresh()->view_count)->toBe(1000000);
        });

        it('handles zero view count', function () {
            $tool = Tool::factory()->create(['view_count' => 0]);
            $tool->incrementViewCount();
            
            expect($tool->fresh()->view_count)->toBe(1);
        });

        it('handles empty scopes correctly', function () {
            // Delete all tools first
            Tool::query()->delete();
            
            expect(Tool::published()->count())->toBe(0)
                ->and(Tool::featured()->count())->toBe(0)
                ->and(Tool::mostViewed()->count())->toBe(0);
        });
    });
});