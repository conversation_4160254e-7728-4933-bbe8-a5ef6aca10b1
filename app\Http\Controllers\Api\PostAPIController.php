<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\BlogPost;
use Illuminate\Http\Request;
use App\Traits\TracksApiRequests;

class PostAPIController extends Controller
{
    use TracksApiRequests;

    public function get5Posts(Request $request)
    {
        $posts = BlogPost::select('id','title','slug','content','featured_image')->where('is_published', true)
            ->latest()
            ->take(5)
            ->get();

        $response = response()->json(['data' => $posts]);

        $this->trackApiRequest($request, $response);
        return $response;
    }

    public function show(Request $request, $slug)
    {
        $post = BlogPost::where('slug', $slug)
            ->with('comments.user')
            ->firstOrFail();

        $latestPosts = BlogPost::where('id', '!=', $post->id)
            ->latest()
            ->take(5)
            ->get();

        $keywords = json_decode($post->keywords, true);
        $keywordsString = is_array($keywords) ? implode(', ', $keywords) : '';

        $response = response()->json([
            'post' => $post,
            'latest_posts' => $latestPosts,
            'meta' => [
                'title' => $post->title,
                'description' => $post->meta_description,
                'keywords' => $keywordsString,
                'image' => asset(config('defaultimg.default_upload_path') . $post->featured_image),
            ],
        ]);

        $this->trackApiRequest($request, $response);
        return $response;
    }
}