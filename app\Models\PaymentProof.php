<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentProof extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'payment_id',
        'file_path',
        'file_name',
        'original_filename',
        'file_size',
        'mime_type',
        'verification_status',
        'verification_notes',
        'admin_notes',
        'rejection_reason',
        'verified_by',
        'verified_at',
    ];

    protected $casts = [
        'file_size' => 'integer',
        'verified_at' => 'datetime',
    ];

    /**
     * Verification status constants.
     */
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';

    /**
     * Relationship with payment.
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }

    /**
     * Relationship with admin who verified the proof.
     */
    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Scope for pending proofs.
     */
    public function scopePending($query)
    {
        return $query->where('verification_status', self::STATUS_PENDING);
    }

    /**
     * Scope for approved proofs.
     */
    public function scopeApproved($query)
    {
        return $query->where('verification_status', self::STATUS_APPROVED);
    }

    /**
     * Scope for rejected proofs.
     */
    public function scopeRejected($query)
    {
        return $query->where('verification_status', self::STATUS_REJECTED);
    }

    /**
     * Check if proof is pending.
     */
    public function isPending(): bool
    {
        return $this->verification_status === self::STATUS_PENDING;
    }

    /**
     * Check if proof is approved.
     */
    public function isApproved(): bool
    {
        return $this->verification_status === self::STATUS_APPROVED;
    }

    /**
     * Check if proof is rejected.
     */
    public function isRejected(): bool
    {
        return $this->verification_status === self::STATUS_REJECTED;
    }

    /**
     * Mark proof as approved.
     */
    public function markAsApproved($adminId, $notes = null)
    {
        $this->update([
            'verification_status' => self::STATUS_APPROVED,
            'verified_at' => now(),
            'verified_by' => $adminId,
            'verification_notes' => $notes,
        ]);
        return $this;
    }

    /**
     * Mark proof as rejected.
     */
    public function markAsRejected($adminId, $notes = null)
    {
        $this->update([
            'verification_status' => self::STATUS_REJECTED,
            'verified_at' => now(),
            'verified_by' => $adminId,
            'verification_notes' => $notes,
        ]);
        return $this;
    }

    /**
     * Get file URL.
     */
    public function getFileUrl(): string
    {
        return asset('storage/' . $this->file_path);
    }

    /**
     * Get formatted file size.
     */
    public function getFormattedFileSize(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if file is an image.
     */
    public function isImage(): bool
    {
        return str_starts_with($this->mime_type, 'image/');
    }

    /**
     * Check if file is a PDF.
     */
    public function isPdf(): bool
    {
        return $this->mime_type === 'application/pdf';
    }

    /**
     * Get verification status display name.
     */
    public function getVerificationStatusDisplayName(): string
    {
        return match($this->verification_status) {
            self::STATUS_PENDING => 'Pending',
            self::STATUS_APPROVED => 'Approved',
            self::STATUS_REJECTED => 'Rejected',
            default => ucfirst($this->verification_status)
        };
    }

    /**
     * Get verification status color for UI.
     */
    public function getVerificationStatusColor(): string
    {
        return match($this->verification_status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_APPROVED => 'success',
            self::STATUS_REJECTED => 'danger',
            default => 'secondary'
        };
    }
}