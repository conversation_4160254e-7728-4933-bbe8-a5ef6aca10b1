<?php

namespace App\Services\Payment;

class PaymentStatus
{
    public bool $success;
    public ?string $paymentId = null;
    public ?string $orderId = null;
    public ?string $status = null;
    public ?float $amount = null;
    public ?string $currency = null;
    public ?string $message = null;
    public ?array $gatewayResponse = null;
    public ?array $metadata = null;
    public ?\DateTime $paidAt = null;
    public ?\DateTime $updatedAt = null;

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REFUNDED = 'refunded';
    const STATUS_PARTIALLY_REFUNDED = 'partially_refunded';

    public function __construct(bool $success = false)
    {
        $this->success = $success;
    }

    /**
     * Create a successful payment status response.
     */
    public static function success(array $data = []): self
    {
        $response = new self(true);
        
        if (isset($data['payment_id'])) {
            $response->paymentId = $data['payment_id'];
        }
        
        if (isset($data['order_id'])) {
            $response->orderId = $data['order_id'];
        }
        
        if (isset($data['status'])) {
            $response->status = $data['status'];
        }
        
        if (isset($data['amount'])) {
            $response->amount = $data['amount'];
        }
        
        if (isset($data['currency'])) {
            $response->currency = $data['currency'];
        }
        
        if (isset($data['message'])) {
            $response->message = $data['message'];
        }
        
        if (isset($data['gateway_response'])) {
            $response->gatewayResponse = $data['gateway_response'];
        }
        
        if (isset($data['metadata'])) {
            $response->metadata = $data['metadata'];
        }
        
        if (isset($data['paid_at'])) {
            $response->paidAt = $data['paid_at'] instanceof \DateTime 
                ? $data['paid_at'] 
                : new \DateTime($data['paid_at']);
        }
        
        if (isset($data['updated_at'])) {
            $response->updatedAt = $data['updated_at'] instanceof \DateTime 
                ? $data['updated_at'] 
                : new \DateTime($data['updated_at']);
        }
        
        return $response;
    }

    /**
     * Create an error payment status response.
     */
    public static function error(string $message, ?array $gatewayResponse = null): self
    {
        $response = new self(false);
        $response->message = $message;
        $response->gatewayResponse = $gatewayResponse;
        
        return $response;
    }

    /**
     * Set payment ID.
     */
    public function setPaymentId(string $paymentId): self
    {
        $this->paymentId = $paymentId;
        return $this;
    }

    /**
     * Set order ID.
     */
    public function setOrderId(string $orderId): self
    {
        $this->orderId = $orderId;
        return $this;
    }

    /**
     * Set status.
     */
    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    /**
     * Set amount.
     */
    public function setAmount(float $amount): self
    {
        $this->amount = $amount;
        return $this;
    }

    /**
     * Set currency.
     */
    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    /**
     * Set message.
     */
    public function setMessage(string $message): self
    {
        $this->message = $message;
        return $this;
    }

    /**
     * Set gateway response data.
     */
    public function setGatewayResponse(array $gatewayResponse): self
    {
        $this->gatewayResponse = $gatewayResponse;
        return $this;
    }

    /**
     * Set metadata.
     */
    public function setMetadata(array $metadata): self
    {
        $this->metadata = $metadata;
        return $this;
    }

    /**
     * Set paid at timestamp.
     */
    public function setPaidAt(\DateTime $paidAt): self
    {
        $this->paidAt = $paidAt;
        return $this;
    }

    /**
     * Set updated at timestamp.
     */
    public function setUpdatedAt(\DateTime $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    /**
     * Check if the response is successful.
     */
    public function isSuccess(): bool
    {
        return $this->success;
    }

    /**
     * Check if the response is an error.
     */
    public function isError(): bool
    {
        return !$this->success;
    }

    /**
     * Check if payment is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if payment is pending.
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if payment is failed.
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * Check if payment is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    /**
     * Check if payment is refunded.
     */
    public function isRefunded(): bool
    {
        return in_array($this->status, [self::STATUS_REFUNDED, self::STATUS_PARTIALLY_REFUNDED]);
    }

    /**
     * Get the response as an array.
     */
    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'payment_id' => $this->paymentId,
            'order_id' => $this->orderId,
            'status' => $this->status,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'message' => $this->message,
            'gateway_response' => $this->gatewayResponse,
            'metadata' => $this->metadata,
            'paid_at' => $this->paidAt?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Convert to JSON.
     */
    public function toJson(): string
    {
        return json_encode($this->toArray());
    }
}