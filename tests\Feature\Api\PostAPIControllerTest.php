<?php

namespace Tests\Feature\Api;

use App\Models\BlogPost;
use App\Models\Comment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class PostAPIControllerTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_returns_the_5_most_recent_published_posts(): void
    {
        BlogPost::factory()->count(10)->create(['is_published' => true]);
        BlogPost::factory()->count(3)->create(['is_published' => false]);

        $response = $this->getJson('/api/get5Posts');

        $response->assertStatus(200)
            ->assertJsonCount(5, 'data')
            ->assertJsonStructure([
                'data' => [
                    '*' => ['id', 'title', 'slug', 'content', 'featured_image']
                ]
            ]);

        $publishedPosts = BlogPost::where('is_published', true)->latest()->take(5)->get();
        $response_posts = json_decode($response->getContent())->data;

        $this->assertEquals($publishedPosts->pluck('id')->toArray(), collect($response_posts)->pluck('id')->toArray());
    }

    #[Test]
    public function it_returns_a_single_post_by_slug(): void
    {
        $post = BlogPost::factory()->has(Comment::factory()->count(3))->create();
        BlogPost::factory()->count(5)->create();

        $response = $this->getJson('/api/posts/' . $post->slug);

        $response->assertStatus(200)
            ->assertJson([
                'post' => [
                    'slug' => $post->slug
                ]
            ])
            ->assertJsonCount(5, 'latest_posts')
            ->assertJsonCount(3, 'post.comments');
    }

    #[Test]
    public function it_returns_a_404_if_post_slug_does_not_exist(): void
    {
        $response = $this->getJson('/api/posts/non-existent-slug');

        $response->assertStatus(404);
    }
}
