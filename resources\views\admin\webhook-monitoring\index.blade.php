@extends('admin.layouts.app')

@section('title', 'Webhook Monitoring')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <div class="d-flex">
                        <div class="dropdown me-2">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="mdi mdi-filter"></i> Filter
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="?status=pending">Pending</a></li>
                                <li><a class="dropdown-item" href="?status=processed">Processed</a></li>
                                <li><a class="dropdown-item" href="?status=failed">Failed</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="?gateway=razorpay">Razorpay</a></li>
                                <li><a class="dropdown-item" href="?gateway=paypal">PayPal</a></li>
                                <li><a class="dropdown-item" href="?gateway=stripe">Stripe</a></li>
                            </ul>
                        </div>
                        <button type="button" class="btn btn-info me-2" id="refresh-metrics">
                            <i class="mdi mdi-refresh"></i> Refresh
                        </button>
                        <button type="button" class="btn btn-warning" id="clear-logs">
                            <i class="mdi mdi-delete"></i> Clear Old Logs
                        </button>
                    </div>
                </div>
                <h4 class="page-title">Webhook Monitoring</h4>
            </div>
        </div>
    </div>

    <!-- Security Metrics -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-muted mb-3 lh-1 d-block text-truncate">Total Webhooks (24h)</span>
                            <h4 class="mb-3">
                                <span class="counter-value" data-target="{{ $metrics['total_webhooks'] ?? 0 }}">0</span>
                            </h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-primary">
                                <span class="avatar-title bg-primary rounded-circle">
                                    <i class="mdi mdi-webhook font-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-muted mb-3 lh-1 d-block text-truncate">Valid Signatures</span>
                            <h4 class="mb-3">
                                <span class="counter-value" data-target="{{ $metrics['valid_signatures'] ?? 0 }}">0</span>
                            </h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-success">
                                <span class="avatar-title bg-success rounded-circle">
                                    <i class="mdi mdi-shield-check font-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-muted mb-3 lh-1 d-block text-truncate">Invalid Signatures</span>
                            <h4 class="mb-3">
                                <span class="counter-value" data-target="{{ $metrics['invalid_signatures'] ?? 0 }}">0</span>
                            </h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-danger">
                                <span class="avatar-title bg-danger rounded-circle">
                                    <i class="mdi mdi-shield-alert font-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-muted mb-3 lh-1 d-block text-truncate">Error Rate</span>
                            <h4 class="mb-3">
                                <span class="counter-value" data-target="{{ $metrics['error_rate'] ?? 0 }}">0</span>%
                            </h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-warning">
                                <span class="avatar-title bg-warning rounded-circle">
                                    <i class="mdi mdi-alert-circle font-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <div class="col-xl-8">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="header-title">Webhook Activity</h4>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                Last 24 Hours
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" data-period="24">Last 24 Hours</a></li>
                                <li><a class="dropdown-item" href="#" data-period="168">Last 7 Days</a></li>
                                <li><a class="dropdown-item" href="#" data-period="720">Last 30 Days</a></li>
                            </ul>
                        </div>
                    </div>
                    <div id="webhook-activity-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Top Request IPs</h4>
                    <div class="table-responsive">
                        <table class="table table-sm table-centered mb-0">
                            <thead>
                                <tr>
                                    <th>IP Address</th>
                                    <th>Requests</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($metrics['top_ips'] ?? [] as $ip)
                                <tr>
                                    <td>
                                        <code>{{ $ip['ip_address'] }}</code>
                                    </td>
                                    <td>{{ $ip['request_count'] }}</td>
                                    <td>
                                        <span class="badge bg-success">Normal</span>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="3" class="text-center text-muted">No data available</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Webhook Logs -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="header-title">Recent Webhook Events</h4>
                        <div class="d-flex align-items-center">
                            <div class="form-check form-switch me-3">
                                <input class="form-check-input" type="checkbox" id="auto-refresh" checked>
                                <label class="form-check-label" for="auto-refresh">
                                    Auto Refresh
                                </label>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-primary" id="export-logs">
                                <i class="mdi mdi-download"></i> Export
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Timestamp</th>
                                    <th>Gateway</th>
                                    <th>Event Type</th>
                                    <th>IP Address</th>
                                    <th>Signature</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="webhook-logs-tbody">
                                @forelse($webhookLogs ?? [] as $log)
                                <tr>
                                    <td>
                                        <span class="text-muted">{{ $log->created_at->format('M d, H:i:s') }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0 me-2">
                                                <div class="avatar-xs">
                                                    <span class="avatar-title rounded-circle bg-primary">
                                                        {{ strtoupper(substr($log->gateway->name ?? 'N', 0, 1)) }}
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-0">{{ $log->gateway->display_name ?? 'Unknown' }}</h6>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $log->event_type }}</span>
                                    </td>
                                    <td>
                                        <code>{{ $log->ip_address }}</code>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $log->signature === 'valid' ? 'success' : 'danger' }}">
                                            {{ ucfirst($log->signature) }}
                                        </span>
                                    </td>
                                    <td>
                                        @php
                                            $statusColors = [
                                                'pending' => 'warning',
                                                'processed' => 'success',
                                                'failed' => 'danger'
                                            ];
                                        @endphp
                                        <span class="badge bg-{{ $statusColors[$log->status] ?? 'secondary' }}">
                                            {{ ucfirst($log->status) }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" 
                                                    onclick="viewWebhookDetails({{ $log->id }})" title="View Details">
                                                <i class="mdi mdi-eye"></i>
                                            </button>
                                            @if($log->status === 'failed' && $log->retry_count < 3)
                                            <button type="button" class="btn btn-outline-success" 
                                                    onclick="retryWebhook({{ $log->id }})" title="Retry">
                                                <i class="mdi mdi-refresh"></i>
                                            </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="mdi mdi-webhook font-48 text-muted mb-3"></i>
                                            <h5 class="text-muted">No Webhook Events</h5>
                                            <p class="text-muted">No webhook events have been received yet.</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    @if(isset($webhookLogs) && $webhookLogs->hasPages())
                    <div class="d-flex justify-content-center mt-3">
                        {{ $webhookLogs->links() }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Webhook Details Modal -->
<div class="modal fade" id="webhookDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Webhook Event Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="webhook-details-content">
                    <!-- Content will be loaded dynamically -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection@p
ush('scripts')
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize webhook activity chart
    initWebhookActivityChart();
    
    // Auto-refresh functionality
    let autoRefreshInterval;
    const autoRefreshCheckbox = document.getElementById('auto-refresh');
    
    function startAutoRefresh() {
        if (autoRefreshCheckbox.checked) {
            autoRefreshInterval = setInterval(refreshWebhookLogs, 30000); // 30 seconds
        }
    }
    
    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
    }
    
    autoRefreshCheckbox.addEventListener('change', function() {
        if (this.checked) {
            startAutoRefresh();
        } else {
            stopAutoRefresh();
        }
    });
    
    // Start auto-refresh if enabled
    startAutoRefresh();
    
    // Refresh metrics button
    document.getElementById('refresh-metrics').addEventListener('click', function() {
        location.reload();
    });
    
    // Clear logs button
    document.getElementById('clear-logs').addEventListener('click', function() {
        if (confirm('Are you sure you want to clear old webhook logs? This action cannot be undone.')) {
            clearOldLogs();
        }
    });
    
    // Export logs button
    document.getElementById('export-logs').addEventListener('click', function() {
        exportWebhookLogs();
    });
    
    // Counter animation
    document.querySelectorAll('.counter-value').forEach(function(counter) {
        const target = parseInt(counter.getAttribute('data-target'));
        const increment = target / 200;
        let current = 0;
        
        const timer = setInterval(function() {
            current += increment;
            if (current >= target) {
                counter.textContent = target.toLocaleString();
                clearInterval(timer);
            } else {
                counter.textContent = Math.ceil(current).toLocaleString();
            }
        }, 10);
    });
});

function initWebhookActivityChart() {
    // Sample data - in production, this would come from the server
    const chartData = @json($chartData ?? []);
    
    const options = {
        series: [{
            name: 'Successful',
            data: chartData.successful || []
        }, {
            name: 'Failed',
            data: chartData.failed || []
        }],
        chart: {
            type: 'area',
            height: 350,
            toolbar: {
                show: false
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        xaxis: {
            type: 'datetime',
            categories: chartData.timestamps || []
        },
        yaxis: {
            title: {
                text: 'Webhook Count'
            }
        },
        fill: {
            type: 'gradient',
            gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.3,
                stops: [0, 90, 100]
            }
        },
        colors: ['#28a745', '#dc3545'],
        legend: {
            position: 'top'
        }
    };
    
    const chart = new ApexCharts(document.querySelector("#webhook-activity-chart"), options);
    chart.render();
}

function refreshWebhookLogs() {
    fetch('{{ route("admin.webhook-monitoring.logs") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateWebhookLogsTable(data.logs);
            }
        })
        .catch(error => {
            console.error('Failed to refresh webhook logs:', error);
        });
}

function updateWebhookLogsTable(logs) {
    const tbody = document.getElementById('webhook-logs-tbody');
    
    if (logs.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <div class="d-flex flex-column align-items-center">
                        <i class="mdi mdi-webhook font-48 text-muted mb-3"></i>
                        <h5 class="text-muted">No Webhook Events</h5>
                        <p class="text-muted">No webhook events have been received yet.</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = logs.map(log => `
        <tr>
            <td><span class="text-muted">${formatDate(log.created_at)}</span></td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0 me-2">
                        <div class="avatar-xs">
                            <span class="avatar-title rounded-circle bg-primary">
                                ${log.gateway.name.charAt(0).toUpperCase()}
                            </span>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-0">${log.gateway.display_name}</h6>
                    </div>
                </div>
            </td>
            <td><span class="badge bg-info">${log.event_type}</span></td>
            <td><code>${log.ip_address}</code></td>
            <td><span class="badge bg-${log.signature === 'valid' ? 'success' : 'danger'}">${log.signature}</span></td>
            <td><span class="badge bg-${getStatusColor(log.status)}">${log.status}</span></td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="viewWebhookDetails(${log.id})">
                        <i class="mdi mdi-eye"></i>
                    </button>
                    ${log.status === 'failed' && log.retry_count < 3 ? 
                        `<button type="button" class="btn btn-outline-success" onclick="retryWebhook(${log.id})">
                            <i class="mdi mdi-refresh"></i>
                        </button>` : ''
                    }
                </div>
            </td>
        </tr>
    `).join('');
}

function viewWebhookDetails(logId) {
    const modal = new bootstrap.Modal(document.getElementById('webhookDetailsModal'));
    const content = document.getElementById('webhook-details-content');
    
    // Show loading
    content.innerHTML = '<div class="text-center p-4"><div class="spinner-border" role="status"></div></div>';
    modal.show();

    fetch(`{{ route('admin.webhook-monitoring.details', '') }}/${logId}`)
        .then(response => response.text())
        .then(html => {
            content.innerHTML = html;
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">Failed to load webhook details</div>';
        });
}

function retryWebhook(logId) {
    if (!confirm('Are you sure you want to retry processing this webhook?')) {
        return;
    }
    
    fetch(`{{ route('admin.webhook-monitoring.retry', '') }}/${logId}`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Webhook retry initiated successfully');
            setTimeout(() => location.reload(), 2000);
        } else {
            showAlert('error', data.message || 'Failed to retry webhook');
        }
    })
    .catch(error => {
        showAlert('error', 'Failed to retry webhook');
    });
}

function clearOldLogs() {
    fetch('{{ route("admin.webhook-monitoring.clear-logs") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', `Cleared ${data.deleted_count} old webhook logs`);
            setTimeout(() => location.reload(), 2000);
        } else {
            showAlert('error', data.message || 'Failed to clear logs');
        }
    })
    .catch(error => {
        showAlert('error', 'Failed to clear logs');
    });
}

function exportWebhookLogs() {
    const params = new URLSearchParams(window.location.search);
    const exportUrl = '{{ route("admin.webhook-monitoring.export") }}?' + params.toString();
    window.open(exportUrl, '_blank');
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        month: 'short',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

function getStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'processed': 'success',
        'failed': 'danger'
    };
    return colors[status] || 'secondary';
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
@endpush