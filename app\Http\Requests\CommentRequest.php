<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CommentRequest extends FormRequest
{
    public function rules()
    {
        $rules = [
            'content' => [
                'required',
                'string',
                'max:300',
                'not_regex:/<script\b[^>]*>(.*?)<\/script>/is' // Prevent script tags
            ],
            'parent_id' => 'nullable|exists:comments,id'
        ];

        // If user is not authenticated, require guest fields
        if (!auth()->check()) {
            $rules['guest_name'] = 'required|string|max:100';
            $rules['guest_email'] = 'required|email|max:255';
        }

        return $rules;
    }

    public function messages()
    {
        return [
            'content.not_regex' => 'HTML scripts are not allowed in comments.',
            'guest_name.required' => 'Name is required for guest comments.',
            'guest_email.required' => 'Email is required for guest comments.',
            'guest_email.email' => 'Please provide a valid email address.'
        ];
    }
}