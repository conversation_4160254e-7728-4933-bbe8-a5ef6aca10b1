<?php

use App\Models\ContactNumberChange;
use App\Models\PinCode;
use App\Models\User;

test('contact number change can be created', function () {
    $contactNumberChange = ContactNumberChange::factory()->create([
        'old_number' => '1234567890',
        'new_number' => '9876543210',
        'reason' => 'Number has changed',
    ]);

    expect($contactNumberChange)->toBeInstanceOf(ContactNumberChange::class)
        ->and($contactNumberChange->old_number)->toBe('1234567890')
        ->and($contactNumberChange->new_number)->toBe('9876543210')
        ->and($contactNumberChange->reason)->toBe('Number has changed');
});

test('contact number change has pincode relationship', function () {
    $pincode = PinCode::factory()->create();
    $contactNumberChange = ContactNumberChange::factory()->create([
        'pincode_id' => $pincode->id
    ]);

    expect($contactNumberChange->pincode)->toBeInstanceOf(PinCode::class)
        ->and($contactNumberChange->pincode->id)->toBe($pincode->id);
});

test('contact number change has user relationship', function () {
    $user = User::factory()->create();
    $contactNumberChange = ContactNumberChange::factory()->create([
        'user_id' => $user->id
    ]);

    expect($contactNumberChange->user)->toBeInstanceOf(User::class)
        ->and($contactNumberChange->user->id)->toBe($user->id);
});

test('pending scope returns only pending requests', function () {
    // Create one of each status
    ContactNumberChange::factory()->pending()->create();
    ContactNumberChange::factory()->approved()->create();
    ContactNumberChange::factory()->rejected()->create();

    $pendingRequests = ContactNumberChange::pending()->get();

    expect($pendingRequests)->toHaveCount(1)
        ->and($pendingRequests->first()->status)->toBe('pending');
});

test('approved scope returns only approved requests', function () {
    // Create one of each status
    ContactNumberChange::factory()->pending()->create();
    ContactNumberChange::factory()->approved()->create();
    ContactNumberChange::factory()->rejected()->create();

    $approvedRequests = ContactNumberChange::approved()->get();

    expect($approvedRequests)->toHaveCount(1)
        ->and($approvedRequests->first()->status)->toBe('approved');
});

test('rejected scope returns only rejected requests', function () {
    // Create one of each status
    ContactNumberChange::factory()->pending()->create();
    ContactNumberChange::factory()->approved()->create();
    ContactNumberChange::factory()->rejected()->create();

    $rejectedRequests = ContactNumberChange::rejected()->get();

    expect($rejectedRequests)->toHaveCount(1)
        ->and($rejectedRequests->first()->status)->toBe('rejected');
});

test('factory states create correct status records', function () {
    $pending = ContactNumberChange::factory()->pending()->create();
    $approved = ContactNumberChange::factory()->approved()->create();
    $rejected = ContactNumberChange::factory()->rejected()->create();

    expect($pending->status)->toBe('pending')
        ->and($pending->admin_notes)->toBeNull()
        ->and($approved->status)->toBe('approved')
        ->and($approved->admin_notes)->not->toBeNull()
        ->and($rejected->status)->toBe('rejected')
        ->and($rejected->admin_notes)->not->toBeNull();
});

test('fillable attributes are correctly defined', function () {
    $fillable = (new ContactNumberChange())->getFillable();

    expect($fillable)->toContain('pincode_id')
        ->toContain('old_number')
        ->toContain('new_number')
        ->toContain('reason')
        ->toContain('status')
        ->toContain('admin_notes')
        ->toContain('user_id');
});

test('casts are correctly defined', function () {
    $casts = (new ContactNumberChange())->getCasts();

    expect($casts)->toHaveKey('created_at')
        ->toHaveKey('updated_at')
        ->and($casts['created_at'])->toBe('datetime')
        ->and($casts['updated_at'])->toBe('datetime');
});