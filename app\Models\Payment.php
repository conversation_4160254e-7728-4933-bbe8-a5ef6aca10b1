<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Payment extends Model
{
    use HasFactory;

    // Payment status constants
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_REFUNDED = 'refunded';

    // Payment method constants
    const METHOD_PAYPAL = 'paypal';
    const METHOD_RAZORPAY = 'razorpay';
    const METHOD_QR_BANK_TRANSFER = 'qr_bank_transfer';

    protected $fillable = [
        'order_id',
        'payment_id',
        'payer_email',
        'amount',
        'currency',
        'payment_status',
        'payment_method',
        'payment_details',
        'paid_at',
        // New gateway integration fields
        'gateway_id',
        'gateway_payment_id',
        'gateway_order_id',
        'exchange_rate',
        'gateway_fee',
        'net_amount',
        'webhook_data',
        'payment_proof',
        'admin_notes',
        'failed_reason',
        'refund_id',
        'verified_at',
        'verified_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'exchange_rate' => 'decimal:6',
        'gateway_fee' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'payment_details' => 'array',
        'webhook_data' => 'array',
        'paid_at' => 'datetime',
        'verified_at' => 'datetime',
    ];

    /**
     * Relationship with order.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Relationship with payment gateway.
     */
    public function gateway(): BelongsTo
    {
        return $this->belongsTo(PaymentGateway::class, 'gateway_id');
    }

    /**
     * Relationship with admin who verified the payment.
     */
    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Relationship with payment proofs (for QR bank transfer).
     */
    public function paymentProofs(): HasMany
    {
        return $this->hasMany(PaymentProof::class);
    }

    /**
     * Get the latest payment proof.
     */
    public function latestPaymentProof()
    {
        return $this->hasOne(PaymentProof::class)->latest();
    }

    /**
     * Scope for completed payments.
     */
    public function scopeCompleted($query)
    {
        return $query->where('payment_status', self::STATUS_COMPLETED);
    }

    /**
     * Scope for pending payments.
     */
    public function scopePending($query)
    {
        return $query->where('payment_status', self::STATUS_PENDING);
    }

    /**
     * Scope for failed payments.
     */
    public function scopeFailed($query)
    {
        return $query->where('payment_status', self::STATUS_FAILED);
    }

    /**
     * Scope for payments by gateway.
     */
    public function scopeByGateway($query, $gatewayName)
    {
        return $query->whereHas('gateway', function ($q) use ($gatewayName) {
            $q->where('name', $gatewayName);
        });
    }

    /**
     * Scope for payments requiring verification.
     */
    public function scopeRequiresVerification($query)
    {
        return $query->where('payment_method', self::METHOD_QR_BANK_TRANSFER)
                    ->where('payment_status', self::STATUS_PENDING)
                    ->whereNotNull('payment_proof');
    }

    /**
     * Check if payment is completed.
     */
    public function isCompleted(): bool
    {
        return $this->payment_status === self::STATUS_COMPLETED;
    }

    /**
     * Check if payment is pending.
     */
    public function isPending(): bool
    {
        return $this->payment_status === self::STATUS_PENDING;
    }

    /**
     * Check if payment is failed.
     */
    public function isFailed(): bool
    {
        return $this->payment_status === self::STATUS_FAILED;
    }

    /**
     * Check if payment is refunded.
     */
    public function isRefunded(): bool
    {
        return $this->payment_status === self::STATUS_REFUNDED;
    }

    /**
     * Check if payment requires manual verification.
     */
    public function requiresVerification(): bool
    {
        return $this->payment_method === self::METHOD_QR_BANK_TRANSFER 
               && $this->isPending() 
               && !empty($this->payment_proof);
    }

    /**
     * Mark payment as completed.
     */
    public function markAsCompleted($gatewayPaymentId = null, $webhookData = null)
    {
        $updateData = [
            'payment_status' => self::STATUS_COMPLETED,
            'paid_at' => now(),
        ];

        if ($gatewayPaymentId) {
            $updateData['gateway_payment_id'] = $gatewayPaymentId;
        }

        if ($webhookData) {
            $updateData['webhook_data'] = $webhookData;
        }

        $this->update($updateData);
        return $this;
    }

    /**
     * Mark payment as failed.
     */
    public function markAsFailed($reason = null, $webhookData = null)
    {
        $updateData = [
            'payment_status' => self::STATUS_FAILED,
            'failed_reason' => $reason,
        ];

        if ($webhookData) {
            $updateData['webhook_data'] = $webhookData;
        }

        $this->update($updateData);
        return $this;
    }

    /**
     * Mark payment as verified by admin.
     */
    public function markAsVerified($adminId, $notes = null)
    {
        $this->update([
            'payment_status' => self::STATUS_COMPLETED,
            'verified_at' => now(),
            'verified_by' => $adminId,
            'admin_notes' => $notes,
            'paid_at' => now(),
        ]);
        return $this;
    }

    /**
     * Reject payment verification.
     */
    public function rejectVerification($adminId, $reason = null)
    {
        $this->update([
            'payment_status' => self::STATUS_FAILED,
            'verified_at' => now(),
            'verified_by' => $adminId,
            'failed_reason' => $reason,
            'admin_notes' => $reason,
        ]);
        return $this;
    }

    /**
     * Calculate net amount after gateway fee.
     */
    public function calculateNetAmount()
    {
        $netAmount = $this->amount - $this->gateway_fee;
        $this->update(['net_amount' => $netAmount]);
        return $netAmount;
    }

    /**
     * Get payment method display name.
     */
    public function getPaymentMethodDisplayName(): string
    {
        return match($this->payment_method) {
            self::METHOD_PAYPAL => 'PayPal',
            self::METHOD_RAZORPAY => 'Razorpay',
            self::METHOD_QR_BANK_TRANSFER => 'Bank Transfer (QR)',
            default => ucfirst(str_replace('_', ' ', $this->payment_method))
        };
    }

    /**
     * Get payment status display name.
     */
    public function getPaymentStatusDisplayName(): string
    {
        return match($this->payment_status) {
            self::STATUS_PENDING => 'Pending',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_FAILED => 'Failed',
            self::STATUS_REFUNDED => 'Refunded',
            default => ucfirst($this->payment_status)
        };
    }

    /**
     * Get payment status color for UI.
     */
    public function getPaymentStatusColor(): string
    {
        return match($this->payment_status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_FAILED => 'danger',
            self::STATUS_REFUNDED => 'info',
            default => 'secondary'
        };
    }

    /**
     * Check if payment can be refunded.
     */
    public function canBeRefunded(): bool
    {
        return $this->isCompleted() && empty($this->refund_id);
    }

    /**
     * Get gateway-specific payment details.
     */
    public function getGatewayDetails(): array
    {
        $details = $this->payment_details ?? [];
        
        // Add gateway-specific information
        if ($this->gateway) {
            $details['gateway_name'] = $this->gateway->display_name;
            $details['gateway_logo'] = $this->gateway->logo_url;
        }

        return $details;
    }

    /**
     * Store webhook data.
     */
    public function storeWebhookData(array $webhookData)
    {
        $existingData = $this->webhook_data ?? [];
        $existingData[] = [
            'timestamp' => now()->toISOString(),
            'data' => $webhookData,
        ];

        $this->update(['webhook_data' => $existingData]);
        return $this;
    }

    /**
     * Get formatted amount with currency.
     */
    public function getFormattedAmount(): string
    {
        return number_format($this->amount, 2) . ' ' . strtoupper($this->currency);
    }

    /**
     * Get formatted net amount with currency.
     */
    public function getFormattedNetAmount(): string
    {
        $netAmount = $this->net_amount ?? $this->amount;
        return number_format($netAmount, 2) . ' ' . strtoupper($this->currency);
    }

    /**
     * Get formatted gateway fee with currency.
     */
    public function getFormattedGatewayFee(): string
    {
        return number_format($this->gateway_fee, 2) . ' ' . strtoupper($this->currency);
    }
}
