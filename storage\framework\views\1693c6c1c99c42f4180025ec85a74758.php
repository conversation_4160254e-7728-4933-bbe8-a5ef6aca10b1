<?php $__env->startSection('content'); ?>
    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark">Contact Number Change Requests</h1>
            <div class="flex space-x-4">
                <div class="bg-white dark:bg-bg-dark rounded-lg shadow p-4 border border-border-light dark:border-border-dark">
                    <h3 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Total Requests</h3>
                    <p class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark" id="total-count">-</p>
                </div>
                <div class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg shadow p-4 border border-yellow-200 dark:border-yellow-700">
                    <h3 class="text-sm font-medium text-yellow-600 dark:text-yellow-400">Pending</h3>
                    <p class="text-2xl font-bold text-yellow-700 dark:text-yellow-300" id="pending-count">-</p>
                </div>
                <div class="bg-green-50 dark:bg-green-900/20 rounded-lg shadow p-4 border border-green-200 dark:border-green-700">
                    <h3 class="text-sm font-medium text-green-600 dark:text-green-400">Approved</h3>
                    <p class="text-2xl font-bold text-green-700 dark:text-green-300" id="approved-count">-</p>
                </div>
                <div class="bg-red-50 dark:bg-red-900/20 rounded-lg shadow p-4 border border-red-200 dark:border-red-700">
                    <h3 class="text-sm font-medium text-red-600 dark:text-red-400">Rejected</h3>
                    <p class="text-2xl font-bold text-red-700 dark:text-red-300" id="rejected-count">-</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white dark:bg-bg-dark rounded-lg shadow p-4 mb-6 border border-border-light dark:border-border-dark">
            <form action="<?php echo e(route('admin.contact-number-changes.index')); ?>" method="GET" class="flex flex-wrap gap-4">
                <div class="flex-1 min-w-[200px]">
                    <label for="search" class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Search</label>
                    <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>"
                        class="w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark"
                        placeholder="Search by post office, pincode, or number...">
                </div>
                <div class="w-48">
                    <label for="status" class="block text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Status</label>
                    <select name="status" id="status"
                        class="w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark">
                        <option value="">All Status</option>
                        <option value="pending" <?php echo e(request('status') === 'pending' ? 'selected' : ''); ?>>Pending</option>
                        <option value="approved" <?php echo e(request('status') === 'approved' ? 'selected' : ''); ?>>Approved</option>
                        <option value="rejected" <?php echo e(request('status') === 'rejected' ? 'selected' : ''); ?>>Rejected</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="submit"
                        class="bg-primary-light dark:bg-primary-dark text-white px-4 py-2 rounded-md hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-2 dark:focus:ring-offset-bg-dark transition-colors">
                        Filter
                    </button>
                    <?php if(request()->hasAny(['search', 'status'])): ?>
                        <a href="<?php echo e(route('admin.contact-number-changes.index')); ?>"
                            class="ml-2 text-text-secondary-light dark:text-text-secondary-dark hover:text-text-primary-light dark:hover:text-text-primary-dark transition-colors">
                            Clear
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>

        <div class="bg-white dark:bg-bg-dark rounded-lg shadow overflow-hidden border border-border-light dark:border-border-dark">
            <table class="min-w-full divide-y divide-border-light dark:divide-border-dark">
                <thead class="bg-bg-light dark:bg-bg-dark">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Post
                            Office</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Old
                            Number</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">New
                            Number</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Requested
                            By</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                    <?php $__empty_1 = true; $__currentLoopData = $changes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $change): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-bg-light dark:hover:bg-gray-800/50 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark"><?php echo e($change->pincode->name); ?></div>
                                <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark"><?php echo e($change->pincode->pincode); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                <?php echo e($change->old_number ?? 'N/A'); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                <?php echo e($change->new_number); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span
                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                            <?php if($change->status === 'pending'): ?> bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300
                            <?php elseif($change->status === 'approved'): ?> bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300
                            <?php else: ?> bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 <?php endif; ?>">
                                    <?php echo e(ucfirst($change->status)); ?>

                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                <?php echo e($change->user ? $change->user->name : 'Anonymous'); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                <?php echo e($change->created_at->format('M d, Y H:i')); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="<?php echo e(route('admin.contact-number-changes.show', $change)); ?>"
                                    class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light transition-colors">Review</a>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-text-secondary-light dark:text-text-secondary-dark">
                                No contact number change requests found.
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <div class="mt-4">
            <?php echo e($changes->links()); ?>

        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Fetch stats
            fetch('<?php echo e(route('admin.contact-number-changes.stats')); ?>')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('total-count').textContent = data.total;
                    document.getElementById('pending-count').textContent = data.pending;
                    document.getElementById('approved-count').textContent = data.approved;
                    document.getElementById('rejected-count').textContent = data.rejected;
                })
                .catch(error => console.error('Error fetching stats:', error));

            // Auto-submit form when status changes
            document.getElementById('status').addEventListener('change', function() {
                this.form.submit();
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/admin/contact-number-changes/index.blade.php ENDPATH**/ ?>