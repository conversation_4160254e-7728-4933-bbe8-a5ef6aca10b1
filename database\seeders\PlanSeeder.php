<?php

namespace Database\Seeders;

use App\Models\Plan;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Clear existing plans to avoid duplicates
        Plan::query()->delete();

        // Basic Plan
        Plan::create([
            'name' => 'Basic',
            'slug' => 'basic',
            'price' => 9.99,
            'request_limit' => 1000,
            'description' => 'Ideal for startups and small applications with moderate pincode lookup needs.',
            'is_active' => true,
            'features' => [
                'Basic pincode validation',
                'City and state lookup',
                'Standard rate limiting',
                'Email support',
                'Up to 1,000 requests per month'
            ]
        ]);

        // Premium Plan
        Plan::create([
            'name' => 'Premium',
            'slug' => 'premium',
            'price' => 29.99,
            'request_limit' => 10000,
            'description' => 'Perfect for growing businesses with regular pincode API requirements.',
            'is_active' => true,
            'features' => [
                'Advanced pincode validation',
                'City, state and district lookup',
                'Postal code formatting',
                'Geocoding capabilities',
                'Higher rate limits',
                'Priority email support',
                'Data export options',
                'Up to 10,000 requests per month'
            ]
        ]);

        // Enterprise Plan
        Plan::create([
            'name' => 'Enterprise',
            'slug' => 'enterprise',
            'price' => 99.99,
            'request_limit' => 50000,
            'description' => 'For businesses with high-volume pincode lookup needs and advanced requirements.',
            'is_active' => true,
            'features' => [
                'Complete location data access',
                'Bulk lookup capabilities',
                'Address validation',
                'Distance calculation',
                'Nearby location finder',
                'Custom integration support',
                'Dedicated account manager',
                'Service level agreement',
                'Webhook notifications',
                'Up to 50,000 requests per month'
            ]
        ]);
    }
}

// php artisan db:seed --class=PlanSeeder