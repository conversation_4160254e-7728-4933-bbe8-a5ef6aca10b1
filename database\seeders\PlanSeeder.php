<?php

namespace Database\Seeders;

use App\Models\Plan;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Clear existing plans to avoid duplicates
        Plan::query()->delete();

        // Free Plan
        Plan::create([
            'name' => 'Free',
            'slug' => 'free',
            'price' => 0,
            'currency' => '₹',
            'billing_cycle' => 'forever',
            'request_limit' => 100,
            'description' => 'Perfect for personal use and small projects',
            'is_active' => true,
            'sort_order' => 1,
            'features' => [
                '100 API calls per month',
                'Basic pincode search',
                'Standard support',
                'Web interface access',
                'Basic data export'
            ]
        ]);

        // Professional Plan
        Plan::create([
            'name' => 'Professional',
            'slug' => 'professional',
            'price' => 999,
            'currency' => '₹',
            'billing_cycle' => 'monthly',
            'request_limit' => 10000,
            'description' => 'Ideal for growing businesses and developers',
            'is_active' => true,
            'sort_order' => 2,
            'features' => [
                '10,000 API calls per month',
                'Advanced search filters',
                'Priority support',
                'Bulk data download',
                'API documentation',
                'Custom integrations',
                'Analytics dashboard'
            ]
        ]);

        // Enterprise Plan
        Plan::create([
            'name' => 'Enterprise',
            'slug' => 'enterprise',
            'price' => null,
            'currency' => '₹',
            'billing_cycle' => 'custom',
            'request_limit' => 999999,
            'description' => 'Tailored solutions for large organizations',
            'is_active' => true,
            'sort_order' => 3,
            'features' => [
                'Unlimited API calls',
                'Custom data solutions',
                'Dedicated support manager',
                'SLA guarantees',
                'Custom integrations',
                'Advanced analytics',
                'White-label options',
                'On-premise deployment'
            ]
        ]);
    }
}

// php artisan db:seed --class=PlanSeeder