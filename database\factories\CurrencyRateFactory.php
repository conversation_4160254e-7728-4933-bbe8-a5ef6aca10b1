<?php

namespace Database\Factories;

use App\Models\CurrencyRate;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CurrencyRate>
 */
class CurrencyRateFactory extends Factory
{
    protected $model = CurrencyRate::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $currencies = ['USD', 'EUR', 'INR', 'GBP', 'JPY', 'CAD', 'AUD'];
        $fromCurrency = $this->faker->randomElement($currencies);
        $toCurrency = $this->faker->randomElement(array_diff($currencies, [$fromCurrency]));

        return [
            'from_currency' => $fromCurrency,
            'to_currency' => $toCurrency,
            'rate' => $this->generateExchangeRate($fromCurrency, $toCurrency),
            'source' => $this->faker->randomElement(['api', 'manual', 'bank']),
        ];
    }

    /**
     * Create a USD to INR rate
     */
    public function usdToInr(): static
    {
        return $this->state(fn (array $attributes) => [
            'from_currency' => 'USD',
            'to_currency' => 'INR',
            'rate' => $this->faker->randomFloat(4, 80.0, 85.0),
        ]);
    }

    /**
     * Create a USD to EUR rate
     */
    public function usdToEur(): static
    {
        return $this->state(fn (array $attributes) => [
            'from_currency' => 'USD',
            'to_currency' => 'EUR',
            'rate' => $this->faker->randomFloat(4, 0.80, 0.90),
        ]);
    }

    /**
     * Create a USD to GBP rate
     */
    public function usdToGbp(): static
    {
        return $this->state(fn (array $attributes) => [
            'from_currency' => 'USD',
            'to_currency' => 'GBP',
            'rate' => $this->faker->randomFloat(4, 0.70, 0.80),
        ]);
    }

    /**
     * Create an API sourced rate
     */
    public function apiSource(): static
    {
        return $this->state(fn (array $attributes) => [
            'source' => 'api',
        ]);
    }

    /**
     * Create a manually entered rate
     */
    public function manualSource(): static
    {
        return $this->state(fn (array $attributes) => [
            'source' => 'manual',
        ]);
    }

    /**
     * Create a recent rate
     */
    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'updated_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
        ]);
    }

    /**
     * Create a stale rate
     */
    public function stale(): static
    {
        return $this->state(fn (array $attributes) => [
            'updated_at' => $this->faker->dateTimeBetween('-30 days', '-2 days'),
        ]);
    }

    /**
     * Generate realistic exchange rate based on currency pair
     */
    private function generateExchangeRate(string $fromCurrency, string $toCurrency): float
    {
        // Base rates relative to USD
        $baseRates = [
            'USD' => 1.0,
            'EUR' => 0.85,
            'GBP' => 0.73,
            'INR' => 83.0,
            'JPY' => 150.0,
            'CAD' => 1.35,
            'AUD' => 1.50,
        ];

        $fromRate = $baseRates[$fromCurrency] ?? 1.0;
        $toRate = $baseRates[$toCurrency] ?? 1.0;

        $rate = $toRate / $fromRate;

        // Add some random variation (±5%)
        $variation = $this->faker->randomFloat(4, 0.95, 1.05);
        
        return round($rate * $variation, 6);
    }
}