<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class VillagePincode extends Model
{
    protected $table = 'village_pincodes';
    
    public $timestamps = false;
    
    protected $fillable = [
        'state_id',
        'state_name_en',
        'district_code',
        'district_name_en',
        'subdistrict_code',
        'subdistrict_name_en',
        'village_code',
        'village_name_en',
        'pincode'
    ];

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    public function district(): BelongsTo
    {
        return $this->belongsTo(District::class, 'district_code', 'district_code');
    }

    public function pincode(): BelongsTo
    {
        return $this->belongsTo(PinCode::class, 'pincode', 'pincode');
    }
}
