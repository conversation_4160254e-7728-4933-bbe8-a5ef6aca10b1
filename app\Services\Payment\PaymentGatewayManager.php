<?php

namespace App\Services\Payment;

use App\Models\Order;
use App\Models\Payment;
use App\Models\PaymentGateway;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use Illuminate\Support\Facades\Log;

class PaymentGatewayManager
{
    /**
     * Get the default payment gateway.
     */
    public function getDefaultGateway(): ?PaymentGateway
    {
        return PaymentGateway::getDefault();
    }

    /**
     * Get all active payment gateways.
     */
    public function getActiveGateways(): \Illuminate\Database\Eloquent\Collection
    {
        return PaymentGateway::getActiveGateways();
    }

    /**
     * Get gateways that support a specific currency.
     */
    public function getGatewaysForCurrency(string $currency): \Illuminate\Database\Eloquent\Collection
    {
        return PaymentGateway::active()
                           ->get()
                           ->filter(function ($gateway) use ($currency) {
                               return $gateway->supportsCurrency($currency);
                           });
    }

    /**
     * Get the best gateway for an order based on currency and amount.
     */
    public function getBestGatewayForOrder(Order $order): ?PaymentGateway
    {
        $currency = $order->currency ?? 'USD';
        $amount = $order->amount;

        // Get gateways that support the currency
        $supportedGateways = $this->getGatewaysForCurrency($currency);

        if ($supportedGateways->isEmpty()) {
            return null;
        }

        // Sort by lowest fees and preference
        return $supportedGateways->sortBy(function ($gateway) use ($amount, $currency) {
            try {
                $service = PaymentGatewayFactory::create($gateway);
                $fee = $service->calculateFee($amount, $currency);
                
                // Add preference weight (lower sort_order = higher preference)
                return $fee + ($gateway->sort_order * 0.01);
            } catch (\Exception $e) {
                // If service creation fails, deprioritize this gateway
                return 999999;
            }
        })->first();
    }

    /**
     * Create a payment using the specified gateway.
     */
    public function createPayment(Order $order, ?int $gatewayId = null): PaymentResponse
    {
        try {
            $gateway = $this->selectGateway($order, $gatewayId);
            
            if (!$gateway) {
                throw new PaymentGatewayException('No suitable payment gateway found');
            }

            $service = PaymentGatewayFactory::create($gateway);
            
            Log::info('Creating payment', [
                'order_id' => $order->id,
                'gateway' => $gateway->name,
                'amount' => $order->amount,
                'currency' => $order->currency,
            ]);

            return $service->createPayment($order);

        } catch (\Exception $e) {
            Log::error('Payment creation failed', [
                'order_id' => $order->id,
                'gateway_id' => $gatewayId,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Verify a payment using its gateway service.
     */
    public function verifyPayment(Payment $payment): PaymentResponse
    {
        try {
            if (!$payment->gateway) {
                throw new PaymentGatewayException('Payment gateway not found');
            }

            $service = PaymentGatewayFactory::create($payment->gateway);
            
            Log::info('Verifying payment', [
                'payment_id' => $payment->id,
                'gateway' => $payment->gateway->name,
            ]);

            return $service->verifyPayment($payment->gateway_payment_id ?? $payment->id);

        } catch (\Exception $e) {
            Log::error('Payment verification failed', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Get payment status using its gateway service.
     */
    public function getPaymentStatus(Payment $payment): PaymentStatus
    {
        try {
            if (!$payment->gateway) {
                throw new PaymentGatewayException('Payment gateway not found');
            }

            $service = PaymentGatewayFactory::create($payment->gateway);
            
            return $service->getPaymentStatus($payment->gateway_payment_id ?? $payment->id);

        } catch (\Exception $e) {
            Log::error('Payment status check failed', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Refund a payment using its gateway service.
     */
    public function refundPayment(Payment $payment, float $amount): RefundResponse
    {
        try {
            if (!$payment->gateway) {
                throw new PaymentGatewayException('Payment gateway not found');
            }

            $service = PaymentGatewayFactory::create($payment->gateway);
            
            Log::info('Refunding payment', [
                'payment_id' => $payment->id,
                'gateway' => $payment->gateway->name,
                'amount' => $amount,
            ]);

            return $service->refundPayment($payment->gateway_payment_id ?? $payment->id, $amount);

        } catch (\Exception $e) {
            Log::error('Payment refund failed', [
                'payment_id' => $payment->id,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle webhook for a specific gateway.
     */
    public function handleWebhook(string $gatewayName, \Illuminate\Http\Request $request): WebhookResponse
    {
        try {
            $service = PaymentGatewayFactory::createByName($gatewayName);
            
            Log::info('Processing webhook', [
                'gateway' => $gatewayName,
                'event' => $request->input('event', 'unknown'),
            ]);

            return $service->handleWebhook($request);

        } catch (\Exception $e) {
            Log::error('Webhook processing failed', [
                'gateway' => $gatewayName,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Test connection for all active gateways.
     */
    public function testAllConnections(): array
    {
        return PaymentGatewayFactory::testAllServices();
    }

    /**
     * Test connection for a specific gateway.
     */
    public function testGatewayConnection(PaymentGateway $gateway): bool
    {
        try {
            $service = PaymentGatewayFactory::create($gateway);
            return $service->testConnection();
        } catch (\Exception $e) {
            Log::error('Gateway connection test failed', [
                'gateway' => $gateway->name,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get gateway statistics.
     */
    public function getGatewayStats(PaymentGateway $gateway, ?\DateTime $from = null, ?\DateTime $to = null): array
    {
        return $gateway->getStats($from, $to);
    }

    /**
     * Get all gateway statistics.
     */
    public function getAllGatewayStats(?\DateTime $from = null, ?\DateTime $to = null): array
    {
        $stats = [];
        
        foreach ($this->getActiveGateways() as $gateway) {
            $stats[$gateway->name] = $this->getGatewayStats($gateway, $from, $to);
        }

        return $stats;
    }

    /**
     * Calculate fees for all gateways for a given amount and currency.
     */
    public function calculateFeesForAllGateways(float $amount, string $currency): array
    {
        $fees = [];
        
        foreach ($this->getGatewaysForCurrency($currency) as $gateway) {
            try {
                $service = PaymentGatewayFactory::create($gateway);
                $fees[$gateway->name] = [
                    'gateway' => $gateway->display_name,
                    'fee' => $service->calculateFee($amount, $currency),
                    'net_amount' => $amount - $service->calculateFee($amount, $currency),
                ];
            } catch (\Exception $e) {
                $fees[$gateway->name] = [
                    'gateway' => $gateway->display_name,
                    'fee' => 0,
                    'net_amount' => $amount,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $fees;
    }

    /**
     * Get gateway configuration for frontend.
     */
    public function getGatewayConfigForFrontend(PaymentGateway $gateway): array
    {
        $config = [
            'id' => $gateway->id,
            'name' => $gateway->name,
            'display_name' => $gateway->display_name,
            'description' => $gateway->description,
            'logo_url' => $gateway->logo_url,
            'supported_currencies' => $gateway->supported_currencies,
            'is_default' => $gateway->is_default,
        ];

        // Add gateway-specific frontend configuration
        try {
            $service = PaymentGatewayFactory::create($gateway);
            
            if (method_exists($service, 'getFrontendConfig')) {
                $config['frontend_config'] = $service->getFrontendConfig();
            }
        } catch (\Exception $e) {
            Log::warning('Failed to get frontend config for gateway', [
                'gateway' => $gateway->name,
                'error' => $e->getMessage(),
            ]);
        }

        return $config;
    }

    /**
     * Get all active gateways configuration for frontend.
     */
    public function getAllGatewaysConfigForFrontend(): array
    {
        return $this->getActiveGateways()
                   ->map(fn($gateway) => $this->getGatewayConfigForFrontend($gateway))
                   ->toArray();
    }

    /**
     * Select the appropriate gateway for an order.
     */
    private function selectGateway(Order $order, ?int $gatewayId = null): ?PaymentGateway
    {
        if ($gatewayId) {
            // Use specified gateway
            $gateway = PaymentGateway::where('id', $gatewayId)
                                   ->where('is_active', true)
                                   ->first();
            
            if (!$gateway) {
                throw new PaymentGatewayException("Gateway not found or inactive: {$gatewayId}");
            }

            if (!$gateway->supportsCurrency($order->currency ?? 'USD')) {
                throw new PaymentGatewayException("Gateway does not support currency: {$order->currency}");
            }

            return $gateway;
        }

        // Auto-select best gateway
        return $this->getBestGatewayForOrder($order);
    }

    /**
     * Enable a gateway.
     */
    public function enableGateway(PaymentGateway $gateway): bool
    {
        try {
            $gateway->update(['is_active' => true]);
            
            Log::info('Gateway enabled', ['gateway' => $gateway->name]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to enable gateway', [
                'gateway' => $gateway->name,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Disable a gateway.
     */
    public function disableGateway(PaymentGateway $gateway): bool
    {
        try {
            // Don't disable if it's the only active gateway
            $activeCount = PaymentGateway::where('is_active', true)->count();
            if ($activeCount <= 1) {
                throw new PaymentGatewayException('Cannot disable the last active gateway');
            }

            $gateway->update(['is_active' => false]);
            
            // If this was the default gateway, set another as default
            if ($gateway->is_default) {
                $newDefault = PaymentGateway::where('is_active', true)
                                          ->where('id', '!=', $gateway->id)
                                          ->orderBy('sort_order')
                                          ->first();
                
                if ($newDefault) {
                    $newDefault->setAsDefault();
                }
            }
            
            Log::info('Gateway disabled', ['gateway' => $gateway->name]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to disable gateway', [
                'gateway' => $gateway->name,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
}