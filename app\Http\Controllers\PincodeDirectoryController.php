<?php

namespace App\Http\Controllers;

use App\Models\ContactNumberChange;
use App\Models\Like;
use App\Models\Review;
use App\Models\VillagePincode;
use Illuminate\Http\Request;
use App\Models\PinCode;
use App\Models\District;
use App\Models\State;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\ValidationException;

class PincodeDirectoryController extends Controller
{
    public function storeReviews(Request $request)
    {
        $request->validate([
            'pincode_id' => 'required|exists:pin_codes,id',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'required|string|min:10|max:500',
            'name' => $request->user() ? 'nullable' : 'required|string|max:255',
        ]);

        $pincode_id = $request->pincode_id;
        $reviewModel = new Review();

        \Illuminate\Support\Facades\DB::beginTransaction();

        try {
            // Check if this IP has already reviewed this pincode
            $existingReview = $reviewModel->where('pincode_id', $pincode_id)
                ->where('ip_address', $request->ip())
                ->first();

            if ($existingReview) {
                \Illuminate\Support\Facades\DB::rollBack();
                
                if ($request->ajax() || $request->wantsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'You have already reviewed this pincode'
                    ]);
                }
                
                return redirect()->back()->with('success', 'You have already reviewed this pincode.');
            }

            // Create new review
            $reviewModel->create([
                'pincode_id' => $pincode_id,
                'user_id' => $request->user() ? $request->user()->id : null,
                'name' => $request->user() ? $request->user()->name : $request->input('name'),
                'rating' => $request->rating,
                'comment' => strip_tags($request->comment),
                'ip_address' => $request->ip()
            ]);

            // Get updated count
            $reviewsCount = $reviewModel->where('pincode_id', $pincode_id)->count();

            \Illuminate\Support\Facades\DB::commit();

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Review added successfully',
                    'reviews_count' => $reviewsCount
                ]);
            }
            
            return redirect()->back()->with('success', 'Thank you for reviewing this pincode!');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\DB::rollBack();
            \Illuminate\Support\Facades\Log::error('Error adding review: ' . $e->getMessage());

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error adding review'
                ], 500);
            }
            
            return redirect()->back()->with('error', 'An error occurred while processing your request.');
        }
    }

    public function storeLikes(Request $request, Like $likeModel)
    {
        $pincode_id = $request->input('pincode_id');

        // Check if user already liked this pincode (using IP)
        $existingLike = $likeModel->where('pincode_id', $pincode_id)
            ->where('ip_address', $request->ip())
            ->first();

        if ($existingLike) {
            // User already liked this pincode
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Already liked',
                    'likes_count' => $likeModel->where('pincode_id', $pincode_id)->count()
                ]);
            }
            
            return redirect()->back()->with('success', 'You have already liked this pincode.');
        }

        // Create new like
            try {
                \Illuminate\Support\Facades\DB::beginTransaction();

                $likeModel->create([
                    'pincode_id' => $pincode_id,
                    'user_id' => $request->user() ? $request->user()->id : null,
                    'ip_address' => $request->ip()
                ]);

            // Get updated count
            $likesCount = $likeModel->where('pincode_id', $pincode_id)->count();

            \Illuminate\Support\Facades\DB::commit();

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Like added successfully',
                    'likes_count' => $likesCount
                ]);
            }
            
            return redirect()->back()->with('success', 'Thank you for liking this pincode!');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\DB::rollBack();
            \Illuminate\Support\Facades\Log::error('Error adding like: ' . $e->getMessage());

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error adding like'
                ], 500);
            }
            
            return redirect()->back()->with('error', 'An error occurred while processing your request.');
        }
    }

    public function viewAllReviews($pincode_id)
    {
        $pincode = PinCode::with('reviews', 'likes')->findOrFail($pincode_id);

        // Get paginated reviews with increased limit
        $reviews = $pincode->reviews()
            ->where('status', 'approved')
            ->latest()
            ->paginate(50);

        // Handle AJAX requests for pagination
        if (request()->ajax()) {
            $html = '';
            foreach ($reviews as $review) {
                $html .= view('partials.review-item', compact('review'))->render();
            }

            return response()->json([
                'html' => $html,
                'next_page_url' => $reviews->nextPageUrl(),
                'has_more_pages' => $reviews->hasMorePages()
            ]);
        }

        $path = 'pincodes/' . urlencode($pincode->state) . '/' . urlencode($pincode->district) . '/' . urlencode($pincode->name) . '/reviews';
        $breadcrumbs = (new BreadcrumbController())->generateBreadcrumb($path);

        $m_states = State::get();

        return view('pincodes.reviews', [
            'pincode' => $pincode,
            'reviews' => $reviews,
            'pincode_id' => $pincode_id,
            'postoffice' => $pincode->name,
            'district' => $pincode->district,
            'state' => $pincode->state,
            'breadcrumbs' => $breadcrumbs,
            'pageTitle' => 'Reviews for ' . $pincode->pincode,
            'metaDescription' => 'Reviews for ' . $pincode->pincode,
            'm_states' => $m_states,
        ]);
    }

    /**
     * URL /india-postal-code-list/
     */
    public function allPincodelist()
    {
        // Get the current page from the request
        $page = request()->get('page', 1);
        
        // Get paginated results directly without caching
        $pincodes = PinCode::with(['state', 'district'])
            ->select('id', 'pincode', 'name', 'circle', 'district', 'division', 'region', 'state')
            ->orderBy('pincode')
            ->paginate(50);

        // Cache states list for sidebar
        $m_states = Cache::remember('all_states', now()->addHours(24), function () {
            return State::select('id', 'name')
                ->orderBy('name')
                ->get();
        });

        $path = 'india-postal-code-list';
        $breadcrumbs = (new BreadcrumbController())->generateBreadcrumb($path);

        $pageTitle = "India Postal Code List";
        $metaDescription = "Find comprehensive and up-to-date India Postal Code List. Easily search postal codes by state, district, and locality to streamline your mail and delivery services.";
        $metaKeywords = "postal code of india, pin code of india, pin code list, india zip code 6 digit";

        $imgPath = uploads_url("pincodes/pincodes.webp");
        setSEO($pageTitle, $metaDescription, $metaKeywords, $imgPath);

        return view("pincodes.allPincodelist", compact(
            'breadcrumbs',
            'pincodes',
            'metaDescription',
            'pageTitle',
            'm_states'
        ));
    }


    /**
     * URL /pincodes
     */
    public function listofstates()
    {
        try {
           
            // Cache states and their pincode counts with better organization
            $statesData = Cache::remember('states_with_counts', now()->addHours(24), function () {
                // Get all states
                $states = State::orderBy('name')->get();

                // Create a collection for state data
                $stateData = collect();

                foreach ($states as $state) {
                    // Count pincodes directly from the database for each state
                    $pincodeCount = PinCode::where('state', $state->name)->count();

                    // Check if the state has head offices
                    $hasHeadOffice = PinCode::where('state', $state->name)
                        ->where('branch_type', 'HO')
                        ->exists();

                    // Count districts for this state
                    $districtsCount = District::where('state_id', $state->id)->count();

                    // Add to collection
                    $stateData->put($state->name, [
                        'state' => $state,
                        'count' => $pincodeCount,
                        'percentage' => 0, // Will be calculated below
                        'has_ho' => $hasHeadOffice,
                        'districts_count' => $districtsCount
                    ]);
                }

                return $stateData;
            });

            // Calculate total pincodes for percentage calculation
            $totalPincodes = $statesData->sum('count');

            // Cache the total pincodes count for performance
            Cache::remember('total_pincodes', now()->addHours(24), function () use ($totalPincodes) {
                return $totalPincodes;
            });

            // Calculate percentages
            $statesData = $statesData->map(function ($data) use ($totalPincodes) {
                if ($totalPincodes > 0) {
                    $data['percentage'] = ($data['count'] / $totalPincodes) * 100;
                } else {
                    $data['percentage'] = 0;
                }
                return $data;
            });

            // Cache sidebar states
            $m_states = Cache::remember('all_states', now()->addHours(24), function () {
                return State::select('id', 'name')
                    ->orderBy('name')
                    ->get();
            });

            // Cache all states model for performance
            Cache::remember('all_states_model', now()->addHours(24), function () {
                return State::all();
            });

            $pageTitle = "State Wise Pin Codes";
            $metaDescription = "Comprehensive list of Indian Pin Codes organized by state. Find postal codes for all states with detailed statistics and easy navigation.";
            $metaKeywords = "pin code, pincode, postal code, zip code, all india pin code list, pincode directory";

            $imgPath = uploads_url("pincodes/pincodes.webp");
            setSEO($pageTitle, $metaDescription, $metaKeywords, $imgPath);

            $breadcrumbs = (new BreadcrumbController())->generateBreadcrumb('pincodes');

            return view("pincodes.1-all-state-listing", compact(
                'statesData',
                'totalPincodes',
                'm_states',
                'breadcrumbs',
                'pageTitle',
                'metaDescription'
            ));
        } catch (\Exception $e) {
            // Log the error only if not running in the test environment
            if (!app()->environment('testing')) {
            \Illuminate\Support\Facades\Log::error('Database error in listofstates: ' . $e->getMessage());
            }
            // Return a 500 error page
            abort(500, 'Database connection error');
        }
    }

    /**
     * District Wise
     * * URL /pincodes/{state}
     */
    public function listofdistricts($new_state_name)
    {
        try {
            // Standardize state name
            $new_state_name = ucwords(strtolower(str_replace('-', ' ', $new_state_name)));

            // Find the state by name or fail
            $stateList = State::where('name', $new_state_name)->firstOrFail();
            $stateUrl = str_replace(' ', '-', strtolower($stateList->name));

            $path = "pincodes/{$stateUrl}";
            $breadcrumbs = (new BreadcrumbController())->generateBreadcrumb($path);
            
            // Get all states for the sidebar
            $m_states = Cache::remember('all_states', now()->addHours(24), function () {
                return State::select('id', 'name')
                    ->orderBy('name')
                    ->get();
            });

            // Cache the districts for this state
            $m_districts = Cache::remember("districts_{$new_state_name}", now()->addHours(24), function () use ($stateList) {
                return District::where('state_id', $stateList->id)->get();
            });

            // Optimize pincode counting with a more efficient query
            $pincode_counts = Cache::remember("pincode_counts_{$new_state_name}", now()->addHours(6), function () use ($m_districts) {
                return $m_districts->mapWithKeys(function ($district) {
                    $pincode_count = PinCode::where('district', $district->name)
                        ->distinct('pincode')
                        ->count('pincode');
                    return [$district->name => $pincode_count];
                });
            })->toArray();

            $pageTitle = "Pincode list of " . $new_state_name . " State";
            $metaDescription = "Get list of all the Pincode falls under " . $new_state_name . ". Get details of all post offices and related pincodes, district eise, block wise.";
            $metaKeywords = $new_state_name . " Pincodes," . $new_state_name . " Pincode List," . $new_state_name . " pincode, " . " all states pin codes";

            $imgPath = uploads_url("pincodes/pincodes.webp");
            setSEO($pageTitle, $metaDescription, $metaKeywords, $imgPath);

            return view('pincodes.2-pincodes-of-single-state', [
                'stateList' => $stateList,
                'breadcrumbs' => $breadcrumbs,
                'pageTitle' => $pageTitle,
                'metaDescription' => $metaDescription,
                'm_states' => $m_states,
                'm_districts' => $m_districts,
                'pincode_counts' => $pincode_counts,
                'state_name' => $new_state_name
            ]);
        } catch (ModelNotFoundException $e) {
            // Handle not found case
            abort(404, 'State not found');
        } catch (\Exception $e) {
            // Log any errors that occur
            \Log::error('Error in district listing', [
                'state' => $new_state_name,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return a 500 error page instead of re-throwing
            abort(500, 'Database connection error');
        }
    }

    /**
     * Post Office Wise
     * * URL /pincodes/{state}/{district}
     */
    public function listofpostoffices($new_state_name, $new_district_name)
    {
        try {
            $state_name = ucwords(str_replace('-', ' ', $new_state_name));
            $district_name = ucwords(str_replace('-', ' ', $new_district_name));

            $state = State::where('name', $state_name)->firstOrFail();
            $district = District::where('name', $district_name)->where('state_id', $state->id)->firstOrFail();

            $cacheKey = "post_offices_{$state_name}_{$district_name}";

            $pin_codes = Cache::remember($cacheKey, now()->addHours(24), function () use ($district_name) {
                return PinCode::where('district', $district_name)
                    ->orderBy('pincode')
                    ->get();
            });

            $post_offices = $pin_codes->groupBy('pincode');
            $po_counts = $post_offices->map(function ($items) {
                return $items->count();
            })->toArray();

            $m_states = Cache::remember('all_states', now()->addHours(24), function () {
                return State::orderBy('name')->get();
            });
            $m_districts = District::where('state_id', $state->id)->orderBy('name')->get();
            
            $path = 'pincodes/' . str_replace(' ', '-', strtolower($state_name)) . '/' . str_replace(' ', '-', strtolower($district_name));
            $breadcrumbs = (new BreadcrumbController())->generateBreadcrumb($path);

            $pageTitle = "Pincode list of " . $district_name . ", " . $state_name;
            $metaDescription = "Get list of all the Pincode falls under " . $district_name . " district, " . $state_name . ". Get details of all post offices and related pincodes.";
            $metaKeywords = $district_name . " Pincodes," . $district_name . " Pincode List," . $district_name . "-" . $state_name . " Pincode," . $district_name . " district pin code list";
            
            $imgPath = uploads_url("pincodes/pincodes.webp");
            setSEO($pageTitle, $metaDescription, $metaKeywords, $imgPath);

            return view('pincodes.3-pincodes-of-single-district', compact(
                'breadcrumbs', 
                'po_counts', 
                'm_states', 
                'new_state_name', 
                'new_district_name', 
                'm_districts', 
                'pageTitle', 
                'metaDescription', 
                'pin_codes', 
                'post_offices'
            ));
        } catch (ModelNotFoundException $e) {
            abort(404, 'State or district not found');
        } catch (\Exception $e) {
            \Log::error('Error in post office listing', [
                'state' => $new_state_name,
                'district' => $new_district_name,
                'error' => $e->getMessage(),
            ]);
            abort(500, 'An error occurred while fetching post offices.');
        }
    }


    /**
     * URL /pincodes/{state}/{district}/{name}
     */
    public function searchByStateDistrictAndName($state, $district, $name, Request $request)
    {
        $state = urldecode($state);
        $district = urldecode($district);
        $name = urldecode($name);

        $pincodes = PinCode::where('state', $state)
            ->where('district', $district)
            ->where('name', $name)
            ->first();


        if (!$pincodes) {
            abort(404, 'Pincode not found');
        }


        // Load reviews with pagination logic similar to tools
        $reviews = $pincodes->reviews()
            ->where('status', 'approved')
            ->latest()
            ->take(10)
            ->get();

        $totalReviewsCount = $pincodes->reviews()->where('status', 'approved')->count();
        $hasMoreReviews = $totalReviewsCount > 10;

        $pincodes->load('likes');
        $relatedPincodes = PinCode::where('pincode', $pincodes->pincode)
            ->where('id', '!=', $pincodes->id)
            ->get();

        $m_states = State::all();
        $m_district_data = District::where('name', $district)->first();

        $dist_for_coords = PinCode::where('name', $district)
            ->where('branch_type', 'HO')
            ->first();

        $dist_coords_lat = $dist_for_coords->latitude ?? null;
        $dist_coords_long = $dist_for_coords->longitude ?? null;

        $selected_pincode_lat = $pincodes->latitude ?? null;
        $selected_pincode_long = $pincodes->longitude ?? null;

        $distance = null;
        if ($dist_coords_lat && $dist_coords_long && $selected_pincode_lat && $selected_pincode_long) {
            $distance = $this->calculateDistance(
                $dist_coords_lat,
                $dist_coords_long,
                $selected_pincode_lat,
                $selected_pincode_long
            );
        }

        $dist_url = $m_district_data->official_site ?? "Not Found";

        $path = "pincodes/$state/$district/$name";
        $breadcrumbs = (new BreadcrumbController())->generateBreadcrumb($path);

        $pageTitle = "Pincode of " . e($name) . ", " . e($district) . ", " . e($state) . " - " . e($pincodes->pincode);
        $metaDescription = "Find the pincode of " . e($name) . " in " . e($district) . " district, " . e($state) . ". Get details of all post offices and related pincodes.";
        $metaKeywords = e($name) . " pin code, " . e($name) . " pincode, " . e($name) . " post office, " . e($pincodes->pincode) . ", " . e($name) . " " . e($district) . " pin code";

        $imgPath = uploads_url("pincodes/pincodes.webp");

        try {
            setSEO($pageTitle, $metaDescription, $metaKeywords, $imgPath);
        } catch (\Exception $e) {
            // Log the error or handle it as needed
            report($e);
        }
        // Usage example:
        // $dummyData = $this->generateDummyReviewData();

        return view('pincodes.4-pincodes-by-post-office-name', compact(
            'breadcrumbs',
            'pincodes',
            'pageTitle',
            'metaDescription',
            'relatedPincodes',
            'm_states',
            'imgPath',
            'dist_url',
            'distance',
            'reviews',
            'hasMoreReviews',
            'totalReviewsCount',
            'dist_coords_lat',
            'dist_coords_long'
        ));
    }

    /**
     * Calculate the distance between two points given their latitude and longitude.
     *
     * @param float $lat1 Latitude of the first point
     * @param float $lon1 Longitude of the first point
     * @param float $lat2 Latitude of the second point
     * @param float $lon2 Longitude of the second point
     * @param string $unit The unit of measurement ('km' for kilometers, 'mi' for miles)
     * @return float|null The calculated distance or null if coordinates are invalid
     */
    private function calculateDistance($lat1, $lon1, $lat2, $lon2, $unit = 'km')
    {
        if (!$lat1 || !$lon1 || !$lat2 || !$lon2) {
            return null;
        }

        $theta = $lon1 - $lon2;
        $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
        $dist = acos($dist);
        $dist = rad2deg($dist);
        $miles = $dist * 60 * 1.1515;

        if ($unit == 'km') {
            return round($miles * 1.609344, 2);
        }

        return round($miles, 2);
    }

    /**
     * URL /pincodes/{state}/{district}/postal-code/{pincode}
     * 
     * @param mixed $new_state_name
     * @param mixed $new_district_name
     * @param mixed $pincode
     * @return \Illuminate\Contracts\View\View
     */
    public function searchByStateDistrictAndPincode($new_state_name, $new_district_name, $pincode)
    {
        $state = urldecode(ucfirst($new_state_name));
        $district = urldecode(ucfirst($new_district_name));
        $pincode = urldecode($pincode);

        if (strlen($pincode) != 6) {
            abort(404);
        }

        $pincodes = PinCode::where('pincode', $pincode)->get();

        if ($pincodes->isEmpty()) {
            abort(404, 'Page not found');
        }

        $firstPincode = $pincodes->first();

        $pincode_with_po = $pincodes->where('branch_type', 'PO')->first()
            ?? $pincodes->where('branch_type', 'SO')->first();

        $relatedPincodes = PinCode::where('district', $firstPincode->district)
            ->where('pincode', '!=', $pincode)
            ->take(15)
            ->get();

        $m_states = State::all();

        $po_count = $pincodes->count();

        $path = implode('/', ['pincodes', $state, $district, $pincode]);

        $breadcrumbController = new BreadcrumbController();
        $breadcrumbs = $breadcrumbController->generateBreadcrumb($path);

        $pageTitle = "The pincode {$pincode} corresponds to {$po_count} post offices in " . ucfirst($district) . " district";

        $metaDescription = "Find post offices related to pincode {$pincode} in " . ucfirst($district) . " district, " . ucfirst($state) . ". Get details of all post offices and related pincodes.";
        $metaKeywords = "{$pincode}, pincode {$pincode}, pin code {$pincode}, post offices {$pincode}";

        $imgPath = uploads_url("pincodes/pincodes.webp");

        setSEO($pageTitle, $metaDescription, $metaKeywords, $imgPath);

        $villageList = VillagePincode::select('village_name_en', 'pincode')->where("pincode", $pincodes->first()->pincode)->get();

        return view('pincodes.5-pincode-wise-post-offices', compact(
            'breadcrumbs',
            'relatedPincodes',
            'pincodes',
            'villageList',
            'po_count',
            'pincode',
            'district',
            'state',
            'pageTitle',
            'metaDescription',
            'm_states'
        ));
    }


    /**
     * Haader Search (Top Navigation)
     * 
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function searchPincode(Request $request)
    {
        try {
            $query = $request->input('query');
            // Sanitize the query to prevent XSS attacks and SQL injection
            $sanitizedQuery = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $query);
            $sanitizedQuery = preg_replace('/DROP\s+TABLE|UNION\s+SELECT|--/i', '', $sanitizedQuery);
            $sanitizedQuery = strip_tags($sanitizedQuery);
    
            $searchType = $request->input('type', 'pincode'); // Default to pincode search if not specified
    
            if (empty($sanitizedQuery)) {
                if ($request->ajax() || $request->wantsJson()) {
                    return response()->json([
                        'results' => [],
                        'pagination' => [
                            'total' => 0,
                            'per_page' => 15,
                            'current_page' => 1,
                            'last_page' => 1
                        ]
                    ]);
                }
                return view('pincodes.search-results', ['results' => null, 'query' => '', 'type' => $searchType]);
            }
    
            // Use where directly for pincode search to match the test mock
            if ($searchType === 'pincode') {
                $results = PinCode::where('pincode', 'like', $sanitizedQuery . '%')
                    ->with(['district', 'state']) // Eager load relationships
                    ->withCount(['likes', 'reviews']) // Count related models
                    ->orderBy('name', 'asc') // Sort alphabetically by name
                    ->orderBy('pincode') // Secondary sort by pincode for consistency
                    ->paginate(15);
            } else {
                $results = PinCode::query()
                    ->where('name', 'like', '%' . $sanitizedQuery . '%')
                    ->with(['district', 'state']) // Eager load relationships
                    ->withCount(['likes', 'reviews']) // Count related models
                    ->orderBy('name', 'asc') // Sort alphabetically by name
                    ->paginate(15);
            }
    
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'results' => $results->items(),
                    'pagination' => [
                        'total' => $results->total(),
                        'per_page' => $results->perPage(),
                        'current_page' => $results->currentPage(),
                        'last_page' => $results->lastPage()
                    ]
                ]);
            }
    
            return view('pincodes.search-results', [
                'results' => $results,
                'query' => $sanitizedQuery,
                'type' => $searchType
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in searchPincode: ' . $e->getMessage());
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'error' => 'An error occurred while searching',
                    'message' => $e->getMessage()
                ], 500);
            }
            throw $e;
        }
    }


    public function changeContactNumber(Request $request, $name, ContactNumberChange $contactNumberChangeModel)
    {
        try {
            // Validate the request
            $validated = $request->validate([
                'contact_number' => 'required|string|max:20|regex:/^[0-9]+$/',
                'reason' => 'required|string|max:500'
            ]);

            // Find the pincode
            $pincode = PinCode::where('name', $name)->firstOrFail();

            // Sanitize the reason field to prevent XSS
            $sanitizedReason = strip_tags($validated['reason']);

            // Create the change request using the injected model
            $contactNumberChangeModel->create([
                'pincode_id' => $pincode->id,
                'old_number' => $pincode->getOriginal('contact_number'),
                'new_number' => $validated['contact_number'],
                'reason' => $sanitizedReason,
                'status' => 'pending',
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Contact number change request submitted successfully.'
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Post office not found.'
            ], 404);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit contact number change request.',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
