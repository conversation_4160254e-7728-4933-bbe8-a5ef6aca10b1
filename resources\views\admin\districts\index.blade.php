@extends('admin.layouts.admin')

@section('content')
<div class="container px-4 py-6 mx-auto">
    <div class="mb-6">
        <div class="bg-white dark:bg-bg-dark rounded-lg shadow-md border border-border-light dark:border-border-dark">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between p-4 border-b border-border-light dark:border-border-dark space-y-3 sm:space-y-0">
                <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">Districts</h3>
                <div>
                    <a href="{{ route('admin.districts.create') }}" class="inline-flex items-center w-full sm:w-auto justify-center px-3 py-2 text-sm font-medium text-white bg-primary-light dark:bg-primary-dark rounded-md hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        Add New District
                    </a>
                </div>
            </div>
            <div class="p-4">
                @if(session('success'))
                    <div class="p-4 mb-4 text-sm text-green-700 dark:text-green-200 bg-green-100 dark:bg-green-900/20 rounded-lg">
                        {{ session('success') }}
                    </div>
                @endif

                <!-- Note about URL slugs -->
                <div class="p-4 mb-4 text-sm text-blue-700 dark:text-blue-200 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    <p class="mb-2"><strong>Note:</strong> The slug (URL) is based on the State Name, District Name, and Post Office Name. In the existing database, all entries are stored in lowercase.</p>
                    <p>Please remember that URLs with lowercase and uppercase letters can be treated differently on some systems. Therefore, always ensure that the above-mentioned values are kept in lowercase to maintain consistency and avoid SEO issues.</p>
                </div>

                <!-- Mobile Card View (hidden on desktop) -->
                <div class="block lg:hidden space-y-4">
                    @foreach($districts as $district)
                    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-border-light dark:border-border-dark">
                        <div class="flex justify-between items-start mb-3">
                            <div class="text-xs text-text-secondary-light dark:text-text-secondary-dark">
                                ID: {{ $district->id }}
                            </div>
                        </div>
                        <div class="space-y-2 mb-4">
                            <div>
                                <span class="text-text-secondary-light dark:text-text-secondary-dark font-medium text-sm">District:</span>
                                <span class="text-text-primary-light dark:text-text-primary-dark ml-2 font-semibold">{{ $district->name }}</span>
                            </div>
                            <div>
                                <span class="text-text-secondary-light dark:text-text-secondary-dark font-medium text-sm">State:</span>
                                <span class="text-text-primary-light dark:text-text-primary-dark ml-2">{{ $district->state->name }}</span>
                            </div>
                        </div>
                        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 pt-3 border-t border-border-light dark:border-border-dark">
                            <a href="{{ route('admin.districts.edit', $district) }}" class="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-white bg-primary-light dark:bg-primary-dark rounded-md hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                                Edit
                            </a>
                            <form action="{{ route('admin.districts.destroy', $district) }}" method="POST" class="flex-1">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="w-full inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-white bg-red-600 dark:bg-red-700 rounded-md hover:bg-red-700 dark:hover:bg-red-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-red-400" onclick="return confirm('Are you sure you want to delete this district?')">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                    Delete
                                </button>
                            </form>
                        </div>
                    </div>
                    @endforeach

                    @if($districts->isEmpty())
                    <div class="text-center py-8 text-text-secondary-light dark:text-text-secondary-dark">
                        No districts found.
                    </div>
                    @endif
                </div>

                <!-- Desktop Table View (hidden on mobile) -->
                <div class="hidden lg:block overflow-x-auto">
                    <table class="min-w-full divide-y divide-border-light dark:divide-border-dark">
                        <thead class="bg-gray-50 dark:bg-gray-800">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-text-secondary-light dark:text-text-secondary-dark uppercase">ID</th>
                                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-text-secondary-light dark:text-text-secondary-dark uppercase">Name</th>
                                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-text-secondary-light dark:text-text-secondary-dark uppercase">State</th>
                                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-text-secondary-light dark:text-text-secondary-dark uppercase">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                            @foreach($districts as $district)
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                    <td class="px-6 py-4 text-sm text-text-secondary-light dark:text-text-secondary-dark whitespace-nowrap">{{ $district->id }}</td>
                                    <td class="px-6 py-4 text-sm text-text-primary-light dark:text-text-primary-dark whitespace-nowrap">{{ $district->name }}</td>
                                    <td class="px-6 py-4 text-sm text-text-primary-light dark:text-text-primary-dark whitespace-nowrap">{{ $district->state->name }}</td>
                                    <td class="px-6 py-4 text-sm whitespace-nowrap">
                                        <a href="{{ route('admin.districts.edit', $district) }}" class="inline-flex items-center px-3 py-1 text-sm font-medium text-white bg-primary-light dark:bg-primary-dark rounded-md hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark mr-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                            </svg>
                                            Edit
                                        </a>
                                        <form action="{{ route('admin.districts.destroy', $district) }}" method="POST" class="inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="inline-flex items-center px-3 py-1 text-sm font-medium text-white bg-red-600 dark:bg-red-700 rounded-md hover:bg-red-700 dark:hover:bg-red-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-red-400" onclick="return confirm('Are you sure you want to delete this district?')">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                                Delete
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="mt-4">
                    {{ $districts->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection