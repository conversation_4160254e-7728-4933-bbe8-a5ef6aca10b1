<?php

use App\Models\User;
use App\Models\ApiRequest;
use Laravel\Sanctum\PersonalAccessToken;

beforeEach(function () {
    $this->admin = User::factory()->create([
        'role' => 'admin',
        'status' => 'active'
    ]);
    
    $this->actingAs($this->admin);
});

describe('API Token Management Integration', function () {
    
    describe('Token Listing', function () {
        it('displays all API tokens', function () {
            $user = User::factory()->create();
            $user->createToken('Test Token 1');
            $user->createToken('Test Token 2');

            $response = $this->get(route('admin.api-tokens.index'));

            $response->assertStatus(200);
            $response->assertViewIs('admin.api-tokens.index');
            $response->assertViewHas('tokens');
        });

        it('shows token usage statistics', function () {
            $user = User::factory()->create();
            $token = $user->createToken('Test Token');
            
            ApiRequest::factory()->count(10)->create([
                'user_id' => $user->id,
                'token_id' => $token->accessToken->id
            ]);

            $response = $this->get(route('admin.api-tokens.index'));

            $response->assertStatus(200);
        });

        it('filters tokens by user', function () {
            $user1 = User::factory()->create(['name' => 'John Doe']);
            $user2 = User::factory()->create(['name' => 'Jane Smith']);
            
            $user1->createToken('John Token');
            $user2->createToken('Jane Token');

            $response = $this->get(route('admin.api-tokens.index', ['user' => 'John']));

            $response->assertStatus(200);
        });

        it('filters tokens by status', function () {
            $user = User::factory()->create();
            $activeToken = $user->createToken('Active Token');
            
            // Create expired token
            $expiredToken = $user->createToken('Expired Token', ['*'], now()->subDays(1));

            $response = $this->get(route('admin.api-tokens.index', ['status' => 'active']));

            $response->assertStatus(200);
        });
    });

    describe('Token Details', function () {
        it('displays token details and usage', function () {
            $user = User::factory()->create();
            $token = $user->createToken('Test Token');
            
            ApiRequest::factory()->count(5)->create([
                'user_id' => $user->id,
                'token_id' => $token->accessToken->id
            ]);

            $response = $this->get(route('admin.api-tokens.show', $token->accessToken->id));

            $response->assertStatus(200);
            $response->assertViewIs('admin.api-tokens.show');
            $response->assertViewHas('token');
        });

        it('shows token request history', function () {
            $user = User::factory()->create();
            $token = $user->createToken('Test Token');
            
            ApiRequest::factory()->count(3)->create([
                'user_id' => $user->id,
                'token_id' => $token->accessToken->id,
                'endpoint' => '/api/pincodes',
                'method' => 'GET',
                'status_code' => 200
            ]);

            $response = $this->get(route('admin.api-tokens.show', $token->accessToken->id));

            $response->assertStatus(200);
        });

        it('shows token rate limiting information', function () {
            $user = User::factory()->create();
            $token = $user->createToken('Test Token');

            $response = $this->get(route('admin.api-tokens.show', $token->accessToken->id));

            $response->assertStatus(200);
        });
    });

    describe('Token Revocation', function () {
        it('revokes token successfully', function () {
            $user = User::factory()->create();
            $token = $user->createToken('Test Token');
            $tokenId = $token->accessToken->id;

            $response = $this->delete(route('admin.api-tokens.destroy', $tokenId));

            $response->assertRedirect(route('admin.api-tokens.index'));
            $response->assertSessionHas('success');
            
            $this->assertDatabaseMissing('personal_access_tokens', [
                'id' => $tokenId
            ]);
        });

        it('handles non-existent token gracefully', function () {
            $response = $this->delete(route('admin.api-tokens.destroy', 99999));

            $response->assertStatus(404);
        });

        it('logs token revocation', function () {
            $user = User::factory()->create();
            $token = $user->createToken('Test Token');
            $tokenId = $token->accessToken->id;

            $response = $this->delete(route('admin.api-tokens.destroy', $tokenId));

            $response->assertRedirect(route('admin.api-tokens.index'));
            $response->assertSessionHas('success');
        });
    });

    describe('Token Statistics', function () {
        it('returns token statistics as JSON', function () {
            $user1 = User::factory()->create();
            $user2 = User::factory()->create();
            
            $token1 = $user1->createToken('Token 1');
            $token2 = $user2->createToken('Token 2');
            
            ApiRequest::factory()->count(10)->create(['user_id' => $user1->id]);
            ApiRequest::factory()->count(5)->create(['user_id' => $user2->id]);

            $response = $this->get(route('admin.api-tokens.stats'));

            $response->assertStatus(200);
            $response->assertJsonStructure([
                'total_tokens',
                'active_tokens',
                'expired_tokens',
                'total_requests',
                'requests_today',
                'top_users'
            ]);
        });

        it('calculates token metrics correctly', function () {
            $user = User::factory()->create();
            
            // Create active token
            $activeToken = $user->createToken('Active Token');
            
            // Create expired token
            $expiredToken = $user->createToken('Expired Token', ['*'], now()->subDays(1));
            
            ApiRequest::factory()->count(15)->create([
                'user_id' => $user->id,
                'created_at' => now()
            ]);
            ApiRequest::factory()->count(5)->create([
                'user_id' => $user->id,
                'created_at' => now()->subDays(1)
            ]);

            $response = $this->get(route('admin.api-tokens.stats'));

            $response->assertStatus(200);
            $data = $response->json();
            
            expect($data['total_tokens'])->toBe(2);
            expect($data['total_requests'])->toBe(20);
            expect($data['requests_today'])->toBe(15);
        });

        it('shows top API users', function () {
            $user1 = User::factory()->create(['name' => 'Heavy User']);
            $user2 = User::factory()->create(['name' => 'Light User']);
            
            $user1->createToken('Heavy Token');
            $user2->createToken('Light Token');
            
            ApiRequest::factory()->count(100)->create(['user_id' => $user1->id]);
            ApiRequest::factory()->count(10)->create(['user_id' => $user2->id]);

            $response = $this->get(route('admin.api-tokens.stats'));

            $response->assertStatus(200);
            $data = $response->json();
            
            expect($data['top_users'])->toBeArray();
            expect(count($data['top_users']))->toBeGreaterThan(0);
        });
    });

    describe('Token Search and Filtering', function () {
        it('searches tokens by name', function () {
            $user = User::factory()->create();
            $user->createToken('Production API');
            $user->createToken('Development API');
            $user->createToken('Testing Token');

            $response = $this->get(route('admin.api-tokens.index', ['search' => 'API']));

            $response->assertStatus(200);
        });

        it('filters tokens by creation date', function () {
            $user = User::factory()->create();
            
            // Create old token
            $oldToken = $user->createToken('Old Token');
            $oldToken->accessToken->update(['created_at' => now()->subDays(30)]);
            
            // Create new token
            $newToken = $user->createToken('New Token');

            $response = $this->get(route('admin.api-tokens.index', [
                'date_from' => now()->subDays(7)->format('Y-m-d')
            ]));

            $response->assertStatus(200);
        });

        it('filters tokens by usage frequency', function () {
            $user1 = User::factory()->create();
            $user2 = User::factory()->create();
            
            $heavyToken = $user1->createToken('Heavy Usage');
            $lightToken = $user2->createToken('Light Usage');
            
            ApiRequest::factory()->count(100)->create([
                'user_id' => $user1->id,
                'token_id' => $heavyToken->accessToken->id
            ]);
            ApiRequest::factory()->count(5)->create([
                'user_id' => $user2->id,
                'token_id' => $lightToken->accessToken->id
            ]);

            $response = $this->get(route('admin.api-tokens.index', ['usage' => 'high']));

            $response->assertStatus(200);
        });
    });

    describe('Token Analytics Dashboard', function () {
        it('shows request patterns over time', function () {
            $user = User::factory()->create();
            $token = $user->createToken('Analytics Token');
            
            // Create requests over different days
            ApiRequest::factory()->count(10)->create([
                'user_id' => $user->id,
                'created_at' => now()
            ]);
            ApiRequest::factory()->count(15)->create([
                'user_id' => $user->id,
                'created_at' => now()->subDays(1)
            ]);
            ApiRequest::factory()->count(8)->create([
                'user_id' => $user->id,
                'created_at' => now()->subDays(2)
            ]);

            $response = $this->get(route('admin.api-tokens.index'));

            $response->assertStatus(200);
        });

        it('shows error rate statistics', function () {
            $user = User::factory()->create();
            $token = $user->createToken('Error Token');
            
            // Create successful requests
            ApiRequest::factory()->count(80)->create([
                'user_id' => $user->id,
                'status_code' => 200
            ]);
            
            // Create error requests
            ApiRequest::factory()->count(20)->create([
                'user_id' => $user->id,
                'status_code' => 429
            ]);

            $response = $this->get(route('admin.api-tokens.stats'));

            $response->assertStatus(200);
        });

        it('shows endpoint usage distribution', function () {
            $user = User::factory()->create();
            $token = $user->createToken('Endpoint Token');
            
            ApiRequest::factory()->count(50)->create([
                'user_id' => $user->id,
                'endpoint' => '/api/pincodes'
            ]);
            ApiRequest::factory()->count(30)->create([
                'user_id' => $user->id,
                'endpoint' => '/api/distance'
            ]);
            ApiRequest::factory()->count(20)->create([
                'user_id' => $user->id,
                'endpoint' => '/api/nearest'
            ]);

            $response = $this->get(route('admin.api-tokens.stats'));

            $response->assertStatus(200);
        });
    });

    describe('Bulk Token Operations', function () {
        it('revokes multiple tokens at once', function () {
            $user = User::factory()->create();
            $token1 = $user->createToken('Token 1');
            $token2 = $user->createToken('Token 2');
            $token3 = $user->createToken('Token 3');

            $tokenIds = [
                $token1->accessToken->id,
                $token2->accessToken->id
            ];

            $response = $this->post(route('admin.api-tokens.bulk-revoke'), [
                'token_ids' => $tokenIds
            ]);

            $response->assertRedirect(route('admin.api-tokens.index'));
            $response->assertSessionHas('success');
            
            foreach ($tokenIds as $tokenId) {
                $this->assertDatabaseMissing('personal_access_tokens', [
                    'id' => $tokenId
                ]);
            }
        });

        it('validates bulk operation data', function () {
            $response = $this->post(route('admin.api-tokens.bulk-revoke'), [
                'token_ids' => []
            ]);

            $response->assertSessionHasErrors(['token_ids']);
        });
    });

    describe('Authorization', function () {
        it('prevents non-admin from accessing token management', function () {
            $regularUser = User::factory()->create(['role' => 'user']);
            $this->actingAs($regularUser);

            $response = $this->get(route('admin.api-tokens.index'));

            $response->assertStatus(403);
        });

        it('prevents guest from accessing token management', function () {
            auth()->logout();

            $response = $this->get(route('admin.api-tokens.index'));

            $response->assertRedirect(route('admin.login'));
        });
    });
});