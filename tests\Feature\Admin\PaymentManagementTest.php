<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Models\Order;
use App\Models\Payment;
use App\Models\PaymentGateway;
use App\Models\PaymentProof;
use App\Models\Plan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class PaymentManagementTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected User $user;
    protected PaymentGateway $gateway;
    protected Order $order;
    protected Payment $payment;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create(['is_admin' => true]);
        $this->user = User::factory()->create();
        
        $this->gateway = PaymentGateway::factory()->create([
            'name' => 'qr_bank_transfer',
            'display_name' => 'Bank Transfer (QR)',
            'is_active' => true
        ]);

        $plan = Plan::factory()->create();
        $this->order = Order::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $plan->id
        ]);

        $this->payment = Payment::factory()->create([
            'order_id' => $this->order->id,
            'gateway_id' => $this->gateway->id,
            'payment_method' => 'qr_bank_transfer',
            'payment_status' => Payment::STATUS_PENDING
        ]);

        Storage::fake('private');
    }

    public function test_admin_can_view_payments_index()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payments.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.payments.index');
        $response->assertViewHas('payments');
        $response->assertSee($this->payment->id);
    }

    public function test_admin_can_view_payment_details()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payments.show', $this->payment));

        $response->assertStatus(200);
        $response->assertViewIs('admin.payments.show');
        $response->assertViewHas('payment');
        $response->assertSee($this->payment->amount);
        $response->assertSee($this->user->name);
    }

    public function test_admin_can_update_payment_status()
    {
        $this->actingAs($this->admin);

        $response = $this->patch(route('admin.payments.update', $this->payment), [
            'payment_status' => Payment::STATUS_REFUNDED
        ]);

        $response->assertRedirect(route('admin.payments.show', $this->payment));
        $response->assertSessionHas('success');

        $this->payment->refresh();
        $this->assertEquals(Payment::STATUS_REFUNDED, $this->payment->payment_status);

        // Verify order status also updated
        $this->order->refresh();
        $this->assertEquals(Order::STATUS_REFUNDED, $this->order->status);
    }

    public function test_admin_can_view_pending_verifications()
    {
        // Create payment proof
        PaymentProof::factory()->create([
            'payment_id' => $this->payment->id,
            'verification_status' => PaymentProof::STATUS_PENDING
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payment-verification.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.payment-verification.index');
        $response->assertViewHas('pendingPayments');
        $response->assertSee($this->payment->id);
    }

    public function test_admin_can_view_payment_verification_details()
    {
        $paymentProof = PaymentProof::factory()->create([
            'payment_id' => $this->payment->id,
            'verification_status' => PaymentProof::STATUS_PENDING,
            'file_name' => 'payment_proof.jpg'
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payment-verification.show', $this->payment));

        $response->assertStatus(200);
        $response->assertViewIs('admin.payment-verification.show');
        $response->assertViewHas('payment');
        $response->assertSee($paymentProof->file_name);
    }

    public function test_admin_can_approve_payment()
    {
        $paymentProof = PaymentProof::factory()->create([
            'payment_id' => $this->payment->id,
            'verification_status' => PaymentProof::STATUS_PENDING
        ]);

        $this->actingAs($this->admin);

        $response = $this->post(route('admin.payment-verification.approve', $this->payment), [
            'admin_notes' => 'Payment verified successfully'
        ]);

        $response->assertRedirect(route('admin.payments.pending-verifications'));
        $response->assertSessionHas('success');

        // Verify payment status updated
        $this->payment->refresh();
        $this->assertEquals(Payment::STATUS_COMPLETED, $this->payment->payment_status);
        $this->assertEquals($this->admin->id, $this->payment->verified_by);
        $this->assertNotNull($this->payment->verified_at);

        // Verify payment proof status updated
        $paymentProof->refresh();
        $this->assertEquals(PaymentProof::STATUS_APPROVED, $paymentProof->verification_status);
        $this->assertEquals('Payment verified successfully', $paymentProof->admin_notes);

        // Verify order status updated
        $this->order->refresh();
        $this->assertEquals(Order::STATUS_COMPLETED, $this->order->status);
    }

    public function test_admin_can_reject_payment()
    {
        $paymentProof = PaymentProof::factory()->create([
            'payment_id' => $this->payment->id,
            'verification_status' => PaymentProof::STATUS_PENDING
        ]);

        $this->actingAs($this->admin);

        $response = $this->post(route('admin.payment-verification.reject', $this->payment), [
            'admin_notes' => 'Invalid payment proof',
            'rejection_reason' => 'invalid_proof'
        ]);

        $response->assertRedirect(route('admin.payments.pending-verifications'));
        $response->assertSessionHas('success');

        // Verify payment status updated
        $this->payment->refresh();
        $this->assertEquals(Payment::STATUS_FAILED, $this->payment->payment_status);
        $this->assertEquals($this->admin->id, $this->payment->verified_by);

        // Verify payment proof status updated
        $paymentProof->refresh();
        $this->assertEquals(PaymentProof::STATUS_REJECTED, $paymentProof->verification_status);
        $this->assertEquals('Invalid payment proof', $paymentProof->admin_notes);

        // Verify order status updated
        $this->order->refresh();
        $this->assertEquals(Order::STATUS_FAILED, $this->order->status);
    }

    public function test_admin_can_download_payment_proof()
    {
        // Create a test file
        Storage::disk('private')->put('payment_proofs/test_proof.jpg', 'test content');

        $paymentProof = PaymentProof::factory()->create([
            'payment_id' => $this->payment->id,
            'file_path' => 'payment_proofs/test_proof.jpg',
            'file_name' => 'test_proof.jpg',
            'mime_type' => 'image/jpeg'
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payment-verification.download-proof', $paymentProof));

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'image/jpeg');
        $response->assertHeader('Content-Disposition', 'attachment; filename="test_proof.jpg"');
    }

    public function test_admin_can_view_payment_proof_image()
    {
        // Create a test image file
        Storage::disk('private')->put('payment_proofs/test_image.jpg', 'fake image content');

        $paymentProof = PaymentProof::factory()->create([
            'payment_id' => $this->payment->id,
            'file_path' => 'payment_proofs/test_image.jpg',
            'file_name' => 'test_image.jpg',
            'mime_type' => 'image/jpeg'
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payment-verification.view-proof', $paymentProof));

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'image/jpeg');
        $response->assertHeader('Content-Disposition', 'inline; filename="test_image.jpg"');
    }

    public function test_admin_cannot_view_non_image_proof_inline()
    {
        $paymentProof = PaymentProof::factory()->create([
            'payment_id' => $this->payment->id,
            'file_path' => 'payment_proofs/test_document.pdf',
            'file_name' => 'test_document.pdf',
            'mime_type' => 'application/pdf'
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payment-verification.view-proof', $paymentProof));

        $response->assertStatus(400);
        $response->assertJson(['error' => 'File type not supported for viewing']);
    }

    public function test_admin_can_get_verification_statistics()
    {
        // Create various payment proofs with different statuses
        PaymentProof::factory()->create([
            'payment_id' => $this->payment->id,
            'verification_status' => PaymentProof::STATUS_PENDING
        ]);

        $approvedPayment = Payment::factory()->create([
            'payment_method' => 'qr_bank_transfer',
            'payment_status' => Payment::STATUS_COMPLETED,
            'verified_at' => today()
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payment-verification.stats'));

        $response->assertStatus(200);
        $response->assertJson([
            'pending' => 1,
            'approved_today' => 1,
            'rejected_today' => 0,
            'total_verified' => 1
        ]);
    }

    public function test_admin_can_get_security_statistics()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payment-security.stats'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'file_uploads',
            'security_events',
            'rate_limiting',
            'system_health'
        ]);
    }

    public function test_admin_can_cleanup_old_files()
    {
        $this->actingAs($this->admin);

        $response = $this->post(route('admin.payment-security.cleanup-files'), [
            'days_old' => 90
        ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        $response->assertJsonStructure(['deleted_count']);
    }

    public function test_admin_validation_for_payment_approval()
    {
        $paymentProof = PaymentProof::factory()->create([
            'payment_id' => $this->payment->id,
            'verification_status' => PaymentProof::STATUS_PENDING
        ]);

        $this->actingAs($this->admin);

        // Test with admin notes too long
        $response = $this->post(route('admin.payment-verification.approve', $this->payment), [
            'admin_notes' => str_repeat('a', 1001) // Exceeds 1000 character limit
        ]);

        $response->assertSessionHasErrors('admin_notes');
    }

    public function test_admin_validation_for_payment_rejection()
    {
        $paymentProof = PaymentProof::factory()->create([
            'payment_id' => $this->payment->id,
            'verification_status' => PaymentProof::STATUS_PENDING
        ]);

        $this->actingAs($this->admin);

        // Test missing required fields
        $response = $this->post(route('admin.payment-verification.reject', $this->payment), [
            // Missing admin_notes and rejection_reason
        ]);

        $response->assertSessionHasErrors(['admin_notes', 'rejection_reason']);
    }

    public function test_admin_cannot_approve_non_qr_payment()
    {
        $nonQrPayment = Payment::factory()->create([
            'payment_method' => 'razorpay',
            'payment_status' => Payment::STATUS_PENDING
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payment-verification.show', $nonQrPayment));

        $response->assertRedirect(route('admin.payments.pending-verifications'));
        $response->assertSessionHas('error');
    }

    public function test_admin_cannot_approve_payment_without_proof()
    {
        // Payment without any proof
        $paymentWithoutProof = Payment::factory()->create([
            'payment_method' => 'qr_bank_transfer',
            'payment_status' => Payment::STATUS_PENDING
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payment-verification.show', $paymentWithoutProof));

        $response->assertRedirect(route('admin.payments.pending-verifications'));
        $response->assertSessionHas('error');
    }

    public function test_non_admin_cannot_access_payment_management()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('admin.payments.index'));
        $response->assertStatus(403);

        $response = $this->get(route('admin.payment-verification.index'));
        $response->assertStatus(403);

        $response = $this->post(route('admin.payment-verification.approve', $this->payment));
        $response->assertStatus(403);
    }

    public function test_guest_cannot_access_payment_management()
    {
        $response = $this->get(route('admin.payments.index'));
        $response->assertRedirect(route('admin.login'));

        $response = $this->get(route('admin.payment-verification.index'));
        $response->assertRedirect(route('admin.login'));
    }

    public function test_admin_can_handle_file_not_found_error()
    {
        $paymentProof = PaymentProof::factory()->create([
            'payment_id' => $this->payment->id,
            'file_path' => 'payment_proofs/nonexistent_file.jpg',
            'file_name' => 'nonexistent_file.jpg'
        ]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payment-verification.download-proof', $paymentProof));

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Payment proof file not found or access denied.');
    }

    public function test_admin_payment_management_pagination()
    {
        // Create multiple payments
        Payment::factory()->count(15)->create();

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payments.index'));

        $response->assertStatus(200);
        $response->assertViewHas('payments');
        
        // Check pagination links are present
        $response->assertSee('Next');
    }

    public function test_admin_can_filter_payments_by_status()
    {
        // Create payments with different statuses
        Payment::factory()->create(['payment_status' => Payment::STATUS_COMPLETED]);
        Payment::factory()->create(['payment_status' => Payment::STATUS_FAILED]);

        $this->actingAs($this->admin);

        $response = $this->get(route('admin.payments.index', ['status' => Payment::STATUS_COMPLETED]));

        $response->assertStatus(200);
        // In a real implementation, you'd verify the filtering works
    }

    public function test_admin_audit_logging_for_payment_actions()
    {
        $paymentProof = PaymentProof::factory()->create([
            'payment_id' => $this->payment->id,
            'verification_status' => PaymentProof::STATUS_PENDING
        ]);

        $this->actingAs($this->admin);

        // Approve payment
        $this->post(route('admin.payment-verification.approve', $this->payment), [
            'admin_notes' => 'Approved by admin'
        ]);

        // Verify audit log entry was created (would check logs in real implementation)
        $this->assertTrue(true); // Placeholder for actual log verification
    }

    public function test_admin_bulk_payment_operations()
    {
        // Create multiple pending payments
        $payments = Payment::factory()->count(3)->create([
            'payment_status' => Payment::STATUS_PENDING
        ]);

        $this->actingAs($this->admin);

        // In a real implementation, you might have bulk operations
        // This is a placeholder for such functionality
        $response = $this->post(route('admin.payments.bulk-action'), [
            'action' => 'mark_reviewed',
            'payment_ids' => $payments->pluck('id')->toArray()
        ]);

        // This route doesn't exist yet, but would be part of bulk operations
        // $response->assertStatus(200);
    }
}