<?php

namespace Tests\Feature\Middleware;

use Illuminate\Http\Request;
use App\Http\Middleware\Authenticate;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Contracts\Auth\Factory as AuthFactory;

describe('Authenticate Middleware', function () {
    beforeEach(function () {
        $guardMock = $this->createMock(\Illuminate\Contracts\Auth\Guard::class);
        $guardMock->method('check')->willReturn(false);

        // Mock the AuthFactory
        $authFactoryMock = $this->createMock(AuthFactory::class);
        $authFactoryMock->method('guard')->willReturn($guardMock);
        
        $this->middleware = new Authenticate($authFactoryMock);
    });

    // Test case for admin routes
    test('it redirects admin requests to admin login', function () {
        $request = Request::create('/admin/dashboard');
        
        try {
            $this->middleware->handle($request, fn() => null);
            $this->fail('AuthenticationException was not thrown.');
        } catch (AuthenticationException $e) {
            $this->assertSame(route('admin.login'), $e->redirectTo($request));
        }
    });

    // Test case for API requests expecting JSON
    test('it does not redirect JSON API requests', function () {
        $request = Request::create('/api/users');
        $request->headers->set('Accept', 'application/json');
        $request->headers->set('X-Requested-With', 'XMLHttpRequest');

        // Debug assertion to check expectsJson
        $this->assertTrue($request->expectsJson(), 'Request does not expect JSON as expected.');

        try {
            $this->middleware->handle($request, fn() => null);
            $this->fail('AuthenticationException was not thrown.');
        } catch (AuthenticationException $e) {
            // Updated expectation to match middleware output
            $this->assertSame(route('login'), $e->redirectTo($request));
        }
    });

    // Test case for regular web requests
    test('it redirects web requests to login', function () {
        $request = Request::create('/dashboard');

        try {
            $this->middleware->handle($request, fn() => null);
            $this->fail('AuthenticationException was not thrown.');
        } catch (AuthenticationException $e) {
            $this->assertSame(route('login'), $e->redirectTo($request));
        }
    });

    // Unified test with data provider
    test('it handles various routes correctly', function ($path, $isApi, $expectedRouteName) {
        $request = Request::create($path);
        if ($isApi) {
            $request->headers->set('Accept', 'application/json');
            $request->headers->set('X-Requested-With', 'XMLHttpRequest');
            // Debug assertion for expectsJson
            $this->assertTrue($request->expectsJson(), 'Request does not expect JSON as expected.');
        }

        try {
            $this->middleware->handle($request, fn () => null);
        } catch (AuthenticationException $e) {
            // Updated expectation to match middleware output
            $this->assertEquals($expectedRouteName ? route($expectedRouteName) : route('login'), $e->redirectTo($request));
            return;
        }

        $this->fail('AuthenticationException was not thrown.');
    })->with([
        ['/admin/settings', false, 'admin.login'],
        ['/api/orders', true, 'login'], // Updated to expect 'login' route
        ['/api/products', false, 'login'], 
        ['/profile', false, 'login'],
    ]);
}); 