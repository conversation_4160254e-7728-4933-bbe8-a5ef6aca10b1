<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogPost;
use App\Models\BlogPostCategory;
use App\Models\BlogPostTag;
use App\Models\Comment;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BlogController extends Controller
{
    /**
     * Display a listing of the blog posts.
     *
     */
    public function index(Request $request)
    {
        $query = BlogPost::withBasicRelations();

        // Apply filters
        if ($request->has('search')) {
            $query->search($request->search);
        }

        if ($request->has('category')) {
            $query->where('blog_post_category_id', $request->category);
        }

        if ($request->has('status')) {
            if ($request->status === 'published') {
                $query->published();
            } elseif ($request->status === 'draft') {
                $query->where('is_published', false);
            }
        }

        if ($request->has('featured')) {
            $query->where('featured', true);
        }

        // Sort posts
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $posts = $query->paginate(15)->withQueryString();

        // Get categories for filter dropdown
        $categories = BlogPostCategory::orderBy('name')->get();

        return view('admin.blog.posts.blog-index', compact('posts', 'categories'));
    }

    /**
     * Show the form for creating a new blog post.
     *
     */
    public function create()
    {
        $categories = BlogPostCategory::orderBy('name')->get();
        $tags = BlogPostTag::orderBy('name')->get();

        return view('admin.blog.posts.blog-create', compact('categories', 'tags'));
    }

    /**
     * Store a newly created blog post in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'excerpt' => 'nullable|string',
            'blog_post_category_id' => 'required|exists:blog_post_categories,id',
            'featured_image' => 'nullable|image|max:2048',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'meta_keywords' => 'nullable|string',
        ]);

        // Handle featured image upload
        $featuredImage = null;
        if ($request->hasFile('featured_image')) {
            $featuredImage = $request->file('featured_image')->store('blog', 'public');
        }

        // Process meta keywords
        $metaKeywords = null;
        if ($request->filled('meta_keywords')) {
            $metaKeywords = explode(',', $request->meta_keywords);
            $metaKeywords = array_map('trim', $metaKeywords);
        }

        // Create the blog post
        $post = BlogPost::create([
            'title' => $request->title,
            'content' => $request->content,
            'excerpt' => $request->excerpt,
            'featured_image' => $featuredImage,
            'user_id' => Auth::id(),
            'is_published' => $request->has('is_published'),
            'published_at' => $request->has('is_published') ? now() : null,
            'meta_title' => $request->meta_title ?? $request->title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => $metaKeywords,
            'blog_post_category_id' => $request->blog_post_category_id,
            'featured' => $request->has('featured'),
        ]);

        // Attach tags
        if ($request->has('tags')) {
            $post->tags()->attach($request->tags);
        }

        return redirect()->route('admin.blog.index')
            ->with('success', 'Blog post created successfully.');
    }

    /**
     * Show the form for editing the specified blog post.
     *
     */
    public function edit(BlogPost $post)
    {
        $post->load(['category', 'tags']);
        $categories = BlogPostCategory::orderBy('name')->get();
        $tags = BlogPostTag::orderBy('name')->get();

        return view('admin.blog.posts.blog-edit', compact('post', 'categories', 'tags'));
    }

    /**
     * Update the specified blog post in storage.
     *
     */
    public function update(Request $request, BlogPost $post)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'excerpt' => 'nullable|string',
            'blog_post_category_id' => 'required|exists:blog_post_categories,id',
            'featured_image' => 'nullable|image|max:2048',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'meta_keywords' => 'nullable|string',
        ]);

        // Handle featured image upload
        $featuredImage = $post->featured_image;
        if ($request->hasFile('featured_image')) {
            // Delete old image if exists
            if ($post->featured_image) {
                Storage::disk('public')->delete($post->featured_image);
            }
            $featuredImage = $request->file('featured_image')->store('blog', 'public');
        }

        // Process meta keywords
        $metaKeywords = null;
        if ($request->filled('meta_keywords')) {
            $metaKeywords = explode(',', $request->meta_keywords);
            $metaKeywords = array_map('trim', $metaKeywords);
        }

        // Check if publishing status changed
        $wasPublished = $post->is_published;
        $isPublished = $request->has('is_published');
        $publishedAt = $post->published_at;

        if (!$wasPublished && $isPublished) {
            // If post is being published for the first time
            $publishedAt = now();
        }

        // Update the blog post
        $post->update([
            'title' => $request->title,
            'content' => $request->content,
            'excerpt' => $request->excerpt,
            'featured_image' => $featuredImage,
            'is_published' => $isPublished,
            'published_at' => $publishedAt,
            'meta_title' => $request->meta_title ?? $request->title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => $metaKeywords,
            'blog_post_category_id' => $request->blog_post_category_id,
            'featured' => $request->has('featured'),
        ]);

        // Sync tags
        $post->tags()->sync($request->tags ?? []);

        return redirect()->route('admin.blog.index')
            ->with('success', 'Blog post updated successfully.');
    }

    /**
     * Remove the specified blog post from storage.
     */
    public function destroy(BlogPost $post)
    {
        // Delete featured image if exists
        if ($post->featured_image) {
            Storage::disk('public')->delete($post->featured_image);
        }

        // Delete associated comments
        $post->comments()->delete();

        // Delete the post
        $post->delete();

        return redirect()->route('admin.blog.index')
            ->with('success', 'Blog post deleted successfully.');
    }

    /**
     * Handle image upload from editor.
     */
    public function uploadImage(Request $request)
    {
        $request->validate([
            'file' => 'required|image|max:2048',
        ]);

        $path = $request->file('file')->store('blog/content', 'public');
        $url = Storage::disk('public')->url($path);

        return response()->json(['location' => $url]);
    }
}