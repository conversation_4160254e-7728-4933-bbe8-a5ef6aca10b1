@extends('layouts.app')

@section('title', 'Payment Successful')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Success Message -->
            <div class="card border-success shadow-sm">
                <div class="card-body text-center py-5">
                    <div class="success-icon mb-4">
                        <i class="fas fa-check-circle fa-5x text-success"></i>
                    </div>
                    <h2 class="text-success mb-3">Payment Successful!</h2>
                    <p class="lead text-muted mb-4">
                        Thank you for your payment. Your order has been processed successfully.
                    </p>
                    
                    <!-- Order Details -->
                    <div class="order-summary bg-light p-4 rounded mb-4">
                        <div class="row">
                            <div class="col-md-6 text-md-start text-center mb-3 mb-md-0">
                                <h6 class="mb-2">Order Information</h6>
                                <p class="mb-1"><strong>Order ID:</strong> {{ $order->order_number }}</p>
                                <p class="mb-1"><strong>Plan:</strong> {{ $order->plan->name ?? 'N/A' }}</p>
                                <p class="mb-1"><strong>Duration:</strong> {{ $order->plan->duration ?? 'N/A' }} {{ $order->plan->duration_type ?? '' }}</p>
                            </div>
                            <div class="col-md-6 text-md-end text-center">
                                <h6 class="mb-2">Payment Details</h6>
                                <p class="mb-1"><strong>Amount Paid:</strong> {{ $payment->currency ?? $order->currency }} {{ number_format($payment->amount ?? $order->amount, 2) }}</p>
                                <p class="mb-1"><strong>Payment Method:</strong> {{ ucfirst($payment->payment_method ?? 'Razorpay') }}</p>
                                <p class="mb-1"><strong>Transaction ID:</strong> <code>{{ $payment->gateway_payment_id ?? 'N/A' }}</code></p>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                        <a href="{{ route('user.orders.show', $order->id) }}" class="btn btn-primary">
                            <i class="fas fa-receipt me-2"></i>
                            View Order Details
                        </a>
                        <a href="{{ route('user.dashboard') }}" class="btn btn-outline-primary">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Go to Dashboard
                        </a>
                        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>
                            Print Receipt
                        </button>
                    </div>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list-check me-2"></i>
                        What's Next?
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="next-step mb-3">
                                <div class="d-flex align-items-start">
                                    <div class="step-number bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 14px;">
                                        1
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Account Activation</h6>
                                        <p class="text-muted small mb-0">Your plan has been activated and you can start using all features immediately.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="next-step mb-3">
                                <div class="d-flex align-items-start">
                                    <div class="step-number bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 14px;">
                                        2
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Email Confirmation</h6>
                                        <p class="text-muted small mb-0">A confirmation email with your receipt has been sent to your email address.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="next-step mb-3">
                                <div class="d-flex align-items-start">
                                    <div class="step-number bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 14px;">
                                        3
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Access Your Features</h6>
                                        <p class="text-muted small mb-0">Log in to your dashboard to access all the features included in your plan.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="next-step mb-3">
                                <div class="d-flex align-items-start">
                                    <div class="step-number bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 14px;">
                                        4
                                    </div>
                                    <div>
                                        <h6 class="mb-1">Need Help?</h6>
                                        <p class="text-muted small mb-0">Contact our support team if you have any questions or need assistance.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Support Information -->
            <div class="card mt-4">
                <div class="card-body text-center">
                    <h6 class="mb-3">Need Help or Have Questions?</h6>
                    <p class="text-muted mb-3">Our support team is here to help you get the most out of your plan.</p>
                    <div class="d-flex flex-column flex-md-row gap-2 justify-content-center">
                        <a href="mailto:<EMAIL>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-envelope me-2"></i>
                            Email Support
                        </a>
                        <a href="#" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-phone me-2"></i>
                            Call Support
                        </a>
                        <a href="#" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-question-circle me-2"></i>
                            Help Center
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.success-icon {
    animation: successPulse 2s ease-in-out;
}

@keyframes successPulse {
    0% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.order-summary {
    border-left: 4px solid #28a745;
}

.next-step {
    transition: all 0.3s ease;
}

.next-step:hover {
    transform: translateY(-2px);
}

.step-number {
    flex-shrink: 0;
    font-weight: bold;
}

@media print {
    .btn, .card-header, .next-step, .support-info {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .container {
        max-width: 100% !important;
    }
}

@media (max-width: 768px) {
    .success-icon i {
        font-size: 3rem !important;
    }
    
    .lead {
        font-size: 1rem;
    }
    
    .order-summary .row > div {
        text-align: center !important;
        margin-bottom: 1rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-scroll to top
    window.scrollTo(0, 0);
    
    // Confetti effect (optional)
    if (typeof confetti !== 'undefined') {
        confetti({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 }
        });
    }
    
    // Track successful payment (for analytics)
    if (typeof gtag !== 'undefined') {
        gtag('event', 'purchase', {
            transaction_id: '{{ $order->order_number }}',
            value: {{ $payment->amount ?? $order->amount }},
            currency: '{{ $payment->currency ?? $order->currency }}',
            items: [{
                item_id: '{{ $order->plan->id ?? '' }}',
                item_name: '{{ $order->plan->name ?? '' }}',
                category: 'subscription',
                quantity: 1,
                price: {{ $payment->amount ?? $order->amount }}
            }]
        });
    }
    
    // Send success notification to parent window (if in iframe)
    if (window.parent !== window) {
        window.parent.postMessage({
            type: 'payment_success',
            order_id: '{{ $order->id }}',
            amount: {{ $payment->amount ?? $order->amount }}
        }, '*');
    }
});

// Handle print functionality
function printReceipt() {
    window.print();
}

// Auto-redirect after 5 minutes (optional)
setTimeout(function() {
    if (confirm('Would you like to go to your dashboard now?')) {
        window.location.href = '{{ route("user.dashboard") }}';
    }
}, 300000); // 5 minutes
</script>
@endpush