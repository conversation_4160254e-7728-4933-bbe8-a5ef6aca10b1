<?php

namespace App\Services\Payment;

use App\Models\Payment;
use App\Models\PaymentGateway;
use App\Models\Order;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PaymentMonitoringService
{
    /**
     * Cache key prefixes
     */
    private const FAILURE_RATE_PREFIX = 'payment_failure_rate:';
    private const HEALTH_CHECK_PREFIX = 'gateway_health:';
    private const ALERT_SENT_PREFIX = 'alert_sent:';

    /**
     * Monitoring thresholds
     */
    private const CRITICAL_FAILURE_RATE = 10.0; // 10% failure rate
    private const WARNING_FAILURE_RATE = 5.0;   // 5% failure rate
    private const HEALTH_CHECK_INTERVAL = 300;   // 5 minutes
    private const ALERT_COOLDOWN = 3600;         // 1 hour

    /**
     * Monitor payment failure rates
     */
    public function monitorFailureRates(): array
    {
        $results = [];
        $gateways = PaymentGateway::where('is_active', true)->get();

        foreach ($gateways as $gateway) {
            $failureRate = $this->calculateFailureRate($gateway->id);
            $results[$gateway->name] = [
                'gateway_id' => $gateway->id,
                'gateway_name' => $gateway->name,
                'failure_rate' => $failureRate,
                'status' => $this->determineFailureRateStatus($failureRate),
                'last_checked' => now()->toISOString()
            ];

            // Check if we need to send alerts
            $this->checkFailureRateAlerts($gateway, $failureRate);

            // Cache the failure rate
            Cache::put(
                self::FAILURE_RATE_PREFIX . $gateway->id,
                $failureRate,
                300 // 5 minutes
            );
        }

        return $results;
    }

    /**
     * Calculate failure rate for a gateway
     */
    public function calculateFailureRate(int $gatewayId, int $hours = 1): float
    {
        $startTime = now()->subHours($hours);
        
        $totalPayments = Payment::where('gateway_id', $gatewayId)
                               ->where('created_at', '>=', $startTime)
                               ->count();

        if ($totalPayments === 0) {
            return 0.0;
        }

        $failedPayments = Payment::where('gateway_id', $gatewayId)
                                ->where('created_at', '>=', $startTime)
                                ->where('payment_status', Payment::STATUS_FAILED)
                                ->count();

        return round(($failedPayments / $totalPayments) * 100, 2);
    }

    /**
     * Determine failure rate status
     */
    private function determineFailureRateStatus(float $failureRate): string
    {
        if ($failureRate >= self::CRITICAL_FAILURE_RATE) {
            return 'critical';
        } elseif ($failureRate >= self::WARNING_FAILURE_RATE) {
            return 'warning';
        }

        return 'healthy';
    }

    /**
     * Check and send failure rate alerts
     */
    private function checkFailureRateAlerts(PaymentGateway $gateway, float $failureRate): void
    {
        $alertKey = self::ALERT_SENT_PREFIX . "failure_rate:{$gateway->id}";

        if ($failureRate >= self::CRITICAL_FAILURE_RATE) {
            if (!Cache::has($alertKey . ':critical')) {
                $this->sendFailureRateAlert($gateway, $failureRate, 'critical');
                Cache::put($alertKey . ':critical', true, self::ALERT_COOLDOWN);
            }
        } elseif ($failureRate >= self::WARNING_FAILURE_RATE) {
            if (!Cache::has($alertKey . ':warning')) {
                $this->sendFailureRateAlert($gateway, $failureRate, 'warning');
                Cache::put($alertKey . ':warning', true, self::ALERT_COOLDOWN);
            }
        }
    }

    /**
     * Send failure rate alert
     */
    private function sendFailureRateAlert(PaymentGateway $gateway, float $failureRate, string $severity): void
    {
        $alertData = [
            'gateway_id' => $gateway->id,
            'gateway_name' => $gateway->name,
            'failure_rate' => $failureRate,
            'severity' => $severity,
            'threshold' => $severity === 'critical' ? self::CRITICAL_FAILURE_RATE : self::WARNING_FAILURE_RATE,
            'timestamp' => now()->toISOString(),
            'recent_failures' => $this->getRecentFailures($gateway->id)
        ];

        Log::channel('admin-alerts')->{$severity}(
            "Payment gateway failure rate alert: {$gateway->name} at {$failureRate}%",
            $alertData
        );

        // Send notification to administrators
        $this->sendAdminNotification('payment_failure_rate_alert', $alertData);
    }

    /**
     * Get recent payment failures for context
     */
    private function getRecentFailures(int $gatewayId, int $limit = 10): array
    {
        return Payment::where('gateway_id', $gatewayId)
                     ->where('payment_status', Payment::STATUS_FAILED)
                     ->where('created_at', '>=', now()->subHour())
                     ->orderBy('created_at', 'desc')
                     ->limit($limit)
                     ->get(['id', 'failed_reason', 'amount', 'currency', 'created_at'])
                     ->toArray();
    }

    /**
     * Perform health checks on payment gateways
     */
    public function performHealthChecks(): array
    {
        $results = [];
        $gateways = PaymentGateway::where('is_active', true)->get();

        foreach ($gateways as $gateway) {
            $healthStatus = $this->checkGatewayHealth($gateway);
            $results[$gateway->name] = $healthStatus;

            // Cache health status
            Cache::put(
                self::HEALTH_CHECK_PREFIX . $gateway->id,
                $healthStatus,
                self::HEALTH_CHECK_INTERVAL
            );

            // Send alerts for unhealthy gateways
            if ($healthStatus['status'] !== 'healthy') {
                $this->checkHealthAlerts($gateway, $healthStatus);
            }
        }

        return $results;
    }

    /**
     * Check individual gateway health
     */
    public function checkGatewayHealth(PaymentGateway $gateway): array
    {
        $health = [
            'gateway_id' => $gateway->id,
            'gateway_name' => $gateway->name,
            'status' => 'healthy',
            'checks' => [],
            'last_checked' => now()->toISOString(),
            'issues' => []
        ];

        // Check 1: Recent payment success rate
        $successRate = $this->calculateSuccessRate($gateway->id);
        $health['checks']['success_rate'] = [
            'value' => $successRate,
            'status' => $successRate >= 95 ? 'pass' : ($successRate >= 90 ? 'warning' : 'fail'),
            'threshold' => 95
        ];

        if ($successRate < 90) {
            $health['issues'][] = "Low success rate: {$successRate}%";
        }

        // Check 2: Recent payment volume
        $recentVolume = $this->getRecentPaymentVolume($gateway->id);
        $expectedVolume = $this->getExpectedPaymentVolume($gateway->id);
        $volumeRatio = $expectedVolume > 0 ? ($recentVolume / $expectedVolume) * 100 : 100;

        $health['checks']['payment_volume'] = [
            'recent' => $recentVolume,
            'expected' => $expectedVolume,
            'ratio' => round($volumeRatio, 2),
            'status' => $volumeRatio >= 50 ? 'pass' : ($volumeRatio >= 25 ? 'warning' : 'fail')
        ];

        if ($volumeRatio < 25) {
            $health['issues'][] = "Unusually low payment volume: {$recentVolume} (expected ~{$expectedVolume})";
        }

        // Check 3: Response time (if available)
        $avgResponseTime = $this->getAverageResponseTime($gateway->id);
        if ($avgResponseTime !== null) {
            $health['checks']['response_time'] = [
                'value' => $avgResponseTime,
                'status' => $avgResponseTime <= 5000 ? 'pass' : ($avgResponseTime <= 10000 ? 'warning' : 'fail'),
                'threshold' => 5000
            ];

            if ($avgResponseTime > 10000) {
                $health['issues'][] = "High response time: {$avgResponseTime}ms";
            }
        }

        // Check 4: Recent errors
        $recentErrors = $this->getRecentErrorCount($gateway->id);
        $health['checks']['recent_errors'] = [
            'count' => $recentErrors,
            'status' => $recentErrors <= 5 ? 'pass' : ($recentErrors <= 10 ? 'warning' : 'fail'),
            'threshold' => 5
        ];

        if ($recentErrors > 10) {
            $health['issues'][] = "High error count: {$recentErrors} in last hour";
        }

        // Determine overall status
        $failedChecks = collect($health['checks'])->where('status', 'fail')->count();
        $warningChecks = collect($health['checks'])->where('status', 'warning')->count();

        if ($failedChecks > 0) {
            $health['status'] = 'unhealthy';
        } elseif ($warningChecks > 1) {
            $health['status'] = 'degraded';
        }

        return $health;
    }

    /**
     * Calculate success rate for a gateway
     */
    private function calculateSuccessRate(int $gatewayId, int $hours = 1): float
    {
        $startTime = now()->subHours($hours);
        
        $totalPayments = Payment::where('gateway_id', $gatewayId)
                               ->where('created_at', '>=', $startTime)
                               ->count();

        if ($totalPayments === 0) {
            return 100.0; // No payments means no failures
        }

        $successfulPayments = Payment::where('gateway_id', $gatewayId)
                                    ->where('created_at', '>=', $startTime)
                                    ->where('payment_status', Payment::STATUS_COMPLETED)
                                    ->count();

        return round(($successfulPayments / $totalPayments) * 100, 2);
    }

    /**
     * Get recent payment volume
     */
    private function getRecentPaymentVolume(int $gatewayId, int $hours = 1): int
    {
        return Payment::where('gateway_id', $gatewayId)
                     ->where('created_at', '>=', now()->subHours($hours))
                     ->count();
    }

    /**
     * Get expected payment volume based on historical data
     */
    private function getExpectedPaymentVolume(int $gatewayId, int $hours = 1): int
    {
        // Calculate average volume for the same time period over the last 7 days
        $averages = [];
        
        for ($i = 1; $i <= 7; $i++) {
            $startTime = now()->subDays($i)->subHours($hours);
            $endTime = now()->subDays($i);
            
            $volume = Payment::where('gateway_id', $gatewayId)
                            ->whereBetween('created_at', [$startTime, $endTime])
                            ->count();
            
            $averages[] = $volume;
        }

        return count($averages) > 0 ? (int) round(array_sum($averages) / count($averages)) : 0;
    }

    /**
     * Get average response time (placeholder - would need actual implementation)
     */
    private function getAverageResponseTime(int $gatewayId): ?int
    {
        // This would require storing response times in the database
        // For now, return null to indicate unavailable
        return null;
    }

    /**
     * Get recent error count
     */
    private function getRecentErrorCount(int $gatewayId, int $hours = 1): int
    {
        return Payment::where('gateway_id', $gatewayId)
                     ->where('created_at', '>=', now()->subHours($hours))
                     ->where('payment_status', Payment::STATUS_FAILED)
                     ->count();
    }

    /**
     * Check and send health alerts
     */
    private function checkHealthAlerts(PaymentGateway $gateway, array $healthStatus): void
    {
        $alertKey = self::ALERT_SENT_PREFIX . "health:{$gateway->id}:{$healthStatus['status']}";

        if (!Cache::has($alertKey)) {
            $this->sendHealthAlert($gateway, $healthStatus);
            Cache::put($alertKey, true, self::ALERT_COOLDOWN);
        }
    }

    /**
     * Send health alert
     */
    private function sendHealthAlert(PaymentGateway $gateway, array $healthStatus): void
    {
        $severity = match ($healthStatus['status']) {
            'unhealthy' => 'error',
            'degraded' => 'warning',
            default => 'info'
        };

        Log::channel('admin-alerts')->{$severity}(
            "Payment gateway health alert: {$gateway->name} is {$healthStatus['status']}",
            $healthStatus
        );

        $this->sendAdminNotification('payment_gateway_health_alert', $healthStatus);
    }

    /**
     * Get comprehensive monitoring dashboard data
     */
    public function getDashboardData(): array
    {
        return [
            'overview' => $this->getOverviewStats(),
            'failure_rates' => $this->monitorFailureRates(),
            'health_checks' => $this->performHealthChecks(),
            'recent_alerts' => $this->getRecentAlerts(),
            'payment_trends' => $this->getPaymentTrends(),
            'gateway_comparison' => $this->getGatewayComparison()
        ];
    }

    /**
     * Get overview statistics
     */
    private function getOverviewStats(): array
    {
        $last24Hours = now()->subHours(24);
        
        return [
            'total_payments_24h' => Payment::where('created_at', '>=', $last24Hours)->count(),
            'successful_payments_24h' => Payment::where('created_at', '>=', $last24Hours)
                                               ->where('payment_status', Payment::STATUS_COMPLETED)
                                               ->count(),
            'failed_payments_24h' => Payment::where('created_at', '>=', $last24Hours)
                                           ->where('payment_status', Payment::STATUS_FAILED)
                                           ->count(),
            'total_revenue_24h' => Payment::where('created_at', '>=', $last24Hours)
                                         ->where('payment_status', Payment::STATUS_COMPLETED)
                                         ->sum('amount'),
            'active_gateways' => PaymentGateway::where('is_active', true)->count(),
            'overall_success_rate' => $this->calculateOverallSuccessRate()
        ];
    }

    /**
     * Calculate overall success rate across all gateways
     */
    private function calculateOverallSuccessRate(int $hours = 24): float
    {
        $startTime = now()->subHours($hours);
        
        $totalPayments = Payment::where('created_at', '>=', $startTime)->count();
        
        if ($totalPayments === 0) {
            return 100.0;
        }

        $successfulPayments = Payment::where('created_at', '>=', $startTime)
                                    ->where('payment_status', Payment::STATUS_COMPLETED)
                                    ->count();

        return round(($successfulPayments / $totalPayments) * 100, 2);
    }

    /**
     * Get recent alerts
     */
    private function getRecentAlerts(int $hours = 24): array
    {
        // This would typically come from a dedicated alerts table
        // For now, we'll return a placeholder
        return [
            'total_alerts' => 0,
            'critical_alerts' => 0,
            'warning_alerts' => 0,
            'alerts' => []
        ];
    }

    /**
     * Get payment trends
     */
    private function getPaymentTrends(int $days = 7): array
    {
        $trends = [];
        
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $startOfDay = now()->subDays($i)->startOfDay();
            $endOfDay = now()->subDays($i)->endOfDay();
            
            $trends[] = [
                'date' => $date,
                'total_payments' => Payment::whereBetween('created_at', [$startOfDay, $endOfDay])->count(),
                'successful_payments' => Payment::whereBetween('created_at', [$startOfDay, $endOfDay])
                                               ->where('payment_status', Payment::STATUS_COMPLETED)
                                               ->count(),
                'failed_payments' => Payment::whereBetween('created_at', [$startOfDay, $endOfDay])
                                           ->where('payment_status', Payment::STATUS_FAILED)
                                           ->count(),
                'total_revenue' => Payment::whereBetween('created_at', [$startOfDay, $endOfDay])
                                         ->where('payment_status', Payment::STATUS_COMPLETED)
                                         ->sum('amount')
            ];
        }

        return $trends;
    }

    /**
     * Get gateway comparison data
     */
    private function getGatewayComparison(int $hours = 24): array
    {
        $gateways = PaymentGateway::where('is_active', true)->get();
        $comparison = [];

        foreach ($gateways as $gateway) {
            $startTime = now()->subHours($hours);
            
            $totalPayments = Payment::where('gateway_id', $gateway->id)
                                   ->where('created_at', '>=', $startTime)
                                   ->count();
            
            $successfulPayments = Payment::where('gateway_id', $gateway->id)
                                        ->where('created_at', '>=', $startTime)
                                        ->where('payment_status', Payment::STATUS_COMPLETED)
                                        ->count();
            
            $revenue = Payment::where('gateway_id', $gateway->id)
                             ->where('created_at', '>=', $startTime)
                             ->where('payment_status', Payment::STATUS_COMPLETED)
                             ->sum('amount');

            $comparison[] = [
                'gateway_name' => $gateway->name,
                'total_payments' => $totalPayments,
                'successful_payments' => $successfulPayments,
                'success_rate' => $totalPayments > 0 ? round(($successfulPayments / $totalPayments) * 100, 2) : 0,
                'revenue' => $revenue,
                'market_share' => 0 // Will be calculated after all gateways are processed
            ];
        }

        // Calculate market share
        $totalAllPayments = array_sum(array_column($comparison, 'total_payments'));
        if ($totalAllPayments > 0) {
            foreach ($comparison as &$gateway) {
                $gateway['market_share'] = round(($gateway['total_payments'] / $totalAllPayments) * 100, 2);
            }
        }

        return $comparison;
    }

    /**
     * Send admin notification
     */
    private function sendAdminNotification(string $type, array $data): void
    {
        // In a real implementation, you would send actual notifications
        Log::channel('admin-notifications')->info("Admin notification: {$type}", $data);
    }
}