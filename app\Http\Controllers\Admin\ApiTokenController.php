<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\ApiRequest;
use App\Models\PersonalAccessToken;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;

class ApiTokenController extends Controller
{
    /**
     * Display a listing of all API tokens.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request): View
    {
        $query = PersonalAccessToken::with('tokenable');

        // Apply search filter
        if ($search = $request->input('search')) {
            $query->whereHas('tokenable', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            })->orWhere('name', 'like', "%{$search}%");
        }

        // Apply user filter
        if ($userId = $request->input('user_id')) {
            $query->where('tokenable_id', $userId)
                ->where('tokenable_type', User::class);
        }

        // Get paginated results
        $tokens = $query->orderBy('created_at', 'desc')
            ->paginate(15)
            ->withQueryString();

        // Get users for filter dropdown
        $users = User::orderBy('name')->get(['id', 'name', 'email']);

        // Get token usage statistics
        $tokenStats = $this->getTokenStats();

        return view('admin.api-tokens.index', compact('tokens', 'users', 'tokenStats'));
    }

    /**
     * Show the details for a specific token.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show(int $id): View
    {
        $token = PersonalAccessToken::with('tokenable', 'apiRequests')->findOrFail($id);

        // Get recent API requests for this token
        $recentRequests = $token->apiRequests()
            ->orderBy('created_at', 'desc')
            ->take(50)
            ->get();

        // Get usage statistics
        $usageStats = [
            'total_requests' => $token->apiRequests()->count(),
            'successful_requests' => $token->apiRequests()->where('status', 200)->count(),
            'failed_requests' => $token->apiRequests()->where('status', '!=', 200)->count(),
            'last_used' => $token->last_used_at,
            'endpoints' => $token->apiRequests()
                ->select('endpoint', DB::raw('count(*) as count'))
                ->groupBy('endpoint')
                ->orderBy('count', 'desc')
                ->take(5)
                ->get()
        ];

        return view('admin.api-tokens.show', compact('token', 'recentRequests', 'usageStats'));
    }

    /**
     * Revoke the specified token.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(int $id): RedirectResponse
    {
        $token = PersonalAccessToken::findOrFail($id);
        $userName = $token->tokenable->name;
        $tokenName = $token->name;

        $token->delete();

        return redirect()->route('admin.api-tokens.index')
            ->with('success', "Token '{$tokenName}' for user '{$userName}' has been revoked.");
    }

    /**
     * Get token usage statistics.
     *
     * @return array
     */
    private function getTokenStats(): array
    {
        return [
            'total_tokens' => PersonalAccessToken::count(),
            'active_tokens' => PersonalAccessToken::where('created_at', '>=', now()->subDays(User::TOKEN_EXPIRY_DAYS))->count(),
            'expired_tokens' => PersonalAccessToken::where('created_at', '<', now()->subDays(User::TOKEN_EXPIRY_DAYS))->count(),
            'total_requests' => ApiRequest::count(),
            'users_with_tokens' => DB::table('personal_access_tokens')
                ->select('tokenable_id')
                ->where('tokenable_type', User::class)
                ->distinct()
                ->count('tokenable_id'),
            'most_active_token' => PersonalAccessToken::withCount('apiRequests')
                ->orderBy('api_requests_count', 'desc')
                ->first()
        ];
    }

    /**
     * Display token usage statistics.
     *
     * @return \Illuminate\View\View
     */
    public function getStats(): View
    {
        $total_tokens = PersonalAccessToken::count();
        $total_requests = ApiRequest::count();
        $stats = [
            'total_tokens' => PersonalAccessToken::count(),
            'active_tokens' => PersonalAccessToken::where('created_at', '>=', now()->subDays(User::TOKEN_EXPIRY_DAYS))->count(),
            'expired_tokens' => PersonalAccessToken::where('created_at', '<', now()->subDays(User::TOKEN_EXPIRY_DAYS))->count(),
            'total_requests' => ApiRequest::count(),
            'successful_requests' => ApiRequest::where('status', '>=', 200)->where('status', '<', 300)->count(),
            'failed_requests' => ApiRequest::where(function ($query) {
                $query->where('status', '<', 200)->orWhere('status', '>=', 300);
            })->count(),
            'users_with_tokens' => DB::table('personal_access_tokens')
                ->select('tokenable_id')
                ->where('tokenable_type', User::class)
                ->distinct()
                ->count('tokenable_id'),
            'avg_requests_per_token' => $total_tokens > 0 ? round($total_requests / $total_tokens, 2) : 0,
        ];

        return view('admin.api-tokens.stats', ['stats' => $stats]);
    }
}