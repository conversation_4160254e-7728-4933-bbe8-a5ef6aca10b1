<?php

use App\Models\Plan;
use App\Models\User;
use App\Models\Order;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('plan can be created', function () {
    $plan = Plan::factory()->create([
        'name' => 'Premium Plan',
        'slug' => 'premium-plan',
        'price' => 99.99,
        'request_limit' => 1000,
        'description' => 'This is a premium plan',
        'is_active' => true,
        'features' => ['feature1' => 'API Access', 'feature2' => 'Priority Support']
    ]);

    expect($plan)->toBeInstanceOf(Plan::class)
        ->and($plan->name)->toBe('Premium Plan')
        ->and($plan->slug)->toBe('premium-plan')
        ->and((float) $plan->price)->toBe(99.99)
        ->and($plan->request_limit)->toBe(1000)
        ->and($plan->description)->toBe('This is a premium plan')
        ->and($plan->is_active)->toBeTrue()
        ->and($plan->features)->toBeArray()
        ->and($plan->features)->toHaveKey('feature1', 'API Access')
        ->and($plan->features)->toHaveKey('feature2', 'Priority Support');
});

test('plan has required attributes', function () {
    $plan = Plan::factory()->create();

    expect($plan)->toHaveKeys([
        'name',
        'slug',
        'price',
        'request_limit',
        'description',
        'is_active',
        'features'
    ]);
});

test('plan price is cast to decimal', function () {
    $plan = Plan::factory()->create(['price' => 99.99]);

    expect((float) $plan->price)->toBe(99.99);
    expect($plan->price)->toBeNumeric();
});

test('plan request limit is cast to integer', function () {
    $plan = Plan::factory()->create(['request_limit' => '100']);

    expect($plan->request_limit)->toBe(100);
    expect($plan->request_limit)->toBeInt();
});

test('plan is_active is cast to boolean', function () {
    $plan = Plan::factory()->create(['is_active' => 1]);

    expect($plan->is_active)->toBeTrue();
    expect($plan->is_active)->toBeBool();

    $plan = Plan::factory()->create(['is_active' => 0]);

    expect($plan->is_active)->toBeFalse();
    expect($plan->is_active)->toBeBool();
});

test('plan features is cast to array', function () {
    $features = [
        'feature1' => 'API Access',
        'feature2' => 'Priority Support',
        'feature3' => 'Unlimited Downloads'
    ];

    $plan = Plan::factory()->create(['features' => $features]);

    expect($plan->features)->toBeArray();
    expect($plan->features)->toBe($features);
});

test('plan has orders relationship', function () {
    $plan = Plan::factory()->create();
    
    // Create orders for the plan
    $orders = Order::factory()->count(3)->create([
        'plan_id' => $plan->id
    ]);

    expect($plan->orders)->toHaveCount(3);
    
    $plan->orders->each(function ($order) {
        expect($order)->toBeInstanceOf(Order::class);
    });
});

test('plan has users relationship', function () {
    // Skip this test since User model doesn't have a direct plan_id column
    // The relationship is managed through orders
    $this->markTestSkipped('User model does not have a direct plan_id column');
});

test('active scope returns only active plans', function () {
    // Create active plans
    Plan::factory()->count(3)->create(['is_active' => true]);
    
    // Create inactive plans
    Plan::factory()->count(2)->create(['is_active' => false]);
    
    $activePlans = Plan::active()->get();
    
    expect($activePlans)->toHaveCount(3);
    
    $activePlans->each(function ($plan) {
        expect($plan->is_active)->toBeTrue();
    });
});

test('isActive method returns correct boolean value', function () {
    $activePlan = Plan::factory()->create(['is_active' => true]);
    $inactivePlan = Plan::factory()->create(['is_active' => false]);
    
    expect($activePlan->isActive())->toBeTrue();
    expect($inactivePlan->isActive())->toBeFalse();
});

test('plan can be updated', function () {
    $plan = Plan::factory()->create();
    
    $plan->update([
        'name' => 'Updated Plan',
        'price' => 199.99,
        'features' => ['new_feature' => 'New Feature']
    ]);
    
    $plan->refresh();
    
    expect($plan->name)->toBe('Updated Plan');
    expect((float) $plan->price)->toBe(199.99);
    expect($plan->features)->toHaveKey('new_feature', 'New Feature');
});

test('plan can be soft deleted and restored', function () {
    // Skip this test if the Plan model doesn't use SoftDeletes
    if (!in_array('Illuminate\Database\Eloquent\SoftDeletes', class_uses_recursive(Plan::class))) {
        $this->markTestSkipped('Plan model does not use soft deletes');
    }
    
    $plan = Plan::factory()->create();
    $planId = $plan->id;
    
    $plan->delete();
    
    expect(Plan::find($planId))->toBeNull();
    expect(Plan::withTrashed()->find($planId))->not->toBeNull();
    
    $plan->restore();
    
    expect(Plan::find($planId))->not->toBeNull();
});