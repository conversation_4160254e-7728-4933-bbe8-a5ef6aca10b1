<?php

namespace App\Http\Controllers;

use App\Models\State;
use App\Models\Tool;
use App\Models\ToolReview;
use Illuminate\Http\Request;

class ToolsController extends Controller
{
    public function index()
    {
        $path = 'tools';
        $breadcrumbController = new BreadcrumbController();
        $breadcrumbs = $breadcrumbController->generateBreadcrumb($path);

        $pageTitle = "Useful Pincode (Postal Code) Tools";
        $metaDescription = "Useful Pincode Tools, Pincode Tools, Pincode Address Search Tool";
        $metaKeywords = "Useful Pincode (Postal Code) Tools";

        $imgPath = "assets/images/tools/tools.webp";
        setSEO($pageTitle, $metaDescription, $metaKeywords, $imgPath);

        $tools = Tool::published()->get();
        $emptyMessage = 'No tools found.';

        return view("tools.tools", compact('tools', 'imgPath', 'breadcrumbs', 'metaDescription', 'pageTitle', 'emptyMessage'));
    }

    public function showTool($slug)
    {
        $m_states = State::get();

        $tool = Tool::published()->where('slug', $slug)->firstOrFail();
        
        // Increment the view count
        $tool->incrementViewCount();

        // Load approved reviews for the tool (limit to 10 for display)
        $reviews = ToolReview::where('tool_id', $tool->id)
            ->where('is_approved', true)
            ->latest()
            ->limit(10)
            ->get();

        // Get total count of approved reviews
        $totalReviewsCount = ToolReview::where('tool_id', $tool->id)
            ->where('is_approved', true)
            ->count();

        // Check if there are more reviews than displayed
        $hasMoreReviews = $totalReviewsCount > 10;

        // Add reviews count to tool object for JSON-LD
        $tool->reviews_count = $totalReviewsCount;

        $path = "tools/" . $slug;
        $breadcrumbController = new BreadcrumbController();
        $breadcrumbs = $breadcrumbController->generateBreadcrumb($path);

        $pageTitle = $tool->meta_title ?? $tool->name;
        $metaDescription = $tool->meta_description;
        // $metaKeywords = $tool->meta_keywords; // removed, not in migration/model

        $imgPath = "assets/images/tools/" . ($tool->thumbnail ?? 'default.webp');
        setSEO($pageTitle, $metaDescription, null, $imgPath);

        return view('tools.'.$slug, compact('breadcrumbs', 'metaDescription', 'pageTitle', 'm_states', 'tool', 'reviews', 'hasMoreReviews', 'totalReviewsCount'));
    }
}