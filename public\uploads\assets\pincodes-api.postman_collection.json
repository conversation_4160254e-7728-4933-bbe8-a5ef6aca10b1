{"info": {"name": "Pincodes API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Public Routes", "item": [{"name": "Get Card Data", "request": {"method": "GET", "url": "{{base_url}}/api/getCardData"}}, {"name": "Get 5 Posts", "request": {"method": "GET", "url": "{{base_url}}/api/get5Posts"}}, {"name": "Get General Setting", "request": {"method": "GET", "url": "{{base_url}}/api/general-setting"}}, {"name": "Get Countries", "request": {"method": "GET", "url": "{{base_url}}/api/get-countries"}}]}, {"name": "Protected Routes", "item": [{"name": "<PERSON>", "request": {"method": "GET", "url": "{{base_url}}/api/pincode/400001", "header": {"Authorization": "Bearer {{token}}"}}}, {"name": "Get State Names", "request": {"method": "GET", "url": "{{base_url}}/api/pincodes/state/Maharashtra", "header": {"Authorization": "Bearer {{token}}"}}}, {"name": "Get District Names", "request": {"method": "GET", "url": "{{base_url}}/api/pincodes/district/27/Mumbai", "header": {"Authorization": "Bearer {{token}}"}}}, {"name": "Calculate Distance Between Coordinates", "request": {"method": "GET", "url": "{{base_url}}/api/calculate-distance-between-two-coordinates/19.0760/72.8777/18.5204/73.8567", "header": {"Authorization": "Bearer {{token}}"}}}, {"name": "Get Nearest Location", "request": {"method": "GET", "url": "{{base_url}}/api/get-nearest-location/19.0760/72.8777", "header": {"Authorization": "Bearer {{token}}"}}}, {"name": "Validate Pincode (GET)", "request": {"method": "GET", "url": "{{base_url}}/api/validate-pincode/400001", "header": {"Authorization": "Bearer {{token}}"}}}, {"name": "Calculate Distance Between Pincodes", "request": {"method": "GET", "url": "{{base_url}}/api/calculate-distance-between-two-pincodes/400001/411001", "header": {"Authorization": "Bearer {{token}}"}}}, {"name": "Validate Pincode (POST)", "request": {"method": "POST", "url": "{{base_url}}/api/validate-pincode", "header": {"Authorization": "Bearer {{token}}", "Content-Type": "application/json"}, "body": {"mode": "raw", "raw": "{\"pincode\": \"400001\"}"}}}, {"name": "Calculate Distance (POST)", "request": {"method": "POST", "url": "{{base_url}}/api/calculate-distance", "header": {"Authorization": "Bearer {{token}}", "Content-Type": "application/json"}, "body": {"mode": "raw", "raw": "{\"pincode1\": \"400001\", \"pincode2\": \"411001\"}"}}}, {"name": "Get Tehsil Names", "request": {"method": "GET", "url": "{{base_url}}/api/pincodes/tehsil/Maharashtra/Mumbai/Andheri", "header": {"Authorization": "Bearer {{token}}"}}}, {"name": "Get Pincodes By Post Name", "request": {"method": "GET", "url": "{{base_url}}/api/pincodes/post/Maharashtra/Mumbai/Andheri", "header": {"Authorization": "Bearer {{token}}"}}}, {"name": "Get Pincode Details", "request": {"method": "GET", "url": "{{base_url}}/api/pincodes/details/Maharashtra/Mumbai/Andheri", "header": {"Authorization": "Bearer {{token}}"}}}]}], "variable": [{"key": "base_url", "value": "http://pincodes-new.test"}, {"key": "token", "value": "2|zgyHHfd5Z5IsUBHgrcEuUdbc46xvPcoCs0fEtDDa3e2ce227"}]}