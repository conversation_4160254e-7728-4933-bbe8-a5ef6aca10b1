@extends('admin.layouts.admin')

@section('content')
<div class="container px-4 py-6 mx-auto">
    <div class="mb-6">
        <div class="bg-white dark:bg-bg-dark rounded-lg shadow-md border border-border-light dark:border-border-dark">
            <div class="p-4 border-b border-border-light dark:border-border-dark">
                <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">Edit District</h3>
            </div>
            <div class="p-4">
                <!-- Note about URL slugs -->
                <div class="p-4 mb-6 text-sm text-blue-700 dark:text-blue-200 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    <p><strong>Note:</strong> The slug (URL) is based on the State Name, District Name, and Post Office Name. In the existing database, all entries are stored in lowercase.</p>
                    <p>Please remember that URLs with lowercase and uppercase letters can be treated differently on some systems. Therefore, always ensure that the above-mentioned values are kept in lowercase to maintain consistency and avoid SEO issues.</p>
                </div>

                <form action="{{ route('admin.districts.update', $district) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="mb-4">
                        <label for="name" class="block mb-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">District Name</label>
                        <input type="text" id="name" name="name" value="{{ old('name', $district->name) }}" 
                            class="w-full px-3 py-2 border border-border-light dark:border-border-dark rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark @error('name') border-red-500 @enderror" 
                            required>
                        @error('name')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div class="mb-4">
                        <label for="state_id" class="block mb-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">State</label>
                        <select id="state_id" name="state_id" 
                            class="w-full px-3 py-2 border border-border-light dark:border-border-dark rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark @error('state_id') border-red-500 @enderror" 
                            required>
                            <option value="">Select State</option>
                            @foreach($states as $state)
                                <option value="{{ $state->id }}" {{ old('state_id', $district->state_id) == $state->id ? 'selected' : '' }}>
                                    {{ $state->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('state_id')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div class="mb-6">
                        <label for="featured_image" class="block mb-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Featured Image</label>
                        @if($district->featured_image_url !== asset('images/default-district.jpg'))
                            <div class="mb-3">
                                <img src="{{ $district->featured_image_url }}" alt="{{ $district->name }}" class="max-h-48 rounded border border-border-light dark:border-border-dark">
                            </div>
                        @endif
                        <input type="file" id="featured_image" name="featured_image" 
                            class="w-full px-3 py-2 text-sm text-text-secondary-light dark:text-text-secondary-dark border border-border-light dark:border-border-dark rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark @error('featured_image') border-red-500 @enderror">
                        @error('featured_image')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-text-secondary-light dark:text-text-secondary-dark">Recommended size: 800x600 pixels. Max file size: 2MB</p>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-primary-light dark:bg-primary-dark rounded-md hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                            Update District
                        </button>
                        <a href="{{ route('admin.districts.index') }}" class="px-4 py-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark bg-gray-200 dark:bg-bg-dark rounded-md hover:bg-gray-300 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-border-light dark:focus:ring-border-dark">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection