<?php

use Illuminate\Http\Request;

define('LARAVEL_START', microtime(true));

// Determine if the application is in maintenance mode...
if (file_exists($maintenance = __DIR__.'/../storage/framework/maintenance.php')) {
    require $maintenance;
}

// Register the Composer autoloader...
require __DIR__.'/../vendor/autoload.php';

// Bootstrap <PERSON> and handle the request...
(require_once __DIR__.'/../bootstrap/app.php')
    ->handleRequest(Request::capture());

// Fallback for API 500 errors
// set_exception_handler(function ($e) {
//     $requestUri = $_SERVER['REQUEST_URI'] ?? '';
//     if (str_starts_with($requestUri, '/api/')) {
//         http_response_code(500);
//         header('Content-Type: application/json');
//         echo json_encode([
//             'status' => 500,
//             'message' => 'Internal server error',
//             'error' => 'Internal server error',
//         ]);
//         exit;
//     }
//     // Default handler
//     http_response_code(500);
//     echo 'Server Error';
//     exit;
// });
