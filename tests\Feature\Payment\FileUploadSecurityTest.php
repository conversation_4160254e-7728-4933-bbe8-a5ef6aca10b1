<?php

namespace Tests\Feature\Payment;

use App\Models\User;
use App\Models\Order;
use App\Models\Payment;
use App\Services\Payment\FileUploadSecurityService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class FileUploadSecurityTest extends TestCase
{
    use RefreshDatabase;

    protected FileUploadSecurityService $fileSecurityService;
    protected User $user;
    protected Order $order;
    protected Payment $payment;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->fileSecurityService = app(FileUploadSecurityService::class);
        
        // Create test user and order
        $this->user = User::factory()->create();
        $this->order = Order::factory()->create(['user_id' => $this->user->id]);
        $this->payment = Payment::factory()->create(['order_id' => $this->order->id]);
        
        // Ensure private disk is configured for testing
        Storage::fake('private');
    }

    public function test_validates_allowed_file_types()
    {
        // Test valid image file
        $validFile = UploadedFile::fake()->image('payment_proof.jpg', 800, 600);
        $result = $this->fileSecurityService->validateUploadedFile($validFile, $this->user->id);
        
        $this->assertTrue($result['valid']);
        $this->assertArrayHasKey('file_info', $result);
    }

    public function test_rejects_invalid_file_types()
    {
        // Test invalid file type
        $invalidFile = UploadedFile::fake()->create('malicious.exe', 1024);
        $result = $this->fileSecurityService->validateUploadedFile($invalidFile, $this->user->id);
        
        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('File type not allowed', $result['error']);
    }

    public function test_rejects_oversized_files()
    {
        // Create a file larger than 5MB
        $oversizedFile = UploadedFile::fake()->create('large_file.pdf', 6 * 1024); // 6MB
        $result = $this->fileSecurityService->validateUploadedFile($oversizedFile, $this->user->id);
        
        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('File size exceeds maximum', $result['error']);
    }

    public function test_detects_suspicious_filenames()
    {
        // Test file with suspicious double extension
        $suspiciousFile = UploadedFile::fake()->create('image.php.jpg', 1024);
        $result = $this->fileSecurityService->validateUploadedFile($suspiciousFile, $this->user->id);
        
        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Suspicious', $result['error']);
    }

    public function test_enforces_rate_limiting()
    {
        $file = UploadedFile::fake()->image('test.jpg');
        
        // Upload files up to the limit
        for ($i = 0; $i < 10; $i++) {
            $result = $this->fileSecurityService->validateUploadedFile($file, $this->user->id);
            $this->assertTrue($result['valid']);
        }
        
        // Next upload should be rate limited
        $result = $this->fileSecurityService->validateUploadedFile($file, $this->user->id);
        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('rate limit', $result['error']);
    }

    public function test_securely_stores_files()
    {
        $file = UploadedFile::fake()->image('payment_proof.jpg', 800, 600);
        
        $result = $this->fileSecurityService->securelyStoreFile($file, $this->user->id, $this->payment->id);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('file_path', $result);
        $this->assertArrayHasKey('secure_filename', $result);
        
        // Verify file was stored in correct location
        $this->assertTrue(Storage::disk('private')->exists($result['file_path']));
        
        // Verify filename is secure (contains payment ID and random string)
        $this->assertStringContainsString("payment_proof_{$this->payment->id}", $result['secure_filename']);
    }

    public function test_validates_file_path_security()
    {
        // Test path traversal attempt
        $maliciousPath = '../../../etc/passwd';
        $result = $this->fileSecurityService->securelyRetrieveFile($maliciousPath, $this->user->id);
        
        $this->assertFalse($result['success']);
        $this->assertStringContainsString('Invalid file path', $result['error']);
    }

    public function test_validates_user_file_access()
    {
        // Create another user
        $otherUser = User::factory()->create();
        
        // Store file for first user
        $file = UploadedFile::fake()->image('test.jpg');
        $storeResult = $this->fileSecurityService->securelyStoreFile($file, $this->user->id, $this->payment->id);
        
        // Try to access with different user
        $accessResult = $this->fileSecurityService->securelyRetrieveFile($storeResult['file_path'], $otherUser->id);
        
        $this->assertFalse($accessResult['success']);
        $this->assertStringContainsString('access denied', $accessResult['error']);
    }

    public function test_validates_image_content()
    {
        // Create a fake image file with invalid content
        $fakeImage = UploadedFile::fake()->create('fake.jpg', 1024, 'image/jpeg');
        
        // This should fail content validation
        $result = $this->fileSecurityService->validateUploadedFile($fakeImage, $this->user->id);
        
        // Note: This test might pass if the fake file happens to have valid headers
        // In a real scenario, you'd test with actual malformed files
        $this->assertIsArray($result);
        $this->assertArrayHasKey('valid', $result);
    }

    public function test_cleanup_old_files()
    {
        // Store some test files
        $file = UploadedFile::fake()->image('old_file.jpg');
        $this->fileSecurityService->securelyStoreFile($file, $this->user->id, $this->payment->id);
        
        // Run cleanup (should not delete recent files)
        $deletedCount = $this->fileSecurityService->cleanupOldFiles(1); // 1 day old
        
        $this->assertIsInt($deletedCount);
        $this->assertGreaterThanOrEqual(0, $deletedCount);
    }

    public function test_get_upload_statistics()
    {
        // Store a test file
        $file = UploadedFile::fake()->image('test.jpg', 800, 600);
        $this->fileSecurityService->securelyStoreFile($file, $this->user->id, $this->payment->id);
        
        $stats = $this->fileSecurityService->getUploadStatistics();
        
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total_files', $stats);
        $this->assertArrayHasKey('total_size', $stats);
        $this->assertArrayHasKey('file_types', $stats);
    }

    protected function tearDown(): void
    {
        // Clean up any test files
        Storage::fake('private');
        parent::tearDown();
    }
}