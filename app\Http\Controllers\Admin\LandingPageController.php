<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\LandingPageSection;
use App\Models\LandingPageContent;
use App\Services\LandingPageService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class LandingPageController extends Controller
{
    protected $landingPageService;

    public function __construct(LandingPageService $landingPageService)
    {
        $this->landingPageService = $landingPageService;
    }

    /**
     * Display a listing of all landing page sections.
     *
     */
    public function index()
    {
        $sections = LandingPageSection::with('contents')
            ->orderBy('sort_order')
            ->get();

        return view('admin.landing-page.index', compact('sections'));
    }

    /**
     * Show the form for editing a specific section.
     *
     */
    public function edit(LandingPageSection $section)
    {
        $section->load('contents');
        return view('admin.landing-page.edit', compact('section'));
    }

    /**
     * Update the specified section and its contents.
     *
     */
    public function update(Request $request, LandingPageSection $section)
    {
        // Update section details
        // Update section details
        $section->update([
            'name' => $request->name,
            'is_active' => $request->has('is_active'),
            'sort_order' => $request->sort_order
        ]);
                // Ensure the content data is an array and has an ID
        // Process each content item
        if ($request->has('contents')) {
            foreach ($request->contents as $contentData) {
                // Ensure the content data is an array and has an ID
                if (!is_array($contentData) || !isset($contentData['id'])) {
                    continue;
                }
                
                $content = LandingPageContent::find($contentData['id']);
                if (!$content) {
                    continue;
                }

                $value = $contentData['value'] ?? null;
                
                // Handle different content types
                if ($content->type === 'image' && isset($contentData['image']) && $contentData['image']) {
                    // Store the new image
                    $imagePath = $contentData['image']->store('landing-page', 'public');
                    $value = $imagePath;
                } elseif ($content->type === 'repeater') {
                    // For repeaters, re-index the array to ensure it's not associative
                    $repeaterValue = is_array($value) ? array_values($value) : [];
                    $value = json_encode($repeaterValue);
                }
                
                $content->update([
                    'value' => $value
                ]);
            }
        }
        
        // Clear the cache
        $this->landingPageService->clearCache();

        return redirect()->route('admin.landing-page.index')
            ->with('success', 'Landing page section updated successfully.');
    }

    /**
     * Toggle the active status of a section.
     *
     */
    public function toggleActive(LandingPageSection $section)
    {
        $section->update([
            'is_active' => !$section->is_active
        ]);

        // Clear the cache
        $this->landingPageService->clearCache();

        return redirect()->route('admin.landing-page.index')
            ->with('success', 'Section status updated successfully.');
    }

    /**
     * Update the order of sections.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateOrder(Request $request)
    {
        $sections = $request->sections;

        foreach ($sections as $order => $sectionId) {
            LandingPageSection::where('id', $sectionId)
                ->update(['sort_order' => $order + 1]);
        }

        // Clear the cache
        $this->landingPageService->clearCache();

        return response()->json(['success' => true]);
    }

    /**
     * Alias for updateOrder to match the route definition.
     */
    public function reorder(Request $request)
    {
        return $this->updateOrder($request);
    }
}