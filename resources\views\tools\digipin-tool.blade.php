@extends('layouts.app')

@include('layouts.partials.tools-json-ld')

@section('content')
    <x-breadcrumb :segments="$breadcrumbs" :pageTitle="$pageTitle" :metaDescription="$metaDescription" />

    <div class="container max-w-6xl mx-auto px-4 py-8">
        <div class="flex flex-wrap -mx-4">
            <div class="w-full lg:w-2/3 px-4">
                <h2 class="text-3xl font-bold mb-6 text-text-primary-light dark:text-text-primary-dark">DIGIPIN Encoder &
                    Decoder</h2>
                <p class="mb-6 text-text-secondary-light dark:text-text-secondary-dark">
                    DIGIPIN (Digital PIN) is India Post's innovative system for encoding geographical coordinates into
                    a 10-character alphanumeric code. This tool allows you to convert latitude and longitude coordinates
                    to DIGIPIN and vice versa, making location sharing more convenient and secure.
                </p>
                <p class="mb-6 text-text-secondary-light dark:text-text-secondary-dark">
                    Perfect for logistics, delivery services, and location-based applications across India.
                </p>

                <!-- Tab Navigation -->
                <div x-data="{ activeTab: 'generate' }" class="mb-8">
                    <div class="border-b border-border-light dark:border-border-dark mb-6">
                        <nav class="-mb-px flex space-x-8">
                            <button @click="activeTab = 'generate'"
                                :class="activeTab === 'generate' ?
                                    'border-primary-light dark:border-primary-dark text-primary-light dark:text-primary-dark' :
                                    'border-transparent text-text-secondary-light dark:text-text-secondary-dark hover:text-text-primary-light dark:hover:text-text-primary-dark hover:border-border-light dark:hover:border-border-dark'"
                                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                                Generate DIGIPIN
                            </button>
                            <button @click="activeTab = 'decode'"
                                :class="activeTab === 'decode' ?
                                    'border-primary-light dark:border-primary-dark text-primary-light dark:text-primary-dark' :
                                    'border-transparent text-text-secondary-light dark:text-text-secondary-dark hover:text-text-primary-light dark:hover:text-text-primary-dark hover:border-border-light dark:hover:border-border-dark'"
                                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                                Decode DIGIPIN
                            </button>
                            <button @click="activeTab = 'info'"
                                :class="activeTab === 'info' ?
                                    'border-primary-light dark:border-primary-dark text-primary-light dark:text-primary-dark' :
                                    'border-transparent text-text-secondary-light dark:text-text-secondary-dark hover:text-text-primary-light dark:hover:text-text-primary-dark hover:border-border-light dark:hover:border-border-dark'"
                                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                                DIGIPIN Info
                            </button>
                        </nav>
                    </div>

                    <!-- Generate DIGIPIN Tab -->
                    <div x-show="activeTab === 'generate'" x-cloak>
                        <div x-data="digipinGenerator()" class="space-y-6">
                            <div
                                class="bg-white dark:bg-bg-dark shadow-md rounded-lg border border-border-light dark:border-border-dark">
                                <div
                                    class="bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark px-6 py-4 border-b border-border-light dark:border-border-dark">
                                    <h3 class="text-xl font-semibold">Generate DIGIPIN from Coordinates</h3>
                                </div>
                                <div class="p-6">
                                    <form @submit.prevent="generateDigipin" class="space-y-4">
                                        @csrf
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <label for="latitude"
                                                    class="block mb-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">
                                                    Latitude <span class="text-red-500 dark:text-red-400">*</span>
                                                </label>
                                                <input type="number" step="0.000001" id="latitude" x-model="latitude"
                                                    class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark"
                                                    :class="{ 'border-red-500 dark:border-red-400': latitudeError, 'border-green-500 dark:border-green-400': latitudeValid }"
                                                    placeholder="28.6139" required>
                                                <div x-show="latitudeError" x-text="latitudeErrorMessage"
                                                    class="mt-1 text-sm text-red-500 dark:text-red-400"></div>
                                                <p
                                                    class="mt-1 text-xs text-text-secondary-light dark:text-text-secondary-dark">
                                                    Range: 2.5° to 38.5° (India)</p>
                                            </div>
                                            <div>
                                                <label for="longitude"
                                                    class="block mb-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">
                                                    Longitude <span class="text-red-500 dark:text-red-400">*</span>
                                                </label>
                                                <input type="number" step="0.000001" id="longitude" x-model="longitude"
                                                    class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark"
                                                    :class="{ 'border-red-500 dark:border-red-400': longitudeError, 'border-green-500 dark:border-green-400': longitudeValid }"
                                                    placeholder="77.2090" required>
                                                <div x-show="longitudeError" x-text="longitudeErrorMessage"
                                                    class="mt-1 text-sm text-red-500 dark:text-red-400"></div>
                                                <p
                                                    class="mt-1 text-xs text-text-secondary-light dark:text-text-secondary-dark">
                                                    Range: 63.5° to 99.5° (India)</p>
                                            </div>
                                        </div>
                                        <div
                                            class="flex flex-col md:flex-row md:items-center md:space-x-4 space-y-3 md:space-y-0">
                                            <button type="button" @click="getMyLocation" :disabled="isLocating"
                                                class="inline-flex items-center px-4 py-2 bg-accent-light dark:bg-accent-dark hover:bg-accent-dark dark:hover:bg-accent-light disabled:bg-gray-400 dark:disabled:bg-gray-600 text-white font-medium rounded-md transition duration-150 ease-in-out">
                                                <svg x-show="isLocating" class="animate-spin h-5 w-5 mr-2" fill="none"
                                                    viewBox="0 0 24 24">
                                                    <circle class="opacity-25" cx="12" cy="12" r="10"
                                                        stroke="currentColor" stroke-width="4"></circle>
                                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z">
                                                    </path>
                                                </svg>
                                                <svg x-show="!isLocating" xmlns="http://www.w3.org/2000/svg"
                                                    class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd"
                                                        d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                                        clip-rule="evenodd" />
                                                </svg>
                                                <span
                                                    x-text="isLocating ? 'Getting Location...' : 'Get My Location'"></span>
                                            </button>
                                            <button type="submit" :disabled="isLoading"
                                                class="w-full md:w-auto bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light disabled:bg-gray-400 dark:disabled:bg-gray-600 text-white font-bold py-3 px-4 rounded-md transition duration-300 flex items-center justify-center">
                                                <span x-show="isLoading"
                                                    class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                                                <span x-text="isLoading ? 'Generating...' : 'Generate DIGIPIN'"></span>
                                            </button>
                                        </div>
                                        <span x-show="locationError" class="text-red-500 dark:text-red-400 text-sm"
                                            x-text="locationErrorMessage"></span>
                                    </form>

                                    <!-- Result -->
                                    <div x-show="showResult" x-cloak class="mt-6">
                                        <div
                                            class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-400/30 rounded-lg p-4">
                                            <h4 class="text-lg font-semibold text-green-800 dark:text-green-300 mb-3">
                                                Generated DIGIPIN</h4>
                                            <div
                                                class="bg-white dark:bg-bg-dark border border-green-300 dark:border-green-400/30 rounded p-3 mb-3">
                                                <div class="flex items-center justify-between">
                                                    <span
                                                        class="text-2xl font-mono font-bold text-green-700 dark:text-green-300"
                                                        x-text="result.digipin"></span>
                                                    <button @click="copyToClipboard(result.digipin)"
                                                        class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 transition-colors">
                                                        <svg class="w-5 h-5" fill="none" stroke="currentColor"
                                                            viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z">
                                                            </path>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                                <div>
                                                    <span
                                                        class="font-medium text-text-primary-light dark:text-text-primary-dark">Latitude:</span>
                                                    <span class="text-text-secondary-light dark:text-text-secondary-dark"
                                                        x-text="result.latitude"></span>
                                                </div>
                                                <div>
                                                    <span
                                                        class="font-medium text-text-primary-light dark:text-text-primary-dark">Longitude:</span>
                                                    <span class="text-text-secondary-light dark:text-text-secondary-dark"
                                                        x-text="result.longitude"></span>
                                                </div>
                                            </div>
                                            <!-- Google Maps iframe -->
                                            <div class="mt-4 w-full h-64">
                                                <iframe
                                                    :src="'https://maps.google.com/maps?q=' + result.latitude + ',' + result
                                                        .longitude + '&hl=es;z=14&output=embed'"
                                                    allowfullscreen="" loading="lazy"
                                                    class="w-full h-full rounded-lg border border-border-light dark:border-border-dark"></iframe>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Error -->
                                    <div x-show="showError" x-cloak class="mt-6">
                                        <div
                                            class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-400/30 rounded-lg p-4">
                                            <h4 class="text-lg font-semibold text-red-800 dark:text-red-300 mb-2">Error
                                            </h4>
                                            <p class="text-red-700 dark:text-red-300" x-text="errorMessage"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Decode DIGIPIN Tab -->
                    <div x-show="activeTab === 'decode'" x-cloak>
                        <div x-data="digipinDecoder()" class="space-y-6">
                            <div
                                class="bg-white dark:bg-bg-dark shadow-md rounded-lg border border-border-light dark:border-border-dark">
                                <div
                                    class="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-6 py-4 border-b border-border-light dark:border-border-dark">
                                    <h3 class="text-xl font-semibold">Decode DIGIPIN to Coordinates</h3>
                                </div>
                                <div class="p-6">
                                    <form @submit.prevent="decodeDigipin" class="space-y-4">
                                        @csrf
                                        <div>
                                            <label for="digipin"
                                                class="block mb-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">
                                                DIGIPIN <span class="text-red-500 dark:text-red-400">*</span>
                                            </label>
                                            <input type="text" id="digipin" x-model="digipin"
                                                class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark font-mono"
                                                :class="{ 'border-red-500 dark:border-red-400': digipinError, 'border-green-500 dark:border-green-400': digipinValid }"
                                                placeholder="39J-438-TJC7" required>
                                            <div x-show="digipinError" x-text="digipinErrorMessage"
                                                class="mt-1 text-sm text-red-500 dark:text-red-400"></div>
                                            <p
                                                class="mt-1 text-xs text-text-secondary-light dark:text-text-secondary-dark">
                                                Format: 10 characters with optional hyphens (e.g., 39J-438-TJC7)</p>
                                        </div>
                                        <button type="submit" :disabled="isLoading"
                                            class="w-full bg-green-500 dark:bg-green-600 hover:bg-green-600 dark:hover:bg-green-500 disabled:bg-gray-400 dark:disabled:bg-gray-600 text-white font-bold py-3 px-4 rounded-md transition duration-300 flex items-center justify-center">
                                            <span x-show="isLoading"
                                                class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                                            <span x-text="isLoading ? 'Decoding...' : 'Decode DIGIPIN'"></span>
                                        </button>
                                    </form>

                                    <!-- Result -->
                                    <div x-show="showResult" x-cloak class="mt-6">
                                        <div
                                            class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-400/30 rounded-lg p-4">
                                            <h4 class="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-3">Decoded
                                                Coordinates</h4>
                                            <div
                                                class="bg-white dark:bg-bg-dark border border-blue-300 dark:border-blue-400/30 rounded p-3 mb-3">
                                                <div class="flex items-center justify-between">
                                                    <span class="text-lg font-mono text-blue-700 dark:text-blue-300"
                                                        x-text="result.digipin"></span>
                                                    <button @click="copyToClipboard(result.digipin)"
                                                        class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors">
                                                        <svg class="w-5 h-5" fill="none" stroke="currentColor"
                                                            viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z">
                                                            </path>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                                <div>
                                                    <span
                                                        class="font-medium text-text-primary-light dark:text-text-primary-dark">Latitude:</span>
                                                    <span class="text-text-secondary-light dark:text-text-secondary-dark"
                                                        x-text="result.latitude"></span>
                                                </div>
                                                <div>
                                                    <span
                                                        class="font-medium text-text-primary-light dark:text-text-primary-dark">Longitude:</span>
                                                    <span class="text-text-secondary-light dark:text-text-secondary-dark"
                                                        x-text="result.longitude"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Error -->
                                    <div x-show="showError" x-cloak class="mt-6">
                                        <div
                                            class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-400/30 rounded-lg p-4">
                                            <h4 class="text-lg font-semibold text-red-800 dark:text-red-300 mb-2">Error
                                            </h4>
                                            <p class="text-red-700 dark:text-red-300" x-text="errorMessage"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- DIGIPIN Info Tab -->
                    <div x-show="activeTab === 'info'" x-cloak>
                        <div x-data="digipinInfo()" class="space-y-6">
                            <div
                                class="bg-white dark:bg-bg-dark shadow-md rounded-lg border border-border-light dark:border-border-dark">
                                <div
                                    class="bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 px-6 py-4 border-b border-border-light dark:border-border-dark">
                                    <h3 class="text-xl font-semibold">DIGIPIN Information</h3>
                                </div>
                                <div class="p-6">
                                    <button @click="loadInfo" :disabled="isLoading"
                                        class="w-full bg-purple-500 dark:bg-purple-600 hover:bg-purple-600 dark:hover:bg-purple-500 disabled:bg-gray-400 dark:disabled:bg-gray-600 text-white font-bold py-3 px-4 rounded-md transition duration-300 flex items-center justify-center">
                                        <span x-show="isLoading"
                                            class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                                        <span x-text="isLoading ? 'Loading...' : 'Load DIGIPIN Information'"></span>
                                    </button>

                                    <!-- Info Result -->
                                    <div x-show="showResult" x-cloak class="mt-6 space-y-4">
                                        <div
                                            class="bg-bg-light dark:bg-bg-dark rounded-lg p-4 border border-border-light dark:border-border-dark">
                                            <h4
                                                class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">
                                                Description</h4>
                                            <p class="text-text-secondary-light dark:text-text-secondary-dark"
                                                x-text="info.description"></p>
                                        </div>

                                        <div
                                            class="bg-bg-light dark:bg-bg-dark rounded-lg p-4 border border-border-light dark:border-border-dark">
                                            <h4
                                                class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">
                                                Geographic Bounds</h4>
                                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                                <div>
                                                    <span
                                                        class="font-medium text-text-primary-light dark:text-text-primary-dark">Min
                                                        Latitude:</span>
                                                    <span class="text-text-secondary-light dark:text-text-secondary-dark"
                                                        x-text="info.bounds?.minLat"></span>
                                                </div>
                                                <div>
                                                    <span
                                                        class="font-medium text-text-primary-light dark:text-text-primary-dark">Max
                                                        Latitude:</span>
                                                    <span class="text-text-secondary-light dark:text-text-secondary-dark"
                                                        x-text="info.bounds?.maxLat"></span>
                                                </div>
                                                <div>
                                                    <span
                                                        class="font-medium text-text-primary-light dark:text-text-primary-dark">Min
                                                        Longitude:</span>
                                                    <span class="text-text-secondary-light dark:text-text-secondary-dark"
                                                        x-text="info.bounds?.minLon"></span>
                                                </div>
                                                <div>
                                                    <span
                                                        class="font-medium text-text-primary-light dark:text-text-primary-dark">Max
                                                        Longitude:</span>
                                                    <span class="text-text-secondary-light dark:text-text-secondary-dark"
                                                        x-text="info.bounds?.maxLon"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div
                                            class="bg-bg-light dark:bg-bg-dark rounded-lg p-4 border border-border-light dark:border-border-dark">
                                            <h4
                                                class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">
                                                DIGIPIN Grid</h4>
                                            <div class="grid grid-cols-4 gap-2 max-w-xs">
                                                <template x-for="(row, rowIndex) in info.grid" :key="rowIndex">
                                                    <template x-for="(cell, colIndex) in row" :key="colIndex">
                                                        <div class="bg-white dark:bg-bg-dark border border-border-light dark:border-border-dark rounded p-2 text-center font-mono font-bold text-text-primary-light dark:text-text-primary-dark"
                                                            x-text="cell"></div>
                                                    </template>
                                                </template>
                                            </div>
                                        </div>

                                        <div
                                            class="bg-bg-light dark:bg-bg-dark rounded-lg p-4 border border-border-light dark:border-border-dark">
                                            <h4
                                                class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">
                                                Valid Characters</h4>
                                            <div class="flex flex-wrap gap-2">
                                                <template x-for="char in info.validCharacters" :key="char">
                                                    <span
                                                        class="bg-white dark:bg-bg-dark border border-border-light dark:border-border-dark rounded px-2 py-1 text-sm font-mono font-bold text-text-primary-light dark:text-text-primary-dark"
                                                        x-text="char"></span>
                                                </template>
                                            </div>
                                        </div>

                                        <div
                                            class="bg-bg-light dark:bg-bg-dark rounded-lg p-4 border border-border-light dark:border-border-dark">
                                            <h4
                                                class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">
                                                API Endpoints</h4>
                                            <div class="space-y-2 text-sm">
                                                <template x-for="(url, name) in info.endpoints" :key="name">
                                                    <div class="flex items-center justify-between">
                                                        <span
                                                            class="font-medium text-text-primary-light dark:text-text-primary-dark"
                                                            x-text="name + ':'"></span>
                                                        <code
                                                            class="bg-white dark:bg-bg-dark border border-border-light dark:border-border-dark rounded px-2 py-1 font-mono text-text-primary-light dark:text-text-primary-dark"
                                                            x-text="url"></code>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Error -->
                                    <div x-show="showError" x-cloak class="mt-6">
                                        <div
                                            class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-400/30 rounded-lg p-4">
                                            <h4 class="text-lg font-semibold text-red-800 dark:text-red-300 mb-2">Error
                                            </h4>
                                            <p class="text-red-700 dark:text-red-300" x-text="errorMessage"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- How it works section -->
                <div class="space-y-6">
                    <h2 class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark">How DIGIPIN Works
                    </h2>
                    <p class="text-text-secondary-light dark:text-text-secondary-dark">
                        DIGIPIN (Digital PIN) is India Post's innovative system that converts geographical coordinates
                        into a compact 10-character alphanumeric code. This system makes location sharing more convenient
                        and secure across India.
                    </p>

                    <div
                        class="bg-bg-light dark:bg-bg-dark p-6 rounded-lg border border-border-light dark:border-border-dark mb-6">
                        <h3 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark mb-3">Encoding
                            Process:</h3>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark">
                            The system uses a 4x4 grid of characters to encode coordinates. Each character represents
                            a specific quadrant of the current geographical area. The process repeats 10 times,
                            progressively narrowing down the location with each iteration.
                        </p>
                    </div>

                    <div
                        class="bg-bg-light dark:bg-bg-dark p-6 rounded-lg border border-border-light dark:border-border-dark mb-6">
                        <h3 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark mb-3">
                            Geographic Coverage:</h3>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark">
                            DIGIPIN covers the entire geographical area of India, with latitude ranging from 2.5° to 38.5°
                            and longitude from 63.5° to 99.5°. This ensures comprehensive coverage of all Indian locations.
                        </p>
                    </div>

                    <div
                        class="bg-bg-light dark:bg-bg-dark p-6 rounded-lg border border-border-light dark:border-border-dark mb-6">
                        <h3 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark mb-3">
                            Applications:</h3>
                        <ul class="list-disc pl-5 space-y-2 text-text-secondary-light dark:text-text-secondary-dark">
                            <li><strong>Logistics & Delivery:</strong> Quick location identification for delivery services
                            </li>
                            <li><strong>Emergency Services:</strong> Rapid location sharing in emergency situations</li>
                            <li><strong>Travel & Navigation:</strong> Easy location sharing for travel planning</li>
                            <li><strong>E-commerce:</strong> Simplified address management for online orders</li>
                            <li><strong>Field Operations:</strong> Efficient location tracking for field workers</li>
                        </ul>
                    </div>

                    <div
                        class="bg-bg-light dark:bg-bg-dark p-6 rounded-lg border border-border-light dark:border-border-dark">
                        <h3 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark mb-3">
                            Benefits:</h3>
                        <ul class="list-disc pl-5 space-y-2 text-text-secondary-light dark:text-text-secondary-dark">
                            <li><strong>Compact:</strong> 10 characters vs traditional coordinate formats</li>
                            <li><strong>Human-readable:</strong> Easy to read, write, and share</li>
                            <li><strong>Secure:</strong> Reduces exposure of exact coordinates</li>
                            <li><strong>Standardized:</strong> Consistent format across India</li>
                            <li><strong>Offline-capable:</strong> Works without internet connectivity</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="w-full lg:w-1/3 px-4">
                <div class="sticky top-20">
                    @include('pincodes.partials.sidebar')
                </div>
            </div>
        </div>

        <!-- Reviews Section -->
        <div class="mt-8">
            <x-tool-reviews
                :reviews="$reviews"
                :tool="$tool"
                :hasMoreReviews="$hasMoreReviews ?? false"
                :totalReviewsCount="$totalReviewsCount ?? 0"
            />
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function digipinGenerator() {
            return {
                latitude: '',
                longitude: '',
                latitudeValid: false,
                longitudeValid: false,
                latitudeError: false,
                longitudeError: false,
                latitudeErrorMessage: '',
                longitudeErrorMessage: '',
                isLoading: false,
                showResult: false,
                showError: false,
                result: {},
                errorMessage: '',
                isLocating: false,
                locationError: false,
                locationErrorMessage: '',

                getMyLocation() {
                    this.isLocating = true;
                    this.locationError = false;
                    this.locationErrorMessage = '';
                    if (!navigator.geolocation) {
                        this.locationError = true;
                        this.locationErrorMessage = 'Geolocation is not supported by your browser.';
                        this.isLocating = false;
                        return;
                    }
                    navigator.geolocation.getCurrentPosition(
                        (position) => {
                            this.latitude = position.coords.latitude.toFixed(6);
                            this.longitude = position.coords.longitude.toFixed(6);
                            this.isLocating = false;
                        },
                        (error) => {
                            this.locationError = true;
                            const errorMessages = {
                                1: 'Permission denied. Please allow location access.',
                                2: 'Location unavailable. Try again.',
                                3: 'Location request timed out. Try again.'
                            };
                            this.locationErrorMessage = errorMessages[error.code] || 'Unable to get your location.';
                            this.isLocating = false;
                        }, {
                            enableHighAccuracy: true,
                            timeout: 10000,
                            maximumAge: 0
                        }
                    );
                },

                validateLatitude() {
                    const lat = parseFloat(this.latitude);
                    if (isNaN(lat)) {
                        this.latitudeError = true;
                        this.latitudeValid = false;
                        this.latitudeErrorMessage = 'Please enter a valid latitude';
                        return false;
                    }
                    if (lat < 2.5 || lat > 38.5) {
                        this.latitudeError = true;
                        this.latitudeValid = false;
                        this.latitudeErrorMessage = 'Latitude must be between 2.5° and 38.5°';
                        return false;
                    }
                    this.latitudeError = false;
                    this.latitudeValid = true;
                    this.latitudeErrorMessage = '';
                    return true;
                },

                validateLongitude() {
                    const lon = parseFloat(this.longitude);
                    if (isNaN(lon)) {
                        this.longitudeError = true;
                        this.longitudeValid = false;
                        this.longitudeErrorMessage = 'Please enter a valid longitude';
                        return false;
                    }
                    if (lon < 63.5 || lon > 99.5) {
                        this.longitudeError = true;
                        this.longitudeValid = false;
                        this.longitudeErrorMessage = 'Longitude must be between 63.5° and 99.5°';
                        return false;
                    }
                    this.longitudeError = false;
                    this.longitudeValid = true;
                    this.longitudeErrorMessage = '';
                    return true;
                },

                async generateDigipin() {
                    if (!this.validateLatitude() || !this.validateLongitude()) {
                        return;
                    }

                    this.isLoading = true;
                    this.showResult = false;
                    this.showError = false;

                    try {
                        const response = await fetch('/api/digipin/generate', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                                    'content')
                            },
                            body: JSON.stringify({
                                latitude: parseFloat(this.latitude),
                                longitude: parseFloat(this.longitude)
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            this.result = data.data;
                            this.showResult = true;
                        } else {
                            this.errorMessage = data.message || 'Failed to generate DIGIPIN';
                            this.showError = true;
                        }
                    } catch (error) {
                        this.errorMessage = 'Network error. Please try again.';
                        this.showError = true;
                    } finally {
                        this.isLoading = false;
                    }
                },

                copyToClipboard(text) {
                    navigator.clipboard.writeText(text).then(() => {
                        // Show a brief success message
                        const button = event.target.closest('button');
                        const originalHTML = button.innerHTML;
                        button.innerHTML =
                            '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
                        setTimeout(() => {
                            button.innerHTML = originalHTML;
                        }, 1000);
                    });
                }
            }
        }

        function digipinDecoder() {
            return {
                digipin: '',
                digipinValid: false,
                digipinError: false,
                digipinErrorMessage: '',
                isLoading: false,
                showResult: false,
                showError: false,
                result: {},
                errorMessage: '',

                validateDigipin() {
                    const cleanPin = this.digipin.replace(/-/g, '');
                    if (cleanPin.length !== 10) {
                        this.digipinError = true;
                        this.digipinValid = false;
                        this.digipinErrorMessage = 'DIGIPIN must be exactly 10 characters (excluding hyphens)';
                        return false;
                    }

                    const validChars = ['F', 'C', '9', '8', 'J', '3', '2', '7', 'K', '4', '5', '6', 'L', 'M', 'P', 'T'];
                    for (let char of cleanPin) {
                        if (!validChars.includes(char.toUpperCase())) {
                            this.digipinError = true;
                            this.digipinValid = false;
                            this.digipinErrorMessage = 'DIGIPIN contains invalid characters';
                            return false;
                        }
                    }

                    this.digipinError = false;
                    this.digipinValid = true;
                    this.digipinErrorMessage = '';
                    return true;
                },

                async decodeDigipin() {
                    if (!this.validateDigipin()) {
                        return;
                    }

                    this.isLoading = true;
                    this.showResult = false;
                    this.showError = false;

                    try {
                        const response = await fetch('/api/digipin/decode', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                                    'content')
                            },
                            body: JSON.stringify({
                                digipin: this.digipin
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            this.result = data.data;
                            this.showResult = true;
                        } else {
                            this.errorMessage = data.message || 'Failed to decode DIGIPIN';
                            this.showError = true;
                        }
                    } catch (error) {
                        this.errorMessage = 'Network error. Please try again.';
                        this.showError = true;
                    } finally {
                        this.isLoading = false;
                    }
                },

                copyToClipboard(text) {
                    navigator.clipboard.writeText(text).then(() => {
                        const button = event.target.closest('button');
                        const originalHTML = button.innerHTML;
                        button.innerHTML =
                            '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
                        setTimeout(() => {
                            button.innerHTML = originalHTML;
                        }, 1000);
                    });
                }
            }
        }

        function digipinInfo() {
            return {
                isLoading: false,
                showResult: false,
                showError: false,
                info: {},
                errorMessage: '',

                async loadInfo() {
                    this.isLoading = true;
                    this.showResult = false;
                    this.showError = false;

                    try {
                        const response = await fetch('/api/digipin/info');
                        const data = await response.json();

                        if (data.success) {
                            this.info = data.data;
                            this.showResult = true;
                        } else {
                            this.errorMessage = data.message || 'Failed to load DIGIPIN information';
                            this.showError = true;
                        }
                    } catch (error) {
                        this.errorMessage = 'Network error. Please try again.';
                        this.showError = true;
                    } finally {
                        this.isLoading = false;
                    }
                }
            }
        }
    </script>
@endpush
