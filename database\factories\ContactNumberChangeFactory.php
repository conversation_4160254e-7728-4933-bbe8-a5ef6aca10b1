<?php

namespace Database\Factories;

use App\Models\ContactNumberChange;
use App\Models\PinCode;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ContactNumberChangeFactory extends Factory
{
    protected $model = ContactNumberChange::class;

    public function definition()
    {
        return [
            'pincode_id' => PinCode::factory(),
            'old_number' => $this->faker->numerify('##########'),
            'new_number' => $this->faker->numerify('##########'),
            'reason' => $this->faker->sentence,
            'status' => $this->faker->randomElement(['pending', 'approved', 'rejected']),
            'admin_notes' => $this->faker->optional()->sentence,
            'user_id' => User::factory()
        ];
    }

    /**
     * Indicate that the request is pending.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function pending()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'pending',
                'admin_notes' => null
            ];
        });
    }

    /**
     * Indicate that the request is approved.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function approved()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'approved',
                'admin_notes' => $this->faker->sentence
            ];
        });
    }

    /**
     * Indicate that the request is rejected.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function rejected()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'rejected',
                'admin_notes' => $this->faker->sentence
            ];
        });
    }
}