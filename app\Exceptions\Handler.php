<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Illuminate\Auth\Access\AuthorizationException;
use Throwable;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Symfony\Component\HttpKernel\Exception\HttpException;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        // Handle Authentication Exception
        $this->renderable(function (AuthenticationException $e, $request) {
            if ($request->is('api/*')) {
                return response()->json([
                    'message' => 'Unauthorized',
                    'error' => 'Please provide a valid API token.'
                ], 401);
            }
            return redirect()->guest(route('login'));
        });

        // Handle Validation Exception
        $this->renderable(function (ValidationException $e, $request) {
            if ($request->is('api/*')) {
                return response()->json([
                    'message' => 'Validation Error',
                    'errors' => $e->errors()
                ], 422);
            }
            return back()->withErrors($e->errors())->withInput();
        });

        // Handle Throttle Requests Exception
        $this->renderable(function (ThrottleRequestsException $e, $request) {
            if ($request->is('api/*')) {
                $retryAfter = $e->getHeaders()['Retry-After'] ?? 60;
                return response()->json([
                    'status' => 429,
                    'message' => 'Too Many Requests',
                    'error' => 'Too Many Requests',
                    'retry_after' => $retryAfter,
                ], 429);
            }
            return response()->view('errors.429', [], 429);
        });

        // Handle Server Error (catch-all for other exceptions)
        $this->renderable(function (Throwable $e, $request) {
            // Don't handle HTTP exceptions here as they're handled in render() method
            if ($e instanceof HttpException) {
                return null;
            }
            // Prevent duplicate logging: do not log here, controller already logs
            if ($request->is('api/*')) {
                return response()->json([
                    'message' => 'Server Error',
                    'error' => config('app.debug') ? $e->getMessage() : 'An unexpected error occurred.'
                ], 500);
            }
            return response()->view('errors.500', [], 500);
        });
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $e
     * @return \Symfony\Component\HttpFoundation\Response
     *
     * @throws \Throwable
     */
    public function render($request, Throwable $e)
    {
        // Handle 404 Not Found exceptions
        if ($e instanceof NotFoundHttpException || $e instanceof ModelNotFoundException) {
            if ($request->is('api/*')) {
                return response()->json([
                    'message' => 'Not Found',
                    'error' => 'The requested resource was not found.'
                ], 404);
            }
            return response()->view('errors.404', [], 404);
        }

        // Handle Authorization exceptions
        if ($e instanceof AuthorizationException) {
            if ($request->is('api/*')) {
                return response()->json([
                    'message' => 'Forbidden',
                    'error' => 'You do not have permission to access this resource.'
                ], 403);
            }
            return response()->view('errors.403', [], 403);
        }

        // Handle other HTTP exceptions
        if ($e instanceof HttpException) {
            $status = $e->getStatusCode();
            
            if ($request->is('api/*')) {
                return response()->json([
                    'status' => $status,
                    'message' => $e->getMessage() ?: 'HTTP error',
                    'error' => $e->getMessage() ?: 'HTTP error',
                ], $status);
            }
            
            // Try to return a custom error view if it exists
            if (view()->exists("errors.{$status}")) {
                return response()->view("errors.{$status}", [], $status);
            }
        }

        return parent::render($request, $e);
    }
} 