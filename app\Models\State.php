<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class State extends Model
{
    use HasFactory;
    
    protected $table = 'pin_states';
    protected $fillable = ['name', 'featured_image'];

    public function districts()
    {
        return $this->hasMany(District::class, 'state_id');
    }

    public function pincodes()
    {
        return $this->hasMany(PinCode::class, 'state', 'name');
    }

    // Get featured image URL or default image if not set
    public function getFeaturedImageUrlAttribute()
    {
        if ($this->featured_image) {
            return Storage::disk('public')->url($this->featured_image);
        }
        return asset('images/default-state.jpg');
    }
}
