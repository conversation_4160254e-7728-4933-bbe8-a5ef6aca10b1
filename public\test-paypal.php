<?php
// This is a standalone script to test PayPal integration
// Access it directly at: http://your-site.com/test-paypal.php

// Load the Laravel environment
require __DIR__.'/../vendor/autoload.php';
$app = require_once __DIR__.'/../bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// Get PayPal credentials from .env
$mode = env('PAYPAL_MODE', 'sandbox');
$clientId = env('PAYPAL_SANDBOX_CLIENT_ID', '');
$clientSecret = env('PAYPAL_SANDBOX_CLIENT_SECRET', '');

echo "<h1>PayPal Integration Test</h1>";
echo "<p>Mode: $mode</p>";
echo "<p>Client ID exists: " . (!empty($clientId) ? 'Yes' : 'No') . "</p>";
echo "<p>Client Secret exists: " . (!empty($clientSecret) ? 'Yes' : 'No') . "</p>";

try {
    // Manual PayPal API call without using the package
    $url = $mode === 'sandbox' 
        ? 'https://api-m.sandbox.paypal.com/v1/oauth2/token' 
        : 'https://api-m.paypal.com/v1/oauth2/token';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, "grant_type=client_credentials");
    curl_setopt($ch, CURLOPT_USERPWD, $clientId . ":" . $clientSecret);
    
    $headers = array();
    $headers[] = 'Accept: application/json';
    $headers[] = 'Accept-Language: en_US';
    $headers[] = 'Content-Type: application/x-www-form-urlencoded';
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    $result = curl_exec($ch);
    $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        echo '<p style="color:red">Error: ' . curl_error($ch) . '</p>';
    } else {
        echo "<p>HTTP Response Code: $httpcode</p>";
        echo "<pre>" . json_encode(json_decode($result), JSON_PRETTY_PRINT) . "</pre>";
        
        if ($httpcode == 200) {
            $tokenData = json_decode($result, true);
            $accessToken = $tokenData['access_token'] ?? '';
            
            if (!empty($accessToken)) {
                echo "<p style='color:green'>Successfully obtained access token!</p>";
                
                // Now try to create an order
                $orderUrl = $mode === 'sandbox' 
                    ? 'https://api-m.sandbox.paypal.com/v2/checkout/orders' 
                    : 'https://api-m.paypal.com/v2/checkout/orders';
                
                $orderData = [
                    'intent' => 'CAPTURE',
                    'purchase_units' => [
                        [
                            'amount' => [
                                'currency_code' => 'USD',
                                'value' => '5.00'
                            ],
                            'description' => 'Test Order'
                        ]
                    ],
                    'application_context' => [
                        'return_url' => 'http://example.com/success',
                        'cancel_url' => 'http://example.com/cancel'
                    ]
                ];
                
                $ch2 = curl_init();
                curl_setopt($ch2, CURLOPT_URL, $orderUrl);
                curl_setopt($ch2, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch2, CURLOPT_POST, 1);
                curl_setopt($ch2, CURLOPT_POSTFIELDS, json_encode($orderData));
                
                $headers2 = array();
                $headers2[] = 'Content-Type: application/json';
                $headers2[] = 'Authorization: Bearer ' . $accessToken;
                curl_setopt($ch2, CURLOPT_HTTPHEADER, $headers2);
                
                $result2 = curl_exec($ch2);
                $httpcode2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
                
                if (curl_errno($ch2)) {
                    echo '<p style="color:red">Order Creation Error: ' . curl_error($ch2) . '</p>';
                } else {
                    echo "<p>Order Creation HTTP Response Code: $httpcode2</p>";
                    echo "<pre>" . json_encode(json_decode($result2), JSON_PRETTY_PRINT) . "</pre>";
                    
                    if ($httpcode2 >= 200 && $httpcode2 < 300) {
                        $orderResponse = json_decode($result2, true);
                        $approveLink = '';
                        
                        foreach ($orderResponse['links'] as $link) {
                            if ($link['rel'] === 'approve') {
                                $approveLink = $link['href'];
                                break;
                            }
                        }
                        
                        if (!empty($approveLink)) {
                            echo "<p><a href='$approveLink' target='_blank' style='background-color:#0070ba;color:white;padding:10px 15px;text-decoration:none;border-radius:5px;'>Complete Payment on PayPal</a></p>";
                        }
                    }
                }
                curl_close($ch2);
            }
        }
    }
    curl_close($ch);
    
} catch (Exception $e) {
    echo '<p style="color:red">Exception: ' . $e->getMessage() . '</p>';
}