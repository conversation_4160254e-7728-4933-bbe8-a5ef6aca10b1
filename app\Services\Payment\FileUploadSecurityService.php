<?php

namespace App\Services\Payment;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Carbon\Carbon;

class FileUploadSecurityService
{
    /**
     * Allowed file types for payment proofs
     */
    private const ALLOWED_MIME_TYPES = [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif',
        'image/webp',
        'application/pdf'
    ];

    /**
     * Allowed file extensions
     */
    private const ALLOWED_EXTENSIONS = [
        'jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf'
    ];

    /**
     * Maximum file size in bytes (5MB)
     */
    private const MAX_FILE_SIZE = 5 * 1024 * 1024;

    /**
     * Maximum files per user per hour
     */
    private const MAX_UPLOADS_PER_HOUR = 10;

    /**
     * Suspicious file patterns
     */
    private const SUSPICIOUS_PATTERNS = [
        '/\.php$/i',
        '/\.exe$/i',
        '/\.bat$/i',
        '/\.cmd$/i',
        '/\.scr$/i',
        '/\.vbs$/i',
        '/\.js$/i',
        '/\.html$/i',
        '/\.htm$/i'
    ];

    /**
     * Validate uploaded file for security
     *
     * @param UploadedFile $file
     * @param int $userId
     * @return array
     */
    public function validateUploadedFile(UploadedFile $file, int $userId): array
    {
        try {
            // Check upload rate limiting
            $rateLimitCheck = $this->checkUploadRateLimit($userId);
            if (!$rateLimitCheck['allowed']) {
                return [
                    'valid' => false,
                    'error' => 'Upload rate limit exceeded',
                    'details' => $rateLimitCheck
                ];
            }

            // Basic file validation
            $basicValidation = $this->performBasicValidation($file);
            if (!$basicValidation['valid']) {
                return $basicValidation;
            }

            // Advanced security checks
            $securityCheck = $this->performSecurityChecks($file);
            if (!$securityCheck['valid']) {
                return $securityCheck;
            }

            // Malware scanning
            $malwareCheck = $this->scanForMalware($file);
            if (!$malwareCheck['valid']) {
                return $malwareCheck;
            }

            return [
                'valid' => true,
                'file_info' => [
                    'original_name' => $file->getClientOriginalName(),
                    'size' => $file->getSize(),
                    'mime_type' => $file->getMimeType(),
                    'extension' => $file->getClientOriginalExtension()
                ]
            ];

        } catch (\Exception $e) {
            Log::error('File validation error', [
                'user_id' => $userId,
                'file_name' => $file->getClientOriginalName(),
                'error' => $e->getMessage()
            ]);

            return [
                'valid' => false,
                'error' => 'File validation failed',
                'details' => $e->getMessage()
            ];
        }
    }

    /**
     * Securely store uploaded file
     *
     * @param UploadedFile $file
     * @param int $userId
     * @param int $paymentId
     * @return array
     */
    public function securelyStoreFile(UploadedFile $file, int $userId, int $paymentId): array
    {
        try {
            // Generate secure filename
            $secureFilename = $this->generateSecureFilename($file, $paymentId);
            
            // Create directory structure
            $directory = $this->createSecureDirectory($userId, $paymentId);
            
            // Store file outside web root
            $filePath = $directory . '/' . $secureFilename;
            
            // Store with restricted permissions
            $stored = Storage::disk('private')->putFileAs(
                $directory,
                $file,
                $secureFilename
            );

            if (!$stored) {
                throw new \Exception('Failed to store file');
            }

            // Set file permissions (if on Unix-like system)
            $this->setSecureFilePermissions($stored);

            // Log successful upload
            Log::info('Payment proof file uploaded securely', [
                'user_id' => $userId,
                'payment_id' => $paymentId,
                'file_path' => $stored,
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize()
            ]);

            return [
                'success' => true,
                'file_path' => $stored,
                'secure_filename' => $secureFilename,
                'directory' => $directory,
                'file_info' => [
                    'original_name' => $file->getClientOriginalName(),
                    'size' => $file->getSize(),
                    'mime_type' => $file->getMimeType(),
                    'stored_at' => now()
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Secure file storage failed', [
                'user_id' => $userId,
                'payment_id' => $paymentId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to store file securely',
                'details' => $e->getMessage()
            ];
        }
    }

    /**
     * Securely retrieve file for download
     *
     * @param string $filePath
     * @param int $userId
     * @return array
     */
    public function securelyRetrieveFile(string $filePath, int $userId): array
    {
        try {
            // Validate file path
            if (!$this->isValidFilePath($filePath, $userId)) {
                return [
                    'success' => false,
                    'error' => 'Invalid file path or access denied'
                ];
            }

            // Check if file exists
            if (!Storage::disk('private')->exists($filePath)) {
                return [
                    'success' => false,
                    'error' => 'File not found'
                ];
            }

            // Get file metadata
            $fileInfo = $this->getSecureFileInfo($filePath);

            return [
                'success' => true,
                'file_path' => $filePath,
                'file_info' => $fileInfo
            ];

        } catch (\Exception $e) {
            Log::error('Secure file retrieval failed', [
                'file_path' => $filePath,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to retrieve file',
                'details' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete file securely
     *
     * @param string $filePath
     * @param int $userId
     * @return bool
     */
    public function securelyDeleteFile(string $filePath, int $userId): bool
    {
        try {
            // Validate file path and ownership
            if (!$this->isValidFilePath($filePath, $userId)) {
                Log::warning('Unauthorized file deletion attempt', [
                    'file_path' => $filePath,
                    'user_id' => $userId
                ]);
                return false;
            }

            // Delete file
            $deleted = Storage::disk('private')->delete($filePath);

            if ($deleted) {
                Log::info('Payment proof file deleted securely', [
                    'file_path' => $filePath,
                    'user_id' => $userId
                ]);
            }

            return $deleted;

        } catch (\Exception $e) {
            Log::error('Secure file deletion failed', [
                'file_path' => $filePath,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Check upload rate limiting
     *
     * @param int $userId
     * @return array
     */
    private function checkUploadRateLimit(int $userId): array
    {
        $key = "file_upload_rate_limit_user_{$userId}";
        $uploads = Cache::get($key, 0);

        if ($uploads >= self::MAX_UPLOADS_PER_HOUR) {
            return [
                'allowed' => false,
                'current_uploads' => $uploads,
                'max_uploads' => self::MAX_UPLOADS_PER_HOUR,
                'reset_time' => Cache::get($key . '_reset', now()->addHour())
            ];
        }

        // Increment counter
        Cache::put($key, $uploads + 1, 3600); // 1 hour
        Cache::put($key . '_reset', now()->addHour(), 3600);

        return [
            'allowed' => true,
            'current_uploads' => $uploads + 1,
            'max_uploads' => self::MAX_UPLOADS_PER_HOUR
        ];
    }

    /**
     * Perform basic file validation
     *
     * @param UploadedFile $file
     * @return array
     */
    private function performBasicValidation(UploadedFile $file): array
    {
        // Check if file was uploaded successfully
        if (!$file->isValid()) {
            return [
                'valid' => false,
                'error' => 'File upload failed',
                'details' => $file->getErrorMessage()
            ];
        }

        // Check file size
        if ($file->getSize() > self::MAX_FILE_SIZE) {
            return [
                'valid' => false,
                'error' => 'File size exceeds maximum allowed size',
                'details' => [
                    'file_size' => $file->getSize(),
                    'max_size' => self::MAX_FILE_SIZE,
                    'max_size_mb' => round(self::MAX_FILE_SIZE / 1024 / 1024, 2)
                ]
            ];
        }

        // Check file extension
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, self::ALLOWED_EXTENSIONS)) {
            return [
                'valid' => false,
                'error' => 'File type not allowed',
                'details' => [
                    'file_extension' => $extension,
                    'allowed_extensions' => self::ALLOWED_EXTENSIONS
                ]
            ];
        }

        // Check MIME type
        $mimeType = $file->getMimeType();
        if (!in_array($mimeType, self::ALLOWED_MIME_TYPES)) {
            return [
                'valid' => false,
                'error' => 'File MIME type not allowed',
                'details' => [
                    'file_mime_type' => $mimeType,
                    'allowed_mime_types' => self::ALLOWED_MIME_TYPES
                ]
            ];
        }

        return ['valid' => true];
    }

    /**
     * Perform advanced security checks
     *
     * @param UploadedFile $file
     * @return array
     */
    private function performSecurityChecks(UploadedFile $file): array
    {
        $filename = $file->getClientOriginalName();

        // Check for suspicious filename patterns
        foreach (self::SUSPICIOUS_PATTERNS as $pattern) {
            if (preg_match($pattern, $filename)) {
                return [
                    'valid' => false,
                    'error' => 'Suspicious filename detected',
                    'details' => [
                        'filename' => $filename,
                        'pattern_matched' => $pattern
                    ]
                ];
            }
        }

        // Check for double extensions
        if (substr_count($filename, '.') > 1) {
            $parts = explode('.', $filename);
            if (count($parts) > 2) {
                // Check if any part before the last extension is suspicious
                for ($i = 1; $i < count($parts) - 1; $i++) {
                    if (in_array(strtolower($parts[$i]), ['php', 'exe', 'bat', 'cmd', 'scr'])) {
                        return [
                            'valid' => false,
                            'error' => 'Suspicious double extension detected',
                            'details' => ['filename' => $filename]
                        ];
                    }
                }
            }
        }

        // Check file content headers for images
        if (str_starts_with($file->getMimeType(), 'image/')) {
            $contentCheck = $this->validateImageContent($file);
            if (!$contentCheck['valid']) {
                return $contentCheck;
            }
        }

        // Check PDF content
        if ($file->getMimeType() === 'application/pdf') {
            $pdfCheck = $this->validatePdfContent($file);
            if (!$pdfCheck['valid']) {
                return $pdfCheck;
            }
        }

        return ['valid' => true];
    }

    /**
     * Validate image content
     *
     * @param UploadedFile $file
     * @return array
     */
    private function validateImageContent(UploadedFile $file): array
    {
        try {
            // Read first few bytes to check image headers
            $handle = fopen($file->getPathname(), 'rb');
            $header = fread($handle, 20);
            fclose($handle);

            // Check for valid image headers
            $validHeaders = [
                'image/jpeg' => ["\xFF\xD8\xFF"],
                'image/png' => ["\x89\x50\x4E\x47"],
                'image/gif' => ["\x47\x49\x46\x38"],
                'image/webp' => ["\x52\x49\x46\x46"]
            ];

            $mimeType = $file->getMimeType();
            if (isset($validHeaders[$mimeType])) {
                $headerFound = false;
                foreach ($validHeaders[$mimeType] as $validHeader) {
                    if (str_starts_with($header, $validHeader)) {
                        $headerFound = true;
                        break;
                    }
                }

                if (!$headerFound) {
                    return [
                        'valid' => false,
                        'error' => 'Invalid image file header',
                        'details' => 'File content does not match declared image type'
                    ];
                }
            }

            // Try to create image resource to validate
            $imageInfo = @getimagesize($file->getPathname());
            if ($imageInfo === false) {
                return [
                    'valid' => false,
                    'error' => 'Invalid image file',
                    'details' => 'Unable to read image data'
                ];
            }

            return ['valid' => true];

        } catch (\Exception $e) {
            return [
                'valid' => false,
                'error' => 'Image validation failed',
                'details' => $e->getMessage()
            ];
        }
    }

    /**
     * Validate PDF content
     *
     * @param UploadedFile $file
     * @return array
     */
    private function validatePdfContent(UploadedFile $file): array
    {
        try {
            // Read first few bytes to check PDF header
            $handle = fopen($file->getPathname(), 'rb');
            $header = fread($handle, 10);
            fclose($handle);

            // Check for PDF header
            if (!str_starts_with($header, '%PDF-')) {
                return [
                    'valid' => false,
                    'error' => 'Invalid PDF file header',
                    'details' => 'File does not appear to be a valid PDF'
                ];
            }

            // Basic PDF structure validation
            $content = file_get_contents($file->getPathname());
            
            // Check for suspicious JavaScript or embedded content
            $suspiciousPatterns = [
                '/\/JavaScript/i',
                '/\/JS/i',
                '/\/Action/i',
                '/\/OpenAction/i'
            ];

            foreach ($suspiciousPatterns as $pattern) {
                if (preg_match($pattern, $content)) {
                    return [
                        'valid' => false,
                        'error' => 'Suspicious PDF content detected',
                        'details' => 'PDF contains potentially malicious content'
                    ];
                }
            }

            return ['valid' => true];

        } catch (\Exception $e) {
            return [
                'valid' => false,
                'error' => 'PDF validation failed',
                'details' => $e->getMessage()
            ];
        }
    }

    /**
     * Scan file for malware (basic implementation)
     *
     * @param UploadedFile $file
     * @return array
     */
    private function scanForMalware(UploadedFile $file): array
    {
        try {
            // Basic malware patterns (in production, use proper antivirus)
            $malwarePatterns = [
                '/eval\s*\(/i',
                '/exec\s*\(/i',
                '/system\s*\(/i',
                '/shell_exec\s*\(/i',
                '/passthru\s*\(/i',
                '/<script/i',
                '/javascript:/i',
                '/vbscript:/i'
            ];

            // Read file content for text-based scanning
            $content = file_get_contents($file->getPathname());
            
            foreach ($malwarePatterns as $pattern) {
                if (preg_match($pattern, $content)) {
                    Log::alert('Malware pattern detected in uploaded file', [
                        'filename' => $file->getClientOriginalName(),
                        'pattern' => $pattern,
                        'file_size' => $file->getSize()
                    ]);

                    return [
                        'valid' => false,
                        'error' => 'Malicious content detected',
                        'details' => 'File contains suspicious patterns'
                    ];
                }
            }

            // In production, integrate with ClamAV or similar
            // $this->scanWithClamAV($file->getPathname());

            return ['valid' => true];

        } catch (\Exception $e) {
            Log::error('Malware scanning failed', [
                'filename' => $file->getClientOriginalName(),
                'error' => $e->getMessage()
            ]);

            // Fail safe - reject file if scanning fails
            return [
                'valid' => false,
                'error' => 'Security scan failed',
                'details' => 'Unable to verify file safety'
            ];
        }
    }

    /**
     * Generate secure filename
     *
     * @param UploadedFile $file
     * @param int $paymentId
     * @return string
     */
    private function generateSecureFilename(UploadedFile $file, int $paymentId): string
    {
        $extension = strtolower($file->getClientOriginalExtension());
        $timestamp = now()->format('YmdHis');
        $random = Str::random(8);
        
        return "payment_proof_{$paymentId}_{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Create secure directory structure
     *
     * @param int $userId
     * @param int $paymentId
     * @return string
     */
    private function createSecureDirectory(int $userId, int $paymentId): string
    {
        $year = now()->year;
        $month = now()->format('m');
        
        return "payment_proofs/{$year}/{$month}/user_{$userId}";
    }

    /**
     * Set secure file permissions
     *
     * @param string $filePath
     */
    private function setSecureFilePermissions(string $filePath): void
    {
        try {
            $fullPath = Storage::disk('private')->path($filePath);
            
            // Set restrictive permissions (owner read/write only)
            if (file_exists($fullPath)) {
                chmod($fullPath, 0600);
            }
        } catch (\Exception $e) {
            Log::warning('Failed to set file permissions', [
                'file_path' => $filePath,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Validate file path for security
     *
     * @param string $filePath
     * @param int $userId
     * @return bool
     */
    private function isValidFilePath(string $filePath, int $userId): bool
    {
        // Check for path traversal attempts
        if (str_contains($filePath, '..') || str_contains($filePath, '//')) {
            return false;
        }

        // Ensure file path belongs to the user
        if (!str_contains($filePath, "user_{$userId}")) {
            return false;
        }

        // Ensure file is in payment_proofs directory
        if (!str_starts_with($filePath, 'payment_proofs/')) {
            return false;
        }

        return true;
    }

    /**
     * Get secure file information
     *
     * @param string $filePath
     * @return array
     */
    private function getSecureFileInfo(string $filePath): array
    {
        try {
            $disk = Storage::disk('private');
            
            return [
                'size' => $disk->size($filePath),
                'last_modified' => Carbon::createFromTimestamp($disk->lastModified($filePath)),
                'mime_type' => $disk->mimeType($filePath),
                'exists' => $disk->exists($filePath)
            ];
        } catch (\Exception $e) {
            return [
                'error' => 'Unable to get file information',
                'details' => $e->getMessage()
            ];
        }
    }

    /**
     * Clean up old files (for maintenance)
     *
     * @param int $daysOld
     * @return int
     */
    public function cleanupOldFiles(int $daysOld = 90): int
    {
        try {
            $cutoffDate = now()->subDays($daysOld);
            $disk = Storage::disk('private');
            $deletedCount = 0;

            // Get all payment proof directories
            $directories = $disk->directories('payment_proofs');
            
            foreach ($directories as $directory) {
                $files = $disk->allFiles($directory);
                
                foreach ($files as $file) {
                    $lastModified = Carbon::createFromTimestamp($disk->lastModified($file));
                    
                    if ($lastModified->lt($cutoffDate)) {
                        if ($disk->delete($file)) {
                            $deletedCount++;
                        }
                    }
                }
            }

            Log::info('Old payment proof files cleaned up', [
                'deleted_count' => $deletedCount,
                'cutoff_date' => $cutoffDate
            ]);

            return $deletedCount;

        } catch (\Exception $e) {
            Log::error('File cleanup failed', [
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }

    /**
     * Get upload statistics
     *
     * @return array
     */
    public function getUploadStatistics(): array
    {
        try {
            $disk = Storage::disk('private');
            $totalFiles = 0;
            $totalSize = 0;
            $fileTypes = [];

            $directories = $disk->directories('payment_proofs');
            
            foreach ($directories as $directory) {
                $files = $disk->allFiles($directory);
                
                foreach ($files as $file) {
                    $totalFiles++;
                    $totalSize += $disk->size($file);
                    
                    $extension = pathinfo($file, PATHINFO_EXTENSION);
                    $fileTypes[$extension] = ($fileTypes[$extension] ?? 0) + 1;
                }
            }

            return [
                'total_files' => $totalFiles,
                'total_size' => $totalSize,
                'total_size_mb' => round($totalSize / 1024 / 1024, 2),
                'file_types' => $fileTypes,
                'average_file_size' => $totalFiles > 0 ? round($totalSize / $totalFiles / 1024, 2) : 0
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get upload statistics', [
                'error' => $e->getMessage()
            ]);

            return [
                'error' => 'Unable to get statistics',
                'details' => $e->getMessage()
            ];
        }
    }
}