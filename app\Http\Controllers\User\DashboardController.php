<?php

declare(strict_types=1);

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        
        // Get current plan information
        $currentPlan = $user->getCurrentPlan();
        $plan = Plan::find($currentPlan); // Get the plan by ID
        $totalRequests = $user->total_requests;
        $remainingRequests = $user->remaining_requests;
        
        // Get API tokens
        $tokens = $user->tokens()->latest()->get();
        
        // Get recent orders
        $recentOrders = $user->orders()
            ->with('plan')
            ->latest()
            ->take(5)
            ->get();
        
        return view('user.dashboard', compact(
            'currentPlan',
            'plan',
            'totalRequests',
            'remainingRequests',
            'tokens',
            'recentOrders'
        ));
    }
}