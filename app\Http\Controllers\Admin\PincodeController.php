<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PinCode;
use App\Models\PincodeImport;
use App\Models\State;
use App\Models\District;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;

class PincodeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // $this->middleware(['auth', 'admin']);
    }

    /**
     * Display a listing of the pincodes.
     *
     */
    public function index(Request $request)
    {
        try {
            $query = PinCode::query();


            // Add search filter if provided
            if ($request->filled('search')) {
                $search = $request->input('search');
                $query->where(function ($q) use ($search) {
                    $q->where('pincode', 'like', "%{$search}%")
                        ->orWhere('name', 'like', "%{$search}%")
                        ->orWhere('district', 'like', "%{$search}%");
                });
            }

            // Add state filter if provided
            if ($request->filled('state')) {
                $query->where('state', $request->input('state'));
                // Load districts for the selected state
                $state = State::where('name', $request->input('state'))->first();
                if ($state) {
                    $districts = District::where('state_id', $state->id)
                        ->orderBy('name')
                        ->get();
                } else {
                    $districts = collect();
                }
            } else {
                $districts = collect();
            }

            // Add district filter if provided
            if ($request->filled('district_id')) {
                $district = District::find($request->input('district_id'));
                if ($district) {
                    $query->where('district', $district->name);
                } else {
                    \Log::warning('Invalid district_id provided: ' . $request->input('district_id'));
                }
            }

            // Get paginated results with eager loading
            $pincodes = $query->with(['state', 'district'])
                ->orderBy('pincode')
                ->paginate(50);

            // Get all states for dropdown
            $states = State::orderBy('name')->get();

            return view('admin.pincodes.index', compact('pincodes', 'states', 'districts'));

        } catch (\Exception $e) {
            \Log::error('Error in PincodeController@index: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->with('error', 'An error occurred while fetching pincodes. Please try again later.');
        }
    }

    /**
     * Show the form for creating a new pincode.
     *
     * @return \Illuminate\View\View
     */
    public function create(): View
    {
        $states = State::orderBy('name')->get();
        return view('admin.pincodes.create', compact('states'));
    }

    /**
     * Store a newly created pincode in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'pincode' => 'required|string|size:6|unique:pin_codes',
                'name' => 'required|string|max:255',
                'circle' => 'nullable|string|max:255',
                'region' => 'nullable|string|max:255',
                'division' => 'nullable|string|max:255',
                'branch_type' => 'nullable|string|max:50',
                'delivery_status' => 'nullable|string|max:50',
                'district' => 'required|string|max:255',
                'state' => 'required|string|max:255',
                'latitude' => 'nullable|string|max:255',
                'longitude' => 'nullable|string|max:255'
            ]);

            // Find or create state if it doesn't exist
            $state = State::firstOrCreate(['name' => $validated['state']]);

            // Find or create district if it doesn't exist
            $district = District::firstOrCreate(
                ['name' => $validated['district'], 'state_id' => $state->id]
            );

            // Create pincode
            $pincode = PinCode::create($validated);

            // Clear relevant caches
            $this->clearPincodeCaches($validated['state']);

            return redirect()->route('admin.pincodes.index')
                ->with('success', 'Pincode created successfully.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::warning('Validation error while creating pincode', [
                'errors' => $e->errors(),
                'input' => $request->all()
            ]);

            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput();

        } catch (\Exception $e) {
            \Log::error('Error creating pincode: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->with('error', 'An error occurred while creating the pincode. Please try again later.')
                ->withInput();
        }
    }

    /**
     * Show the form for editing the specified pincode.
     *
     * @param  \App\Models\Pincode  $pincode
     * @return \Illuminate\View\View
     */
    public function edit(PinCode $pincode): View
    {
        $states = State::orderBy('name')->get();
        return view('admin.pincodes.edit', compact('pincode', 'states'));
    }

    /**
     * Update the specified pincode in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Pincode  $pincode
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, PinCode $pincode)
    {
        try {
            $validated = $request->validate([
                'pincode' => 'required|string|size:6|unique:pin_codes,pincode,' . $pincode->id,
                'name' => 'required|string|max:255',
                'circle' => 'nullable|string|max:255',
                'region' => 'nullable|string|max:255',
                'division' => 'nullable|string|max:255',
                'branch_type' => 'nullable|string|max:50',
                'delivery_status' => 'nullable|string|max:50',
                'district' => 'required|string|max:255',
                'state' => 'required|string|max:255',
                'latitude' => 'nullable|string|max:255',
                'longitude' => 'nullable|string|max:255'
            ]);

            // Get the old state and district before updating
            $oldState = $pincode->state;
            $oldDistrict = $pincode->district;

            // Find or create state if it doesn't exist
            $state = State::firstOrCreate(['name' => $validated['state']]);

            // Find or create district if it doesn't exist
            $district = District::firstOrCreate(
                ['name' => $validated['district'], 'state_id' => $state->id]
            );

            // Update pincode
            $pincode->update($validated);

            // Clear relevant caches
            $this->clearPincodeCaches($validated['state']);

            // If state changed, clear cache for old state too
            if ($oldState !== $validated['state']) {
                $this->clearPincodeCaches($oldState);
            }

            return redirect()->route('admin.pincodes.index')
                ->with('success', 'Pincode updated successfully.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::warning('Validation error while updating pincode', [
                'errors' => $e->errors(),
                'input' => $request->all()
            ]);

            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput();

        } catch (\Exception $e) {
            \Log::error('Error updating pincode: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->with('error', 'An error occurred while updating the pincode. Please try again later.')
                ->withInput();
        }
    }

    /**
     * Remove the specified pincode from storage.
     *
     * @param  \App\Models\Pincode  $pincode
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(PinCode $pincode)
    {
        try {
            // Store state before deleting for cache clearing
            $state = $pincode->state;
            $pincodeId = $pincode->id;
            $pincodeData = $pincode->toArray();

            // Delete the pincode
            $pincode->delete();

            // Clear relevant caches
            $this->clearPincodeCaches($state);

            return redirect()->route('admin.pincodes.index')
                ->with('success', 'Pincode deleted successfully.');

        } catch (\Exception $e) {
            \Log::error('Error deleting pincode: ' . $e->getMessage(), [
                'pincode_id' => $pincode->id,
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->with('error', 'An error occurred while deleting the pincode. Please try again later.');
        }
    }

    /**
     * Show the form for importing pincodes.
     *
     * @return \Illuminate\View\View
     */
    public function showImportForm(): View
    {
        $importHistory = PincodeImport::with('user')
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        return view('admin.pincodes.import', compact('importHistory'));
    }

    /**
     * Process the import file.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processImport(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv|max:10240',
            'header_row' => 'boolean',
            'update_existing' => 'boolean',
        ]);

        try {
            // Store the uploaded file
            $path = $request->file('csv_file')->store('imports');
            $filename = $request->file('csv_file')->getClientOriginalName();

            // Create import record
            $import = PincodeImport::create([
                'user_id' => Auth::id(),
                'filename' => $filename,
                'file_path' => $path,
                'status' => 'processing',
                'has_header' => $request->boolean('header_row'),
                'update_existing' => $request->boolean('update_existing'),
            ]);

            // Estimate record count to decide processing method
            $estimatedRecords = $this->estimateRecordCount($path, $request->boolean('header_row'));
            
            if ($estimatedRecords > 5000) {
                // Queue the import job for large files
                dispatch(new \App\Jobs\ProcessPincodeImport($import));
                
                return redirect()->route('admin.pincodes.import')
                    ->with('success', "Large import queued successfully. Estimated {$estimatedRecords} records will be processed in background. Check import history for progress.");
            } else {
                // Process small files immediately
                $this->processCsvImport($import);
                
                // Refresh the import record to get updated counts
                $import->refresh();

                return redirect()->route('admin.pincodes.import')
                    ->with('success', "Import completed successfully. Processed {$import->total_records} records with {$import->successful_records} successful and {$import->failed_records} failed.");
            }

        } catch (\Exception $e) {
            \Log::error('Import error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('admin.pincodes.import')
                ->with('error', 'An error occurred during import: ' . $e->getMessage());
        }
    }

    /**
     * Estimate the number of records in the CSV file.
     *
     * @param string $filePath
     * @param bool $hasHeader
     * @return int
     */
    protected function estimateRecordCount($filePath, $hasHeader)
    {
        $file = fopen(Storage::path($filePath), 'r');
        $count = 0;
        
        if ($hasHeader) {
            fgetcsv($file);
        }
        
        // Count first 1000 rows to estimate total
        while (($row = fgetcsv($file)) !== false && $count < 1000) {
            $count++;
        }
        
        fclose($file);
        
        // If we hit 1000, estimate based on file size
        if ($count >= 1000) {
            $fileSize = Storage::size($filePath);
            $avgRowSize = $fileSize / $count;
            $estimatedTotal = (int) ($fileSize / $avgRowSize);
            return $estimatedTotal;
        }
        
        return $count;
    }

    /**
     * Process the CSV import.
     *
     * @param  \App\Models\PincodeImport  $import
     * @return void
     */
    protected function processCsvImport(PincodeImport $import)
    {
        $filePath = Storage::path($import->file_path);
        $file = fopen($filePath, 'r');

        $totalRecords = 0;
        $successfulRecords = 0;
        $failedRecords = 0;
        $errors = [];
        $batchSize = 50; // Reduced batch size for better memory management
        $batch = [];
        $processedRows = 0;

        // Skip header row if needed
        if ($import->has_header) {
            fgetcsv($file);
        }

        // Clear cache keys that might be affected by this import
        Cache::forget('pincode_states');
        Cache::forget('pincode_branch_types');
        Cache::forget('pincode_divisions');

        // Clear any paginated result caches
        $cacheKeys = Cache::get('pincode_list_cache_keys', []);
        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }
        Cache::put('pincode_list_cache_keys', [], now()->addDay());

        // First pass: count total records
        $tempFile = fopen($filePath, 'r');
        if ($import->has_header) {
            fgetcsv($tempFile);
        }
        while (fgetcsv($tempFile) !== false) {
            $totalRecords++;
        }
        fclose($tempFile);

        // Update import record with total count
        $import->update(['total_records' => $totalRecords]);

        // Reset file pointer
        $file = fopen($filePath, 'r');
        if ($import->has_header) {
            fgetcsv($file);
        }

        while (($row = fgetcsv($file)) !== false) {
            $processedRows++;

            try {
                // Validate row data
                if (count($row) < 5) {
                    throw new \Exception('Row does not have enough columns. Expected: pincode, name, district, state, delivery_status');
                }

                list($pincode, $name, $district, $state, $deliveryStatus) = $row;

                // Validate pincode
                if (!preg_match('/^\d{6}$/', $pincode)) {
                    throw new \Exception('Invalid pincode format. Must be 6 digits.');
                }

                // Validate required fields
                if (empty(trim($name))) {
                    throw new \Exception('Area name is required.');
                }

                if (empty(trim($district))) {
                    throw new \Exception('District is required.');
                }

                if (empty(trim($state))) {
                    throw new \Exception('State is required.');
                }

                // Validate delivery status
                if (!in_array(strtolower($deliveryStatus), ['delivery', 'non-delivery'])) {
                    $deliveryStatus = 'Delivery'; // Default to Delivery
                }

                // Add to batch for processing
                $batch[] = [
                    'pincode' => $pincode,
                    'name' => trim($name),
                    'district' => trim($district),
                    'state' => trim($state),
                    'delivery_status' => ucfirst(strtolower($deliveryStatus)),
                ];

                // Process in smaller batches to reduce memory usage
                if (count($batch) >= $batchSize) {
                    $this->processBatch($batch, $import->update_existing, $successfulRecords, $failedRecords, $errors, $processedRows);
                    $batch = []; // Reset batch after processing
                    
                    // Update progress every 1000 records
                    if ($processedRows % 1000 === 0) {
                        $import->update([
                            'successful_records' => $successfulRecords,
                            'failed_records' => $failedRecords,
                            'status' => 'processing'
                        ]);
                        
                        // Clear memory
                        gc_collect_cycles();
                    }
                }

            } catch (\Exception $e) {
                $failedRecords++;
                $errors[] = [
                    'row' => $processedRows,
                    'data' => $row,
                    'error' => $e->getMessage(),
                ];
                
                // Limit error storage to prevent memory issues
                if (count($errors) > 1000) {
                    $errors = array_slice($errors, -1000);
                }
            }
        }

        // Process any remaining records in the last batch
        if (!empty($batch)) {
            $this->processBatch($batch, $import->update_existing, $successfulRecords, $failedRecords, $errors, $processedRows);
        }

        fclose($file);

        // Update import record
        $import->update([
            'successful_records' => $successfulRecords,
            'failed_records' => $failedRecords,
            'status' => 'completed',
            'has_errors' => $failedRecords > 0,
            'error_details' => $errors ? json_encode(array_slice($errors, -500)) : null, // Limit stored errors
        ]);

        // Clear all pincode caches after import
        $this->clearPincodeCaches();
    }

    /**
     * Process a batch of pincode records.
     *
     * @param  array  $batch
     * @param  bool  $updateExisting
     * @param  int  &$successfulRecords
     * @param  int  &$failedRecords
     * @param  array  &$errors
     * @param  int  $totalProcessed
     * @return void
     */
    protected function processBatch(array $batch, bool $updateExisting, int &$successfulRecords, int &$failedRecords, array &$errors, int $totalProcessed)
    {
        try {
            // Extract all pincodes from the batch
            $pincodes = array_column($batch, 'pincode');

            // Find existing pincodes in a single query
            $existingPincodes = PinCode::whereIn('pincode', $pincodes)
                ->get()
                ->keyBy('pincode');

            $newRecords = [];
            $updateRecords = [];

            foreach ($batch as $index => $record) {
                try {
                    $pincode = $record['pincode'];

                    // Find or create state
                    $state = State::firstOrCreate(['name' => $record['state']]);

                    // Find or create district
                    $district = District::firstOrCreate([
                        'name' => $record['district'],
                        'state_id' => $state->id
                    ]);

                    if ($existingPincodes->has($pincode)) {
                        // Update existing record if update_existing is true
                        if ($updateExisting) {
                            $existingPincode = $existingPincodes[$pincode];
                            $existingPincode->update([
                                'name' => $record['name'],
                                'district' => $record['district'],
                                'state' => $record['state'],
                                'delivery_status' => $record['delivery_status'],
                            ]);
                        }
                    } else {
                        // New record to be inserted
                        $newRecords[] = [
                            'pincode' => $record['pincode'],
                            'name' => $record['name'],
                            'district' => $record['district'],
                            'state' => $record['state'],
                            'delivery_status' => $record['delivery_status'],
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                    }

                    $successfulRecords++;
                } catch (\Exception $e) {
                    $failedRecords++;
                    $errors[] = [
                        'row' => $totalProcessed + $index + 1,
                        'data' => $record,
                        'error' => $e->getMessage(),
                    ];
                }
            }

            // Bulk insert new records
            if (!empty($newRecords)) {
                PinCode::insert($newRecords);
            }

        } catch (\Exception $e) {
            $failedRecords += count($batch);
            $errors[] = [
                'row' => $totalProcessed,
                'data' => $batch,
                'error' => 'Batch processing error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Download a sample CSV template.
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadTemplate()
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="pincode-template.csv"',
        ];

        $callback = function () {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['pincode', 'name', 'district', 'state', 'delivery_status']);
            fputcsv($file, ['110001', 'Connaught Place', 'New Delhi', 'Delhi', 'Delivery']);
            fputcsv($file, ['400001', 'Fort', 'Mumbai City', 'Maharashtra', 'Delivery']);
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Display import errors.
     *
     * @param  \App\Models\PincodeImport  $import
     * @return \Illuminate\View\View
     */
    public function showImportErrors(PincodeImport $import): View
    {
        $importErrors = json_decode($import->error_details, true) ?? [];

        return view('admin.pincodes.import-errors', compact('import', 'importErrors'));
    }

    /**
     * Clear pincode related caches.
     *
     * @param  string|null  $state  Optional state to clear specific state-related caches
     * @return void
     */
    protected function clearPincodeCaches(?string $state = null)
    {
        // Clear dropdown data caches
        Cache::forget('pincode_states');
        Cache::forget('pincode_branch_types');
        Cache::forget('pincode_divisions');

        // Clear all paginated result caches
        $cacheKeys = Cache::get('pincode_list_cache_keys', []);
        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }

        // Reset the cache keys tracking
        Cache::put('pincode_list_cache_keys', [], now()->addDay());
    }

    /**
     * Get active imports for progress tracking.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getActiveImports()
    {
        $activeImports = PincodeImport::whereIn('status', ['processing', 'queued'])
            ->orderBy('created_at', 'desc')
            ->get(['id', 'filename', 'status', 'total_records', 'successful_records', 'failed_records', 'created_at']);

        return response()->json([
            'active_imports' => $activeImports
        ]);
    }
}