@extends('layouts.app')

@include('layouts.partials.tools-json-ld')

@section('content')
    <x-breadcrumb :segments="$breadcrumbs" :pageTitle="$pageTitle" :metaDescription="$metaDescription" />
    <div class="container max-w-6xl mx-auto px-4 py-8 bg-bg-light dark:bg-bg-dark transition-colors duration-300">
        <div class="flex flex-wrap -mx-4">
            <div class="w-full lg:w-2/3 px-4">

                <h2 class="text-3xl font-bold mb-6 text-text-primary-light dark:text-text-primary-dark">Pincode Distance Calculator</h2>
                <p class="mb-6 text-text-secondary-light dark:text-text-secondary-dark">
                    The Pincode to Pincode Distance Calculator is an efficient tool that calculates the distance
                    between two Indian locations based on their pincodes. It provides approximate results using
                    geolocation data and mapping algorithms, offering straight-line distances.
                </p>
                <p class="mb-6 text-text-secondary-light dark:text-text-secondary-dark">
                    Ideal for logistics, deliveries, and travel planning, this tool simplifies the process of
                    determining distances across India with just two pincodes.
                </p>

                <div x-data="pincodeCalculator()" class="mb-8">
                    <form @submit.prevent="calculateDistance" class="p-6 border border-border-light dark:border-border-dark rounded-lg shadow-sm bg-gray-50 dark:bg-slate-700 transition-colors duration-300">
                        @csrf
                        <div class="flex flex-wrap -mx-3 mb-4">
                            <div class="w-full md:w-1/2 px-3 mb-6 md:mb-0">
                                <label for="from_pincode" class="block mb-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">From
                                    Pincode:</label>
                                <input type="text"
                                    class="w-full px-3 py-2 border border-border-light dark:border-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary-light/50 dark:focus:ring-primary-dark/50 bg-white dark:bg-slate-600 text-text-primary-light dark:text-text-primary-dark placeholder-text-secondary-light dark:placeholder-text-secondary-dark transition-colors duration-300"
                                    :class="{ 'border-red-500 dark:border-red-400': fromPincodeError, 'border-green-500 dark:border-green-400': fromPincodeValid }"
                                    id="from_pincode" x-model="fromPincode" @blur="validatePincode('from')"
                                    placeholder="441901" required>
                                <div x-show="fromPincodeError" x-text="fromPincodeErrorMessage"
                                    class="mt-1 text-sm text-red-500 dark:text-red-400"></div>
                            </div>
                            <div class="w-full md:w-1/2 px-3">
                                <label for="to_pincode" class="block mb-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">To
                                    Pincode:</label>
                                <input type="text"
                                    class="w-full px-3 py-2 border border-border-light dark:border-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary-light/50 dark:focus:ring-primary-dark/50 bg-white dark:bg-slate-600 text-text-primary-light dark:text-text-primary-dark placeholder-text-secondary-light dark:placeholder-text-secondary-dark transition-colors duration-300"
                                    :class="{ 'border-red-500 dark:border-red-400': toPincodeError, 'border-green-500 dark:border-green-400': toPincodeValid }"
                                    id="to_pincode" x-model="toPincode" @blur="validatePincode('to')" placeholder="400001"
                                    required>
                                <div x-show="toPincodeError" x-text="toPincodeErrorMessage"
                                    class="mt-1 text-sm text-red-500 dark:text-red-400"></div>
                            </div>
                        </div>
                        <button type="submit"
                            class="w-full bg-primary-light dark:bg-primary-dark hover:bg-primary-light/90 dark:hover:bg-primary-dark/90 text-white font-bold py-2 px-4 rounded-md transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98]">Calculate
                            Distance</button>
                    </form>

                    <div x-show="showResult" x-cloak class="mt-8">
                        <div class="bg-white dark:bg-slate-800 shadow-md rounded-lg overflow-hidden border border-border-light dark:border-border-dark transition-colors duration-300">
                            <div class="bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark px-6 py-4 border-b border-border-light dark:border-border-dark">
                                <h5 class="text-xl font-semibold">Distance Calculation Results</h5>
                            </div>
                            <div class="p-6">
                                <h6 class="text-text-secondary-light dark:text-text-secondary-dark mb-4"
                                    x-text="result?.from?.pincode && result?.to?.pincode ? `The distance between ${result.from.pincode} and ${result.to.pincode} is:` : ''">
                                </h6>
                                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
                                    <div class="bg-gray-50 dark:bg-slate-700 p-4 rounded-lg text-center border border-border-light dark:border-border-dark transition-colors duration-300">
                                        <h5 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark"
                                            x-text="result?.distance?.km ? `${result.distance.km} km` : ''"></h5>
                                    </div>
                                    <div class="bg-gray-50 dark:bg-slate-700 p-4 rounded-lg text-center border border-border-light dark:border-border-dark transition-colors duration-300">
                                        <h5 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark"
                                            x-text="result?.distance?.miles ? `${result.distance.miles} miles` : ''"></h5>
                                    </div>
                                    <div class="bg-gray-50 dark:bg-slate-700 p-4 rounded-lg text-center border border-border-light dark:border-border-dark transition-colors duration-300">
                                        <h5 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark"
                                            x-text="result?.distance?.nautical_miles ? `${result.distance.nautical_miles} nautical miles` : ''">
                                        </h5>
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div class="text-text-secondary-light dark:text-text-secondary-dark">
                                        <p><strong class="text-text-primary-light dark:text-text-primary-dark">From:</strong> <span
                                                x-text="result?.from?.name && result?.from?.state ? `${result.from.name}, ${result.from.state}` : ''"></span>
                                        </p>
                                        <p><strong class="text-text-primary-light dark:text-text-primary-dark">To:</strong> <span
                                                x-text="result?.to?.name && result?.to?.state ? `${result.to.name}, ${result.to.state}` : ''"></span>
                                        </p>
                                    </div>
                                    <div class="text-text-secondary-light dark:text-text-secondary-dark">
                                        <p><strong class="text-text-primary-light dark:text-text-primary-dark">Initial bearing:</strong> <span
                                                x-text="result?.bearing ? `${result.bearing}°` : ''"></span></p>
                                        <p><strong class="text-text-primary-light dark:text-text-primary-dark">Midpoint:</strong> <span
                                                x-text="result?.midpoint?.latitude && result?.midpoint?.longitude ? `${result.midpoint.latitude}, ${result.midpoint.longitude}` : ''"></span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div x-show="showError" x-cloak class="mt-8">
                        <div class="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-400 dark:border-red-500 text-red-700 dark:text-red-400 p-4 transition-colors duration-300" role="alert">
                            <p class="font-bold">Error</p>
                            <p x-text="errorMessage">An error occurred. Please try again.</p>
                        </div>
                    </div>
                </div>

                <div class="space-y-6">
                    <h2 class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark">How the Pincode to Pincode Distance Calculator Works</h2>
                    <p class="text-text-secondary-light dark:text-text-secondary-dark">
                        The Pincode to Pincode Distance Calculator functions using advanced geolocation data and mapping
                        algorithms to provide approx. distance results between two Indian pincodes. Here's a
                        step-by-step explanation of how the tool operates:
                    </p>

                    <div class="bg-gray-50 dark:bg-slate-700 p-6 rounded-lg border border-border-light dark:border-border-dark mb-6 transition-colors duration-300">
                        <h3 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark mb-3">Input Pincodes:</h3>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark">
                            You start by entering the From Pincode and To Pincode into the tool. These represent the origin
                            and destination locations, respectively.
                        </p>
                    </div>

                    <div class="bg-gray-50 dark:bg-slate-700 p-6 rounded-lg border border-border-light dark:border-border-dark mb-6 transition-colors duration-300">
                        <h3 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark mb-3">Geolocation Mapping:</h3>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark">
                            Behind the scenes, each pincode is associated with geographical coordinates (latitude and
                            longitude). The tool accesses a vast database of all Indian pincodes and their corresponding
                            geolocations.
                        </p>
                    </div>

                    <div class="bg-gray-50 dark:bg-slate-700 p-6 rounded-lg border border-border-light dark:border-border-dark mb-6 transition-colors duration-300">
                        <h3 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark mb-3">Distance Calculation:</h3>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark">
                            Once the coordinates of both pincodes are identified, the tool uses one of the following methods
                            to calculate the distance:
                        </p>
                        <ul class="list-disc pl-5 space-y-2 text-text-secondary-light dark:text-text-secondary-dark mt-3">
                            <li>
                                <strong class="text-text-primary-light dark:text-text-primary-dark">Straight-line distance (as the crow flies):</strong> This is the shortest distance
                                between two points on
                                the Earth's surface, calculated using the <strong class="text-text-primary-light dark:text-text-primary-dark">Haversine formula</strong>, which takes
                                into
                                account the curvature of the Earth.
                            </li>
                            <li>
                                <strong class="text-text-primary-light dark:text-text-primary-dark">Driving distance (optional):</strong> If the tool is connected to real-time maps, it
                                can
                                calculate the
                                road distance between two pincodes, which is more practical for planning travel or
                                deliveries.
                            </li>
                        </ul>
                    </div>

                    <div class="bg-gray-50 dark:bg-slate-700 p-6 rounded-lg border border-border-light dark:border-border-dark mb-6 transition-colors duration-300">
                        <h3 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark mb-3">Results Displayed:</h3>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark">
                            The calculated distance (in kilometers or miles) is instantly displayed to the user.
                        </p>
                    </div>

                    <div class="bg-gray-50 dark:bg-slate-700 p-6 rounded-lg border border-border-light dark:border-border-dark transition-colors duration-300">
                        <h3 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark mb-3">Applications:</h3>
                        <ul class="list-disc pl-5 space-y-2 text-text-secondary-light dark:text-text-secondary-dark">
                            <li>The tool is widely used for logistics, delivery services, e-commerce, and personal travel
                                planning.</li>
                            <li>It helps businesses optimize their operations by calculating the exact distance between
                                distribution centers and customer locations or among delivery stops.</li>
                        </ul>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark mt-3">
                            By leveraging reliable geolocation data and advanced mathematical formulas, the Pincode Distance
                            Calculator offers users a simple, yet powerful way to determine distances between two Indian
                            locations with just their pincodes!
                        </p>
                    </div>
                </div>
            </div>
            <div class="w-full lg:w-1/3 px-4">
                @include('pincodes.partials.sidebar')
            </div>
        </div>

        <!-- Reviews Section -->
        <div class="mt-8">
            <x-tool-reviews
                :reviews="$reviews"
                :tool="$tool"
                :hasMoreReviews="$hasMoreReviews ?? false"
                :totalReviewsCount="$totalReviewsCount ?? 0"
            />
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function pincodeCalculator() {
            return {
                fromPincode: '',
                toPincode: '',
                fromPincodeValid: false,
                toPincodeValid: false,
                fromPincodeError: false,
                toPincodeError: false,
                fromPincodeErrorMessage: '',
                toPincodeErrorMessage: '',
                showResult: false,
                showError: false,
                errorMessage: 'An error occurred. Please try again.',
                result: {},
                csrfToken: '{{ csrf_token() }}',

                init() {
                    // console.log('Alpine.js pincode calculator initialized');
                },

                validatePincode(type) {
                    const pincode = type === 'from' ? this.fromPincode : this.toPincode;

                    if (!pincode) {
                        this.setPincodeError(type, true, 'Pincode is required');
                        return;
                    }

                    fetch("{{ route('validate.pincode') }}", {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': this.csrfToken
                            },
                            body: JSON.stringify({
                                pincode: pincode,
                                _token: this.csrfToken
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.valid) {
                                this.setPincodeError(type, false, '');
                                this.setPincodeValid(type, true);
                            } else {
                                this.setPincodeError(type, true, 'Invalid pincode');
                                this.setPincodeValid(type, false);
                            }
                        })
                        .catch(error => {
                            console.error(`Error validating ${type} pincode:`, error);
                            this.setPincodeError(type, true, 'Error validating pincode');
                            this.setPincodeValid(type, false);
                        });
                },

                setPincodeError(type, hasError, message) {
                    if (type === 'from') {
                        this.fromPincodeError = hasError;
                        this.fromPincodeErrorMessage = message;
                    } else {
                        this.toPincodeError = hasError;
                        this.toPincodeErrorMessage = message;
                    }
                },

                setPincodeValid(type, isValid) {
                    if (type === 'from') {
                        this.fromPincodeValid = isValid;
                    } else {
                        this.toPincodeValid = isValid;
                    }
                },

                calculateDistance() {
                    this.showResult = false;
                    this.showError = false;

                    // Validate both pincodes once more before calculation
                    this.validatePincode('from');
                    this.validatePincode('to');

                    const formData = new FormData();
                    formData.append('from_pincode', this.fromPincode);
                    formData.append('to_pincode', this.toPincode);
                    formData.append('_token', this.csrfToken);

                    fetch("{{ route('calculate.distance.pincodes') }}", {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': this.csrfToken
                            },
                            body: formData
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Network response was not ok');
                            }
                            return response.json();
                        })
                        .then(data => {
                            this.result = data;
                            this.showResult = true;
                        })
                        .catch(error => {
                            this.errorMessage = 'Failed to calculate distance. Please try again.';
                            this.showError = true;
                        });
                }
            };
        }
    </script>
@endpush