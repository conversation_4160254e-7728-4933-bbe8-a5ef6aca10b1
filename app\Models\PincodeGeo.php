<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PincodeGeo extends Model
{
    use HasFactory;

    protected $table = 'pincodes_geo';

    protected $fillable = [
        'pincode',
        'office_name',
        'division',
        'region',
        'circle',
        'geometry',
    ];

    protected $casts = [
        'geometry' => 'array',
    ];

    /**
     * Scope for searching by pincode.
     */
    public function scopeByPincode($query, $pincode)
    {
        return $query->where('pincode', 'like', $pincode . '%');
    }
} 