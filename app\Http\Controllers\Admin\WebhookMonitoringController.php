<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\WebhookLog;
use App\Services\Payment\WebhookRetryService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class WebhookMonitoringController extends Controller
{
    protected WebhookRetryService $retryService;

    public function __construct(WebhookRetryService $retryService)
    {
        $this->retryService = $retryService;
    }

    /**
     * Display webhook monitoring dashboard
     */
    public function index()
    {
        return view('admin.webhook-monitoring.index');
    }

    /**
     * Get webhook logs with pagination and filtering
     */
    public function getLogs(Request $request): JsonResponse
    {
        $query = WebhookLog::with(['gateway'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('gateway_id')) {
            $query->where('gateway_id', $request->gateway_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('event_type')) {
            $query->where('event_type', 'like', '%' . $request->event_type . '%');
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $logs = $query->paginate($request->get('per_page', 25));

        return response()->json($logs);
    }

    /**
     * Get webhook log details
     */
    public function getDetails(WebhookLog $webhookLog): JsonResponse
    {
        $webhookLog->load(['gateway', 'payment']);
        
        return response()->json([
            'webhook_log' => $webhookLog,
            'formatted_payload' => json_encode($webhookLog->payload, JSON_PRETTY_PRINT),
            'can_retry' => $webhookLog->status === 'failed' && $webhookLog->retry_count < 3
        ]);
    }

    /**
     * Retry failed webhook
     */
    public function retryWebhook(WebhookLog $webhookLog): JsonResponse
    {
        if ($webhookLog->status !== 'failed') {
            return response()->json([
                'success' => false,
                'message' => 'Only failed webhooks can be retried'
            ], 400);
        }

        if ($webhookLog->retry_count >= 3) {
            return response()->json([
                'success' => false,
                'message' => 'Maximum retry attempts reached'
            ], 400);
        }

        try {
            $result = $this->retryService->manualRetry($webhookLog);
            
            return response()->json([
                'success' => $result['success'],
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Retry failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear old webhook logs
     */
    public function clearOldLogs(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        
        $deletedCount = WebhookLog::where('created_at', '<', now()->subDays($days))
            ->delete();

        return response()->json([
            'success' => true,
            'message' => "Deleted {$deletedCount} old webhook logs"
        ]);
    }

    /**
     * Export webhook logs
     */
    public function exportLogs(Request $request)
    {
        // Implementation for exporting logs
        return response()->json([
            'success' => true,
            'message' => 'Export functionality not implemented yet'
        ]);
    }

    /**
     * Get webhook metrics
     */
    public function getMetrics(): JsonResponse
    {
        $metrics = [
            'total_webhooks' => WebhookLog::count(),
            'successful_webhooks' => WebhookLog::where('status', 'processed')->count(),
            'failed_webhooks' => WebhookLog::where('status', 'failed')->count(),
            'pending_webhooks' => WebhookLog::where('status', 'processing')->count(),
            'success_rate' => 0,
            'recent_activity' => WebhookLog::with('gateway')
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get()
        ];

        if ($metrics['total_webhooks'] > 0) {
            $metrics['success_rate'] = round(
                ($metrics['successful_webhooks'] / $metrics['total_webhooks']) * 100, 
                2
            );
        }

        return response()->json($metrics);
    }
}