@extends('layouts.app')


@section('content')
    <section class="py-16">
        <div class="container max-w-6xl mx-auto px-4">
            <div class="flex flex-wrap -mx-4 justify-center">
                <div class="w-full lg:w-3/4 px-4">
                    <div class="space-y-8">
                        <div class="container mt-10">
                            <h1 class="text-3xl font-bold">Pincode Distance Calculation Results</h1>
                            <p class="text-sm mb-6">Results from your bulk distance calculation</p>

                            <div class="bg-white shadow-md rounded-lg overflow-hidden">
                                <div class="bg-blue-600 text-white px-6 py-4 flex justify-between items-center">
                                    <h5 class="text-xl font-semibold mb-0">Results</h5>
                                    <div class="flex space-x-3">
                                        <a href="{{ route('pincode.download.excel') }}" class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-md transition duration-300 ease-in-out flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                            </svg>
                                            Excel
                                        </a>
                                        <a href="{{ route('pincode.download.pdf') }}" class="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-md transition duration-300 ease-in-out flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                            </svg>
                                            PDF
                                        </a>
                                    </div>
                                </div>
                                <div class="p-6">
                                    @if (count($results) > 0)
                                        <div class="overflow-x-auto">
                                            <table class="min-w-full divide-y divide-gray-200">
                                                <thead class="bg-gray-50">
                                                    <tr>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source Pincode</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source City</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source State</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Destination Pincode</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Destination City</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Destination State</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Distance (km)</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="bg-white divide-y divide-gray-200">
                                                    @foreach ($results as $result)
                                                        <tr class="hover:bg-gray-50">
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $result['source_pincode'] }}</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ ucfirst($result['source_city']) }}</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ ucfirst($result['source_state']) }}</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $result['destination_pincode'] }}</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ ucfirst($result['destination_city']) }}</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ ucfirst($result['destination_state']) }}</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{{ number_format($result['distance'], 2) }}</td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <div class="bg-blue-50 border-l-4 border-blue-500 p-4">
                                            <div class="flex">
                                                <div class="flex-shrink-0">
                                                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                                    </svg>
                                                </div>
                                                <div class="ml-3">
                                                    <p class="text-sm text-blue-700">No results found.</p>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <div class="mt-8 bg-white rounded-lg shadow-sm p-6">
                                <h2 class="text-xl font-semibold mb-4">About Your Results</h2>
                                <div class="prose max-w-none text-gray-600">
                                    <p class="mb-4">The distances shown above are calculated using the Haversine formula, which determines the shortest distance between two points on a sphere (Earth) using their latitude and longitude coordinates.</p>
                                    
                                    <div class="bg-yellow-50 p-4 rounded-lg mb-4">
                                        <h3 class="text-lg font-medium text-yellow-800 mb-2">Note:</h3>
                                        <ul class="list-disc list-inside space-y-2 text-yellow-700">
                                            <li>Distances are shown in kilometers and represent straight-line (as the crow flies) distances.</li>
                                            <li>Actual road distances may vary based on available routes.</li>
                                            <li>You can download these results in Excel or PDF format using the buttons above.</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="w-full lg:w-1/4 px-4">
                    <div class="sticky top-20">
                        {{-- @include('pincodes.partials.sidebar') --}}
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection