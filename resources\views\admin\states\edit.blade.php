@extends('admin.layouts.admin')

@section('content')
<div class="container px-4 py-6 mx-auto">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white dark:bg-bg-dark rounded-lg shadow-md border border-border-light dark:border-border-dark">
            <div class="px-4 py-3 border-b border-border-light dark:border-border-dark">
                <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">Edit State</h3>
            </div>
            <div class="p-6">
                <!-- Note about URL slugs -->
                <div class="p-4 mb-6 text-sm text-blue-700 dark:text-blue-200 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    <p><strong>Note:</strong> The slug (URL) is based on the State Name, District Name, and Post Office Name. In the existing database, all entries are stored in lowercase.</p>
                    <p>Please remember that URLs with lowercase and uppercase letters can be treated differently on some systems. Therefore, always ensure that the above-mentioned values are kept in lowercase to maintain consistency and avoid SEO issues.</p>
                </div>

                <form action="{{ route('admin.states.update', $state) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="mb-5">
                        <label for="name" class="block mb-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">State Name</label>
                        <input type="text" 
                            id="name" 
                            name="name" 
                            value="{{ old('name', $state->name) }}" 
                            class="w-full px-3 py-2 border border-border-light dark:border-border-dark rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark @error('name') border-red-500 @enderror" 
                            required>
                        @error('name')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-text-secondary-light dark:text-text-secondary-dark">This name will be used to generate the URL slug. Please use lowercase.</p>
                    </div>
                    
                    <div class="mb-6">
                        <label for="featured_image" class="block mb-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Featured Image</label>
                        @if($state->featured_image_url !== asset('images/default-state.jpg'))
                            <div class="mb-4">
                                <div class="relative group">
                                    <img src="{{ $state->featured_image_url }}" 
                                        alt="{{ $state->name }}" 
                                        class="max-h-48 rounded border object-cover border-border-light dark:border-border-dark">
                                    <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                        <span class="text-white text-sm">Current Image</span>
                                    </div>
                                </div>
                            </div>
                        @endif
                        
                        <div class="mt-2">
                            <input type="file" 
                                id="featured_image" 
                                name="featured_image" 
                                class="w-full text-sm text-text-secondary-light dark:text-text-secondary-dark file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-accent-light dark:file:bg-accent-dark file:text-primary-light dark:file:text-primary-dark hover:file:bg-accent-dark dark:hover:file:bg-accent-light @error('featured_image') border-red-500 @enderror">
                            @error('featured_image')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-sm text-text-secondary-light dark:text-text-secondary-dark">Recommended size: 800x600 pixels. Max file size: 2MB</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <button type="submit" 
                            class="px-4 py-2 text-sm font-medium text-white bg-primary-light dark:bg-primary-dark rounded-md hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark transition-colors duration-200">
                            Update State
                        </button>
                        <a href="{{ route('admin.states.index') }}" 
                            class="px-4 py-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark bg-gray-200 dark:bg-bg-dark rounded-md hover:bg-gray-300 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-border-light dark:focus:ring-border-dark transition-colors duration-200">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection