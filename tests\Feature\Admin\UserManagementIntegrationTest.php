<?php

use App\Models\User;
use App\Models\Order;
use App\Models\ApiRequest;
use Illuminate\Support\Facades\Hash;

beforeEach(function () {
    $this->admin = User::factory()->create([
        'role' => 'admin',
        'status' => 'active'
    ]);
    
    $this->actingAs($this->admin);
});

describe('User Management Integration', function () {
    
    describe('User Listing', function () {
        it('displays all users with pagination', function () {
            User::factory()->count(15)->create();

            $response = $this->get(route('admin.users.index'));

            $response->assertStatus(200);
            $response->assertViewIs('admin.users.index');
            $response->assertViewHas('users');
        });

        it('filters users by status', function () {
            User::factory()->count(3)->create(['status' => 'active']);
            User::factory()->count(2)->create(['status' => 'inactive']);

            $response = $this->get(route('admin.users.index', ['status' => 'active']));

            $response->assertStatus(200);
        });

        it('searches users by name or email', function () {
            $user = User::factory()->create([
                'name' => 'John Doe',
                'email' => '<EMAIL>'
            ]);

            $response = $this->get(route('admin.users.index', ['search' => 'John']));

            $response->assertStatus(200);
        });
    });

    describe('User Creation', function () {
        it('creates new user successfully', function () {
            $userData = [
                'name' => 'New User',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'password_confirmation' => 'password123',
                'role' => 'user',
                'status' => 'active'
            ];

            $response = $this->post(route('admin.users.store'), $userData);

            $response->assertRedirect(route('admin.users.index'));
            $response->assertSessionHas('success');
            
            $this->assertDatabaseHas('users', [
                'name' => 'New User',
                'email' => '<EMAIL>',
                'role' => 'user',
                'status' => 'active'
            ]);
        });

        it('validates required fields for user creation', function () {
            $response = $this->post(route('admin.users.store'), []);

            $response->assertSessionHasErrors([
                'name', 'email', 'password', 'role', 'status'
            ]);
        });

        it('validates unique email for user creation', function () {
            $existingUser = User::factory()->create();

            $response = $this->post(route('admin.users.store'), [
                'name' => 'New User',
                'email' => $existingUser->email,
                'password' => 'password123',
                'password_confirmation' => 'password123',
                'role' => 'user',
                'status' => 'active'
            ]);

            $response->assertSessionHasErrors(['email']);
        });

        it('hashes password when creating user', function () {
            $userData = [
                'name' => 'New User',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'password_confirmation' => 'password123',
                'role' => 'user',
                'status' => 'active'
            ];

            $response = $this->post(route('admin.users.store'), $userData);

            $user = User::where('email', '<EMAIL>')->first();
            expect(Hash::check('password123', $user->password))->toBe(true);
        });
    });

    describe('User Viewing', function () {
        it('displays user details', function () {
            $user = User::factory()->create();
            Order::factory()->count(2)->create(['user_id' => $user->id]);
            ApiRequest::factory()->count(5)->create(['user_id' => $user->id]);

            $response = $this->get(route('admin.users.show', $user));

            $response->assertStatus(200);
            $response->assertViewIs('admin.users.show');
            $response->assertViewHas('user', $user);
        });

        it('shows user statistics', function () {
            $user = User::factory()->create();
            Order::factory()->count(3)->create([
                'user_id' => $user->id,
                'amount' => 100
            ]);

            $response = $this->get(route('admin.users.show', $user));

            $response->assertStatus(200);
        });
    });

    describe('User Editing', function () {
        it('displays user edit form', function () {
            $user = User::factory()->create();

            $response = $this->get(route('admin.users.edit', $user));

            $response->assertStatus(200);
            $response->assertViewIs('admin.users.edit');
            $response->assertViewHas('user', $user);
        });

        it('updates user successfully', function () {
            $user = User::factory()->create();
            $updateData = [
                'name' => 'Updated Name',
                'email' => '<EMAIL>',
                'role' => 'admin',
                'status' => 'inactive'
            ];

            $response = $this->put(route('admin.users.update', $user), $updateData);

            $response->assertRedirect(route('admin.users.index'));
            $response->assertSessionHas('success');
            
            $user->refresh();
            expect($user->name)->toBe('Updated Name');
            expect($user->email)->toBe('<EMAIL>');
            expect($user->role)->toBe('admin');
            expect($user->status)->toBe('inactive');
        });

        it('validates email uniqueness when updating', function () {
            $user1 = User::factory()->create();
            $user2 = User::factory()->create();

            $response = $this->put(route('admin.users.update', $user1), [
                'name' => $user1->name,
                'email' => $user2->email,
                'role' => $user1->role,
                'status' => $user1->status
            ]);

            $response->assertSessionHasErrors(['email']);
        });

        it('allows keeping same email when updating', function () {
            $user = User::factory()->create();

            $response = $this->put(route('admin.users.update', $user), [
                'name' => 'Updated Name',
                'email' => $user->email,
                'role' => $user->role,
                'status' => $user->status
            ]);

            $response->assertRedirect(route('admin.users.index'));
            $response->assertSessionHasNoErrors();
        });

        it('updates password when provided', function () {
            $user = User::factory()->create();

            $response = $this->put(route('admin.users.update', $user), [
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'status' => $user->status,
                'password' => 'newpassword123',
                'password_confirmation' => 'newpassword123'
            ]);

            $response->assertRedirect(route('admin.users.index'));
            $response->assertSessionHasNoErrors();
            
            $user->refresh();
            expect(Hash::check('newpassword123', $user->password))->toBe(true);
        });
    });

    describe('User Deletion', function () {
        it('soft deletes user successfully', function () {
            $user = User::factory()->create();

            $response = $this->delete(route('admin.users.destroy', $user));

            $response->assertRedirect(route('admin.users.index'));
            $response->assertSessionHas('success');
            
            $this->assertSoftDeleted('users', ['id' => $user->id]);
        });

        it('prevents admin from deleting themselves', function () {
            $response = $this->delete(route('admin.users.destroy', $this->admin));

            $response->assertRedirect(route('admin.users.index'));
            $response->assertSessionHas('error');
            
            $this->assertDatabaseHas('users', [
                'id' => $this->admin->id,
                'deleted_at' => null
            ]);
        });

        it('handles user with related data', function () {
            $user = User::factory()->create();
            Order::factory()->create(['user_id' => $user->id]);
            ApiRequest::factory()->create(['user_id' => $user->id]);

            $response = $this->delete(route('admin.users.destroy', $user));

            $response->assertRedirect(route('admin.users.index'));
            $this->assertSoftDeleted('users', ['id' => $user->id]);
        });
    });

    describe('User Status Management', function () {
        it('activates inactive user', function () {
            $user = User::factory()->create(['status' => 'inactive']);

            $response = $this->patch(route('admin.users.update', $user), [
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'status' => 'active'
            ]);

            $response->assertRedirect(route('admin.users.index'));
            
            $user->refresh();
            expect($user->status)->toBe('active');
        });

        it('deactivates active user', function () {
            $user = User::factory()->create(['status' => 'active']);

            $response = $this->patch(route('admin.users.update', $user), [
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'status' => 'inactive'
            ]);

            $response->assertRedirect(route('admin.users.index'));
            
            $user->refresh();
            expect($user->status)->toBe('inactive');
        });
    });

    describe('User Role Management', function () {
        it('promotes user to admin', function () {
            $user = User::factory()->create(['role' => 'user']);

            $response = $this->put(route('admin.users.update', $user), [
                'name' => $user->name,
                'email' => $user->email,
                'role' => 'admin',
                'status' => $user->status
            ]);

            $response->assertRedirect(route('admin.users.index'));
            
            $user->refresh();
            expect($user->role)->toBe('admin');
        });

        it('demotes admin to user', function () {
            $admin = User::factory()->create(['role' => 'admin']);

            $response = $this->put(route('admin.users.update', $admin), [
                'name' => $admin->name,
                'email' => $admin->email,
                'role' => 'user',
                'status' => $admin->status
            ]);

            $response->assertRedirect(route('admin.users.index'));
            
            $admin->refresh();
            expect($admin->role)->toBe('user');
        });
    });

    describe('Authorization', function () {
        it('prevents non-admin from accessing user management', function () {
            $regularUser = User::factory()->create(['role' => 'user']);
            $this->actingAs($regularUser);

            $response = $this->get(route('admin.users.index'));

            $response->assertStatus(403);
        });

        it('prevents guest from accessing user management', function () {
            auth()->logout();

            $response = $this->get(route('admin.users.index'));

            $response->assertRedirect(route('admin.login'));
        });
    });
});