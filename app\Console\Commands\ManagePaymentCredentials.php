<?php

namespace App\Console\Commands;

use App\Models\PaymentGateway;
use App\Services\Payment\CredentialEncryptionService;
use App\Services\Payment\CredentialValidationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ManagePaymentCredentials extends Command
{
    /**
     * The name and signature of the console command.
     *
     * # Validate all gateway credentials
php artisan payment:credentials validate

# Test specific gateway connection
php artisan payment:credentials test --gateway=1

# Rotate encryption for all gateways
php artisan payment:credentials rotate --force

# Export credentials for backup
php artisan payment:credentials export --file=backup.json

# Import credentials from backup
php artisan payment:credentials import --file=backup.json

     * @var string
     */
    protected $signature = 'payment:credentials 
                            {action : Action to perform (validate|rotate|export|import|test)}
                            {--gateway= : Specific gateway ID to target}
                            {--file= : File path for export/import operations}
                            {--force : Force operation without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage payment gateway credentials (validate, rotate encryption, export/import)';

    private CredentialEncryptionService $encryptionService;
    private CredentialValidationService $validationService;

    public function __construct(
        CredentialEncryptionService $encryptionService,
        CredentialValidationService $validationService
    ) {
        parent::__construct();
        $this->encryptionService = $encryptionService;
        $this->validationService = $validationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'validate':
                return $this->validateCredentials();
            case 'rotate':
                return $this->rotateEncryption();
            case 'export':
                return $this->exportCredentials();
            case 'import':
                return $this->importCredentials();
            case 'test':
                return $this->testConnections();
            default:
                $this->error("Unknown action: {$action}");
                $this->info('Available actions: validate, rotate, export, import, test');
                return 1;
        }
    }

    /**
     * Validate gateway credentials
     */
    private function validateCredentials(): int
    {
        $this->info('Validating payment gateway credentials...');

        $gatewayId = $this->option('gateway');

        if ($gatewayId) {
            $gateway = PaymentGateway::find($gatewayId);
            if (!$gateway) {
                $this->error("Gateway with ID {$gatewayId} not found");
                return 1;
            }

            $gateways = collect([$gateway]);
        } else {
            $gateways = PaymentGateway::where('is_active', true)->get();
        }

        $this->withProgressBar($gateways, function ($gateway) {
            $validation = $this->validationService->validateGatewayCredentials($gateway);

            $status = $validation['valid'] ? '<info>✓</info>' : '<error>✗</error>';
            $this->newLine();
            $this->line("{$status} {$gateway->display_name}: {$validation['message']}");

            if (!$validation['valid'] && isset($validation['errors'])) {
                foreach ($validation['errors'] as $error) {
                    $this->line("  <comment>- {$error}</comment>");
                }
            }
        });

        $this->newLine();
        $this->info('Credential validation completed');
        return 0;
    }

    /**
     * Rotate encryption keys
     */
    private function rotateEncryption(): int
    {
        if (!$this->option('force') && !$this->confirm('This will re-encrypt all gateway credentials. Continue?')) {
            $this->info('Operation cancelled');
            return 0;
        }

        $this->info('Rotating encryption keys...');

        $gatewayId = $this->option('gateway');

        if ($gatewayId) {
            $gateway = PaymentGateway::find($gatewayId);
            if (!$gateway) {
                $this->error("Gateway with ID {$gatewayId} not found");
                return 1;
            }

            $gateways = collect([$gateway]);
        } else {
            $gateways = PaymentGateway::all();
        }

        $success = 0;
        $failed = 0;

        $this->withProgressBar($gateways, function ($gateway) use (&$success, &$failed) {
            try {
                if ($gateway->rotateEncryption()) {
                    $success++;
                } else {
                    $failed++;
                }
            } catch (\Exception $e) {
                $failed++;
                Log::error('Failed to rotate encryption for gateway', [
                    'gateway_id' => $gateway->id,
                    'error' => $e->getMessage()
                ]);
            }
        });

        $this->newLine();
        $this->info("Encryption rotation completed: {$success} successful, {$failed} failed");

        return $failed > 0 ? 1 : 0;
    }

    /**
     * Export credentials
     */
    private function exportCredentials(): int
    {
        $file = $this->option('file');
        if (!$file) {
            $file = storage_path('app/gateway-credentials-' . date('Y-m-d-H-i-s') . '.json');
        }

        $this->info("Exporting credentials to: {$file}");

        $gatewayId = $this->option('gateway');

        if ($gatewayId) {
            $gateway = PaymentGateway::find($gatewayId);
            if (!$gateway) {
                $this->error("Gateway with ID {$gatewayId} not found");
                return 1;
            }

            $gateways = collect([$gateway]);
        } else {
            $gateways = PaymentGateway::all();
        }

        $export = [
            'exported_at' => now()->toISOString(),
            'version' => '1.0',
            'gateways' => []
        ];

        foreach ($gateways as $gateway) {
            try {
                $export['gateways'][] = [
                    'id' => $gateway->id,
                    'name' => $gateway->name,
                    'display_name' => $gateway->display_name,
                    'configuration' => $gateway->exportConfiguration()
                ];
            } catch (\Exception $e) {
                $this->error("Failed to export gateway {$gateway->display_name}: {$e->getMessage()}");
            }
        }

        if (file_put_contents($file, json_encode($export, JSON_PRETTY_PRINT))) {
            $this->info("Successfully exported " . count($export['gateways']) . " gateway(s)");
            return 0;
        } else {
            $this->error("Failed to write export file");
            return 1;
        }
    }

    /**
     * Import credentials
     */
    private function importCredentials(): int
    {
        $file = $this->option('file');
        if (!$file || !file_exists($file)) {
            $this->error('Import file not specified or does not exist');
            return 1;
        }

        if (!$this->option('force') && !$this->confirm('This will overwrite existing gateway configurations. Continue?')) {
            $this->info('Operation cancelled');
            return 0;
        }

        $this->info("Importing credentials from: {$file}");

        try {
            $data = json_decode(file_get_contents($file), true);
            if (!$data || !isset($data['gateways'])) {
                $this->error('Invalid import file format');
                return 1;
            }

            $success = 0;
            $failed = 0;

            foreach ($data['gateways'] as $gatewayData) {
                try {
                    $gateway = PaymentGateway::find($gatewayData['id']);
                    if (!$gateway) {
                        $this->warn("Gateway with ID {$gatewayData['id']} not found, skipping");
                        continue;
                    }

                    if ($gateway->importConfiguration($gatewayData['configuration'])) {
                        $success++;
                        $this->line("<info>✓</info> Imported {$gateway->display_name}");
                    } else {
                        $failed++;
                        $this->line("<error>✗</error> Failed to import {$gateway->display_name}");
                    }
                } catch (\Exception $e) {
                    $failed++;
                    $this->line("<error>✗</error> Error importing {$gatewayData['display_name']}: {$e->getMessage()}");
                }
            }

            $this->info("Import completed: {$success} successful, {$failed} failed");
            return $failed > 0 ? 1 : 0;
        } catch (\Exception $e) {
            $this->error("Failed to import credentials: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * Test gateway connections
     */
    private function testConnections(): int
    {
        $this->info('Testing gateway connections...');

        $gatewayId = $this->option('gateway');

        if ($gatewayId) {
            $gateway = PaymentGateway::find($gatewayId);
            if (!$gateway) {
                $this->error("Gateway with ID {$gatewayId} not found");
                return 1;
            }

            $gateways = collect([$gateway]);
        } else {
            $gateways = PaymentGateway::where('is_active', true)->get();
        }

        $passed = 0;
        $failed = 0;

        foreach ($gateways as $gateway) {
            $this->line("Testing {$gateway->display_name}...");

            try {
                $result = $gateway->testConnection();

                if ($result['success']) {
                    $this->line("<info>✓</info> {$result['message']}");
                    $passed++;
                } else {
                    $this->line("<error>✗</error> {$result['message']}");
                    $failed++;
                }

                if (isset($result['details'])) {
                    foreach ($result['details'] as $key => $value) {
                        $this->line("  <comment>{$key}:</comment> {$value}");
                    }
                }
            } catch (\Exception $e) {
                $this->line("<error>✗</error> Connection test failed: {$e->getMessage()}");
                $failed++;
            }

            $this->newLine();
        }

        $this->info("Connection tests completed: {$passed} passed, {$failed} failed");
        return $failed > 0 ? 1 : 0;
    }
}
