<?php

namespace Database\Factories;

use App\Models\LandingPageContent;
use App\Models\LandingPageSection;
use Illuminate\Database\Eloquent\Factories\Factory;

class LandingPageContentFactory extends Factory
{
    protected $model = LandingPageContent::class;

    public function definition()
    {
        return [
            'section_id' => LandingPageSection::factory(),
            'key' => $this->faker->unique()->word(),
            'value' => $this->faker->sentence(),
            'type' => 'text',
            'options' => null,
        ];
    }

    public function withType(string $type)
    {
        return $this->state(function (array $attributes) use ($type) {
            $value = match ($type) {
                'text' => $this->faker->sentence(),
                'textarea' => $this->faker->paragraphs(2, true),
                'image' => 'images/test-image.jpg',
                'boolean' => $this->faker->boolean() ? 'true' : 'false',
                'repeater' => json_encode([
                    ['title' => $this->faker->words(3, true), 'description' => $this->faker->sentence()],
                    ['title' => $this->faker->words(3, true), 'description' => $this->faker->sentence()],
                ]),
                default => $this->faker->sentence(),
            };

            return [
                'type' => $type,
                'value' => $value,
            ];
        });
    }

    public function withOptions(array $options)
    {
        return $this->state(function (array $attributes) use ($options) {
            return [
                'options' => $options,
            ];
        });
    }
}