<section class="bg-bg-light dark:bg-gray-800 py-16">
    <div class="container mx-auto px-4 max-w-6xl">
        <!-- Section Header -->
        <div class="text-center mb-12">
            <span
                class="inline-block px-3 py-1 text-sm font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full mb-3">FAQs</span>
            <h2 class="text-3xl md:text-4xl font-bold text-text-primary-light dark:text-text-primary-dark mb-4">
                Frequently Asked Questions</h2>
            <p class="text-text-secondary-light dark:text-text-secondary-dark max-w-3xl mx-auto">
                Find clear answers to commonly asked questions about locating pincodes, understanding their relevance,
                and making the most of the features available in our directory.
            </p>
        </div>

        <!-- Accordion -->
        <div
            class="bg-white dark:bg-bg-dark rounded-xl shadow-sm overflow-hidden divide-y divide-border-light dark:divide-border-dark">
            <!-- Accordion Item 1 -->
            <div class="border-b border-border-light dark:border-border-dark">
                <button
                    class="flex justify-between items-center w-full px-6 py-5 text-left focus:outline-none hover:bg-bg-light dark:hover:bg-gray-800 transition-colors duration-200"
                    onclick="toggleAccordion('collapse1')" id="heading1">
                    <span class="font-medium text-text-primary-light dark:text-text-primary-dark">1) What is the PIN
                        code for {{ $pincodes->name }}?</span>
                    <svg class="w-5 h-5 text-primary-light dark:text-primary-dark transform transition-transform duration-300"
                        id="icon-collapse1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div id="collapse1" class="px-6 pb-5 block">
                    <p class="text-text-secondary-light dark:text-text-secondary-dark">
                        The PIN code assigned to {{ $pincodes->name }} is {{ $pincodes->pincode }}.
                    </p>
                </div>
            </div>

            <!-- Accordion Item 2 -->
            <div class="border-b border-border-light dark:border-border-dark">
                <button
                    class="flex justify-between items-center w-full px-6 py-5 text-left focus:outline-none hover:bg-bg-light dark:hover:bg-gray-800 transition-colors duration-200"
                    onclick="toggleAccordion('collapse2')" id="heading2">
                    <span class="font-medium text-text-primary-light dark:text-text-primary-dark">2) Which area does the
                        PIN code {{ $pincodes->pincode }}
                        cover?</span>
                    <svg class="w-5 h-5 text-primary-light dark:text-primary-dark transform transition-transform duration-300 rotate-180"
                        id="icon-collapse2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div id="collapse2" class="px-6 pb-5 hidden">
                    <p class="text-text-secondary-light dark:text-text-secondary-dark">
                        The PIN code {{ $pincodes->pincode }} corresponds to the {{ $pincodes->name }} area.
                    </p>
                </div>
            </div>

            <!-- Accordion Item 3 -->
            <div class="border-b border-border-light dark:border-border-dark">
                <button
                    class="flex justify-between items-center w-full px-6 py-5 text-left focus:outline-none hover:bg-bg-light dark:hover:bg-gray-800 transition-colors duration-200"
                    onclick="toggleAccordion('collapse3')" id="heading3">
                    <span class="font-medium text-text-primary-light dark:text-text-primary-dark">3) Which state does
                        {{ $pincodes->name }} belong to?</span>
                    <svg class="w-5 h-5 text-primary-light dark:text-primary-dark transform transition-transform duration-300 rotate-180"
                        id="icon-collapse3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div id="collapse3" class="px-6 pb-5 hidden">
                    <p class="text-text-secondary-light dark:text-text-secondary-dark">
                        {{ $pincodes->name }} is located in the state of {{ $pincodes->state }}.
                    </p>
                </div>
            </div>

            <!-- Accordion Item 4 -->
            <div class="border-b border-border-light dark:border-border-dark">
                <button
                    class="flex justify-between items-center w-full px-6 py-5 text-left focus:outline-none hover:bg-bg-light dark:hover:bg-gray-800 transition-colors duration-200"
                    onclick="toggleAccordion('collapse4')" id="heading4">
                    <span class="font-medium text-text-primary-light dark:text-text-primary-dark">4) In which district
                        is {{ $pincodes->name }}
                        located?</span>
                    <svg class="w-5 h-5 text-primary-light dark:text-primary-dark transform transition-transform duration-300 rotate-180"
                        id="icon-collapse4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div id="collapse4" class="px-6 pb-5 hidden">
                    <p class="text-text-secondary-light dark:text-text-secondary-dark">
                        {{ $pincodes->name }} falls under the jurisdiction of {{ $pincodes->district }} district.
                    </p>
                </div>
            </div>

            <!-- PHP URL Generation -->
            @php
                $state = rawurlencode($pincodes->state);
                $stateurl = url("/pincodes/$state");

                $district1 = rawurlencode($pincodes->district);
                $districturl = $stateurl . "/$district1";

                $post_office1 = rawurlencode($pincodes->pincode);

                $url = $districturl . "/postal-code/$post_office1";
            @endphp

            <!-- Accordion Item 5 -->
            <div class="border-b border-border-light dark:border-border-dark">
                <button
                    class="flex justify-between items-center w-full px-6 py-5 text-left focus:outline-none hover:bg-bg-light dark:hover:bg-gray-800 transition-colors duration-200"
                    onclick="toggleAccordion('collapse5')" id="heading5">
                    <span class="font-medium text-text-primary-light dark:text-text-primary-dark">5) Are there other
                        post offices near
                        {{ $pincodes->name }}?</span>
                    <svg class="w-5 h-5 text-primary-light dark:text-primary-dark transform transition-transform duration-300 rotate-180"
                        id="icon-collapse5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div id="collapse5" class="px-6 pb-5 hidden">
                    <p class="text-text-secondary-light dark:text-text-secondary-dark">
                        Yes, there are nearby post offices around {{ $pincodes->name }}. You can view the complete list
                        by visiting:
                        <a href="{{ $url }}" target="_blank"
                            class="text-primary-light dark:text-primary-dark hover:text-blue-800 dark:hover:text-blue-400 hover:underline">
                            {{ $pincodes->pincode }}
                        </a>.
                    </p>
                </div>
            </div>

            <!-- Accordion Item 6 -->
            <div class="border-b border-border-light dark:border-border-dark">
                <button
                    class="flex justify-between items-center w-full px-6 py-5 text-left focus:outline-none hover:bg-bg-light dark:hover:bg-gray-800 transition-colors duration-200"
                    onclick="toggleAccordion('collapse6')" id="heading6">
                    <span class="font-medium text-text-primary-light dark:text-text-primary-dark">6) How frequently is
                        the postal data updated on this
                        website?</span>
                    <svg class="w-5 h-5 text-primary-light dark:text-primary-dark transform transition-transform duration-300 rotate-180"
                        id="icon-collapse6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7">
                        </path>
                    </svg>
                </button>
                <div id="collapse6" class="px-6 pb-5 hidden">
                    <p class="text-text-secondary-light dark:text-text-secondary-dark">
                        We update our postal data regularly to ensure the information displayed remains current and
                        accurate.
                    </p>
                </div>
            </div>
        </div>
    </div>


    <!-- JavaScript for Accordion Functionality -->
    <script>
        function toggleAccordion(id) {
            const content = document.getElementById(id);
            const icon = document.getElementById('icon-' + id);

            if (content && icon) {
                if (content.classList.contains('hidden')) {
                    content.classList.remove('hidden');
                    icon.classList.remove('rotate-180');
                } else {
                    content.classList.add('hidden');
                    icon.classList.add('rotate-180');
                }
            }
        }

        // Initialize with first item open
        document.addEventListener('DOMContentLoaded', function () {
            // Hide all items except the first one
            const items = ['collapse2', 'collapse3', 'collapse4', 'collapse5', 'collapse6', 'collapse7'];
            items.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.classList.add('hidden');
                }
            });
        });
    </script>

    <!-- JSON-LD Schema for FAQs -->
    @php
        // Helper function to safely get property values with fallbacks
        $getProperty = function ($pincodes, $property, $fallback = '') {
            return isset($pincodes->$property) && $pincodes->$property !== null ? $pincodes->$property : $fallback;
        };

        // Safely get pincode properties
        $pincodeName = $getProperty($pincodes, 'name', 'this post office');
        $pincodeNumber = $getProperty($pincodes, 'pincode', '');
        $pincodeState = $getProperty($pincodes, 'state', '');
        $pincodeDistrict = $getProperty($pincodes, 'district', '');

        $faqQuestions = [
            [
                'question' => "What is the PIN code for {$pincodeName}?",
                'answer' => "The PIN code assigned to {$pincodeName} is {$pincodeNumber}."
            ],
            [
                'question' => "Which area does the PIN code {$pincodeNumber} cover?",
                'answer' => "The PIN code {$pincodeNumber} corresponds to the {$pincodeName} area."
            ],
            [
                'question' => "Which state does {$pincodeName} belong to?",
                'answer' => "{$pincodeName} is located in the state of {$pincodeState}."
            ],
            [
                'question' => "In which district is {$pincodeName} located?",
                'answer' => "{$pincodeName} falls under the jurisdiction of {$pincodeDistrict} district."
            ],
            [
                'question' => "Are there other post offices near {$pincodeName}?",
                'answer' => "Yes, there are nearby post offices around {$pincodeName}. You can view the complete list by visiting: {$pincodeNumber}."
            ],
            [
                'question' => "How frequently is the postal data updated on this website?",
                'answer' => "We update our postal data regularly to ensure the information displayed remains current and accurate."
            ]
        ];

        // Only generate schema if we have at least one valid FAQ
        if (!empty($faqQuestions)) {
            $schema = [
                "@context" => "https://schema.org",
                "@type" => "FAQPage",
                "mainEntity" => array_map(function ($faq) {
                    return [
                        "@type" => "Question",
                        "name" => $faq['question'],
                        "acceptedAnswer" => [
                            "@type" => "Answer",
                            "text" => $faq['answer']
                        ]
                    ];
                }, $faqQuestions)
            ];
        }
    @endphp

    @if(isset($schema))
        <script type="application/ld+json">
            {!! json_encode($schema, JSON_PRETTY_PRINT) !!}
        </script>
    @endif
</section>