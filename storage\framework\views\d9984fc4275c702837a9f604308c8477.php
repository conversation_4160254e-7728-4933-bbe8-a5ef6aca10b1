<?php $__env->startSection('content'); ?>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark mb-4 sm:mb-0">
                <?php echo e(isset($page) ? 'Edit Page' : 'Create New Page'); ?>

            </h1>
            <a href="<?php echo e(route('admin.pages.index')); ?>" class="inline-flex items-center px-4 py-2 bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-medium rounded-lg transition-colors duration-150 ease-in-out shadow-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                Back to Pages
            </a>
        </div>

        <!-- Validation Errors -->
        <?php if($errors->any()): ?>
            <div class="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 dark:border-red-700 p-4 mb-6 rounded-md shadow-sm" role="alert">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-500 dark:text-red-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                            Please fix the following errors:
                        </h3>
                        <ul class="mt-2 text-sm text-red-700 dark:text-red-200 list-disc list-inside">
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                    <div class="ml-auto pl-3">
                        <div class="-mx-1.5 -my-1.5">
                            <button type="button" class="inline-flex rounded-md p-1.5 text-red-500 dark:text-red-300 hover:bg-red-100 dark:hover:bg-red-900/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-red-400" data-bs-dismiss="alert">
                                <span class="sr-only">Dismiss</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Main Form Card -->
        <div class="bg-white dark:bg-bg-dark overflow-hidden shadow-sm rounded-lg border border-border-light dark:border-border-dark">
            <div class="p-6">
                <form action="<?php echo e(isset($page) ? route('admin.pages.update', $page) : route('admin.pages.store')); ?>"
                    method="POST">
                    <?php echo csrf_field(); ?>
                    <?php if(isset($page)): ?>
                        <?php echo method_field('PUT'); ?>
                    <?php endif; ?>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Left Column (2/3 width) -->
                        <div class="lg:col-span-2 space-y-6">
                            <div>
                                <label for="title" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">
                                    Title <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="title" name="title"
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark sm:text-sm"
                                    value="<?php echo e(old('title', $page->title ?? '')); ?>" required>
                            </div>

                            <div>
                                <label for="editor-container" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">
                                    Content <span class="text-red-500">*</span>
                                </label>
                                <div id="editor-container" class="mt-1 block w-full rounded-md border border-border-light dark:border-border-dark bg-white dark:bg-bg-dark shadow-sm h-96 text-text-primary-light dark:text-text-primary-dark"></div>
                                <textarea id="content" name="content" class="hidden"><?php echo e(old('content', $page->content ?? '')); ?></textarea>
                            </div>
                        </div>

                        <!-- Right Column (1/3 width) -->
                        <div class="space-y-6">
                            <div>
                                <label for="template" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">
                                    Template <span class="text-red-500">*</span>
                                </label>
                                <select id="template" name="template"
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark sm:text-sm"
                                    required>
                                    <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>"
                                            <?php echo e(old('template', $page->template ?? '') == $key ? 'selected' : ''); ?>>
                                            <?php echo e($value); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            <div>
                                <label for="meta_title" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">
                                    Meta Title
                                </label>
                                <input type="text" id="meta_title" name="meta_title"
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark sm:text-sm"
                                    value="<?php echo e(old('meta_title', $page->meta_title ?? '')); ?>">
                                <p class="mt-1 text-xs text-text-secondary-light dark:text-text-secondary-dark">If left empty, the page title will be used.</p>
                            </div>

                            <div>
                                <label for="meta_description" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">
                                    Meta Description
                                </label>
                                <textarea id="meta_description" name="meta_description" rows="3"
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark sm:text-sm"><?php echo e(old('meta_description', $page->meta_description ?? '')); ?></textarea>
                            </div>

                            <!-- Settings Card -->
                            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg shadow-sm border border-border-light dark:border-border-dark">
                                <div class="p-4 border-b border-border-light dark:border-border-dark">
                                    <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">Settings</h3>
                                </div>
                                <div class="p-4 space-y-4">
                                    <div class="flex items-center">
                                        <label class="inline-flex items-center cursor-pointer">
                                            <input type="hidden" name="is_active" value="0">
                                            <input type="checkbox" id="is_active" name="is_active" value="1"
                                                class="sr-only peer" <?php echo e(old('is_active', $page->is_active ?? true) ? 'checked' : ''); ?>>
                                            <div class="relative w-11 h-6 bg-gray-200 dark:bg-gray-700 rounded-full peer peer-focus:ring-4 peer-focus:ring-primary-light dark:peer-focus:ring-primary-dark peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white dark:after:bg-bg-dark after:border-gray-300 dark:after:border-border-dark after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-light dark:peer-checked:bg-primary-dark"></div>
                                            <span class="ml-3 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Active</span>
                                        </label>
                                    </div>

                                    <div class="flex items-center">
                                        <label class="inline-flex items-center cursor-pointer">
                                            <input type="hidden" name="show_in_menu" value="0">
                                            <input type="checkbox" id="show_in_menu" name="show_in_menu" value="1"
                                                class="sr-only peer" <?php echo e(old('show_in_menu', $page->show_in_menu ?? true) ? 'checked' : ''); ?>>
                                            <div class="relative w-11 h-6 bg-gray-200 dark:bg-gray-700 rounded-full peer peer-focus:ring-4 peer-focus:ring-primary-light dark:peer-focus:ring-primary-dark peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white dark:after:bg-bg-dark after:border-gray-300 dark:after:border-border-dark after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-light dark:peer-checked:bg-primary-dark"></div>
                                            <span class="ml-3 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Show in Menu</span>
                                        </label>
                                    </div>

                                    <div>
                                        <label for="order" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Order</label>
                                        <input type="number" id="order" name="order"
                                            class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark sm:text-sm"
                                            value="<?php echo e(old('order', $page->order ?? 0)); ?>" min="0">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 text-right">
                        <button type="submit"
                            class="inline-flex items-center px-4 py-2 bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-white font-medium rounded-lg transition-colors duration-150 ease-in-out shadow-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                            <?php echo e(isset($page) ? 'Update Page' : 'Create Page'); ?>

                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <!-- Load Quill Editor -->
        <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
        <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Initialize Quill editor
                var quill = new Quill('#editor-container', {
                    modules: {
                        toolbar: [
                            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                            ['bold', 'italic', 'underline', 'strike'],
                            [{ 'color': [] }, { 'background': [] }],
                            [{ 'align': [] }],
                            [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                            ['link', 'image'],
                            ['clean'],
                            ['code-block']
                        ]
                    },
                    theme: 'snow'
                });

                // Set initial content
                const contentField = document.getElementById('content');
                if (contentField.value) {
                    quill.root.innerHTML = contentField.value;
                }

                // Update hidden form field on content change
                quill.on('text-change', function() {
                    contentField.value = quill.root.innerHTML;
                });

                // Also update on form submit as backup
                const form = document.querySelector('form');
                form.addEventListener('submit', function(e) {
                    const content = quill.root.innerHTML;
                    contentField.value = content;
                    
                    // Check if content is empty (only contains empty tags)
                    const textContent = quill.getText().trim();
                    if (!textContent || textContent === '') {
                        e.preventDefault();
                        alert('Please enter some content for the page.');
                        return false;
                    }
                });
            });
        </script>
    <?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/admin/pages/form.blade.php ENDPATH**/ ?>