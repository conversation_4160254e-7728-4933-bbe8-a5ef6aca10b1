<?php

use App\Models\State;
use App\Models\Tool;
use App\Models\ToolReview;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create a test user
    $this->user = User::factory()->create();
});

test('index page displays published tools', function () {
    // Create some test tools
    $publishedTools = Tool::factory()->published()->count(3)->create()->each(function ($tool, $i) { $tool->update(['slug' => 'test-tool-' . $i]); });
    $unpublishedTools = Tool::factory()->unpublished()->count(2)->create()->each(function ($tool, $i) { $tool->update(['slug' => 'test-tool-unpublished-' . $i]); });

    $response = $this->get(route('tools.index'));

    $response->assertStatus(200)
        ->assertViewIs('tools.tools')
        ->assertViewHas('tools')
        ->assertViewHas('pageTitle', 'Useful Pincode (Postal Code) Tools')
        ->assertViewHas('metaDescription', 'Useful Pincode Tools, Pincode Tools, Pincode Address Search Tool');

    // Assert only published tools are present in the view
    foreach ($publishedTools as $tool) {
        $response->assertSee($tool->name);
    }

    foreach ($unpublishedTools as $tool) {
        $response->assertDontSee($tool->name);
    }
});


test('show tool page returns 404 for non-existent tool', function () {
    $response = $this->get(route('tools.show', 'non-existent-tool'));

    $response->assertStatus(404);
});

test('show tool page returns 404 for unpublished tool', function () {
    $tool = Tool::factory()->unpublished()->create(['slug' => 'test-tool-unpublished']);

    $response = $this->get(route('tools.show', $tool->slug));

    $response->assertStatus(404);
});
