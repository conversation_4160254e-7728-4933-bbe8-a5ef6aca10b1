<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class ApiRateLimiting
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        
        if (!$user) {
            \Log::error('API Authentication failed - no user found');
            return response()->json(['error' => 'Unauthenticated.'], 401);
        }

        \Log::info('API Request Headers:', $request->headers->all());

        $key = 'api:' . $user->id;
        $maxAttempts = $user->is_premium ? 120 : 60; // Premium users get higher limits
        
        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            return response()->json([
                'error' => 'Too many requests',
                'retry_after' => RateLimiter::availableIn($key)
            ], 429);
        }

        RateLimiter::hit($key, 60); // Reset after 60 seconds

        return $next($request);
    }
} 