<!DOCTYPE html>
<html>
<head>
    <title>Pincode Distance Calculation Results</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 12px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>Pincode Distance Calculation Results</h2>
        <p>Generated on: {{ date('Y-m-d H:i:s') }}</p>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>Source Pincode</th>
                <th>Source City</th>
                <th>Source State</th>
                <th>Destination Pincode</th>
                <th>Destination City</th>
                <th>Destination State</th>
                <th>Distance (km)</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($results as $result)
                <tr>
                    <td>{{ $result['source_pincode'] }}</td>
                    <td>{{ ucfirst($result['source_city']) }}</td>
                    <td>{{ ucfirst($result['source_state']) }}</td>
                    <td>{{ $result['destination_pincode'] }}</td>
                    <td>{{ ucfirst($result['destination_city']) }}</td>
                    <td>{{ ucfirst($result['destination_state']) }}</td>
                    <td>{{ number_format($result['distance'], 2) }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
    
    <div class="footer">
        <p>This is a system-generated document.</p>
    </div>
</body>
</html>