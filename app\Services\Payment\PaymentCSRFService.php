<?php

namespace App\Services\Payment;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

class PaymentCSRFService
{
    /**
     * Token expiry time in minutes
     */
    private const TOKEN_EXPIRY_MINUTES = 30;

    /**
     * Maximum tokens per user
     */
    private const MAX_TOKENS_PER_USER = 5;

    /**
     * Generate a CSRF token for payment operations
     *
     * @param int $userId
     * @param string $operation
     * @param array $context
     * @return array
     */
    public function generatePaymentToken(int $userId, string $operation, array $context = []): array
    {
        try {
            // Clean up expired tokens first
            $this->cleanupExpiredTokens($userId);

            // Check if user has too many active tokens
            $activeTokens = $this->getActiveTokens($userId);
            if (count($activeTokens) >= self::MAX_TOKENS_PER_USER) {
                return [
                    'success' => false,
                    'error' => 'Too many active payment tokens',
                    'details' => 'Please complete or cancel existing payment operations'
                ];
            }

            // Generate unique token
            $token = $this->generateUniqueToken();
            $expiresAt = now()->addMinutes(self::TOKEN_EXPIRY_MINUTES);

            // Store token data
            $tokenData = [
                'user_id' => $userId,
                'operation' => $operation,
                'context' => $context,
                'created_at' => now()->toISOString(),
                'expires_at' => $expiresAt->toISOString(),
                'used' => false,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent()
            ];

            $cacheKey = $this->getTokenCacheKey($token);
            Cache::put($cacheKey, $tokenData, self::TOKEN_EXPIRY_MINUTES * 60);

            // Add to user's active tokens list
            $userTokensKey = $this->getUserTokensKey($userId);
            $userTokens = Cache::get($userTokensKey, []);
            $userTokens[] = $token;
            Cache::put($userTokensKey, $userTokens, self::TOKEN_EXPIRY_MINUTES * 60);

            Log::info('Payment CSRF token generated', [
                'user_id' => $userId,
                'operation' => $operation,
                'token_hash' => hash('sha256', $token),
                'expires_at' => $expiresAt
            ]);

            return [
                'success' => true,
                'token' => $token,
                'expires_at' => $expiresAt,
                'operation' => $operation
            ];

        } catch (\Exception $e) {
            Log::error('Failed to generate payment CSRF token', [
                'user_id' => $userId,
                'operation' => $operation,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to generate security token',
                'details' => $e->getMessage()
            ];
        }
    }

    /**
     * Validate and consume a CSRF token
     *
     * @param string $token
     * @param int $userId
     * @param string $operation
     * @param array $context
     * @return array
     */
    public function validateAndConsumeToken(string $token, int $userId, string $operation, array $context = []): array
    {
        try {
            $cacheKey = $this->getTokenCacheKey($token);
            $tokenData = Cache::get($cacheKey);

            if (!$tokenData) {
                Log::warning('Invalid or expired payment CSRF token', [
                    'user_id' => $userId,
                    'operation' => $operation,
                    'token_hash' => hash('sha256', $token)
                ]);

                return [
                    'valid' => false,
                    'error' => 'Invalid or expired security token',
                    'details' => 'Please refresh the page and try again'
                ];
            }

            // Validate token data
            $validation = $this->validateTokenData($tokenData, $userId, $operation, $context);
            if (!$validation['valid']) {
                return $validation;
            }

            // Check if token is already used
            if ($tokenData['used']) {
                Log::warning('Attempt to reuse payment CSRF token', [
                    'user_id' => $userId,
                    'operation' => $operation,
                    'token_hash' => hash('sha256', $token)
                ]);

                return [
                    'valid' => false,
                    'error' => 'Security token already used',
                    'details' => 'Please generate a new token'
                ];
            }

            // Mark token as used
            $tokenData['used'] = true;
            $tokenData['used_at'] = now()->toISOString();
            Cache::put($cacheKey, $tokenData, self::TOKEN_EXPIRY_MINUTES * 60);

            // Remove from user's active tokens
            $this->removeTokenFromUserList($userId, $token);

            Log::info('Payment CSRF token validated and consumed', [
                'user_id' => $userId,
                'operation' => $operation,
                'token_hash' => hash('sha256', $token)
            ]);

            return [
                'valid' => true,
                'token_data' => $tokenData
            ];

        } catch (\Exception $e) {
            Log::error('Failed to validate payment CSRF token', [
                'user_id' => $userId,
                'operation' => $operation,
                'token_hash' => hash('sha256', $token),
                'error' => $e->getMessage()
            ]);

            return [
                'valid' => false,
                'error' => 'Token validation failed',
                'details' => $e->getMessage()
            ];
        }
    }

    /**
     * Invalidate a specific token
     *
     * @param string $token
     * @param int $userId
     * @return bool
     */
    public function invalidateToken(string $token, int $userId): bool
    {
        try {
            $cacheKey = $this->getTokenCacheKey($token);
            $tokenData = Cache::get($cacheKey);

            if ($tokenData && $tokenData['user_id'] === $userId) {
                Cache::forget($cacheKey);
                $this->removeTokenFromUserList($userId, $token);

                Log::info('Payment CSRF token invalidated', [
                    'user_id' => $userId,
                    'token_hash' => hash('sha256', $token)
                ]);

                return true;
            }

            return false;

        } catch (\Exception $e) {
            Log::error('Failed to invalidate payment CSRF token', [
                'user_id' => $userId,
                'token_hash' => hash('sha256', $token),
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Invalidate all tokens for a user
     *
     * @param int $userId
     * @return int
     */
    public function invalidateAllUserTokens(int $userId): int
    {
        try {
            $userTokensKey = $this->getUserTokensKey($userId);
            $userTokens = Cache::get($userTokensKey, []);
            $invalidatedCount = 0;

            foreach ($userTokens as $token) {
                $cacheKey = $this->getTokenCacheKey($token);
                if (Cache::forget($cacheKey)) {
                    $invalidatedCount++;
                }
            }

            Cache::forget($userTokensKey);

            Log::info('All payment CSRF tokens invalidated for user', [
                'user_id' => $userId,
                'invalidated_count' => $invalidatedCount
            ]);

            return $invalidatedCount;

        } catch (\Exception $e) {
            Log::error('Failed to invalidate all user payment CSRF tokens', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }

    /**
     * Get active tokens for a user
     *
     * @param int $userId
     * @return array
     */
    public function getActiveTokens(int $userId): array
    {
        try {
            $userTokensKey = $this->getUserTokensKey($userId);
            $userTokens = Cache::get($userTokensKey, []);
            $activeTokens = [];

            foreach ($userTokens as $token) {
                $cacheKey = $this->getTokenCacheKey($token);
                $tokenData = Cache::get($cacheKey);

                if ($tokenData && !$tokenData['used']) {
                    $activeTokens[] = [
                        'token_hash' => hash('sha256', $token),
                        'operation' => $tokenData['operation'],
                        'created_at' => $tokenData['created_at'],
                        'expires_at' => $tokenData['expires_at']
                    ];
                }
            }

            return $activeTokens;

        } catch (\Exception $e) {
            Log::error('Failed to get active payment CSRF tokens', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Generate a unique token
     *
     * @return string
     */
    private function generateUniqueToken(): string
    {
        return 'payment_' . Str::random(32) . '_' . time();
    }

    /**
     * Get cache key for token
     *
     * @param string $token
     * @return string
     */
    private function getTokenCacheKey(string $token): string
    {
        return 'payment_csrf_token:' . hash('sha256', $token);
    }

    /**
     * Get cache key for user tokens list
     *
     * @param int $userId
     * @return string
     */
    private function getUserTokensKey(int $userId): string
    {
        return "payment_csrf_user_tokens:{$userId}";
    }

    /**
     * Validate token data
     *
     * @param array $tokenData
     * @param int $userId
     * @param string $operation
     * @param array $context
     * @return array
     */
    private function validateTokenData(array $tokenData, int $userId, string $operation, array $context): array
    {
        // Validate user ID
        if ($tokenData['user_id'] !== $userId) {
            return [
                'valid' => false,
                'error' => 'Token user mismatch',
                'details' => 'Security token belongs to different user'
            ];
        }

        // Validate operation
        if ($tokenData['operation'] !== $operation) {
            return [
                'valid' => false,
                'error' => 'Token operation mismatch',
                'details' => 'Security token is for different operation'
            ];
        }

        // Validate expiry
        $expiresAt = Carbon::parse($tokenData['expires_at']);
        if ($expiresAt->isPast()) {
            return [
                'valid' => false,
                'error' => 'Token expired',
                'details' => 'Security token has expired'
            ];
        }

        // Validate IP address (optional, can be disabled for mobile users)
        if (config('payment.csrf.validate_ip', false)) {
            if ($tokenData['ip_address'] !== request()->ip()) {
                return [
                    'valid' => false,
                    'error' => 'IP address mismatch',
                    'details' => 'Security token was issued for different IP address'
                ];
            }
        }

        return ['valid' => true];
    }

    /**
     * Remove token from user's active tokens list
     *
     * @param int $userId
     * @param string $token
     */
    private function removeTokenFromUserList(int $userId, string $token): void
    {
        try {
            $userTokensKey = $this->getUserTokensKey($userId);
            $userTokens = Cache::get($userTokensKey, []);
            
            $userTokens = array_filter($userTokens, function($t) use ($token) {
                return $t !== $token;
            });

            if (empty($userTokens)) {
                Cache::forget($userTokensKey);
            } else {
                Cache::put($userTokensKey, array_values($userTokens), self::TOKEN_EXPIRY_MINUTES * 60);
            }

        } catch (\Exception $e) {
            Log::error('Failed to remove token from user list', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Clean up expired tokens for a user
     *
     * @param int $userId
     */
    private function cleanupExpiredTokens(int $userId): void
    {
        try {
            $userTokensKey = $this->getUserTokensKey($userId);
            $userTokens = Cache::get($userTokensKey, []);
            $validTokens = [];

            foreach ($userTokens as $token) {
                $cacheKey = $this->getTokenCacheKey($token);
                $tokenData = Cache::get($cacheKey);

                if ($tokenData) {
                    $expiresAt = Carbon::parse($tokenData['expires_at']);
                    if ($expiresAt->isFuture()) {
                        $validTokens[] = $token;
                    } else {
                        Cache::forget($cacheKey);
                    }
                }
            }

            if (empty($validTokens)) {
                Cache::forget($userTokensKey);
            } else {
                Cache::put($userTokensKey, $validTokens, self::TOKEN_EXPIRY_MINUTES * 60);
            }

        } catch (\Exception $e) {
            Log::error('Failed to cleanup expired payment CSRF tokens', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }
}