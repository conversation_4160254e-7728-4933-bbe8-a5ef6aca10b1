<?php

namespace App\Http\Controllers\Tools;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;


class CoordinatesDistanceController extends Controller
{
    public function calculateDistance(Request $request)
    {
        $request->validate([
            'from_lat' => 'required|numeric',
            'from_lng' => 'required|numeric',
            'to_lat' => 'required|numeric',
            'to_lng' => 'required|numeric',
        ]);

        $from_lat = $request->input('from_lat');
        $from_lng = $request->input('from_lng');
        $to_lat = $request->input('to_lat');
        $to_lng = $request->input('to_lng');

        $result = $this->calculate($from_lat, $from_lng, $to_lat, $to_lng);

        return response()->json([
            'distance' => [
                'km' => $result['distance'],
                'miles' => round($result['distance'] * 0.621371, 2),
                'nautical_miles' => round($result['distance'] * 0.539957, 2),
            ],
            'from' => [
                'latitude' => $from_lat,
                'longitude' => $from_lng,
                'formatted' => $this->formatCoordinate($from_lat, $from_lng),
            ],
            'to' => [
                'latitude' => $to_lat,
                'longitude' => $to_lng,
                'formatted' => $this->formatCoordinate($to_lat, $to_lng),
            ],
            'bearing' => $result['bearing'],
            'midpoint' => $this->calculateMidpoint($from_lat, $from_lng, $to_lat, $to_lng),
        ]);
    }

    private function calculate($lat1, $lon1, $lat2, $lon2)
    {
        $earthRadius = 6371; // in kilometers

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat / 2) * sin($dLat / 2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon / 2) * sin($dLon / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        $distance = $earthRadius * $c;

        // Calculate bearing
        $y = sin($dLon) * cos(deg2rad($lat2));
        $x = cos(deg2rad($lat1)) * sin(deg2rad($lat2)) - sin(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos($dLon);
        $bearing = atan2($y, $x);
        $bearing = (rad2deg($bearing) + 360) % 360;

        return [
            'distance' => round($distance, 2),
            'bearing' => round($bearing, 2),
        ];
    }

    private function formatCoordinate($lat, $lng)
    {
        $lat_direction = $lat >= 0 ? 'N' : 'S';
        $lng_direction = $lng >= 0 ? 'E' : 'W';
        return abs($lat) . '° ' . $lat_direction . ', ' . abs($lng) . '° ' . $lng_direction;
    }

    private function calculateMidpoint($lat1, $lon1, $lat2, $lon2)
    {
        $lat1 = deg2rad($lat1);
        $lon1 = deg2rad($lon1);
        $lat2 = deg2rad($lat2);
        $lon2 = deg2rad($lon2);

        $bx = cos($lat2) * cos($lon2 - $lon1);
        $by = cos($lat2) * sin($lon2 - $lon1);

        $midLat = atan2(
            sin($lat1) + sin($lat2),
            sqrt((cos($lat1) + $bx) * (cos($lat1) + $bx) + $by * $by)
        );
        $midLon = $lon1 + atan2($by, cos($lat1) + $bx);

        return [
            'latitude' => round(rad2deg($midLat), 6),
            'longitude' => round(rad2deg($midLon), 6),
        ];
    }
}