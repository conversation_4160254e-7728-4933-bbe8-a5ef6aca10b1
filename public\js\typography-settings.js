// This file will be included in your main layout
document.addEventListener('DOMContentLoaded', function() {
    // Get typography settings from meta tags (we'll add these in the layout)
    const primaryFont = document.querySelector('meta[name="typography-primary-font"]')?.content;
    const secondaryFont = document.querySelector('meta[name="typography-secondary-font"]')?.content;
    const h1Size = document.querySelector('meta[name="typography-h1-size"]')?.content;
    const h2Size = document.querySelector('meta[name="typography-h2-size"]')?.content;
    
    // Update CSS variables if settings exist
    if (primaryFont) document.documentElement.style.setProperty('--font-primary', primaryFont);
    if (secondaryFont) document.documentElement.style.setProperty('--font-secondary', secondaryFont);
    if (h1Size) document.documentElement.style.setProperty('--font-size-h1', h1Size);
    if (h2Size) document.documentElement.style.setProperty('--font-size-h2', h2Size);
});