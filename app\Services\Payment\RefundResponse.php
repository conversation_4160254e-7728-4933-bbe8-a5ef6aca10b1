<?php

namespace App\Services\Payment;

class RefundResponse
{
    public bool $success;
    public ?string $refundId = null;
    public ?string $paymentId = null;
    public ?float $refundAmount = null;
    public ?string $currency = null;
    public ?string $status = null;
    public ?string $message = null;
    public ?string $errorCode = null;
    public ?array $gatewayResponse = null;
    public ?array $metadata = null;

    public function __construct(bool $success = false)
    {
        $this->success = $success;
    }

    /**
     * Create a successful refund response.
     */
    public static function success(array $data = []): self
    {
        $response = new self(true);
        
        if (isset($data['refund_id'])) {
            $response->refundId = $data['refund_id'];
        }
        
        if (isset($data['payment_id'])) {
            $response->paymentId = $data['payment_id'];
        }
        
        if (isset($data['refund_amount'])) {
            $response->refundAmount = $data['refund_amount'];
        }
        
        if (isset($data['currency'])) {
            $response->currency = $data['currency'];
        }
        
        if (isset($data['status'])) {
            $response->status = $data['status'];
        }
        
        if (isset($data['message'])) {
            $response->message = $data['message'];
        }
        
        if (isset($data['gateway_response'])) {
            $response->gatewayResponse = $data['gateway_response'];
        }
        
        if (isset($data['metadata'])) {
            $response->metadata = $data['metadata'];
        }
        
        return $response;
    }

    /**
     * Create an error refund response.
     */
    public static function error(string $message, ?string $errorCode = null, ?array $gatewayResponse = null): self
    {
        $response = new self(false);
        $response->message = $message;
        $response->errorCode = $errorCode;
        $response->gatewayResponse = $gatewayResponse;
        
        return $response;
    }

    /**
     * Set refund ID.
     */
    public function setRefundId(string $refundId): self
    {
        $this->refundId = $refundId;
        return $this;
    }

    /**
     * Set payment ID.
     */
    public function setPaymentId(string $paymentId): self
    {
        $this->paymentId = $paymentId;
        return $this;
    }

    /**
     * Set refund amount.
     */
    public function setRefundAmount(float $refundAmount): self
    {
        $this->refundAmount = $refundAmount;
        return $this;
    }

    /**
     * Set currency.
     */
    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;
        return $this;
    }

    /**
     * Set status.
     */
    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    /**
     * Set message.
     */
    public function setMessage(string $message): self
    {
        $this->message = $message;
        return $this;
    }

    /**
     * Set gateway response data.
     */
    public function setGatewayResponse(array $gatewayResponse): self
    {
        $this->gatewayResponse = $gatewayResponse;
        return $this;
    }

    /**
     * Set metadata.
     */
    public function setMetadata(array $metadata): self
    {
        $this->metadata = $metadata;
        return $this;
    }

    /**
     * Check if the response is successful.
     */
    public function isSuccess(): bool
    {
        return $this->success;
    }

    /**
     * Check if the response is an error.
     */
    public function isError(): bool
    {
        return !$this->success;
    }

    /**
     * Get the response as an array.
     */
    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'refund_id' => $this->refundId,
            'payment_id' => $this->paymentId,
            'refund_amount' => $this->refundAmount,
            'currency' => $this->currency,
            'status' => $this->status,
            'message' => $this->message,
            'error_code' => $this->errorCode,
            'gateway_response' => $this->gatewayResponse,
            'metadata' => $this->metadata,
        ];
    }

    /**
     * Convert to JSON.
     */
    public function toJson(): string
    {
        return json_encode($this->toArray());
    }
}