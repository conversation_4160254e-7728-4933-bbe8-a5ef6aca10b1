@extends('layouts.app')
@push('schema')
    <script type="application/ld+json">
        {
        "@context": "https://schema.org",
        "@type": "TechArticle",
        "headline": "API Documentation Information",
        "description": "Comprehensive API documentation for our services",
        "author": {
            "@type": "Organization",
            "name": "Your Company Name"
        },
        "datePublished": "{{ date('Y-m-d') }}",
        "dateModified": "{{ date('Y-m-d') }}",
        "articleSection": "API Documentation"
        }
    </script>
@endpush
@section('content')
    <div class="bg-gradient-to-b from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 min-h-screen pt-16 pb-20">
        <div class="container mx-auto px-4 lg:px-8">
            <div class="flex flex-col lg:flex-row gap-8">
                <!-- Sidebar - Desktop -->
                @include('api-docs.partials.api-sidebar')

                <!-- Main Content -->
                <div class="flex-1 max-w-3xl mx-auto lg:mx-0">
                    <div
                        class="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 p-4 md:p-6 lg:p-8">
                        <section id="introduction" class="mb-12">
                            <div
                                class="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-xl p-4 md:p-6 border border-indigo-100 dark:border-indigo-800">
                                <h1
                                    class="text-2xl md:text-3xl lg:text-4xl font-bold text-slate-900 dark:text-white mb-4 md:mb-6 pb-2 border-b border-indigo-500/30">
                                    Pincode API Documentation
                                </h1>
                                <div class="flex flex-col sm:flex-row sm:items-start gap-4">
                                    <div class="flex-shrink-0 p-3 bg-indigo-100 dark:bg-indigo-900/50 rounded-lg">
                                        <svg class="w-6 h-6 md:w-8 md:h-8 text-indigo-600 dark:text-indigo-400"
                                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6-3l5.447 2.724A1 1 0 0121 7.618v10.764a1 1 0 01-1.447.894L15 17m-6-3l6-3" />
                                        </svg>
                                    </div>
                                    <p class="text-sm md:text-base text-slate-700 dark:text-slate-300 leading-relaxed">
                                        The Pincode API provides a comprehensive set of endpoints to retrieve information
                                        about pincodes, states,
                                        districts, and tehsils in India. This API is designed for applications that require
                                        accurate and up-to-date
                                        location-based data for Indian postal addresses.
                                    </p>
                                </div>
                            </div>
                        </section>

                        <section id="endpoints" class="mb-12">
                            <h2 class="text-xl md:text-2xl font-bold text-indigo-600 dark:text-indigo-400 mb-6">Endpoints
                            </h2>

                            <!-- Endpoint 1 -->
                            <div class="mb-10 pb-8 border-b border-slate-200 dark:border-slate-700">
                                <h3 class="text-lg md:text-xl font-bold text-slate-800 dark:text-slate-200 mb-3">1. Get
                                    Pincode
                                    Information</h3>
                                <p class="text-sm md:text-base text-slate-700 dark:text-slate-300 leading-relaxed mb-4">
                                    Retrieve details about a specific pincode. Standalone API endpoint to retrieve pincode
                                    details of
                                    a specific pincode number.
                                </p>

                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                    <div class="bg-slate-50 dark:bg-slate-900/50 p-3 rounded-lg">
                                        <span class="font-semibold text-slate-900 dark:text-white text-sm">Endpoint:</span>
                                        <div
                                            class="mt-1 text-emerald-600 dark:text-emerald-400 font-mono text-xs md:text-sm break-all">
                                            /pincode/{pincode_number}</div>
                                    </div>
                                    <div class="bg-slate-50 dark:bg-slate-900/50 p-3 rounded-lg">
                                        <span class="font-semibold text-slate-900 dark:text-white text-sm">Method:</span>
                                        <div class="mt-1 text-blue-600 dark:text-blue-400 font-mono text-xs md:text-sm">GET
                                        </div>
                                    </div>
                                    <div class="bg-slate-50 dark:bg-slate-900/50 p-3 rounded-lg">
                                        <span
                                            class="font-semibold text-slate-900 dark:text-white text-sm">Parameters:</span>
                                        <div class="mt-1 text-slate-700 dark:text-slate-300 text-xs md:text-sm">pincode
                                            (string, required)
                                        </div>
                                    </div>
                                </div>

                                <h4 class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">
                                    Request:
                                </h4>
                                <div class="bg-slate-900 rounded-lg overflow-hidden">
                                    <div class="bg-slate-800 px-3 py-2 text-xs text-slate-300 border-b border-slate-700">
                                        HTTP Request
                                    </div>
                                    <pre class="language-http p-3 md:p-4 text-xs md:text-sm overflow-x-auto"><code class="whitespace-pre-wrap break-all">GET /pincode/441901
Authorization: Bearer YOUR_API_KEY</code></pre>
                                </div>

                                <h4 class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">
                                    Response (in
                                    JSON Format):</h4>
                                <div class="bg-slate-900 rounded-lg overflow-hidden">
                                    <div
                                        class="bg-slate-800 px-3 py-2 text-xs text-slate-300 border-b border-slate-700 flex justify-between items-center">
                                        <span>JSON Response</span>
                                        <button onclick="copyToClipboard(this)"
                                            class="text-slate-400 hover:text-white transition-colors">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z">
                                                </path>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="relative">
                                        <pre class="language-json p-3 md:p-4 text-xs md:text-sm overflow-x-auto max-h-96 overflow-y-auto"><code class="whitespace-pre-wrap break-words">{
    "status": "success",
    "data": [
        {
            "postalCircle": "Maharashtra Circle",
            "postalCode": 441901,
            "postalRegion": "Nagpur Region",
            "postalDivision": "Nagpur Moffusil Division",
            "postOfficeName": "Dawki",
            "branchType": "BO",
            "deliveryStatus": "Delivery",
            "districtName": "GONDIA",
            "stateName": "MAHARASHTRA"
        },
        {
            "postalCircle": "Maharashtra Circle",
            "postalCode": 441901,
            "postalRegion": "Nagpur Region",
            "postalDivision": "Nagpur Moffusil Division",
            "postOfficeName": "Kadikasa",
            "branchType": "BO",
            "deliveryStatus": "Delivery",
            "districtName": "GONDIA",
            "stateName": "MAHARASHTRA"
        },
        {
            "postalCircle": "Maharashtra Circle",
            "postalCode": 441901,
            "postalRegion": "Nagpur Region",
            "postalDivision": "Nagpur Moffusil Division",
            "postOfficeName": "Nilaj",
            "branchType": "BO",
            "deliveryStatus": "Delivery",
            "districtName": "GONDIA",
            "stateName": "MAHARASHTRA"
        },
        {
            "postalCircle": "Maharashtra Circle",
            "postalCode": 441901,
            "postalRegion": "Nagpur Region",
            "postalDivision": "Nagpur Moffusil Division",
            "postOfficeName": "Deori(Gondia)",
            "branchType": "PO",
            "deliveryStatus": "Delivery",
            "districtName": "GONDIA",
            "stateName": "MAHARASHTRA"
        }
    ]
}</code></pre>
                                    </div>
                                </div>

                                <h4 class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">
                                    Error Codes:
                                </h4>
                                <div class="space-y-2">
                                    <div
                                        class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                                        <p class="text-sm md:text-base text-slate-700 dark:text-slate-300">
                                            <span class="font-semibold text-red-600 dark:text-red-400">404 Not Found:</span>
                                            Pincode not found.
                                        </p>
                                    </div>
                                    <div
                                        class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                                        <p class="text-sm md:text-base text-slate-700 dark:text-slate-300">
                                            <span class="font-semibold text-yellow-600 dark:text-yellow-400">429 Too Many
                                                Requests:</span> Rate limit exceeded.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Endpoint 2 -->
                            <div class="mb-10 pb-8 border-b border-slate-200 dark:border-slate-700">
                                <h3 class="text-xl font-bold text-slate-800 dark:text-slate-200 mb-3">2. Get State Names
                                </h3>
                                <p class="text-slate-700 dark:text-slate-300 leading-relaxed mb-4">
                                    Retrieve a list of states matching the provided state name.
                                </p>

                                <div class="grid grid-cols-1 md:grid-cols-3 gap-2 mb-4">
                                    <div>
                                        <span class="font-semibold text-slate-900 dark:text-white">Endpoint:</span>
                                        <div class="mt-1 text-emerald-600 dark:text-emerald-400 font-mono">
                                            /state/{stateName}</div>
                                    </div>
                                    <div>
                                        <span class="font-semibold text-slate-900 dark:text-white">Method:</span>
                                        <div class="mt-1 text-blue-600 dark:text-blue-400 font-mono">GET</div>
                                    </div>
                                    <div>
                                        <span class="font-semibold text-slate-900 dark:text-white">Parameters:</span>
                                        <div class="mt-1 text-slate-700 dark:text-slate-300">stateName (string, required)
                                        </div>
                                    </div>
                                </div>

                                <h4 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">Request
                                    Example:</h4>
                                <div class="bg-slate-900 rounded-lg overflow-hidden">
                                    <pre class="language-json p-3 md:p-4 text-xs md:text-sm overflow-x-auto max-h-96 overflow-y-auto"><code class="whitespace-pre-wrap break-words">GET /pincodes/state/pradesh
Authorization: Bearer YOUR_API_KEY</code></pre>
                                </div>

                                <h4 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">Response (in
                                    JSON Format):</h4>
                                <div class="bg-slate-900 rounded-lg overflow-hidden">
                                    <pre class="language-json p-3 md:p-4 text-xs md:text-sm overflow-x-auto max-h-96 overflow-y-auto"><code class="whitespace-pre-wrap break-words">[
    {
        "id": 1,
        "name": "Andhra Pradesh"
    },
    {
        "id": 2,
        "name": "Arunachal Pradesh"
    },
    {
        "id": 13,
        "name": "Himachal Pradesh"
    },
    {
        "id": 19,
        "name": "Madhya Pradesh"
    },
    {
        "id": 34,
        "name": "Uttar Pradesh"
    }
]</code></pre>
                                </div>

                                <h4 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">Error Codes:
                                </h4>
                                <div class="space-y-2">
                                    <p class="text-slate-700 dark:text-slate-300">
                                        <span class="font-semibold text-red-600 dark:text-red-400">404 Not Found:</span>
                                        State not found.
                                    </p>
                                </div>
                            </div>

                            <!-- Endpoint 3 -->
                            <div class="mb-10 pb-8 border-b border-slate-200 dark:border-slate-700">
                                <h3 class="text-xl font-bold text-slate-800 dark:text-slate-200 mb-3">3. Get District Names
                                </h3>
                                <p class="text-slate-700 dark:text-slate-300 leading-relaxed mb-4">
                                    Retrieve a list of districts matching the provided district name.
                                </p>

                                <div class="grid grid-cols-1 md:grid-cols-3 gap-2 mb-4">
                                    <div>
                                        <span class="font-semibold text-slate-900 dark:text-white">Endpoint:</span>
                                        <div class="mt-1 text-emerald-600 dark:text-emerald-400 font-mono">
                                            /district/{districtName}</div>
                                    </div>
                                    <div>
                                        <span class="font-semibold text-slate-900 dark:text-white">Method:</span>
                                        <div class="mt-1 text-blue-600 dark:text-blue-400 font-mono">GET</div>
                                    </div>
                                    <div>
                                        <span class="font-semibold text-slate-900 dark:text-white">Parameters:</span>
                                        <div class="mt-1 text-slate-700 dark:text-slate-300">districtName (string,
                                            required)</div>
                                    </div>
                                </div>

                                <h4 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">Request
                                    Example:</h4>
                                <div class="bg-slate-900 rounded-lg overflow-hidden">
                                    <pre class="language-json p-3 md:p-4 text-xs md:text-sm overflow-x-auto max-h-96 overflow-y-auto"><code class="whitespace-pre-wrap break-words">GET /pincodes/district/gondia
Authorization: Bearer YOUR_API_KEY</code></pre>
                                </div>

                                <h4 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">Response
                                    Example:</h4>
                                <div class="bg-slate-900 rounded-lg overflow-hidden">
                                    <pre class="language-json p-3 md:p-4 text-xs md:text-sm overflow-x-auto max-h-96 overflow-y-auto"><code class="whitespace-pre-wrap break-words">[
    "Bengaluru Rural",
    "Bengaluru Urban"
]</code></pre>
                                </div>
                            </div>

                            <!-- Endpoint 4 -->
                            <div class="mb-10 pb-8 border-b border-slate-200 dark:border-slate-700">
                                <h3 class="text-lg md:text-xl font-bold text-slate-800 dark:text-slate-200 mb-3">4. Get Tehsil Names
                                </h3>
                                <p class="text-sm md:text-base text-slate-700 dark:text-slate-300 leading-relaxed mb-4">
                                    Retrieve a list of tehsils matching the provided state, district, and tehsil names.
                                </p>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div class="bg-slate-50 dark:bg-slate-900/50 p-3 rounded-lg">
                                        <span class="font-semibold text-slate-900 dark:text-white text-sm">Endpoint:</span>
                                        <div class="mt-1 text-emerald-600 dark:text-emerald-400 font-mono text-xs md:text-sm break-all">
                                            /pincodes/tehsil/{state_name}/{district_name}/{tehsil_name}
                                        </div>
                                    </div>
                                    <div class="bg-slate-50 dark:bg-slate-900/50 p-3 rounded-lg">
                                        <span class="font-semibold text-slate-900 dark:text-white text-sm">Method:</span>
                                        <div class="mt-1 text-blue-600 dark:text-blue-400 font-mono text-xs md:text-sm">GET</div>
                                    </div>
                                </div>

                                <h4 class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-4 mb-2">Parameters:</h4>
                                <div class="bg-slate-50 dark:bg-slate-900/50 rounded-lg p-3 md:p-4 mb-4">
                                    <ul class="list-disc pl-4 md:pl-5 space-y-2 text-slate-700 dark:text-slate-300">
                                        <li class="text-sm md:text-base"><span class="font-semibold">state_name</span> (string, required): The name of the state.</li>
                                        <li class="text-sm md:text-base"><span class="font-semibold">district_name</span> (string, required): The name of the district.</li>
                                        <li class="text-sm md:text-base"><span class="font-semibold">tehsil_name</span> (string, required): Partial or full name of the tehsil.</li>
                                    </ul>
                                </div>

                                <h4 class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">Request Example:</h4>
                                <div class="bg-slate-900 rounded-lg overflow-hidden">
                                    <div class="bg-slate-800 px-3 py-2 text-xs text-slate-300 border-b border-slate-700">HTTP Request</div>
                                    <pre class="language-http p-3 md:p-4 text-xs md:text-sm overflow-x-auto"><code class="whitespace-pre-wrap break-all">GET /pincodes/tehsil/maharashtra/gondia/gaon
Authorization: Bearer YOUR_API_KEY</code></pre>
                                </div>

                                <h4 class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">Response Example:</h4>
                                <div class="bg-slate-900 rounded-lg overflow-hidden">
                                    <div class="bg-slate-800 px-3 py-2 text-xs text-slate-300 border-b border-slate-700">JSON Response</div>
                                    <pre class="language-json p-3 md:p-4 text-xs md:text-sm overflow-x-auto max-h-96 overflow-y-auto"><code class="whitespace-pre-wrap break-words">[
  "Amgaon",
  "Arjuni Morgaon",
  "Goregaon(Gondia)",
  "Navegaon Bandh"
]</code></pre>
                                </div>
                            </div>

                            <!-- Endpoint 5 -->
                            <div class="mb-10">
                                <h3 class="text-lg md:text-xl font-bold text-slate-800 dark:text-slate-200 mb-3">5. Get
                                    Pincodes by
                                    using Post Office Name</h3>
                                <p class="text-sm md:text-base text-slate-700 dark:text-slate-300 leading-relaxed mb-4">
                                    Retrieve post office details with a specific post office name. (Includes State name,
                                    District
                                    name, Tehsil name)
                                </p>

                                <div
                                    class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3 md:p-4 mb-6">
                                    <h4 class="text-base md:text-lg font-semibold text-amber-800 dark:text-amber-400 mb-2">
                                        Why do we
                                        require state_name, district_name and tehsil_name along with post_office_name?</h4>
                                    <p class="text-sm md:text-base text-amber-700 dark:text-amber-300">
                                        There are many post offices with the same name. To avoid conflicts between two post
                                        office
                                        names, we require state_name, district_name and tehsil_name along with
                                        post_office_name to retrieve
                                        accurate data.
                                    </p>
                                    <p class="text-sm md:text-base text-amber-700 dark:text-amber-300 mt-2">
                                        After getting State name, District and Tehsil name (using autocomplete
                                        functionality), you
                                        can easily get Post office details by post office name.
                                    </p>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div class="bg-slate-50 dark:bg-slate-900/50 p-3 rounded-lg">
                                        <span class="font-semibold text-slate-900 dark:text-white text-sm">Endpoint:</span>
                                        <div
                                            class="mt-1 text-emerald-600 dark:text-emerald-400 font-mono text-xs md:text-sm break-all">
                                            /pincodes/details/{state_name}/{district_name}/{post_office_name}</div>
                                    </div>
                                    <div class="bg-slate-50 dark:bg-slate-900/50 p-3 rounded-lg">
                                        <span class="font-semibold text-slate-900 dark:text-white text-sm">Method:</span>
                                        <div class="mt-1 text-blue-600 dark:text-blue-400 font-mono text-xs md:text-sm">GET
                                        </div>
                                    </div>
                                </div>

                                <h4
                                    class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-4 mb-2">
                                    Parameters:
                                </h4>
                                <div class="bg-slate-50 dark:bg-slate-900/50 rounded-lg p-3 md:p-4">
                                    <ul class="list-disc pl-4 md:pl-5 space-y-2 text-slate-700 dark:text-slate-300">
                                        <li class="text-sm md:text-base">
                                            <span class="font-semibold">state_name</span> (string, required): The name of
                                            the
                                            state.
                                        </li>
                                        <li class="text-sm md:text-base">
                                            <span class="font-semibold">district_name</span> (string, required): The name
                                            of
                                            the district.
                                        </li>
                                        <li class="text-sm md:text-base">
                                            <span class="font-semibold">post_office_name</span> (string, required): Full
                                            name
                                            of the post office (Exact match NOT partial match).
                                        </li>
                                    </ul>
                                </div>

                                <h4
                                    class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">
                                    Request
                                    Example:</h4>
                                <div class="bg-slate-900 rounded-lg overflow-hidden">
                                    <div
                                        class="bg-slate-800 px-3 py-2 text-xs text-slate-300 border-b border-slate-700 flex justify-between items-center">
                                        <span>HTTP Request</span>
                                        <button onclick="copyToClipboard(this)"
                                            class="text-slate-400 hover:text-white transition-colors">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z">
                                                </path>
                                            </svg>
                                        </button>
                                    </div>
                                    <pre class="language-http p-3 md:p-4 text-xs md:text-sm overflow-x-auto"><code class="whitespace-pre-wrap break-all">GET /pincodes/details/maharashtra/gondia/Dongargaon
Authorization: Bearer YOUR_API_KEY</code></pre>
                                </div>

                                <h4
                                    class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">
                                    Response
                                    Example:</h4>
                                <div class="bg-slate-900 rounded-lg overflow-hidden">
                                    <div
                                        class="bg-slate-800 px-3 py-2 text-xs text-slate-300 border-b border-slate-700 flex justify-between items-center">
                                        <span>JSON Response</span>
                                        <button onclick="copyToClipboard(this)"
                                            class="text-slate-400 hover:text-white transition-colors">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z">
                                                </path>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="relative">
                                        <pre class="language-json p-3 md:p-4 text-xs md:text-sm overflow-x-auto max-h-96 overflow-y-auto"><code class="whitespace-pre-wrap break-words">{
"status": "success",
"data": {
    "postalCircle": "Maharashtra Circle",
    "postalCode": 441614,
    "postalRegion": "Nagpur Region",
    "postalDivision": "Nagpur Moffusil Division",
    "postOfficeName": "Dongargaon",
    "branchType": "BO",
    "deliveryStatus": "Delivery",
    "districtName": "GONDIA",
    "stateName": "MAHARASHTRA",
    "location": {
        "latitude": "21.3678710",
        "longitude": "79.9933980"
    }
}
}</code></pre>
                                    </div>
                                </div>
                            </div>
                        </section>

                        @include('api-docs.partials.digipin')

                        <section id="error-handling" class="mb-12">
                            <h2 class="text-2xl font-bold text-indigo-600 dark:text-indigo-400 mb-4">Error Handling</h2>
                            <p class="text-slate-700 dark:text-slate-300 leading-relaxed mb-4">
                                The API uses standard HTTP status codes to indicate the success or failure of a request.
                                Common error codes include:
                            </p>
                            <ul class="list-disc pl-5 space-y-2 text-slate-700 dark:text-slate-300">
                                <li><span class="font-semibold text-red-600 dark:text-red-400">400 Bad Request:</span> The
                                    request was malformed or invalid.</li>
                                <li><span class="font-semibold text-red-600 dark:text-red-400">401 Unauthorized:</span> The
                                    request lacked proper authentication.</li>
                                <li><span class="font-semibold text-red-600 dark:text-red-400">403 Forbidden:</span> The
                                    user does not have permission to access the resource.</li>
                                <li><span class="font-semibold text-red-600 dark:text-red-400">404 Not Found:</span> The
                                    requested resource was not found.</li>
                                <li><span class="font-semibold text-red-600 dark:text-red-400">429 Too Many
                                        Requests:</span> The rate limit was exceeded.</li>
                                <li><span class="font-semibold text-red-600 dark:text-red-400">500 Internal Server
                                        Error:</span> An unexpected error occurred on the server.</li>
                            </ul>
                        </section>

                        <section id="changelog" class="mb-12">
                            <h2 class="text-2xl font-bold text-indigo-600 dark:text-indigo-400 mb-4">Changelog</h2>
                            <div class="space-y-6">
                                <div class="border-l-4 border-indigo-500 pl-4">
                                    <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">v1.1.0 <span
                                            class="text-sm text-slate-500 dark:text-slate-400">- March 2024</span></h3>
                                    <ul class="mt-2 list-disc pl-5 space-y-1 text-slate-700 dark:text-slate-300">
                                        <li>Added location coordinates (latitude/longitude) to post office details</li>
                                        <li>Improved search accuracy for post office names</li>
                                        <li>Enhanced rate limiting system</li>
                                    </ul>
                                </div>
                                <div class="border-l-4 border-indigo-500/50 pl-4">
                                    <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">v1.0.1 <span
                                            class="text-sm text-slate-500 dark:text-slate-400">- January 2024</span></h3>
                                    <ul class="mt-2 list-disc pl-5 space-y-1 text-slate-700 dark:text-slate-300">
                                        <li>Fixed district name autocomplete functionality</li>
                                        <li>Added support for special characters in post office names</li>
                                        <li>Performance optimization for large data queries</li>
                                    </ul>
                                </div>
                                <div class="border-l-4 border-indigo-500/30 pl-4">
                                    <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">v1.0.0 <span
                                            class="text-sm text-slate-500 dark:text-slate-400">- December 2023</span></h3>
                                    <ul class="mt-2 list-disc pl-5 space-y-1 text-slate-700 dark:text-slate-300">
                                        <li>Initial release with basic pincode, state, district, and tehsil information</li>
                                        <li>Implemented RESTful API endpoints</li>
                                        <li>Added authentication system</li>
                                        <li>Introduced rate limiting</li>
                                    </ul>
                                </div>
                            </div>
                        </section>
                        <section id="support" class="mb-12">
                            <h2 class="text-2xl font-bold text-indigo-600 dark:text-indigo-400 mb-4">Support and Contact
                                Information</h2>
                            <p class="text-slate-700 dark:text-slate-300 leading-relaxed">
                                For support, please contact our team at
                                <a href="mailto:<EMAIL>"
                                    class="text-blue-600 dark:text-blue-400 hover:underline"><EMAIL></a>
                                or visit our support page.
                            </p>
                        </section>

                        <section id="demo" class="mb-12">
                            <h2 class="text-2xl font-bold text-indigo-600 dark:text-indigo-400 mb-4">Live Demo</h2>
                            <ul class="list-disc pl-5 space-y-3 text-slate-700 dark:text-slate-300">
                                <li>
                                    <span class="font-semibold">Github (Javascript):</span>
                                    <a href="https://github.com/Nandeshwar750/india-pincode-api-demo" target="_blank"
                                        rel="noopener noreferrer"
                                        class="text-blue-600 dark:text-blue-400 hover:underline break-words">
                                        https://github.com/Nandeshwar750/india-pincode-api-demo
                                    </a>
                                </li>
                                <li>
                                    <span class="font-semibold">Live (With PHP backend):</span>
                                    <a href="https://mostlyusedtools.com/tool/post-office-search-by-name" target="_blank"
                                        rel="noopener noreferrer"
                                        class="text-blue-600 dark:text-blue-400 hover:underline break-words">
                                        https://mostlyusedtools.com/tool/post-office-search-by-name
                                    </a>
                                </li>
                            </ul>
                        </section>

                        <section id="postman-test" class="mb-12">
                            <h2 class="text-2xl font-bold text-indigo-600 dark:text-indigo-400 mb-4">Postman Test
                                Collection</h2>
                            <div
                                class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                                <div class="flex flex-col md:flex-row items-start md:items-center gap-4 mb-4">
                                    <div class="flex-shrink-0">
                                        <svg class="w-12 h-12 text-orange-500" viewBox="0 0 24 24" fill="currentColor">
                                            <path
                                                d="M13.527 2.987c-.729-.729-1.917-.729-2.646 0l-9.894 9.894c-.729.729-.729 1.917 0 2.646l9.894 9.894c.729.729 1.917.729 2.646 0l9.894-9.894c.729-.729.729-1.917 0-2.646l-9.894-9.894zm.707 17.678c-.391.391-1.023.391-1.414 0l-6.364-6.364c-.391-.391-.391-1.023 0-1.414l6.364-6.364c.391-.391 1.023-.391 1.414 0l6.364 6.364c.391.391.391 1.023 0 1.414l-6.364 6.364z" />
                                        </svg>
                                    </div>
                                    <div class="flex-grow">
                                        <h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-2">Test API
                                            Endpoints with Postman</h3>
                                        <p class="text-slate-600 dark:text-slate-300">Download our pre-configured Postman
                                            collection to quickly test all API endpoints with example requests.</p>
                                    </div>
                                </div>
                                <div
                                    class="flex flex-col sm:flex-row gap-4 items-center p-4 bg-slate-50 dark:bg-slate-900 rounded-lg">
                                    <div class="flex-grow">
                                        <div class="text-sm font-mono bg-slate-200 dark:bg-slate-700 px-3 py-2 rounded">
                                            pincodes-api.postman_collection.json
                                        </div>
                                    </div>
                                    <a href="https://pincodes.nskmultiservices.in/upload/assets/pincodes-api.postman_collection.json"
                                        class="inline-flex items-center gap-2 px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                        </svg>
                                        <span>Download Collection</span>
                                    </a>
                                </div>
                                <div class="mt-4 text-sm text-slate-500 dark:text-slate-400">
                                    <p>✓ Contains all API endpoints</p>
                                    <p>✓ Includes example requests and responses</p>
                                    <p>✓ Environment variables pre-configured</p>
                                </div>
                            </div>
                        </section>
                        <section id="error-handling" class="mb-12">
                            <h2 class="text-2xl font-bold text-indigo-600 dark:text-indigo-400 mb-4">Error Handling</h2>
                            <p class="text-slate-700 dark:text-slate-300 leading-relaxed mb-4">
                                The API uses standard HTTP status codes to indicate the success or failure of a request.
                                Common error codes include:
                            </p>
                            <ul class="list-disc pl-5 space-y-2 text-slate-700 dark:text-slate-300">
                                <li><span class="font-semibold text-red-600 dark:text-red-400">400 Bad Request:</span> The
                                    request was malformed or invalid.</li>
                                <li><span class="font-semibold text-red-600 dark:text-red-400">401 Unauthorized:</span> The
                                    request lacked proper authentication.</li>
                                <li><span class="font-semibold text-red-600 dark:text-red-400">403 Forbidden:</span> The
                                    user does not have permission to access the resource.</li>
                                <li><span class="font-semibold text-red-600 dark:text-red-400">404 Not Found:</span> The
                                    requested resource was not found.</li>
                                <li><span class="font-semibold text-red-600 dark:text-red-400">429 Too Many
                                        Requests:</span> The rate limit was exceeded.</li>
                                <li><span class="font-semibold text-red-600 dark:text-red-400">500 Internal Server
                                        Error:</span> An unexpected error occurred on the server.</li>
                            </ul>
                        </section>

                        {{-- <section id="changelog" class="mb-12">
                            <h2 class="text-2xl font-bold text-indigo-600 dark:text-indigo-400 mb-4">Changelog</h2>
                            <ul class="list-disc pl-5 space-y-2 text-slate-700 dark:text-slate-300">
                                <li><span class="font-semibold">v1.0:</span> Initial release with basic pincode, state,
                                    district, and tehsil information.</li>
                            </ul>
                        </section> --}}

                        {{-- <section id="support" class="mb-12">
                            <h2 class="text-2xl font-bold text-indigo-600 dark:text-indigo-400 mb-4">Support and Contact
                                Information</h2>
                            <p class="text-slate-700 dark:text-slate-300 leading-relaxed">
                                You can test the API using Postman. Here's a sample collection:
                                <span><a href="https://pincodes.nskmultiservices.in/upload/assets/pincodes-api.postman_collection.json"
                                        target="_blank"
                                        class="text-blue-600 dark:text-blue-400 hover:underline break-words">Download
                                        Postman Collection</a></span>
                            </p>
                        </section> --}}

                        <section id="best-practices">
                            <h2 class="text-2xl font-bold text-indigo-600 dark:text-indigo-400 mb-4">Best Practices and
                                Security</h2>
                            <ul class="list-disc pl-5 space-y-2 text-slate-700 dark:text-slate-300">
                                <li>Use HTTPS for secure communication.</li>
                                <li>Do not expose your API key in client-side code.</li>
                                <li>Handle errors gracefully and provide meaningful messages to the end user.</li>
                            </ul>
                        </section>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-json.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-http.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile sidebar toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            const mobileSidebar = document.getElementById('mobileSidebar');

            if (sidebarToggle && mobileSidebar) {
                sidebarToggle.addEventListener('click', function() {
                    mobileSidebar.classList.toggle('hidden');
                });
            }

            // Handle smooth scrolling with offset
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);

                    if (targetElement) {
                        const headerOffset = 100;
                        const elementPosition = targetElement.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                        window.scrollTo({
                            top: offsetPosition,
                            behavior: 'smooth'
                        });

                        history.pushState(null, '', targetId);
                    }
                });
            });

            // Handle direct anchor links
            window.addEventListener('load', function() {
                if (window.location.hash) {
                    const targetElement = document.querySelector(window.location.hash);
                    if (targetElement) {
                        setTimeout(() => {
                            const headerOffset = 100;
                            const elementPosition = targetElement.getBoundingClientRect().top;
                            const offsetPosition = elementPosition + window.pageYOffset -
                                headerOffset;

                            window.scrollTo({
                                top: offsetPosition,
                                behavior: 'smooth'
                            });
                        }, 100);
                    }
                }
            });

            // Close mobile sidebar when clicking on a link
            const mobileLinks = mobileSidebar ? mobileSidebar.querySelectorAll('a') : [];
            mobileLinks.forEach(link => {
                link.addEventListener('click', function() {
                    mobileSidebar.classList.add('hidden');
                });
            });

            // Highlight active section based on scroll position
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('nav a');

            function highlightNavigation() {
                let scrollPosition = window.scrollY + 150;

                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.offsetHeight;
                    const sectionId = section.getAttribute('id');

                    if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                        navLinks.forEach(link => {
                            link.classList.remove('bg-slate-100', 'dark:bg-slate-700',
                                'text-slate-900', 'dark:text-white');
                            if (link.getAttribute('href') === '#' + sectionId) {
                                link.classList.add('bg-slate-100', 'dark:bg-slate-700',
                                    'text-slate-900', 'dark:text-white');
                            }
                        });
                    }
                });
            }

            window.addEventListener('scroll', highlightNavigation);
            highlightNavigation();
        });

        // Copy to clipboard functionality
        function copyToClipboard(button) {
            const codeBlock = button.closest('.bg-slate-900').querySelector('code');
            const text = codeBlock.textContent;

            navigator.clipboard.writeText(text).then(() => {
                const originalHTML = button.innerHTML;
                button.innerHTML = `
                    <svg class="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                `;

                setTimeout(() => {
                    button.innerHTML = originalHTML;
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy text: ', err);
            });
        }
    </script>
@endpush

@push('styles')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism-okaidia.min.css">
    <style>
        /* Add scroll-margin-top to all sections */
        section[id] {
            scroll-margin-top: 100px;
        }

        /* Custom scrollbar for code blocks */
        pre::-webkit-scrollbar {
            height: 8px;
        }

        pre::-webkit-scrollbar-track {
            background: #374151;
            border-radius: 4px;
        }

        pre::-webkit-scrollbar-thumb {
            background: #6b7280;
            border-radius: 4px;
        }

        pre::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }

        /* Improved mobile responsive code blocks */
        @media (max-width: 640px) {
            pre {
                font-size: 11px !important;
                line-height: 1.4 !important;
                padding: 0.75rem !important;
            }

            pre code {
                word-break: break-word !important;
                white-space: pre-wrap !important;
                overflow-wrap: break-word !important;
            }

            .container {
                padding-left: 0.5rem !important;
                padding-right: 0.5rem !important;
            }

            .rounded-xl {
                border-radius: 0.5rem !important;
            }

            .p-4 {
                padding: 0.75rem !important;
            }

            .gap-4 {
                gap: 0.75rem !important;
            }

            .mb-4 {
                margin-bottom: 0.75rem !important;
            }

            .mb-6 {
                margin-bottom: 1rem !important;
            }

            .text-2xl {
                font-size: 1.25rem !important;
            }

            .text-3xl {
                font-size: 1.5rem !important;
            }
        }

        /* Extra small devices */
        @media (max-width: 480px) {
            pre {
                font-size: 10px !important;
                padding: 0.5rem !important;
            }

            .grid-cols-1 {
                grid-template-columns: 1fr !important;
            }

            .flex-col {
                flex-direction: column !important;
            }

            .gap-8 {
                gap: 1rem !important;
            }
        }

        /* Ensure proper text wrapping in all scenarios */
        .break-words {
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            word-break: break-word !important;
        }

        .break-all {
            word-break: break-all !important;
        }

        /* Responsive font sizes */
        @media (max-width: 768px) {
            .text-base {
                font-size: 0.875rem !important;
            }

            .text-sm {
                font-size: 0.75rem !important;
            }

            .text-xs {
                font-size: 0.625rem !important;
            }
        }
    </style>
@endpush
