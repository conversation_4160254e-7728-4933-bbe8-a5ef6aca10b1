@extends('layouts.app')

@section('title', 'QR Code Bank Transfer')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Progress Steps -->
            <div class="payment-progress mb-4">
                <div class="row text-center">
                    <div class="col-3">
                        <div class="step completed">
                            <div class="step-icon"><i class="fas fa-check"></i></div>
                            <div class="step-title">Order</div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="step completed">
                            <div class="step-icon"><i class="fas fa-credit-card"></i></div>
                            <div class="step-title">Payment Method</div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="step active">
                            <div class="step-icon"><i class="fas fa-qrcode"></i></div>
                            <div class="step-title">QR Payment</div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="step">
                            <div class="step-icon"><i class="fas fa-check-circle"></i></div>
                            <div class="step-title">Verification</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- QR Code and Instructions -->
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-qrcode me-2"></i>
                                QR Code Bank Transfer
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- QR Code Display -->
                            <div class="text-center mb-4">
                                <div class="qr-code-container p-4 bg-white border rounded shadow-sm d-inline-block">
                                    <div id="qr-code" class="mb-3">
                                        {!! $qrCode !!}
                                    </div>
                                    <p class="small text-muted mb-0">Scan with your banking app</p>
                                </div>
                            </div>

                            <!-- Bank Transfer Instructions -->
                            <div class="transfer-instructions">
                                <h6 class="mb-3">
                                    <i class="fas fa-list-ol me-2"></i>
                                    How to Pay:
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="instruction-step mb-3">
                                            <div class="d-flex align-items-start">
                                                <div class="step-number bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 14px;">1</div>
                                                <div>
                                                    <h6 class="mb-1">Open Banking App</h6>
                                                    <p class="text-muted small mb-0">Open your mobile banking app or UPI app</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="instruction-step mb-3">
                                            <div class="d-flex align-items-start">
                                                <div class="step-number bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 14px;">2</div>
                                                <div>
                                                    <h6 class="mb-1">Scan QR Code</h6>
                                                    <p class="text-muted small mb-0">Use the QR scanner in your app to scan the code above</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="instruction-step mb-3">
                                            <div class="d-flex align-items-start">
                                                <div class="step-number bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 14px;">3</div>
                                                <div>
                                                    <h6 class="mb-1">Verify Details</h6>
                                                    <p class="text-muted small mb-0">Check amount and reference number before confirming</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="instruction-step mb-3">
                                            <div class="d-flex align-items-start">
                                                <div class="step-number bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 14px;">4</div>
                                                <div>
                                                    <h6 class="mb-1">Complete Transfer</h6>
                                                    <p class="text-muted small mb-0">Confirm the payment in your banking app</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Manual Transfer Details -->
                            <div class="manual-transfer mt-4">
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Can't scan QR code? Transfer manually:
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p class="mb-1"><strong>Account Name:</strong> {{ $bankDetails['account_name'] }}</p>
                                            <p class="mb-1"><strong>Account Number:</strong> {{ $bankDetails['account_number'] }}</p>
                                            <p class="mb-1"><strong>IFSC Code:</strong> {{ $bankDetails['ifsc_code'] }}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p class="mb-1"><strong>Amount:</strong> {{ $order->currency }} {{ number_format($order->amount, 2) }}</p>
                                            <p class="mb-1"><strong>Reference:</strong> {{ $order->order_number }}</p>
                                            <p class="mb-0"><strong>Bank:</strong> {{ $bankDetails['bank_name'] }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Status -->
                            <div class="payment-status mt-4">
                                <div class="d-flex align-items-center justify-content-between p-3 bg-light rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="status-indicator me-3">
                                            <div class="spinner-border spinner-border-sm text-warning" role="status" id="status-spinner">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <i class="fas fa-clock text-warning" id="status-icon" style="display: none;"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-1" id="status-title">Waiting for Payment</h6>
                                            <p class="text-muted small mb-0" id="status-message">We're monitoring for your payment. This may take a few minutes.</p>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="refresh-status">
                                        <i class="fas fa-sync-alt"></i> Refresh
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Proof Upload -->
                    <div class="card mt-4 shadow-sm" id="proof-upload-section">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-upload me-2"></i>
                                Upload Payment Proof
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-3">
                                After completing the payment, upload a screenshot or photo of your transaction receipt for verification.
                            </p>
                            
                            <form id="proof-upload-form" enctype="multipart/form-data">
                                @csrf
                                <input type="hidden" name="payment_id" value="{{ $payment->id }}">
                                
                                <div class="mb-3">
                                    <label class="form-label">Payment Proof *</label>
                                    <div class="file-upload-area border-2 border-dashed rounded p-4 text-center" id="file-upload-area">
                                        <input type="file" class="form-control d-none" id="proof_file" name="proof_file" 
                                               accept="image/*,.pdf" required>
                                        <div class="upload-content">
                                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                            <h6>Click to upload or drag and drop</h6>
                                            <p class="text-muted small mb-0">
                                                Supported formats: JPG, PNG, PDF (Max: 5MB)
                                            </p>
                                        </div>
                                        <div class="file-preview" style="display: none;">
                                            <img id="preview-image" src="" alt="Preview" class="img-thumbnail mb-2" style="max-height: 200px;">
                                            <p class="mb-0" id="file-name"></p>
                                            <button type="button" class="btn btn-sm btn-outline-danger mt-2" id="remove-file">
                                                <i class="fas fa-times"></i> Remove
                                            </button>
                                        </div>
                                    </div>
                                    <div class="invalid-feedback"></div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Transaction ID (Optional)</label>
                                    <input type="text" class="form-control" name="transaction_id" 
                                           placeholder="Enter transaction ID from your bank">
                                    <small class="text-muted">This helps us verify your payment faster</small>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Additional Notes (Optional)</label>
                                    <textarea class="form-control" name="notes" rows="3" 
                                              placeholder="Any additional information about your payment"></textarea>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('user.payment.gateway-selection', $order->id) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        Change Payment Method
                                    </a>
                                    <button type="submit" class="btn btn-success" id="upload-btn">
                                        <i class="fas fa-upload me-2"></i>
                                        Upload Proof
                                        <span class="spinner-border spinner-border-sm ms-2" style="display: none;"></span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Order Summary Sidebar -->
                <div class="col-lg-4">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-receipt me-2"></i>
                                Payment Summary
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="order-item mb-3">
                                <h6 class="mb-1">{{ $order->plan->name ?? 'Plan' }}</h6>
                                <small class="text-muted">{{ $order->plan->duration ?? '' }} {{ $order->plan->duration_type ?? '' }}</small>
                            </div>
                            
                            <hr>
                            
                            <div class="pricing-details">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Amount:</span>
                                    <span>{{ $order->currency }} {{ number_format($order->amount, 2) }}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Processing Fee:</span>
                                    <span class="text-success">FREE</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>Total:</strong>
                                    <strong class="text-primary">{{ $order->currency }} {{ number_format($order->amount, 2) }}</strong>
                                </div>
                            </div>

                            <div class="payment-info mt-4 p-3 bg-light rounded">
                                <h6 class="mb-2">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Important Notes:
                                </h6>
                                <ul class="small text-muted mb-0">
                                    <li>Payment verification may take 2-24 hours</li>
                                    <li>Ensure the reference number is included</li>
                                    <li>Upload clear, readable payment proof</li>
                                    <li>Contact support if you face any issues</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Support Card -->
                    <div class="card mt-4 shadow-sm">
                        <div class="card-body text-center">
                            <h6 class="mb-3">Need Help?</h6>
                            <p class="text-muted small mb-3">
                                Having trouble with the payment? Our support team is here to help.
                            </p>
                            <div class="d-grid gap-2">
                                <a href="mailto:<EMAIL>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-envelope me-2"></i>
                                    Email Support
                                </a>
                                <a href="#" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-phone me-2"></i>
                                    Call Support
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="uploadSuccessModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                <h5>Payment Proof Uploaded!</h5>
                <p class="text-muted mb-4">
                    Your payment proof has been submitted successfully. We'll verify it within 24 hours and notify you via email.
                </p>
                <div class="d-flex gap-2 justify-content-center">
                    <a href="{{ route('user.orders.show', $order->id) }}" class="btn btn-primary">
                        View Order
                    </a>
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection@p
ush('styles')
<style>
.payment-progress {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 0.5rem;
}

.step {
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    right: -50%;
    width: 100%;
    height: 2px;
    background: #dee2e6;
    z-index: 1;
}

.step.completed::after,
.step.active::after {
    background: #007bff;
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #dee2e6;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    position: relative;
    z-index: 2;
}

.step.completed .step-icon {
    background: #28a745;
    color: white;
}

.step.active .step-icon {
    background: #007bff;
    color: white;
}

.step-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6c757d;
}

.step.completed .step-title,
.step.active .step-title {
    color: #495057;
}

.qr-code-container {
    max-width: 250px;
}

.qr-code-container svg {
    width: 100%;
    height: auto;
}

.instruction-step {
    transition: all 0.3s ease;
}

.instruction-step:hover {
    transform: translateY(-2px);
}

.step-number {
    flex-shrink: 0;
    font-weight: bold;
}

.file-upload-area {
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-upload-area:hover {
    border-color: #007bff !important;
    background-color: #f8f9ff;
}

.file-upload-area.dragover {
    border-color: #007bff !important;
    background-color: #e3f2fd;
}

.payment-status {
    border-left: 4px solid #ffc107;
}

.status-indicator {
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-waiting {
    animation: pulse 2s infinite;
}

@media (max-width: 768px) {
    .payment-progress {
        padding: 1rem;
    }
    
    .step-title {
        font-size: 0.75rem;
    }
    
    .step:not(:last-child)::after {
        display: none;
    }
    
    .qr-code-container {
        max-width: 200px;
    }
    
    .instruction-step .d-flex {
        flex-direction: column;
        text-align: center;
    }
    
    .step-number {
        margin-bottom: 0.5rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileUploadArea = document.getElementById('file-upload-area');
    const fileInput = document.getElementById('proof_file');
    const uploadForm = document.getElementById('proof-upload-form');
    const uploadBtn = document.getElementById('upload-btn');
    const refreshStatusBtn = document.getElementById('refresh-status');
    
    // File upload handling
    fileUploadArea.addEventListener('click', () => fileInput.click());
    
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });
    
    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect(files[0]);
        }
    });
    
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });
    
    // Remove file handler
    document.getElementById('remove-file').addEventListener('click', function() {
        fileInput.value = '';
        document.querySelector('.upload-content').style.display = 'block';
        document.querySelector('.file-preview').style.display = 'none';
        fileInput.classList.remove('is-valid');
    });
    
    // Form submission
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!fileInput.files.length) {
            showError('Please select a file to upload');
            return;
        }
        
        const formData = new FormData(this);
        
        // Show loading state
        uploadBtn.disabled = true;
        uploadBtn.querySelector('.spinner-border').style.display = 'inline-block';
        
        fetch('{{ route("user.payment.qr.upload-proof") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success modal
                const modal = new bootstrap.Modal(document.getElementById('uploadSuccessModal'));
                modal.show();
                
                // Update payment status
                updatePaymentStatus('proof_uploaded', 'Payment Proof Uploaded', 'Your payment proof is being verified by our team.');
            } else {
                showError(data.message || 'Failed to upload payment proof');
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            showError('An error occurred while uploading. Please try again.');
        })
        .finally(() => {
            uploadBtn.disabled = false;
            uploadBtn.querySelector('.spinner-border').style.display = 'none';
        });
    });
    
    // Status refresh
    refreshStatusBtn.addEventListener('click', function() {
        checkPaymentStatus();
    });
    
    // Auto-refresh status every 30 seconds
    setInterval(checkPaymentStatus, 30000);
    
    // Initial status check
    checkPaymentStatus();
    
    function handleFileSelect(file) {
        // Validate file
        if (!validateFile(file)) {
            return;
        }
        
        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            const previewImage = document.getElementById('preview-image');
            const fileName = document.getElementById('file-name');
            
            if (file.type.startsWith('image/')) {
                previewImage.src = e.target.result;
                previewImage.style.display = 'block';
            } else {
                previewImage.style.display = 'none';
            }
            
            fileName.textContent = file.name;
            
            document.querySelector('.upload-content').style.display = 'none';
            document.querySelector('.file-preview').style.display = 'block';
            
            fileInput.classList.add('is-valid');
        };
        reader.readAsDataURL(file);
    }
    
    function validateFile(file) {
        const maxSize = 5 * 1024 * 1024; // 5MB
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
        
        if (file.size > maxSize) {
            showError('File size must be less than 5MB');
            return false;
        }
        
        if (!allowedTypes.includes(file.type)) {
            showError('Only JPG, PNG, and PDF files are allowed');
            return false;
        }
        
        return true;
    }
    
    function checkPaymentStatus() {
        fetch('{{ route("user.payment.status", $order->id) }}')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updatePaymentStatusUI(data.status, data.order_status);
                }
            })
            .catch(error => {
                console.error('Status check error:', error);
            });
    }
    
    function updatePaymentStatusUI(paymentStatus, orderStatus) {
        const statusIcon = document.getElementById('status-icon');
        const statusSpinner = document.getElementById('status-spinner');
        const statusTitle = document.getElementById('status-title');
        const statusMessage = document.getElementById('status-message');
        
        switch (paymentStatus) {
            case 'completed':
                statusSpinner.style.display = 'none';
                statusIcon.style.display = 'block';
                statusIcon.className = 'fas fa-check-circle text-success';
                statusTitle.textContent = 'Payment Verified';
                statusMessage.textContent = 'Your payment has been verified and your order is complete.';
                
                // Redirect to success page after 3 seconds
                setTimeout(() => {
                    window.location.href = '{{ route("user.payment.success", $order->id) }}';
                }, 3000);
                break;
                
            case 'failed':
                statusSpinner.style.display = 'none';
                statusIcon.style.display = 'block';
                statusIcon.className = 'fas fa-times-circle text-danger';
                statusTitle.textContent = 'Payment Failed';
                statusMessage.textContent = 'Your payment could not be verified. Please contact support.';
                break;
                
            case 'pending':
            default:
                // Keep current waiting state
                break;
        }
    }
    
    function updatePaymentStatus(status, title, message) {
        const statusIcon = document.getElementById('status-icon');
        const statusSpinner = document.getElementById('status-spinner');
        const statusTitle = document.getElementById('status-title');
        const statusMessage = document.getElementById('status-message');
        
        statusSpinner.style.display = 'none';
        statusIcon.style.display = 'block';
        statusIcon.className = 'fas fa-upload text-info';
        statusTitle.textContent = title;
        statusMessage.textContent = message;
    }
    
    function showError(message) {
        // Create error alert
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Insert at top of form
        uploadForm.insertBefore(alertDiv, uploadForm.firstChild);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
});

// Handle page visibility change
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        // Check payment status when user returns to tab
        setTimeout(() => {
            fetch('{{ route("user.payment.status", $order->id) }}')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.status === 'completed') {
                        window.location.href = '{{ route("user.payment.success", $order->id) }}';
                    }
                })
                .catch(error => console.error('Status check error:', error));
        }, 1000);
    }
});
</script>
@endpush