<?php

use App\Exceptions\Handler;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Illuminate\Auth\Access\AuthorizationException;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->handler = new Handler(app());
});

describe('Exception Handler', function () {
    describe('AuthenticationException', function () {
        it('returns JSON response for API routes', function () {
            $request = Request::create('/api/users', 'GET');
            $exception = new AuthenticationException('Unauthenticated');
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->getStatusCode())->toBe(401);
            expect($response->getContent())->toBeJson();
            
            $content = json_decode($response->getContent(), true);
            expect($content['message'])->toBe('Unauthorized');
            expect($content['error'])->toBe('Please provide a valid API token.');
        });

        it('redirects to login for web routes', function () {
            $request = Request::create('/web/dashboard', 'GET');
            $exception = new AuthenticationException('Unauthenticated');
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->getStatusCode())->toBe(302);
            expect($response->headers->get('Location'))->toContain('/login');
        });
    });

    describe('NotFoundHttpException', function () {
        it('returns JSON response for API routes', function () {
            $request = Request::create('/api/nonexistent', 'GET');
            $exception = new NotFoundHttpException('Not found');
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->getStatusCode())->toBe(404);
            expect($response->getContent())->toBeJson();
            
            $content = json_decode($response->getContent(), true);
            expect($content['message'])->toBe('Not Found');
            expect($content['error'])->toBe('The requested resource was not found.');
        });

        it('returns 404 view for web routes', function () {
            $request = Request::create('/web/nonexistent', 'GET');
            $exception = new NotFoundHttpException('Not found');
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->getStatusCode())->toBe(404);
            expect($response->getContent())->toContain('Page Not Found');
        });
    });

    describe('ModelNotFoundException', function () {
        it('returns JSON response for API routes', function () {
            $request = Request::create('/api/users/999', 'GET');
            $exception = new ModelNotFoundException('Model not found');
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->getStatusCode())->toBe(404);
            expect($response->getContent())->toBeJson();
            
            $content = json_decode($response->getContent(), true);
            expect($content['message'])->toBe('Not Found');
            expect($content['error'])->toBe('The requested resource was not found.');
        });

        it('returns 404 view for web routes', function () {
            $request = Request::create('/web/users/999', 'GET');
            $exception = new ModelNotFoundException('Model not found');
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->getStatusCode())->toBe(404);
            expect($response->getContent())->toContain('Page Not Found');
        });
    });

    describe('ValidationException', function () {
        it('returns JSON response with validation errors for API routes', function () {
            $request = Request::create('/api/users', 'POST');
            
            $validator = Validator::make([], [
                'email' => 'required|email',
                'name' => 'required|string'
            ]);
            $validator->fails();
            
            $exception = new ValidationException($validator);
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->getStatusCode())->toBe(422);
            expect($response->getContent())->toBeJson();
            
            $content = json_decode($response->getContent(), true);
            expect($content['message'])->toBe('Validation Error');
            expect($content)->toHaveKey('errors');
            expect($content['errors'])->toBeArray();
        });

        it('redirects back with errors for web routes', function () {
            $request = Request::create('/web/users', 'POST');
            
            $validator = Validator::make([], [
                'email' => 'required|email'
            ]);
            $validator->fails();
            
            $exception = new ValidationException($validator);
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->getStatusCode())->toBe(302);
            expect($response->getSession()->has('errors'))->toBeTrue();
        });
    });

    describe('AuthorizationException', function () {
        it('returns JSON response for API routes', function () {
            $request = Request::create('/api/admin/users', 'GET');
            $exception = new \Illuminate\Auth\Access\AuthorizationException('This action is unauthorized.');
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->getStatusCode())->toBe(403);
            expect($response->getContent())->toBeJson();
            
            $content = json_decode($response->getContent(), true);
            expect($content['message'])->toBe('Forbidden');
            expect($content['error'])->toBe('You do not have permission to access this resource.');
        });

        it('returns 403 view for web routes', function () {
            $request = Request::create('/web/admin/users', 'GET');
            $exception = new \Illuminate\Auth\Access\AuthorizationException('This action is unauthorized.');
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->getStatusCode())->toBe(403);
            expect($response->getContent())->toContain('Access Forbidden');
        });
    });

    describe('Server Error (500)', function () {
        it('returns JSON response for API routes', function () {
            $request = Request::create('/api/users', 'GET');
            $exception = new \Exception('Server error');
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->getStatusCode())->toBe(500);
            expect($response->getContent())->toBeJson();
            
            $content = json_decode($response->getContent(), true);
            expect($content['message'])->toBe('Server Error');
            expect($content['error'])->toBe('Server error');
        });

        it('returns 500 view for web routes', function () {
            $request = Request::create('/web/users', 'GET');
            $exception = new \Exception('Server error');
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->getStatusCode())->toBe(500);
            expect($response->getContent())->toContain('Server Error');
        });

        it('includes error details in debug mode for API routes', function () {
            config(['app.debug' => true]);
            
            $request = Request::create('/api/users', 'GET');
            $exception = new \Exception('Server error');
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->getStatusCode())->toBe(500);
            $content = json_decode($response->getContent(), true);
            expect($content['error'])->toBe('Server error');
        });
    });

    describe('dontFlash property', function () {
        it('has correct sensitive fields in dontFlash array', function () {
            $reflection = new ReflectionClass($this->handler);
            $property = $reflection->getProperty('dontFlash');
            $property->setAccessible(true);
            $dontFlashValues = $property->getValue($this->handler);
            
            expect($dontFlashValues)->toContain('current_password');
            expect($dontFlashValues)->toContain('password');
            expect($dontFlashValues)->toContain('password_confirmation');
            expect(count($dontFlashValues))->toBe(3);
        });
    });

    describe('edge cases and error conditions', function () {
        it('handles API routes with query parameters', function () {
            $request = Request::create('/api/users?page=1&limit=10', 'GET');
            $exception = new AuthenticationException('Unauthenticated');
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->getStatusCode())->toBe(401);
            expect(json_decode($response->getContent(), true)['message'])->toBe('Unauthorized');
        });

        it('handles API routes with trailing slashes', function () {
            $request = Request::create('/api/users/', 'GET');
            $exception = new NotFoundHttpException('Not found');
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->getStatusCode())->toBe(404);
            expect(json_decode($response->getContent(), true)['message'])->toBe('Not Found');
        });

        it('handles empty validation errors gracefully', function () {
            $request = Request::create('/api/users', 'POST');
            
            $validator = Validator::make(['name' => 'John'], [
                'name' => 'required|string'
            ]);
            $exception = new ValidationException($validator);
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->getStatusCode())->toBe(422);
            $content = json_decode($response->getContent(), true);
            expect($content['errors'])->toBeArray();
        });
    });

    describe('HTTP methods and content types', function () {
        it('handles different HTTP methods for API routes', function () {
            $methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];
            
            foreach ($methods as $method) {
                $request = Request::create('/api/users', $method);
                $exception = new AuthenticationException('Unauthenticated');
                
                $response = $this->handler->render($request, $exception);
                
                expect($response->getStatusCode())->toBe(401);
                expect($response->headers->get('content-type'))->toContain('application/json');
            }
        });

        it('sets correct content type for JSON responses', function () {
            $request = Request::create('/api/users', 'GET');
            $exception = new AuthenticationException('Unauthenticated');
            
            $response = $this->handler->render($request, $exception);
            
            expect($response->headers->get('content-type'))->toContain('application/json');
        });
    });
});