@extends('layouts.app')

@section('json-ld')
    @include('pincodes.json-ld.webpage')

    @if (!empty($breadcrumbs))
        @include('pincodes.json-ld.breadcrumbs')
    @endif
@endsection

@php
    $totalDistricts = count($m_districts);
    $totalPincodes = array_sum($pincode_counts);
@endphp

@section('content')
    <div class="min-h-screen bg-bg-light dark:bg-bg-dark">
        <x-breadcrumb :segments="$breadcrumbs" :pageTitle="$pageTitle" :metaDescription="$metaDescription" />

        <div class="container mx-auto px-4 py-8 max-w-6xl">
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-8">
                {{-- Main Content --}}
                <div class="lg:col-span-8">
                    <div class="bg-bg-light dark:bg-bg-dark shadow-lg rounded-xl overflow-hidden border border-border-light dark:border-border-dark">
                        {{-- Header Section --}}
                        <div class="bg-gradient-to-r from-primary-light to-primary-dark dark:from-primary-dark dark:to-primary-light p-8 relative overflow-hidden">
                            <div class="absolute inset-0 opacity-10">
                                <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                                    <path d="M0,0 L100,0 L100,100 L0,100 Z" fill="url(#header-pattern)" />
                                </svg>
                                <defs>
                                    <pattern id="header-pattern" width="10" height="10"
                                        patternUnits="userSpaceOnUse">
                                        <circle cx="5" cy="5" r="2" fill="white" />
                                    </pattern>
                                </defs>
                            </div>
                            <h2 class="text-3xl md:text-4xl font-extrabold text-white relative z-10 tracking-tight">
                                Postal Codes of {{ ucfirst($stateList->name) }}
                            </h2>
                            <p class="text-white/80 mt-2 max-w-2xl relative z-10">
                                Find all districts and postal codes in {{ ucfirst($stateList->name) }} state
                            </p>
                        </div>

                        {{-- Content Section --}}
                        <div class="p-6 md:p-8">
                            {{-- Stats Overview --}}
                            <div class="grid grid-cols-2 gap-4 mb-8">
                                <div
                                    class="bg-gradient-to-br from-primary-light/10 to-primary-dark/10 dark:from-primary-dark/10 dark:to-primary-light/10 p-6 rounded-xl border border-primary-light/20 dark:border-primary-dark/20 shadow-sm">
                                    <div class="flex items-center space-x-4">
                                        <div class="p-3 bg-primary-light/10 dark:bg-primary-dark/10 rounded-lg">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-light dark:text-primary-dark"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Total Districts</p>
                                            <h3 class="text-2xl font-bold text-primary-light dark:text-primary-dark">
                                                {{ number_format($totalDistricts) }}</h3>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    class="bg-gradient-to-br from-accent-light/10 to-accent-dark/10 dark:from-accent-dark/10 dark:to-accent-light/10 p-6 rounded-xl border border-accent-light/20 dark:border-accent-dark/20 shadow-sm">
                                    <div class="flex items-center space-x-4">
                                        <div class="p-3 bg-accent-light/10 dark:bg-accent-dark/10 rounded-lg">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-accent-light dark:text-accent-dark"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Total Pin Codes</p>
                                            <h3 class="text-2xl font-bold text-accent-light dark:text-accent-dark">
                                                {{ number_format($totalPincodes) }}</h3>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {{-- Search Input --}}
                            <div class="mb-8">
                                <div class="relative">
                                    <input type="text" id="districtSearch" placeholder="Search districts..."
                                        class="w-full px-5 py-4 pl-12 border border-border-light dark:border-border-dark rounded-xl 
                                               focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark 
                                               focus:border-primary-light dark:focus:border-primary-dark focus:outline-none 
                                               transition duration-300 bg-bg-light dark:bg-bg-dark 
                                               text-text-primary-light dark:text-text-primary-dark">
                                    <span class="absolute left-4 top-1/2 transform -translate-y-1/2 text-text-secondary-light dark:text-text-secondary-dark">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                        </svg>
                                    </span>
                                </div>
                            </div>

                            {{-- Districts Grid --}}
                            <div id="districtList" class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6">
                                @foreach ($m_districts as $index => $district)
                                    @php
                                        $state = rawurlencode($stateList->name);
                                        $district1 = rawurlencode($district->name);
                                        $stateurl = url("/pincodes/$state");
                                        $url = $stateurl . "/$district1";
                                    @endphp
                                    <div class="district-item group">
                                        <div
                                            class="bg-bg-light dark:bg-bg-dark h-full shadow-md hover:shadow-xl rounded-xl overflow-hidden 
                                                   border border-border-light dark:border-border-dark 
                                                   hover:border-primary-light dark:hover:border-primary-dark 
                                                   transition-all duration-300 transform hover:-translate-y-1">
                                            <div
                                                class="p-5 bg-gradient-to-r from-primary-light/10 to-primary-dark/10 
                                                       dark:from-primary-dark/10 dark:to-primary-light/10 
                                                       border-b border-primary-light/20 dark:border-primary-dark/20">
                                                <h3 class="text-lg font-bold text-primary-light dark:text-primary-dark truncate">
                                                    {{ ucfirst($district->name) }}
                                                </h3>
                                            </div>
                                            <div class="p-5">
                                                <div class="flex justify-between items-center mb-4">
                                                    <span class="text-sm text-text-secondary-light dark:text-text-secondary-dark flex items-center">
                                                        <svg xmlns="http://www.w3.org/2000/svg"
                                                            class="h-4 w-4 mr-2 text-primary-light dark:text-primary-dark" fill="none"
                                                            viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                                        </svg>
                                                        {{ ucfirst($stateList->name) }}
                                                    </span>
                                                    <span
                                                        class="bg-accent-light/10 dark:bg-accent-dark/10 text-accent-light dark:text-accent-dark px-3 py-1 rounded-full text-xs font-medium">
                                                        {{ number_format($pincode_counts[$district->name] ?? 0) }} Pincodes
                                                    </span>
                                                </div>
                                                <a href="{{ $url }}"
                                                    class="w-full inline-flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-lg 
                                                           text-white bg-primary-light dark:bg-primary-dark 
                                                           hover:bg-accent-light dark:hover:bg-accent-dark 
                                                           focus:outline-none focus:ring-2 focus:ring-offset-2 
                                                           focus:ring-primary-light dark:focus:ring-primary-dark 
                                                           transition-colors duration-200">
                                                    View Details
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2"
                                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M9 5l7 7-7 7" />
                                                    </svg>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            {{-- No Results Message --}}
                            <div id="noResults" class="hidden text-center py-10">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-text-secondary-light dark:text-text-secondary-dark"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <h3 class="mt-4 text-lg font-medium text-text-primary-light dark:text-text-primary-dark">No districts found</h3>
                                <p class="mt-1 text-text-secondary-light dark:text-text-secondary-dark">Try adjusting your search criteria</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sticky Sidebar -->
                <div class="lg:col-span-4">
                    <div class="sticky top-20">
                        @include('pincodes.partials.sidebar')
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('districtSearch');
            const districtItems = document.querySelectorAll('.district-item');
            const noResultsMessage = document.getElementById('noResults');

            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();
                let resultsFound = false;

                districtItems.forEach(item => {
                    const districtName = item.querySelector('h3').textContent.toLowerCase();
                    const isVisible = districtName.includes(searchTerm);

                    item.style.display = isVisible ? '' : 'none';
                    if (isVisible) resultsFound = true;
                });

                // Show/hide no results message
                noResultsMessage.style.display = resultsFound ? 'none' : 'block';
            });
        });
    </script>
@endpush
