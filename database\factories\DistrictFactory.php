<?php

namespace Database\Factories;

use App\Models\District;
use App\Models\State;
use Illuminate\Database\Eloquent\Factories\Factory;

class DistrictFactory extends Factory
{
    protected $model = District::class;

    public function definition()
    {
        return [
            'name' => $this->faker->unique()->city,
            'state_id' => State::factory(),
            'alt_name' => $this->faker->city,
            'official_site' => $this->faker->url
        ];
    }
}