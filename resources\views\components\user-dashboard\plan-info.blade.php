@props(['currentPlan', 'plan', 'totalRequests', 'remainingRequests'])

<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">
                {{ __('Subscription Plan') }}
            </h3>
            <a href="{{ route('user.plans.index') }}" class="text-sm text-indigo-600 hover:text-indigo-900">
                {{ __('Change Plan') }}
            </a>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-gray-50 p-4 rounded-lg">
                <div class="text-sm text-gray-600">{{ __('Current Plan') }}</div>
                <div class="text-2xl font-bold text-gray-900">{{ $plan ? $plan->name : 'No Plan' }}</div>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
                <div class="text-sm text-gray-600">{{ __('Total Requests') }}</div>
                <div class="text-2xl font-bold text-gray-900">{{ number_format($totalRequests) }}</div>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
                <div class="text-sm text-gray-600">{{ __('Remaining Requests') }}</div>
                <div class="text-2xl font-bold text-gray-900">{{ number_format($remainingRequests) }}</div>
            </div>
        </div>
    </div>
</div>