@extends('admin.layouts.app')

@section('title', 'Currency Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <div class="d-flex">
                        <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#addRateModal">
                            <i class="mdi mdi-plus"></i> Add Rate
                        </button>
                        <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#fetchRatesModal">
                            <i class="mdi mdi-download"></i> Fetch Rates
                        </button>
                        <button type="button" class="btn btn-warning me-2" id="clear-cache-btn">
                            <i class="mdi mdi-cached"></i> Clear Cache
                        </button>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#testConversionModal">
                            <i class="mdi mdi-calculator"></i> Test Conversion
                        </button>
                    </div>
                </div>
                <h4 class="page-title">Currency Management</h4>
            </div>
        </div>
    </div>

    <!-- Gateway Currency Configuration -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Gateway Currency Configuration</h4>
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Gateway</th>
                                    <th>Supported Currencies</th>
                                    <th>Default Currency</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($gatewayConfigs as $config)
                                <tr>
                                    <td>
                                        <h6 class="mb-0">{{ $config['display_name'] }}</h6>
                                        <small class="text-muted">{{ $config['name'] }}</small>
                                    </td>
                                    <td>
                                        @foreach($config['supported_currencies'] as $currency)
                                            <span class="badge bg-primary me-1">{{ $currency }}</span>
                                        @endforeach
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ $config['default_currency'] }}</span>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="configureGateway({{ $config['id'] }}, '{{ $config['display_name'] }}')">
                                            Configure
                                        </button>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="text-center">No payment gateways configured</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Currency Rates Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="header-title">Exchange Rates</h4>
                        <button type="button" class="btn btn-sm btn-success" id="bulk-update-btn" style="display: none;">
                            <i class="mdi mdi-content-save"></i> Save Changes
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>From Currency</th>
                                    <th>To Currency</th>
                                    <th>Exchange Rate</th>
                                    <th>Source</th>
                                    <th>Last Updated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($rates as $rate)
                                <tr data-rate-id="{{ $rate->id }}">
                                    <td>
                                        <span class="badge bg-info">{{ $rate->from_currency }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $rate->to_currency }}</span>
                                    </td>
                                    <td>
                                        <input type="number" class="form-control form-control-sm rate-input" 
                                               value="{{ $rate->rate }}" step="0.000001" min="0.000001"
                                               data-original="{{ $rate->rate }}">
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $rate->source === 'api' ? 'success' : 'warning' }}">
                                            {{ ucfirst($rate->source) }}
                                        </span>
                                    </td>
                                    <td>{{ $rate->updated_at->format('M d, Y H:i') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" 
                                                    onclick="editRate({{ $rate->id }}, '{{ $rate->from_currency }}', '{{ $rate->to_currency }}', {{ $rate->rate }}, '{{ $rate->source }}')">
                                                <i class="mdi mdi-pencil"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteRate({{ $rate->id }})">
                                                <i class="mdi mdi-delete"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center">No exchange rates configured</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    @if($rates->hasPages())
                    <div class="d-flex justify-content-center mt-3">
                        {{ $rates->links() }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Rate Modal -->
<div class="modal fade" id="addRateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Exchange Rate</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('admin.currency.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">From Currency</label>
                        <select class="form-select" name="from_currency" required>
                            <option value="">Select Currency</option>
                            @foreach($supportedCurrencies as $code => $name)
                                <option value="{{ $code }}">{{ $code }} - {{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">To Currency</label>
                        <select class="form-select" name="to_currency" required>
                            <option value="">Select Currency</option>
                            @foreach($supportedCurrencies as $code => $name)
                                <option value="{{ $code }}">{{ $code }} - {{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Exchange Rate</label>
                        <input type="number" class="form-control" name="rate" step="0.000001" min="0.000001" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Source</label>
                        <select class="form-select" name="source" required>
                            <option value="manual">Manual</option>
                            <option value="api">API</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Rate</button>
                </div>
            </form>
        </div>
    </div>
</div><!--
 Fetch Rates Modal -->
<div class="modal fade" id="fetchRatesModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Fetch Exchange Rates</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Base Currency</label>
                    <select class="form-select" id="base_currency">
                        @foreach($supportedCurrencies as $code => $name)
                            <option value="{{ $code }}" {{ $code === 'USD' ? 'selected' : '' }}>{{ $code }} - {{ $name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Target Currencies</label>
                    <select class="form-select" id="target_currencies" multiple size="8">
                        @foreach($supportedCurrencies as $code => $name)
                            <option value="{{ $code }}" {{ in_array($code, ['EUR', 'GBP', 'INR', 'JPY']) ? 'selected' : '' }}>{{ $code }} - {{ $name }}</option>
                        @endforeach
                    </select>
                    <small class="text-muted">Hold Ctrl/Cmd to select multiple currencies</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="fetch-rates-btn">Fetch Rates</button>
            </div>
        </div>
    </div>
</div>

<!-- Test Conversion Modal -->
<div class="modal fade" id="testConversionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test Currency Conversion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Amount</label>
                    <input type="number" class="form-control" id="test_amount" step="0.01" min="0.01" value="100">
                </div>
                <div class="mb-3">
                    <label class="form-label">From Currency</label>
                    <select class="form-select" id="test_from_currency">
                        @foreach($supportedCurrencies as $code => $name)
                            <option value="{{ $code }}" {{ $code === 'USD' ? 'selected' : '' }}>{{ $code }} - {{ $name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">To Currency</label>
                    <select class="form-select" id="test_to_currency">
                        @foreach($supportedCurrencies as $code => $name)
                            <option value="{{ $code }}" {{ $code === 'INR' ? 'selected' : '' }}>{{ $code }} - {{ $name }}</option>
                        @endforeach
                    </select>
                </div>
                <div id="conversion-result" class="alert alert-info" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-info" id="test-conversion-btn">Test Conversion</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Rate Modal -->
<div class="modal fade" id="editRateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Exchange Rate</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="edit-rate-form" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Currency Pair</label>
                        <input type="text" class="form-control" id="edit_currency_pair" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Exchange Rate</label>
                        <input type="number" class="form-control" id="edit_rate" name="rate" step="0.000001" min="0.000001" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Source</label>
                        <select class="form-select" id="edit_source" name="source" required>
                            <option value="manual">Manual</option>
                            <option value="api">API</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Rate</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Track changes in rate inputs
    document.querySelectorAll('.rate-input').forEach(input => {
        input.addEventListener('input', function() {
            const original = parseFloat(this.dataset.original);
            const current = parseFloat(this.value);
            
            if (original !== current) {
                this.classList.add('border-warning');
                document.getElementById('bulk-update-btn').style.display = 'inline-block';
            } else {
                this.classList.remove('border-warning');
                checkForChanges();
            }
        });
    });

    // Bulk update functionality
    document.getElementById('bulk-update-btn').addEventListener('click', function() {
        const changes = [];
        document.querySelectorAll('.rate-input.border-warning').forEach(input => {
            const row = input.closest('tr');
            changes.push({
                id: row.dataset.rateId,
                rate: input.value
            });
        });

        if (changes.length === 0) return;

        fetch('{{ route("admin.currency.bulk-update") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ rates: changes })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                location.reload();
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            showAlert('danger', 'An error occurred while updating rates');
        });
    });

    // Fetch rates functionality
    document.getElementById('fetch-rates-btn').addEventListener('click', function() {
        const baseCurrency = document.getElementById('base_currency').value;
        const targetCurrencies = Array.from(document.getElementById('target_currencies').selectedOptions)
            .map(option => option.value);

        if (targetCurrencies.length === 0) {
            showAlert('warning', 'Please select at least one target currency');
            return;
        }

        this.disabled = true;
        this.innerHTML = '<i class="mdi mdi-loading mdi-spin"></i> Fetching...';

        fetch('{{ route("admin.currency.fetch-rates") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                base_currency: baseCurrency,
                target_currencies: targetCurrencies
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            showAlert('danger', 'Failed to fetch exchange rates');
        })
        .finally(() => {
            this.disabled = false;
            this.innerHTML = 'Fetch Rates';
            bootstrap.Modal.getInstance(document.getElementById('fetchRatesModal')).hide();
        });
    });

    // Test conversion functionality
    document.getElementById('test-conversion-btn').addEventListener('click', function() {
        const amount = document.getElementById('test_amount').value;
        const fromCurrency = document.getElementById('test_from_currency').value;
        const toCurrency = document.getElementById('test_to_currency').value;

        if (!amount || !fromCurrency || !toCurrency) {
            showAlert('warning', 'Please fill all fields');
            return;
        }

        fetch('{{ route("admin.currency.test-conversion") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                amount: amount,
                from_currency: fromCurrency,
                to_currency: toCurrency
            })
        })
        .then(response => response.json())
        .then(data => {
            const resultDiv = document.getElementById('conversion-result');
            if (data.success) {
                resultDiv.className = 'alert alert-success';
                resultDiv.innerHTML = `
                    <strong>Conversion Result:</strong><br>
                    ${data.original_amount} ${data.from_currency} = ${data.converted_amount.toFixed(6)} ${data.to_currency}<br>
                    <small>Exchange Rate: 1 ${data.from_currency} = ${data.exchange_rate} ${data.to_currency}</small>
                `;
            } else {
                resultDiv.className = 'alert alert-danger';
                resultDiv.innerHTML = `<strong>Error:</strong> ${data.message}`;
            }
            resultDiv.style.display = 'block';
        })
        .catch(error => {
            const resultDiv = document.getElementById('conversion-result');
            resultDiv.className = 'alert alert-danger';
            resultDiv.innerHTML = '<strong>Error:</strong> Failed to test conversion';
            resultDiv.style.display = 'block';
        });
    });

    // Clear cache functionality
    document.getElementById('clear-cache-btn').addEventListener('click', function() {
        if (confirm('Are you sure you want to clear the currency rate cache?')) {
            fetch('{{ route("admin.currency.clear-cache") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(() => {
                showAlert('success', 'Currency rate cache cleared successfully');
            })
            .catch(() => {
                showAlert('danger', 'Failed to clear cache');
            });
        }
    });
});

function checkForChanges() {
    const hasChanges = document.querySelectorAll('.rate-input.border-warning').length > 0;
    document.getElementById('bulk-update-btn').style.display = hasChanges ? 'inline-block' : 'none';
}

function editRate(id, fromCurrency, toCurrency, rate, source) {
    document.getElementById('edit_currency_pair').value = `${fromCurrency} → ${toCurrency}`;
    document.getElementById('edit_rate').value = rate;
    document.getElementById('edit_source').value = source;
    document.getElementById('edit-rate-form').action = `{{ route('admin.currency.update', '') }}/${id}`;
    
    new bootstrap.Modal(document.getElementById('editRateModal')).show();
}

function deleteRate(id) {
    if (confirm('Are you sure you want to delete this exchange rate?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{{ route('admin.currency.destroy', '') }}/${id}`;
        form.innerHTML = `
            @csrf
            @method('DELETE')
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function configureGateway(gatewayId, gatewayName) {
    // This would open a modal to configure gateway currencies
    // Implementation depends on your specific requirements
    alert(`Configure currencies for ${gatewayName} - Feature to be implemented`);
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
@endpush