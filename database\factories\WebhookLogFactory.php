<?php

namespace Database\Factories;

use App\Models\Payment;
use App\Models\PaymentGateway;
use App\Models\WebhookLog;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WebhookLog>
 */
class WebhookLogFactory extends Factory
{
    protected $model = WebhookLog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $eventTypes = [
            'payment.captured',
            'payment.failed',
            'payment.authorized',
            'order.paid',
            'refund.created',
            'refund.processed'
        ];

        $status = $this->faker->randomElement(['pending', 'processed', 'failed']);

        return [
            'gateway_id' => PaymentGateway::factory(),
            'payment_id' => $this->faker->optional(0.8)->randomElement([1, 2, 3, 4, 5]),
            'webhook_id' => 'whook_' . $this->faker->regexify('[A-Za-z0-9]{20}'),
            'event_type' => $this->faker->randomElement($eventTypes),
            'payload' => $this->generatePayload(),
            'signature' => $this->faker->sha256(),
            'status' => $status,
            'processed_at' => $status === 'processed' ? $this->faker->dateTimeBetween('-7 days', 'now') : null,
            'error_message' => $status === 'failed' ? $this->faker->sentence() : null,
            'retry_count' => $status === 'failed' ? $this->faker->numberBetween(0, 3) : 0,
        ];
    }

    /**
     * Create a processed webhook log
     */
    public function processed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'processed',
            'processed_at' => $this->faker->dateTimeBetween('-7 days', 'now'),
            'error_message' => null,
            'retry_count' => 0,
        ]);
    }

    /**
     * Create a failed webhook log
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'processed_at' => null,
            'error_message' => $this->faker->randomElement([
                'Invalid signature verification',
                'Payment not found',
                'Database connection error',
                'Invalid payload format',
                'Gateway service unavailable'
            ]),
            'retry_count' => $this->faker->numberBetween(1, 5),
        ]);
    }

    /**
     * Create a pending webhook log
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'processed_at' => null,
            'error_message' => null,
            'retry_count' => 0,
        ]);
    }

    /**
     * Create a Razorpay webhook log
     */
    public function razorpay(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => $this->faker->randomElement([
                'payment.captured',
                'payment.failed',
                'payment.authorized',
                'order.paid'
            ]),
            'payload' => $this->generateRazorpayPayload(),
        ]);
    }

    /**
     * Create a PayPal webhook log
     */
    public function paypal(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => $this->faker->randomElement([
                'PAYMENT.CAPTURE.COMPLETED',
                'PAYMENT.CAPTURE.DENIED',
                'CHECKOUT.ORDER.APPROVED'
            ]),
            'payload' => $this->generatePayPalPayload(),
        ]);
    }

    /**
     * Create a webhook log for specific payment
     */
    public function forPayment(Payment $payment): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_id' => $payment->id,
            'gateway_id' => $payment->gateway_id,
        ]);
    }

    /**
     * Create a webhook log for specific gateway
     */
    public function forGateway(PaymentGateway $gateway): static
    {
        return $this->state(fn (array $attributes) => [
            'gateway_id' => $gateway->id,
        ]);
    }

    /**
     * Create a recent webhook log
     */
    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
        ]);
    }

    /**
     * Create an old webhook log
     */
    public function old(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween('-90 days', '-30 days'),
        ]);
    }

    /**
     * Create a webhook log with high retry count
     */
    public function highRetryCount(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'retry_count' => $this->faker->numberBetween(5, 10),
            'error_message' => 'Multiple retry attempts failed',
        ]);
    }

    /**
     * Create a webhook log with signature verification failure
     */
    public function signatureFailure(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'error_message' => 'Webhook signature verification failed',
            'retry_count' => 0,
        ]);
    }

    /**
     * Generate generic webhook payload
     */
    private function generatePayload(): array
    {
        return [
            'id' => $this->faker->uuid(),
            'event' => $this->faker->randomElement([
                'payment.captured',
                'payment.failed',
                'order.paid'
            ]),
            'created_at' => now()->timestamp,
            'data' => [
                'id' => 'pay_' . $this->faker->regexify('[A-Za-z0-9]{14}'),
                'amount' => $this->faker->numberBetween(1000, 100000),
                'currency' => $this->faker->randomElement(['INR', 'USD', 'EUR']),
                'status' => $this->faker->randomElement(['captured', 'failed', 'authorized']),
            ]
        ];
    }

    /**
     * Generate Razorpay-specific webhook payload
     */
    private function generateRazorpayPayload(): array
    {
        return [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_' . $this->faker->regexify('[A-Za-z0-9]{14}'),
                        'amount' => $this->faker->numberBetween(100000, 1000000), // Amount in paise
                        'currency' => 'INR',
                        'status' => 'captured',
                        'order_id' => 'order_' . $this->faker->regexify('[A-Za-z0-9]{14}'),
                        'method' => $this->faker->randomElement(['card', 'upi', 'netbanking']),
                        'captured' => true,
                        'created_at' => now()->timestamp,
                    ]
                ]
            ]
        ];
    }

    /**
     * Generate PayPal-specific webhook payload
     */
    private function generatePayPalPayload(): array
    {
        return [
            'id' => 'WH-' . $this->faker->regexify('[A-Z0-9]{17}'),
            'event_type' => 'PAYMENT.CAPTURE.COMPLETED',
            'resource' => [
                'id' => $this->faker->regexify('[A-Z0-9]{17}'),
                'amount' => [
                    'currency_code' => 'USD',
                    'value' => $this->faker->randomFloat(2, 10.00, 1000.00)
                ],
                'status' => 'COMPLETED',
                'create_time' => now()->toISOString(),
                'update_time' => now()->toISOString(),
            ],
            'create_time' => now()->toISOString(),
        ];
    }
}