@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-border-light dark:border-border-dark flex justify-between items-center">
            <h2 class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark">Courier Dictionary</h2>
           {{-- <a href="{{ route('courier_dict.create') }}" class="bg-primary-light dark:bg-primary-dark hover:bg-blue-700 dark:hover:bg-blue-600 text-white px-4 py-2 rounded-md transition duration-150 ease-in-out">
                Add New Term
            </a> --}}
        </div>
        
        <div class="p-6">
            <form action="{{ route('courier_dict.search') }}" method="GET" class="mb-6" id="searchForm">
                <div class="relative">
                    <input type="text" 
                           name="search" 
                           id="searchInput"
                           class="w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50" 
                           placeholder="Search terms..." 
                           value="{{ $search ?? '' }}"
                           autocomplete="off">
                    <div id="autocomplete-results" class="absolute z-10 w-full bg-white dark:bg-bg-dark border border-border-light dark:border-border-dark mt-1 rounded-md shadow-lg hidden"></div>
                </div>
            </form>

            <script>
                const searchInput = document.getElementById('searchInput');
                const autocompleteResults = document.getElementById('autocomplete-results');
                
                let debounceTimer;
                
                searchInput.addEventListener('input', function() {
                    clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(() => {
                        const query = this.value;
                        if (query.length >= 2) {
                            fetch(`/api/dict/autocomplete?query=${encodeURIComponent(query)}`)
                                .then(response => response.json())
                                .then(data => {
                                    autocompleteResults.innerHTML = '';
                                    if (data.length > 0) {
                                        data.forEach(item => {
                                            const div = document.createElement('div');
                                            div.className = 'px-4 py-2 hover:bg-bg-light dark:hover:bg-gray-800 cursor-pointer text-text-primary-light dark:text-text-primary-dark';
                                            div.textContent = item.term;
                                            div.addEventListener('click', () => {
                                                searchInput.value = item.term;
                                                document.getElementById('searchForm').submit();
                                            });
                                            autocompleteResults.appendChild(div);
                                        });
                                        autocompleteResults.classList.remove('hidden');
                                    }
                                });
                        } else {
                            autocompleteResults.classList.add('hidden');
                        }
                    }, 300);
                });

                document.addEventListener('click', function(e) {
                    if (!searchInput.contains(e.target) && !autocompleteResults.contains(e.target)) {
                        autocompleteResults.classList.add('hidden');
                    }
                });
            </script>

            @if(session('success'))
                <div class="bg-green-100 dark:bg-green-900/20 border border-green-400 dark:border-green-500 text-green-700 dark:text-green-300 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            @if(isset($terms) && count($terms) > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-border-light dark:divide-border-dark">
                        <thead class="bg-bg-light dark:bg-gray-800">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Term</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Description</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Tag</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                            @foreach($terms as $term)
                                <tr class="hover:bg-bg-light dark:hover:bg-gray-800">
                                    <td class="px-6 py-4 whitespace-nowrap text-text-primary-light dark:text-text-primary-dark">{{ $term->term }}</td>
                                    <td class="px-6 py-4 text-text-secondary-light dark:text-text-secondary-dark">{{ Str::limit($term->description, 100) }}</td>
                                    <td class="px-6 py-4">
                                        <span class="px-2 py-1 text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full">
                                            {{ $term->tag }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 space-x-2">
                                        <a href="{{ route('courier_dict.show', $term->slug) }}" 
                                           class="inline-flex items-center px-3 py-1 bg-primary-light dark:bg-primary-dark hover:bg-blue-700 dark:hover:bg-blue-600 text-white text-sm rounded-md">
                                            View
                                        </a>
                                        
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 dark:border-blue-500 p-4 mb-4">
                    <p class="text-blue-700 dark:text-blue-300">No terms found. Please add a new term or try a different search.</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection