<?php

namespace App\Services;

class DigipinService
{
    /**
     * DIGIPIN Grid configuration
     * Based on India Post's DIGIPIN system
     */
    private const DIGIPIN_GRID = [
        ['F', 'C', '9', '8'],
        ['J', '3', '2', '7'],
        ['K', '4', '5', '6'],
        ['L', 'M', 'P', 'T']
    ];

    /**
     * Geographic bounds for India
     */
    private const BOUNDS = [
        'minLat' => 2.5,
        'maxLat' => 38.5,
        'minLon' => 63.5,
        'maxLon' => 99.5
    ];

    /**
     * Encode latitude and longitude into a 10-digit alphanumeric DIGIPIN
     *
     * @param float $lat
     * @param float $lon
     * @return string
     * @throws \Exception
     */
    public function encode(float $lat, float $lon): string
    {
        if ($lat < self::BOUNDS['minLat'] || $lat > self::BOUNDS['maxLat']) {
            throw new \Exception('Latitude out of range');
        }
        if ($lon < self::BOUNDS['minLon'] || $lon > self::BOUNDS['maxLon']) {
            throw new \Exception('Longitude out of range');
        }

        $minLat = self::BOUNDS['minLat'];
        $maxLat = self::BOUNDS['maxLat'];
        $minLon = self::BOUNDS['minLon'];
        $maxLon = self::BOUNDS['maxLon'];

        $digiPin = '';

        for ($level = 1; $level <= 10; $level++) {
            $latDiv = ($maxLat - $minLat) / 4;
            $lonDiv = ($maxLon - $minLon) / 4;

            // REVERSED row logic (to match original)
            $row = 3 - floor(($lat - $minLat) / $latDiv);
            $col = floor(($lon - $minLon) / $lonDiv);

            $row = max(0, min($row, 3));
            $col = max(0, min($col, 3));

            $digiPin .= self::DIGIPIN_GRID[$row][$col];

            if ($level === 3 || $level === 6) {
                $digiPin .= '-';
            }

            // Update bounds (reverse logic for row)
            $maxLat = $minLat + $latDiv * (4 - $row);
            $minLat = $minLat + $latDiv * (3 - $row);

            $minLon = $minLon + $lonDiv * $col;
            $maxLon = $minLon + $lonDiv;
        }

        return $digiPin;
    }

    /**
     * Decode a DIGIPIN back into its central latitude and longitude
     *
     * @param string $digiPin
     * @return array
     * @throws \Exception
     */
    public function decode(string $digiPin): array
    {
        $pin = str_replace('-', '', $digiPin);
        if (strlen($pin) !== 10) {
            throw new \Exception('Invalid DIGIPIN');
        }

        $minLat = self::BOUNDS['minLat'];
        $maxLat = self::BOUNDS['maxLat'];
        $minLon = self::BOUNDS['minLon'];
        $maxLon = self::BOUNDS['maxLon'];

        for ($i = 0; $i < 10; $i++) {
            $char = $pin[$i];
            $found = false;
            $ri = -1;
            $ci = -1;

            // Locate character in DIGIPIN grid
            for ($r = 0; $r < 4; $r++) {
                for ($c = 0; $c < 4; $c++) {
                    if (self::DIGIPIN_GRID[$r][$c] === $char) {
                        $ri = $r;
                        $ci = $c;
                        $found = true;
                        break;
                    }
                }
                if ($found) break;
            }

            if (!$found) {
                throw new \Exception('Invalid character in DIGIPIN');
            }

            $latDiv = ($maxLat - $minLat) / 4;
            $lonDiv = ($maxLon - $minLon) / 4;

            $lat1 = $maxLat - $latDiv * ($ri + 1);
            $lat2 = $maxLat - $latDiv * $ri;
            $lon1 = $minLon + $lonDiv * $ci;
            $lon2 = $minLon + $lonDiv * ($ci + 1);

            // Update bounding box for next level
            $minLat = $lat1;
            $maxLat = $lat2;
            $minLon = $lon1;
            $maxLon = $lon2;
        }

        $centerLat = ($minLat + $maxLat) / 2;
        $centerLon = ($minLon + $maxLon) / 2;

        return [
            'latitude' => number_format($centerLat, 6),
            'longitude' => number_format($centerLon, 6)
        ];
    }

    /**
     * Get DIGIPIN configuration information
     *
     * @return array
     */
    public function getInfo(): array
    {
        return [
            'description' => 'DIGIPIN Encoder and Decoder for India Post',
            'bounds' => self::BOUNDS,
            'grid' => self::DIGIPIN_GRID,
            'validCharacters' => ['F', 'C', '9', '8', 'J', '3', '2', '7', 'K', '4', '5', '6', 'L', 'M', 'P', 'T']
        ];
    }

    /**
     * Validate if a DIGIPIN is in correct format
     *
     * @param string $digiPin
     * @return bool
     */
    public function isValidFormat(string $digiPin): bool
    {
        $pin = str_replace('-', '', $digiPin);
        
        if (strlen($pin) !== 10) {
            return false;
        }

        $validChars = ['F', 'C', '9', '8', 'J', '3', '2', '7', 'K', '4', '5', '6', 'L', 'M', 'P', 'T'];
        
        for ($i = 0; $i < strlen($pin); $i++) {
            if (!in_array($pin[$i], $validChars)) {
                return false;
            }
        }

        return true;
    }
} 