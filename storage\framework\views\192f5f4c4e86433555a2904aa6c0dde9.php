<?php $__env->startSection('title', 'Payment Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="py-4">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-bg-dark overflow-hidden shadow-sm sm:rounded-lg border border-border-light dark:border-border-dark">
            <div class="p-6 bg-white dark:bg-bg-dark border-b border-border-light dark:border-border-dark">
                <div class="mb-6">
                    <a href="<?php echo e(route('admin.payments.index')); ?>" class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light">
                        &larr; Back to Payments
                    </a>
                </div>

                <div class="mb-6">
                    <h1 class="text-2xl font-semibold text-text-primary-light dark:text-text-primary-dark">Payment Details</h1>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Payment Information -->
                    <div class="bg-white dark:bg-bg-dark rounded-lg shadow p-6 border border-border-light dark:border-border-dark">
                        <h2 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Payment Information</h2>
                        <dl class="space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Payment ID</dt>
                                <dd class="mt-1 text-sm text-text-primary-light dark:text-text-primary-dark"><?php echo e($payment->payment_id); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Amount</dt>
                                <dd class="mt-1 text-sm text-text-primary-light dark:text-text-primary-dark"><?php echo e($payment->currency); ?> <?php echo e(number_format($payment->amount, 2)); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Status</dt>
                                <dd class="mt-1">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        <?php if($payment->payment_status === 'completed'): ?> bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400
                                        <?php elseif($payment->payment_status === 'refunded'): ?> bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400
                                        <?php elseif($payment->payment_status === 'failed'): ?> bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400
                                        <?php else: ?> bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200
                                        <?php endif; ?>">
                                        <?php echo e(ucfirst($payment->payment_status)); ?>

                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Payment Method</dt>
                                <dd class="mt-1 text-sm text-text-primary-light dark:text-text-primary-dark"><?php echo e(ucfirst($payment->payment_method)); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Paid At</dt>
                                <dd class="mt-1 text-sm text-text-primary-light dark:text-text-primary-dark"><?php echo e($payment->paid_at ? $payment->paid_at->format('M d, Y H:i:s') : 'N/A'); ?></dd>
                            </div>
                        </dl>
                    </div>

                    <!-- Order Information -->
                    <div class="bg-white dark:bg-bg-dark rounded-lg shadow p-6 border border-border-light dark:border-border-dark">
                        <h2 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Order Information</h2>
                        <dl class="space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Order Number</dt>
                                <dd class="mt-1 text-sm text-text-primary-light dark:text-text-primary-dark"><?php echo e($payment->order->order_number); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Plan</dt>
                                <dd class="mt-1 text-sm text-text-primary-light dark:text-text-primary-dark"><?php echo e($payment->order->plan->name); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">User</dt>
                                <dd class="mt-1 text-sm text-text-primary-light dark:text-text-primary-dark"><?php echo e($payment->order->user->name); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">User Email</dt>
                                <dd class="mt-1 text-sm text-text-primary-light dark:text-text-primary-dark"><?php echo e($payment->order->user->email); ?></dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Payment Details -->
                <div class="mt-8">
                    <div class="bg-white dark:bg-bg-dark rounded-lg shadow p-6 border border-border-light dark:border-border-dark">
                        <h2 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Payment Details</h2>
                        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                            <pre class="text-sm text-text-primary-light dark:text-text-primary-dark overflow-x-auto"><?php echo e(json_encode($payment->payment_details, JSON_PRETTY_PRINT)); ?></pre>
                        </div>
                    </div>
                </div>

                <!-- Update Status Form -->
                <?php if($payment->payment_status !== 'refunded'): ?>
                <div class="mt-8">
                    <div class="bg-white dark:bg-bg-dark rounded-lg shadow p-6 border border-border-light dark:border-border-dark">
                        <h2 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Update Payment Status</h2>
                        <form action="<?php echo e(route('admin.payments.update', $payment)); ?>" method="POST" class="space-y-4">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PATCH'); ?>
                            
                            <div>
                                <label for="payment_status" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Status</label>
                                <select name="payment_status" id="payment_status" 
                                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark focus:outline-none focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark sm:text-sm rounded-md">
                                    <option value="completed" <?php echo e($payment->payment_status === 'completed' ? 'selected' : ''); ?>>Completed</option>
                                    <option value="refunded" <?php echo e($payment->payment_status === 'refunded' ? 'selected' : ''); ?>>Refunded</option>
                                    <option value="failed" <?php echo e($payment->payment_status === 'failed' ? 'selected' : ''); ?>>Failed</option>
                                </select>
                            </div>

                            <div>
                                <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-bg-dark">
                                    Update Status
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/admin/payments/show.blade.php ENDPATH**/ ?>