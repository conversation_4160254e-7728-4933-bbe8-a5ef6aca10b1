<?php

require_once 'vendor/autoload.php';

// Load Laravel application
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\LandingPageSection;
use App\Models\LandingPageContent;

echo "Demo: Changing Tools Section Icons\n";
echo "==================================\n\n";

// Get tools section
$toolsSection = LandingPageSection::where('slug', 'tools')->first();
$toolsContent = LandingPageContent::where('section_id', $toolsSection->id)
    ->where('key', 'tools')
    ->first();

if (!$toolsContent) {
    echo "❌ Tools content not found!\n";
    exit;
}

$tools = is_string($toolsContent->value) ? json_decode($toolsContent->value, true) : $toolsContent->value;

echo "📋 Current Tools and Icons:\n";
foreach ($tools as $index => $tool) {
    echo "   " . ($index + 1) . ". {$tool['title']}\n";
    echo "      Current Icon: " . substr($tool['icon'], 0, 50) . "...\n\n";
}

// Demo: Change the first tool's icon to a search icon
echo "🔄 Demo: Changing first tool icon to Search icon...\n";

$searchIcon = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />';
$originalIcon = $tools[0]['icon'];

$tools[0]['icon'] = $searchIcon;

// Save the change
$toolsContent->value = json_encode($tools);
$toolsContent->save();

echo "✅ Icon changed successfully!\n\n";

echo "📋 Updated Tools:\n";
$freshContent = $toolsContent->fresh();
$updatedTools = is_string($freshContent->value) ? json_decode($freshContent->value, true) : $freshContent->value;
foreach ($updatedTools as $index => $tool) {
    echo "   " . ($index + 1) . ". {$tool['title']}\n";
    echo "      Updated Icon: " . substr($tool['icon'], 0, 50) . "...\n";
    if ($index === 0) {
        echo "      ✨ This icon was just changed!\n";
    }
    echo "\n";
}

// Revert the change for demo purposes
echo "🔄 Reverting change for demo purposes...\n";
$tools[0]['icon'] = $originalIcon;
$toolsContent->value = json_encode($tools);
$toolsContent->save();

echo "✅ Change reverted!\n\n";

echo "💡 Available Icons for Reference:\n";
$availableIconsContent = LandingPageContent::where('section_id', $toolsSection->id)
    ->where('key', 'available_icons')
    ->first();

if ($availableIconsContent) {
    $availableIcons = is_string($availableIconsContent->value) ? json_decode($availableIconsContent->value, true) : $availableIconsContent->value;
    
    foreach ($availableIcons as $index => $iconData) {
        echo "   " . ($index + 1) . ". {$iconData['name']}: {$iconData['description']}\n";
    }
}

echo "\n🎉 Demo completed!\n";
echo "\n📝 To change icons permanently:\n";
echo "   1. Use the admin panel (when available)\n";
echo "   2. Use Laravel Tinker\n";
echo "   3. Update database directly\n";
echo "   4. Modify the seeder and re-run it\n";