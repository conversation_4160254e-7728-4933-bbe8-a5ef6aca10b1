<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('webhook_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('gateway_id')->constrained('payment_gateways')->onDelete('cascade')->comment('Payment gateway that sent webhook');
            $table->foreignId('payment_id')->nullable()->constrained('payments')->onDelete('set null')->comment('Associated payment if applicable');
            
            // Webhook identification
            $table->string('webhook_id')->nullable()->comment('Webhook ID from gateway');
            $table->string('event_type', 100)->comment('Type of webhook event');
            
            // Webhook data
            $table->json('payload')->comment('Complete webhook payload');
            $table->string('signature', 500)->nullable()->comment('Webhook signature for verification');
            
            // Processing status
            $table->enum('status', ['pending', 'processed', 'failed'])->default('pending')->comment('Processing status');
            $table->timestamp('processed_at')->nullable()->comment('When webhook was processed');
            $table->text('error_message')->nullable()->comment('Error message if processing failed');
            $table->unsignedInteger('retry_count')->default(0)->comment('Number of processing retries');
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index('status', 'idx_webhook_logs_status');
            $table->index('event_type', 'idx_webhook_logs_event_type');
            $table->index('created_at', 'idx_webhook_logs_created_at');
            $table->index(['gateway_id', 'status'], 'idx_webhook_logs_gateway_status');
            $table->index(['payment_id', 'status'], 'idx_webhook_logs_payment_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('webhook_logs');
    }
};
