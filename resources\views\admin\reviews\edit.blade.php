@extends('admin.layouts.admin')

@section('title', 'Edit Review')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <a href="{{ route('admin.reviews.index') }}" class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light">
            &larr; Back to Reviews
        </a>
    </div>

    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-lg overflow-hidden border border-border-light dark:border-border-dark">
        <div class="px-6 py-4 border-b border-border-light dark:border-border-dark">
            <h1 class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark">Edit Review</h1>
        </div>

        <div class="p-6">
            <form action="{{ route('admin.reviews.update', $review) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="mb-6">
                    <label for="user" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">User</label>
                    <input type="text" id="user" value="{{ $review->user->name }}" class="mt-1 block w-full bg-gray-100 dark:bg-gray-700 border-border-light dark:border-border-dark rounded-md shadow-sm focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark sm:text-sm" disabled>
                </div>

                <div class="mb-6">
                    <label for="pincode" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">Pincode</label>
                    <input type="text" id="pincode" value="{{ $review->pincode->pincode ?? 'N/A' }}" class="mt-1 block w-full bg-gray-100 dark:bg-gray-700 border-border-light dark:border-border-dark rounded-md shadow-sm focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark sm:text-sm" disabled>
                </div>

                <div class="mb-6">
                    <label for="status" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">Status</label>
                    <select id="status" name="status" class="mt-1 block w-full border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark sm:text-sm @error('status') border-red-500 @enderror">
                        <option value="pending" {{ $review->status === 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="approved" {{ $review->status === 'approved' ? 'selected' : '' }}>Approved</option>
                        <option value="rejected" {{ $review->status === 'rejected' ? 'selected' : '' }}>Rejected</option>
                    </select>
                    @error('status')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <div class="mb-6">
                    <label for="review" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">Review Content</label>
                    <textarea id="review" name="review" rows="4" class="mt-1 block w-full border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark rounded-md shadow-sm focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark sm:text-sm @error('review') border-red-500 @enderror" required>{{ old('review', $review->review) }}</textarea>
                    @error('review')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <div class="flex justify-end">
                    <button type="submit" class="bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-white px-4 py-2 rounded transition-colors">
                        Update Review
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection 