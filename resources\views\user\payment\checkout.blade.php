@extends('layouts.app')

@section('title', 'Checkout')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Progress Steps -->
            <div class="checkout-progress mb-4">
                <div class="row">
                    <div class="col-4">
                        <div class="step completed">
                            <div class="step-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-title">Plan Selection</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="step active">
                            <div class="step-icon">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <div class="step-title">Payment Method</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="step">
                            <div class="step-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="step-title">Confirmation</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Payment Form -->
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>
                                Payment Method
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="checkout-form" action="{{ route('user.payment.process') }}" method="POST">
                                @csrf
                                <input type="hidden" name="order_id" value="{{ $order->id }}">
                                
                                <!-- Payment Gateway Selection -->
                                <div class="mb-4">
                                    <h6 class="mb-3">Select Payment Method</h6>
                                    <x-payment-gateway-selector 
                                        :gateways="$activeGateways" 
                                        :compact="true" 
                                        :show-description="false" 
                                        :show-currencies="false" />
                                </div>

                                <!-- Customer Information -->
                                <div class="mb-4">
                                    <h6 class="mb-3">Billing Information</h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Full Name</label>
                                            <input type="text" class="form-control" name="billing_name" 
                                                   value="{{ auth()->user()->name ?? '' }}" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Email Address</label>
                                            <input type="email" class="form-control" name="billing_email" 
                                                   value="{{ auth()->user()->email ?? '' }}" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control" name="billing_phone" 
                                                   value="{{ auth()->user()->phone ?? '' }}">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Country</label>
                                            <select class="form-select" name="billing_country" required>
                                                <option value="">Select Country</option>
                                                <option value="IN" selected>India</option>
                                                <option value="US">United States</option>
                                                <option value="GB">United Kingdom</option>
                                                <option value="CA">Canada</option>
                                                <option value="AU">Australia</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Terms and Conditions -->
                                <div class="form-check mb-4">
                                    <input class="form-check-input" type="checkbox" id="terms_accepted" name="terms_accepted" required>
                                    <label class="form-check-label" for="terms_accepted">
                                        I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
                                    </label>
                                </div>

                                <!-- Action Buttons -->
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('plans.public') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        Back to Plans
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg" id="checkout-btn" disabled>
                                        <i class="fas fa-lock me-2"></i>
                                        Complete Payment
                                        <span class="spinner-border spinner-border-sm ms-2" style="display: none;"></span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="col-lg-4">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-receipt me-2"></i>
                                Order Summary
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Plan Details -->
                            <div class="order-item mb-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">{{ $order->plan->name ?? 'Plan' }}</h6>
                                        <small class="text-muted">
                                            {{ $order->plan->duration ?? '' }} {{ $order->plan->duration_type ?? '' }}
                                            @if($order->plan->description)
                                                <br>{{ Str::limit($order->plan->description, 50) }}
                                            @endif
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <strong>{{ $order->currency }} {{ number_format($order->amount, 2) }}</strong>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <!-- Pricing Breakdown -->
                            <div class="pricing-breakdown">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Subtotal:</span>
                                    <span>{{ $order->currency }} {{ number_format($order->amount, 2) }}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Tax:</span>
                                    <span>{{ $order->currency }} 0.00</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Discount:</span>
                                    <span class="text-success">{{ $order->currency }} 0.00</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>Total:</strong>
                                    <strong class="text-primary">{{ $order->currency }} {{ number_format($order->amount, 2) }}</strong>
                                </div>
                            </div>

                            <!-- Security Badges -->
                            <div class="security-badges mt-4">
                                <div class="text-center">
                                    <div class="d-flex justify-content-center gap-3 mb-2">
                                        <i class="fas fa-shield-alt text-success" title="SSL Secured"></i>
                                        <i class="fas fa-lock text-primary" title="Encrypted"></i>
                                        <i class="fas fa-user-shield text-info" title="Privacy Protected"></i>
                                    </div>
                                    <small class="text-muted">
                                        Your payment is secured with 256-bit SSL encryption
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Support Information -->
                    <div class="card shadow-sm mt-3">
                        <div class="card-body text-center">
                            <h6 class="mb-2">Need Help?</h6>
                            <p class="text-muted small mb-3">
                                Our support team is here to help you with any questions.
                            </p>
                            <div class="d-flex justify-content-center gap-2">
                                <a href="#" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-envelope me-1"></i>
                                    Email
                                </a>
                                <a href="#" class="btn btn-sm btn-outline-success">
                                    <i class="fas fa-phone me-1"></i>
                                    Call
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.checkout-progress {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 0.5rem;
}

.step {
    text-align: center;
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    right: -50%;
    width: 100%;
    height: 2px;
    background: #dee2e6;
    z-index: 1;
}

.step.completed::after,
.step.active::after {
    background: #007bff;
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #dee2e6;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content-center;
    margin: 0 auto 0.5rem;
    position: relative;
    z-index: 2;
}

.step.completed .step-icon {
    background: #28a745;
    color: white;
}

.step.active .step-icon {
    background: #007bff;
    color: white;
}

.step-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6c757d;
}

.step.completed .step-title,
.step.active .step-title {
    color: #495057;
}

.order-item {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.375rem;
}

.pricing-breakdown {
    font-size: 0.9rem;
}

.security-badges i {
    font-size: 1.5rem;
}

@media (max-width: 768px) {
    .checkout-progress {
        padding: 1rem;
    }
    
    .step-title {
        font-size: 0.75rem;
    }
    
    .step:not(:last-child)::after {
        display: none;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkoutForm = document.getElementById('checkout-form');
    const checkoutBtn = document.getElementById('checkout-btn');
    const termsCheckbox = document.getElementById('terms_accepted');
    
    // Update checkout button state
    function updateCheckoutButton() {
        const selectedGateway = document.querySelector('.gateway-radio:checked');
        const termsAccepted = termsCheckbox.checked;
        const requiredFields = checkoutForm.querySelectorAll('input[required], select[required]');
        
        let allFieldsFilled = true;
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                allFieldsFilled = false;
            }
        });
        
        checkoutBtn.disabled = !(selectedGateway && termsAccepted && allFieldsFilled);
    }
    
    // Listen for changes
    checkoutForm.addEventListener('input', updateCheckoutButton);
    checkoutForm.addEventListener('change', updateCheckoutButton);
    
    // Form submission
    checkoutForm.addEventListener('submit', function(e) {
        const selectedGateway = document.querySelector('.gateway-radio:checked');
        
        if (!selectedGateway) {
            e.preventDefault();
            showAlert('warning', 'Please select a payment method');
            return;
        }
        
        if (!termsCheckbox.checked) {
            e.preventDefault();
            showAlert('warning', 'Please accept the terms and conditions');
            return;
        }
        
        // Show loading state
        checkoutBtn.disabled = true;
        checkoutBtn.querySelector('.spinner-border').style.display = 'inline-block';
        checkoutBtn.innerHTML = checkoutBtn.innerHTML.replace('Complete Payment', 'Processing...');
    });
    
    // Initial state
    updateCheckoutButton();
    
    function showAlert(type, message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            <i class="fas fa-${type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const cardBody = document.querySelector('.card-body');
        cardBody.insertBefore(alertDiv, cardBody.firstChild);
        
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
});
</script>
@endpush