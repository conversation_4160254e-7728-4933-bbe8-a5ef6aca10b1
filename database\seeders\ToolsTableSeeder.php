<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Tool;

class ToolsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tools = [
            [
                'name' => 'Bulk Pin-to-Pin Distance Calculator',
                'slug' => 'bulk-pin-to-pin-distance-calculator',
                'description' => 'Calculate distances between multiple pincodes in bulk for logistics and planning.',
                'content' => 'Upload an Excel file to calculate distances between multiple pincode pairs.',
                'route_name' => 'tools.bulk-pin-to-pin-distance-calculator',
                'thumbnail' => 'bulk-pin-to-pin-distance-calculator.webp',
                'usage_instructions' => 'Upload your Excel file and click Calculate.',
                'meta_title' => 'Bulk Pin-to-Pin Distance Calculator',
                'meta_description' => 'Calculate distances between multiple pincodes in bulk for logistics and planning.',
                'is_featured' => true,
                'is_published' => true,
                'view_count' => 0,
            ],
            [
                'name' => 'Digital Postal Index Number',
                'slug' => 'mydigipin',
                'description' => 'Generate and decode Digipin codes easily.',
                'content' => 'Use this tool to generate or decode Digipin codes.',
                'route_name' => 'tools.mydigipin',
                'thumbnail' => 'mydigipin.webp',
                'usage_instructions' => 'Enter your Digipin or details and click the relevant button.',
                'meta_title' => 'Digital Postal Index Number',
                'meta_description' => 'Generate and decode Digipin codes easily.',
                'is_featured' => true,
                'is_published' => true,
                'view_count' => 0,
            ],
            [
                'name' => 'Distance Calculator Using Coordinates',
                'slug' => 'distance-calculator-using-coordinates',
                'description' => 'Calculate distance between two locations using latitude and longitude.',
                'content' => 'Enter coordinates to get the distance between two points.',
                'route_name' => 'tools.distance-calculator-using-coordinates',
                'thumbnail' => 'distance-calculator-using-coordinates.webp',
                'usage_instructions' => 'Enter latitude and longitude for both locations and click Calculate.',
                'meta_title' => 'Distance Calculator Using Coordinates',
                'meta_description' => 'Calculate distance between two locations using latitude and longitude.',
                'is_featured' => true,
                'is_published' => true,
                'view_count' => 0,
            ],
            [
                'name' => 'District Wise Pincode Download',
                'slug' => 'district-wise-pincode-download',
                'description' => 'Download pincodes for any district in India.',
                'content' => 'Select a district to download all pincodes in Excel format.',
                'route_name' => 'tools.district-wise-pincode-download',
                'thumbnail' => 'district-wise-pincode-download.webp',
                'usage_instructions' => 'Choose a district and click Download.',
                'meta_title' => 'District Wise Pincode Download',
                'meta_description' => 'Download pincodes for any district in India.',
                'is_featured' => true,
                'is_published' => true,
                'view_count' => 0,
            ],
            [
                'name' => 'Pincode Address Search Tool',
                'slug' => 'pincode-address-search-tool',
                'description' => 'Search for address details using pincode.',
                'content' => 'Enter a pincode to get address details.',
                'route_name' => 'tools.pincode-address-search-tool',
                'thumbnail' => 'pincode-address-search-tool.webp',
                'usage_instructions' => 'Enter a pincode and click Search.',
                'meta_title' => 'Pincode Address Search Tool',
                'meta_description' => 'Search for address details using pincode.',
                'is_featured' => true,
                'is_published' => true,
                'view_count' => 0,
            ],
            [
                'name' => 'Pincode Distance Calculator',
                'slug' => 'pincode-distance-calculator',
                'description' => 'Calculate distance between two pincodes.',
                'content' => 'Enter two pincodes to get the distance between them.',
                'route_name' => 'tools.pincode-distance-calculator',
                'thumbnail' => 'pincode-distance-calculator.webp',
                'usage_instructions' => 'Enter source and destination pincodes and click Calculate.',
                'meta_title' => 'Pincode Distance Calculator',
                'meta_description' => 'Calculate distance between two pincodes.',
                'is_featured' => true,
                'is_published' => true,
                'view_count' => 0,
            ],
            [
                'name' => 'Search Pincode of My Location',
                'slug' => 'search-pincode-of-my-location',
                'description' => 'Find the pincode of your current location.',
                'content' => 'Detect your location and get the pincode.',
                'route_name' => 'tools.pincode-of-my-location',
                'thumbnail' => 'pincode-of-my-location.webp',
                'usage_instructions' => 'Allow location access and click Find.',
                'meta_title' => 'Pincode of My Location',
                'meta_description' => 'Find the pincode of your current location.',
                'is_featured' => true,
                'is_published' => true,
                'view_count' => 0,
            ],
            [
                'name' => 'Railway Bike Parcel Charges Calculator',
                'slug' => 'railway-bike-parcel-charges-calculator',
                'description' => 'Calculate railway bike parcel charges easily.',
                'content' => 'Enter details to estimate railway bike parcel charges.',
                'route_name' => 'tools.railway-bike-parcel-charges-calculator',
                'thumbnail' => 'railway-bike-parcel-charges-calculator.webp',
                'usage_instructions' => 'Enter parcel details and click Calculate.',
                'meta_title' => 'Railway Bike Parcel Charges Calculator',
                'meta_description' => 'Calculate railway bike parcel charges easily.',
                'is_featured' => true,
                'is_published' => true,
                'view_count' => 0,
            ],
            [
                'name' => 'Shipping Cost Calculator',
                'slug' => 'shipping-cost-calculator',
                'description' => 'Estimate shipping costs for your parcels.',
                'content' => 'Enter parcel details to estimate shipping costs.',
                'route_name' => 'tools.shipping-cost-calculator',
                'thumbnail' => 'shipping-cost-calculator.webp',
                'usage_instructions' => 'Enter parcel details and click Calculate.',
                'meta_title' => 'Shipping Cost Calculator',
                'meta_description' => 'Estimate shipping costs for your parcels.',
                'is_featured' => true,
                'is_published' => true,
                'view_count' => 0,
            ],
            [
                'name' => 'Pincode Boundaries',
                'slug' => 'pincode-boundaries',
                'description' => 'Find pincode boundaries for any Pincode.',
                'content' => 'Enter a pincode to find pincode boundaries.',
                'route_name' => 'tools.pincode-boundaries',
                'thumbnail' => 'pincode-boundaries.webp',
                'usage_instructions' => 'Enter a pincode and Find Boundaries.',
                'meta_title' => 'Pincode Boundaries',
                'meta_description' => 'Find pincode boundaries for any Pincode.',
                'is_featured' => true,
                'is_published' => true,
                'view_count' => 0,
            ],
        ];

        foreach ($tools as $tool) {
            Tool::updateOrCreate(
                ['slug' => $tool['slug']],
                $tool
            );
        }
    }
}

// php artisan db:seed --class=ToolsTableSeeder