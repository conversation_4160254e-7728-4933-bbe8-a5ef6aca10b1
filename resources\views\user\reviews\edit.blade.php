@extends('layouts.app')

@section('title', 'Edit Review')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <a href="{{ route('user.reviews.index') }}" class="text-blue-500 hover:text-blue-700">
            &larr; Back to Reviews
        </a>
    </div>

    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h1 class="text-2xl font-bold text-gray-800">Edit Review</h1>
        </div>

        <div class="p-6">
            <form action="{{ route('user.reviews.update', $review) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="mb-6">
                    <label for="pincode" class="block text-sm font-medium text-gray-700 mb-2">Pincode</label>
                    <input type="text" id="pincode" value="{{ $review->pincode->pincode }}" class="mt-1 block w-full bg-gray-100 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" disabled>
                </div>

                <div class="mb-6">
                    <label for="review" class="block text-sm font-medium text-gray-700 mb-2">Your Review</label>
                    <textarea id="review" name="review" rows="4" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('review') border-red-500 @enderror" required>{{ old('review', $review->review) }}</textarea>
                    @error('review')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="flex justify-end">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                        Update Review
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection 