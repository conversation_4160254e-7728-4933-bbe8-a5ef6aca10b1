<?php

use App\Models\PinCode;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

// Returns nearest location for valid coordinates
test('returns nearest location for valid coordinates', function () {
    $user = User::factory()->create();
    $delhi = PinCode::factory()->create([
        'name' => 'Connaught Place',
        'pincode' => '110001',
        'district' => 'New Delhi',
        'state' => 'Delhi',
        'latitude' => 28.6139,
        'longitude' => 77.2090,
    ]);
    $mumbai = PinCode::factory()->create([
        'name' => 'Fort',
        'pincode' => '400001',
        'district' => 'Mumbai',
        'state' => 'Maharashtra',
        'latitude' => 19.0760,
        'longitude' => 72.8777,
    ]);

    $response = $this->actingAs($user)->getJson('/api/get-nearest-location/28.61/77.20');
    $response->assertStatus(200)
        ->assertJsonStructure([
            'data' => [
                'nearest_location' => [
                    'post_office',
                    'pincode',
                    'district',
                    'state',
                ],
                'distance',
            ]
        ])
        ->assertJson([
            'data' => [
                'nearest_location' => [
                    'post_office' => 'Connaught Place',
                    'pincode' => '110001',
                    'district' => 'New Delhi',
                    'state' => 'Delhi',
                ]
            ]
        ]);
});

// Returns 404 if no nearby locations found
test('returns 404 if no nearby locations found', function () {
    $user = User::factory()->create();
    // No PinCode in DB
    $response = $this->actingAs($user)->getJson('/api/get-nearest-location/0/0');
    $response->assertStatus(404)
        ->assertJson(['error' => 'No nearby locations found']);
});

// Returns the closest location if multiple are in range
test('returns the closest location if multiple are in range', function () {
    $user = User::factory()->create();
    $near = PinCode::factory()->create([
        'name' => 'Near Place',
        'pincode' => '123456',
        'district' => 'Near District',
        'state' => 'Near State',
        'latitude' => 20.0000,
        'longitude' => 20.0000,
    ]);
    $closer = PinCode::factory()->create([
        'name' => 'Closer Place',
        'pincode' => '654321',
        'district' => 'Closer District',
        'state' => 'Closer State',
        'latitude' => 20.0050,
        'longitude' => 20.0050,
    ]);
    $response = $this->actingAs($user)->getJson('/api/get-nearest-location/20.0050/20.0050');
    $response->assertStatus(200)
        ->assertJson([
            'data' => [
                'nearest_location' => [
                    'post_office' => 'Closer Place',
                    'pincode' => '654321',
                    'district' => 'Closer District',
                    'state' => 'Closer State',
                ]
            ]
        ]);
});

// Handles edge case: coordinates exactly match a PinCode
test('returns location if coordinates exactly match a PinCode', function () {
    $user = User::factory()->create();
    $exact = PinCode::factory()->create([
        'name' => 'Exact Place',
        'pincode' => '222222',
        'district' => 'Exact District',
        'state' => 'Exact State',
        'latitude' => 15.1234,
        'longitude' => 75.5678,
    ]);
    $response = $this->actingAs($user)->getJson('/api/get-nearest-location/15.1234/75.5678');
    $response->assertStatus(200)
        ->assertJson([
            'data' => [
                'nearest_location' => [
                    'post_office' => 'Exact Place',
                    'pincode' => '222222',
                    'district' => 'Exact District',
                    'state' => 'Exact State',
                ],
                'distance' => 0.0
            ]
        ]);
}); 