<?php $__env->startSection('title', 'Import Pincodes'); ?>
<?php $__env->startSection('page-title', 'Bulk Import Pincodes'); ?>

<?php $__env->startSection('content'); ?>
<div class="bg-white dark:bg-bg-dark shadow-md rounded-lg overflow-hidden border border-border-light dark:border-border-dark">
    <div class="px-6 py-4 border-b border-border-light dark:border-border-dark">
        <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">Import Pincodes from CSV</h2>
    </div>

    <div class="p-6">
        <?php if(session('success')): ?>
            <div class="mb-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 text-green-700 dark:text-green-200 px-4 py-3 rounded">
                <?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded">
                <?php echo e(session('error')); ?>

            </div>
        <?php endif; ?>

        <!-- Instructions -->
        <div class="mb-8 bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h3 class="text-lg font-medium text-primary-light dark:text-primary-dark mb-2">Instructions</h3>
            <ul class="list-disc pl-5 text-primary-light dark:text-primary-dark space-y-1">
                <li>Prepare your CSV file with the following columns: <span class="font-mono bg-blue-100 dark:bg-blue-900/30 px-1 rounded">pincode, name, district, state, delivery_status</span></li>
                <li>The first row should be the header row with column names</li>
                <li><strong>pincode:</strong> 6-digit postal code (e.g., 110001)</li>
                <li><strong>name:</strong> Area/Post office name (e.g., Connaught Place)</li>
                <li><strong>district:</strong> District name (e.g., New Delhi)</li>
                <li><strong>state:</strong> State name (e.g., Delhi)</li>
                <li><strong>delivery_status:</strong> Either "Delivery" or "Non-delivery"</li>
                <li>Maximum file size: 10MB</li>
                <li>Only CSV files are supported</li>
            </ul>
            <div class="mt-4">
                <a href="<?php echo e(route('admin.pincodes.download.template')); ?>" class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    Download Sample Template
                </a>
            </div>
        </div>

        <!-- Upload Form -->
        <form action="<?php echo e(route('admin.pincodes.import.process')); ?>" method="POST" enctype="multipart/form-data" class="mb-8">
            <?php echo csrf_field(); ?>
            <div class="space-y-4">
                <div>
                    <label for="csv_file" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">CSV File</label>
                    <div class="flex items-center">
                        <input type="file" name="csv_file" id="csv_file" required accept=".csv"
                            class="block w-full text-sm text-text-secondary-light dark:text-text-secondary-dark file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-light/10 dark:file:bg-primary-dark/10 file:text-primary-light dark:file:text-primary-dark hover:file:bg-primary-light/20 dark:hover:file:bg-primary-dark/20 <?php $__errorArgs = ['csv_file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                    </div>
                    <?php $__errorArgs = ['csv_file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div>
                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input id="header_row" name="header_row" type="checkbox" checked
                                class="h-4 w-4 text-primary-light dark:text-primary-dark border-border-light dark:border-border-dark rounded focus:ring-primary-light dark:focus:ring-primary-dark">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="header_row" class="font-medium text-text-primary-light dark:text-text-primary-dark">File contains header row</label>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark">Check this if your CSV file has a header row with column names</p>
                        </div>
                    </div>
                </div>

                <div>
                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input id="update_existing" name="update_existing" type="checkbox"
                                class="h-4 w-4 text-primary-light dark:text-primary-dark border-border-light dark:border-border-dark rounded focus:ring-primary-light dark:focus:ring-primary-dark">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="update_existing" class="font-medium text-text-primary-light dark:text-text-primary-dark">Update existing records</label>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark">If checked, existing pincodes will be updated. Otherwise, duplicates will be skipped.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6">
                <button type="submit" class="bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-white px-4 py-2 rounded-md text-sm font-medium transition-colors" disabled>
                    Upload and Process
                </button>
            </div>
        </form>

        <!-- Import History -->
        <div>
            <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark mb-4">Recent Import History</h3>
            
            <!-- Progress Tracking for Active Imports -->
            <div id="activeImports" class="mb-6">
                <!-- Active imports will be loaded here via AJAX -->
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-border-light dark:divide-border-dark">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Filename</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Imported By</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Total Records</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Success</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Failed</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                        <?php $__empty_1 = true; $__currentLoopData = $importHistory ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $import): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark"><?php echo e($import->created_at ? $import->created_at->format('M d, Y H:i') : 'N/A'); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark"><?php echo e($import->filename ?? 'N/A'); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark"><?php echo e($import->user ? $import->user->name : 'N/A'); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark"><?php echo e($import->total_records ?? 0); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-green-500 dark:text-green-400"><?php echo e($import->successful_records ?? 0); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-red-500 dark:text-red-400"><?php echo e($import->failed_records ?? 0); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e(($import->status ?? '') === 'completed' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' : (($import->status ?? '') === 'processing' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400')); ?>">
                                        <?php echo e(ucfirst($import->status ?? 'unknown')); ?>

                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                    <?php if($import->has_errors ?? false): ?>
                                        <a href="<?php echo e(route('admin.pincodes.import.errors', $import->id)); ?>" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">
                                            View Errors
                                        </a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="8" class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark text-center">No import history found</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const fileInput = document.getElementById('csv_file');
        const fileLabel = document.querySelector('label[for="csv_file"]');
        const submitButton = document.querySelector('button[type="submit"]');
        
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Validate file type
                if (!file.name.toLowerCase().endsWith('.csv')) {
                    alert('Please select a CSV file.');
                    fileInput.value = '';
                    return;
                }
                
                // Validate file size (10MB = 10 * 1024 * 1024 bytes)
                if (file.size > 10 * 1024 * 1024) {
                    alert('File size must be less than 10MB.');
                    fileInput.value = '';
                    return;
                }
                
                fileLabel.textContent = 'Selected file: ' + file.name;
                submitButton.disabled = false;
            } else {
                fileLabel.textContent = 'CSV File';
                submitButton.disabled = true;
            }
        });
        
        // Disable submit button initially if no file is selected
        if (!fileInput.files[0]) {
            submitButton.disabled = true;
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/admin/pincodes/import.blade.php ENDPATH**/ ?>