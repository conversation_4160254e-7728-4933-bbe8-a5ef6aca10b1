<?php

declare(strict_types=1);

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Auth;

class ApiTokenController extends Controller
{
    /**
     * Display the API tokens page.
     */
    public function index(): View
    {
        $tokens = Auth::user()->tokens()
            ->orderBy('created_at', 'desc')
            ->get();

        return view('user.api-tokens.index', [
            'tokens' => $tokens
        ]);
    }

    /**
     * Show the form for creating a new API token.
     */
    public function create(): View
    {
        return view('user.api-tokens.create');
    }

    /**
     * Store a newly created API token.
     */
    public function store(Request $request): JsonResponse|RedirectResponse
    {
        $user = $request->user();

        try {
            if (!$user->hasActiveSubscription()) {
                throw ValidationException::withMessages([
                    'subscription' => 'You need an active subscription to create API tokens.',
                ]);
            }

            if (!$user->canCreateToken()) {
                throw ValidationException::withMessages([
                    'token_limit' => 'You have reached the maximum number of allowed tokens.',
                ]);
            }

            $request->validate([
                'name' => ['required', 'string', 'max:255'],
            ]);

            $token = $user->createApiToken($request->name);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'token' => $token->plainTextToken,
                    'expires_at' => now()->addDays(User::TOKEN_EXPIRY_DAYS)->format('Y-m-d'),
                ]);
            }

            return redirect()->route('user.api-tokens.index')
                ->with('status', 'api-token-created')
                ->with('plain_text_token', $token->plainTextToken)
                ->with('token_expiry', now()->addDays(User::TOKEN_EXPIRY_DAYS)->format('Y-m-d'));

        } catch (ValidationException $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $e->errors(),
                ], 422);
            }

            throw $e;
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while creating the API token.',
                ], 500);
            }

            throw $e;
        }
    }

    /**
     * Delete an API token.
     */
    public function destroy(Request $request, $token_id): JsonResponse|RedirectResponse
    {
        try {
            $token = $request->user()->tokens()->findOrFail($token_id);
            $token->delete();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'API token deleted successfully.'
                ]);
            }

            return back()->with('status', 'api-token-deleted');
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token not found.'
                ], 404);
            }

            abort(404, 'Token not found.');
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while deleting the token.'
                ], 500);
            }

            throw $e;
        }
    }

    /**
     * Revoke all tokens for the user.
     */
    public function revokeAll(Request $request): JsonResponse|RedirectResponse
    {
        $request->user()->tokens()->delete();

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'All tokens have been revoked.'
            ]);
        }

        return back()->with('status', 'all-tokens-revoked');
    }

    /**
     * Regenerate an existing API token.
     */
    public function regenerate(Request $request, $tokenId)
    {
        $token = $request->user()->tokens()->findOrFail($tokenId);
        $token->delete();

        $newToken = $request->user()->createToken($token->name);

        return response()->json([
            'success' => true,
            'token' => $newToken->plainTextToken,
            'token_regenerated' => true
        ]);
    }
}