<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;

uses(RefreshDatabase::class, WithFaker::class);

// php artisan test tests/Feature/Controllers/Admin/CacheControllerTest.php

/*
|--------------------------------------------------------------------------
| Clear Cache Tests
|--------------------------------------------------------------------------
*/

test('admin can clear cache successfully', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Mock Artisan calls to avoid actual cache clearing during tests
    Artisan::shouldReceive('call')
        ->with('cache:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('config:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('route:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('view:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('clear-compiled')
        ->once()
        ->andReturn(0);

    $response = $this->get(route('admin.clear-cache'));

    $response->assertRedirect();
    $response->assertSessionHas('success', 'Cache cleared successfully!');
});

test('admin can clear cache from different referrer', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Mock Artisan calls
    Artisan::shouldReceive('call')
        ->with('cache:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('config:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('route:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('view:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('clear-compiled')
        ->once()
        ->andReturn(0);

    $response = $this->from('/admin/dashboard')
        ->get(route('admin.clear-cache'));

    $response->assertRedirect('/admin/dashboard');
    $response->assertSessionHas('success', 'Cache cleared successfully!');
});

test('clear cache handles exceptions gracefully', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Mock Artisan to throw an exception
    Artisan::shouldReceive('call')
        ->with('cache:clear')
        ->once()
        ->andThrow(new \Exception('Cache clear failed'));

    $response = $this->get(route('admin.clear-cache'));

    $response->assertRedirect();
    $response->assertSessionHas('error', 'Failed to clear cache: Cache clear failed');
});

test('clear cache handles specific artisan command failures', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Mock first command to succeed, second to fail
    Artisan::shouldReceive('call')
        ->with('cache:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('config:clear')
        ->once()
        ->andThrow(new \Exception('Config clear failed'));

    $response = $this->get(route('admin.clear-cache'));

    $response->assertRedirect();
    $response->assertSessionHas('error', 'Failed to clear cache: Config clear failed');
});

test('clear cache handles all artisan commands in sequence', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Mock all commands to be called in the correct sequence
    Artisan::shouldReceive('call')
        ->with('cache:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('config:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('route:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('view:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('clear-compiled')
        ->once()
        ->andReturn(0);

    $response = $this->get(route('admin.clear-cache'));

    $response->assertRedirect();
    $response->assertSessionHas('success', 'Cache cleared successfully!');
});

test('clear cache handles different exception types', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Mock Artisan to throw different types of exceptions
    Artisan::shouldReceive('call')
        ->with('cache:clear')
        ->once()
        ->andThrow(new \RuntimeException('Runtime error occurred'));

    $response = $this->get(route('admin.clear-cache'));

    $response->assertRedirect();
    $response->assertSessionHas('error', 'Failed to clear cache: Runtime error occurred');
});

test('clear cache handles permission denied exceptions', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Mock Artisan to throw permission exception
    Artisan::shouldReceive('call')
        ->with('cache:clear')
        ->once()
        ->andThrow(new \Exception('Permission denied'));

    $response = $this->get(route('admin.clear-cache'));

    $response->assertRedirect();
    $response->assertSessionHas('error', 'Failed to clear cache: Permission denied');
});

/*
|--------------------------------------------------------------------------
| Authorization Tests
|--------------------------------------------------------------------------
*/

test('non-admin users cannot clear cache', function () {
    $user = User::factory()->create(['role' => 'user']);
    $this->actingAs($user);

    $response = $this->get(route('admin.clear-cache'));

    $response->assertStatus(403);
});

test('guest users cannot clear cache', function () {
    $response = $this->get(route('admin.clear-cache'));

    $response->assertRedirect(route('admin.login'));
});

test('admin users can clear cache', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Mock Artisan calls
    Artisan::shouldReceive('call')
        ->with('cache:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('config:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('route:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('view:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('clear-compiled')
        ->once()
        ->andReturn(0);

    $response = $this->get(route('admin.clear-cache'));

    $response->assertRedirect();
    $response->assertSessionHas('success', 'Cache cleared successfully!');
});

/*
|--------------------------------------------------------------------------
| Edge Cases and Error Handling
|--------------------------------------------------------------------------
*/

test('clear cache handles empty exception message', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Mock Artisan to throw exception with empty message
    Artisan::shouldReceive('call')
        ->with('cache:clear')
        ->once()
        ->andThrow(new \Exception(''));

    $response = $this->get(route('admin.clear-cache'));

    $response->assertRedirect();
    $response->assertSessionHas('error', 'Failed to clear cache: ');
});

test('clear cache handles very long exception message', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $longMessage = str_repeat('This is a very long error message. ', 100);

    // Mock Artisan to throw exception with long message
    Artisan::shouldReceive('call')
        ->with('cache:clear')
        ->once()
        ->andThrow(new \Exception($longMessage));

    $response = $this->get(route('admin.clear-cache'));

    $response->assertRedirect();
    $response->assertSessionHas('error', 'Failed to clear cache: ' . $longMessage);
});

test('clear cache handles special characters in exception message', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $specialMessage = 'Error with special chars: @#$%^&*() <script>alert("xss")</script>';

    // Mock Artisan to throw exception with special characters
    Artisan::shouldReceive('call')
        ->with('cache:clear')
        ->once()
        ->andThrow(new \Exception($specialMessage));

    $response = $this->get(route('admin.clear-cache'));

    $response->assertRedirect();
    $response->assertSessionHas('error', 'Failed to clear cache: ' . $specialMessage);
});

test('clear cache handles unicode characters in exception message', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $unicodeMessage = 'Error with unicode: Café & Résumé 中文';

    // Mock Artisan to throw exception with unicode characters
    Artisan::shouldReceive('call')
        ->with('cache:clear')
        ->once()
        ->andThrow(new \Exception($unicodeMessage));

    $response = $this->get(route('admin.clear-cache'));

    $response->assertRedirect();
    $response->assertSessionHas('error', 'Failed to clear cache: ' . $unicodeMessage);
});

/*
|--------------------------------------------------------------------------
| Integration Tests
|--------------------------------------------------------------------------
*/

test('clear cache redirects to correct referrer', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Test with different referrers
    $referrers = [
        '/admin/dashboard',
        '/admin/users',
        '/admin/blog/posts',
        '/admin/settings',
        'https://example.com/admin',
    ];

    foreach ($referrers as $referrer) {
        // Mock Artisan calls for each iteration
        Artisan::shouldReceive('call')
            ->with('cache:clear')
            ->once()
            ->andReturn(0);

        Artisan::shouldReceive('call')
            ->with('config:clear')
            ->once()
            ->andReturn(0);

        Artisan::shouldReceive('call')
            ->with('route:clear')
            ->once()
            ->andReturn(0);

        Artisan::shouldReceive('call')
            ->with('view:clear')
            ->once()
            ->andReturn(0);

        Artisan::shouldReceive('call')
            ->with('clear-compiled')
            ->once()
            ->andReturn(0);

        $response = $this->from($referrer)
            ->get(route('admin.clear-cache'));

        $response->assertRedirect($referrer);
        $response->assertSessionHas('success', 'Cache cleared successfully!');
    }
});

test('clear cache maintains session after redirect', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Mock Artisan calls
    Artisan::shouldReceive('call')
        ->with('cache:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('config:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('route:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('view:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('clear-compiled')
        ->once()
        ->andReturn(0);

    $response = $this->from('/admin/dashboard')
        ->get(route('admin.clear-cache'));

    $response->assertRedirect('/admin/dashboard');
    
    // Follow the redirect to verify session is maintained
    $redirectResponse = $this->followingRedirects()
        ->get('/admin/dashboard');
    $redirectResponse->assertSessionHas('success', 'Cache cleared successfully!');
});

/*
|--------------------------------------------------------------------------
| Performance and Reliability Tests
|--------------------------------------------------------------------------
*/

test('clear cache handles multiple rapid requests', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Mock Artisan calls for multiple requests
    for ($i = 0; $i < 3; $i++) {
        Artisan::shouldReceive('call')
            ->with('cache:clear')
            ->once()
            ->andReturn(0);

        Artisan::shouldReceive('call')
            ->with('config:clear')
            ->once()
            ->andReturn(0);

        Artisan::shouldReceive('call')
            ->with('route:clear')
            ->once()
            ->andReturn(0);

        Artisan::shouldReceive('call')
            ->with('view:clear')
            ->once()
            ->andReturn(0);

        Artisan::shouldReceive('call')
            ->with('clear-compiled')
            ->once()
            ->andReturn(0);
    }

    // Make multiple rapid requests
    for ($i = 0; $i < 3; $i++) {
        $response = $this->get(route('admin.clear-cache'));
        $response->assertRedirect();
        $response->assertSessionHas('success', 'Cache cleared successfully!');
    }
});

test('clear cache handles partial failures gracefully', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Mock some commands to succeed and others to fail
    Artisan::shouldReceive('call')
        ->with('cache:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('config:clear')
        ->once()
        ->andReturn(0);

    Artisan::shouldReceive('call')
        ->with('route:clear')
        ->once()
        ->andThrow(new \Exception('Route clear failed'));

    $response = $this->get(route('admin.clear-cache'));

    $response->assertRedirect();
    $response->assertSessionHas('error', 'Failed to clear cache: Route clear failed');
}); 