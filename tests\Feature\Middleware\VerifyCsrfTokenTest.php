// <?php

// use App\Http\Middleware\VerifyCsrfToken;
// use Illuminate\Http\Request;
// use Illuminate\Session\TokenMismatchException;
// use Illuminate\Contracts\Encryption\Encrypter;
// use Illuminate\Contracts\Foundation\Application;
// use Illuminate\Support\Facades\Route;

// beforeEach(function () {
//     Route::any('/', fn() => 'ok');
//     Route::any('', fn() => 'ok');
//     $this->middleware = new \App\Http\Middleware\VerifyCsrfToken(
//         $this->app, 
//         $this->app->make(Encrypter::class)
//     );
//     $this->next = function ($request) {
//         return response('Next middleware called');
//     };
// });

// it('passes through requests with valid csrf token', function () {
//     $request = Request::create('/test', 'POST');
//     $request->setLaravelSession($this->app['session.store']);
    
//     // Set a valid CSRF token
//     $token = $this->app['session.store']->token();
//     $request->headers->set('X-CSRF-TOKEN', $token);

//     $response = $this->middleware->handle($request, $this->next);

//     expect($response->getContent())->toBe('Next middleware called');
// });

// it('passes through get requests without csrf token', function () {
//     $request = Request::create('/test', 'GET');
//     $request->setLaravelSession($this->app['session.store']);
//     $response = $this->middleware->handle($request, $this->next);
//     expect($response->getContent())->toBe('Next middleware called');
// });

// it('passes through head requests without csrf token', function () {
//     $request = Request::create('/test', 'HEAD');
//     $request->setLaravelSession($this->app['session.store']);
//     $response = $this->middleware->handle($request, $this->next);
//     expect($response->getContent())->toBe('Next middleware called');
// });

// it('passes through options requests without csrf token', function () {
//     $request = Request::create('/test', 'OPTIONS');
//     $request->setLaravelSession($this->app['session.store']);
//     $response = $this->middleware->handle($request, $this->next);
//     expect($response->getContent())->toBe('Next middleware called');
// });

// it('excludes post office search route from csrf verification', function () {
//     $request = Request::create('/post-office-search', 'POST');
//     $request->setLaravelSession($this->app['session.store']);
//     $response = $this->middleware->handle($request, $this->next);
//     expect($response->getContent())->toBe('Next middleware called');
// });

// it('excludes post office search route with different methods', function () {
//     $methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
//     foreach ($methods as $method) {
//         $request = Request::create('/post-office-search', $method);
//         $request->setLaravelSession($this->app['session.store']);
//         $response = $this->middleware->handle($request, $this->next);
//         expect($response->getContent())->toBe('Next middleware called');
//     }
// });

// it('excludes post office search route with query parameters', function () {
//     $request = Request::create('/post-office-search?q=test&state=delhi', 'POST');
//     $request->setLaravelSession($this->app['session.store']);
//     $response = $this->middleware->handle($request, $this->next);
//     expect($response->getContent())->toBe('Next middleware called');
// });

// it('excludes post office search route with trailing slash', function () {
//     $request = Request::create('/post-office-search/', 'POST');
//     $request->setLaravelSession($this->app['session.store']);
//     $response = $this->middleware->handle($request, $this->next);
//     expect($response->getContent())->toBe('Next middleware called');
// });

// it('does not exclude similar routes', function () {
//     $similarRoutes = [
//         '/post-office-search-results',
//         '/api/post-office-search',
//         '/admin/post-office-search',
//         '/post-office-search-advanced'
//     ];
//     foreach ($similarRoutes as $route) {
//         $request = Request::create($route, 'POST');
//         $request->setLaravelSession($this->app['session.store']);
//         $response = $this->middleware->handle($request, $this->next);
//         expect($response->getContent())->toBe('Next middleware called');
//     }
// });

// it('throws exception for post requests without csrf token', function () {
//     $this->withoutExceptionHandling();
//     $this->expectException(\Illuminate\Session\TokenMismatchException::class);

//     $this->app['session']->start();
//     $sessionName = $this->app['session']->getName();
//     $sessionId = $this->app['session.store']->getId();
//     $token = $this->app['session.store']->token(); // Generate and store CSRF token in session

//     $this->withHeader('Accept', 'text/html')
//          ->withCookie($sessionName, $sessionId)
//          ->post('/test');
// });

// it('throws exception for put requests without csrf token', function () {
//     $this->withoutExceptionHandling();
//     $this->expectException(TokenMismatchException::class);
//     $this->put('/test');
// });

// it('throws exception for delete requests without csrf token', function () {
//     $this->withoutExceptionHandling();
//     $this->expectException(TokenMismatchException::class);
//     $this->delete('/test');
// });

// it('throws exception for patch requests without csrf token', function () {
//     $this->withoutExceptionHandling();
//     $this->expectException(TokenMismatchException::class);
//     $this->patch('/test');
// });

// it('throws exception for invalid csrf token', function () {
//     $this->withoutExceptionHandling();
//     $this->expectException(TokenMismatchException::class);
//     $this->post('/test', [], ['X-CSRF-TOKEN' => 'invalid-token']);
// });

// it('throws exception for missing csrf token in post data', function () {
//     $this->withoutExceptionHandling();
//     $this->expectException(TokenMismatchException::class);
//     $this->post('/test', []);
// });

// it('throws exception for empty csrf token', function () {
//     $this->withoutExceptionHandling();
//     $this->expectException(TokenMismatchException::class);
//     $this->post('/test', [], ['X-CSRF-TOKEN' => '']);
// });

// it('throws exception for null csrf token', function () {
//     $this->withoutExceptionHandling();
//     $this->expectException(TokenMismatchException::class);
//     $this->post('/test', [], ['X-CSRF-TOKEN' => null]);
// });

// it('handles csrf token in request body', function () {
//     $request = Request::create('/test', 'POST');
//     $request->setLaravelSession($this->app['session.store']);
//     // Set a valid CSRF token in the request body
//     $token = $this->app['session.store']->token();
//     $request->merge(['_token' => $token]);
//     $response = $this->middleware->handle($request, $this->next);
//     expect($response->getContent())->toBe('Next middleware called');
// });

// it('handles csrf token in x csrf token header', function () {
//     $request = Request::create('/test', 'POST');
//     $request->setLaravelSession($this->app['session.store']);
//     // Set a valid CSRF token in the X-CSRF-TOKEN header
//     $token = $this->app['session.store']->token();
//     $request->headers->set('X-CSRF-TOKEN', $token);
//     $response = $this->middleware->handle($request, $this->next);
//     expect($response->getContent())->toBe('Next middleware called');
// });

// it('handles csrf token in x xsrf token header', function () {
//     $request = Request::create('/test', 'POST');
//     $request->setLaravelSession($this->app['session.store']);
//     // Set a valid CSRF token in the X-XSRF-TOKEN header
//     $token = $this->app['session.store']->token();
//     $request->headers->set('X-XSRF-TOKEN', $token);
//     $response = $this->middleware->handle($request, $this->next);
//     expect($response->getContent())->toBe('Next middleware called');
// });

// it('handles edge case with empty route', function () {
//     $this->withoutExceptionHandling();
//     $this->expectException(TokenMismatchException::class);
//     $this->post('');
// });

// it('handles edge case with root route', function () {
//     $this->withoutExceptionHandling();
//     $this->expectException(TokenMismatchException::class);
//     $this->post('/');
// });

// it('handles edge case with root route and excluded uri', function () {
//     $request = Request::create('/post-office-search', 'POST');
//     $request->setLaravelSession($this->app['session.store']);
//     $response = $this->middleware->handle($request, $this->next);
//     expect($response->getContent())->toBe('Next middleware called');
// });

// it('handles requests with different content types', function () {
//     $this->withoutExceptionHandling();
//     $this->expectException(TokenMismatchException::class);
//     $contentTypes = [
//         'application/json',
//         'application/xml',
//         'text/plain',
//         'multipart/form-data',
//     ];
//     foreach ($contentTypes as $contentType) {
//         $this->post('/test', [], ['Content-Type' => $contentType]);
//     }
// });

// it('handles requests with different user agents', function () {
//     $this->withoutExceptionHandling();
//     $this->expectException(TokenMismatchException::class);
//     $userAgents = [
//         'Mozilla/5.0',
//         'curl/7.64.1',
//         'PostmanRuntime/7.26.8',
//     ];
//     foreach ($userAgents as $userAgent) {
//         $this->post('/test', [], ['User-Agent' => $userAgent]);
//     }
// });

// it('handles requests with different origins', function () {
//     $this->withoutExceptionHandling();
//     $this->expectException(TokenMismatchException::class);
//     $origins = [
//         'http://localhost',
//         'https://example.com',
//         'http://127.0.0.1',
//     ];
//     foreach ($origins as $origin) {
//         $this->post('/test', [], ['Origin' => $origin]);
//     }
// });

// it('debugs session for post requests without csrf token', function () {
//     $this->app['session']->start();
//     $sessionName = $this->app['session']->getName();
//     $sessionId = $this->app['session.store']->getId();
//     $token = $this->app['session.store']->token();

//     $response = $this->withHeader('Accept', 'text/html')
//          ->withCookie($sessionName, $sessionId)
//          ->post('/test');

//     dump($response->json());
// });