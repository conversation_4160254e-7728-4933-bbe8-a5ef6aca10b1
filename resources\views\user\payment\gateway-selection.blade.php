@extends('layouts.app')

@section('title', 'Select Payment Method')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>
                        Select Payment Method
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Order Summary -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="order-summary bg-light p-3 rounded">
                                <h5 class="mb-3">Order Summary</h5>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Plan:</span>
                                    <strong>{{ $order->plan->name ?? 'N/A' }}</strong>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Duration:</span>
                                    <span>{{ $order->plan->duration ?? 'N/A' }} {{ $order->plan->duration_type ?? '' }}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Amount:</span>
                                    <strong class="text-primary">{{ $order->currency }} {{ number_format($order->amount, 2) }}</strong>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>Total:</strong>
                                    <strong class="text-success">{{ $order->currency }} {{ number_format($order->amount, 2) }}</strong>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="security-info bg-light p-3 rounded">
                                <h6 class="mb-2">
                                    <i class="fas fa-shield-alt text-success me-2"></i>
                                    Secure Payment
                                </h6>
                                <small class="text-muted">
                                    Your payment information is encrypted and secure. We use industry-standard security measures to protect your data.
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Gateway Selection -->
                    <form id="payment-form" action="{{ route('user.payment.process') }}" method="POST">
                        @csrf
                        <input type="hidden" name="order_id" value="{{ $order->id }}">
                        
                        <h5 class="mb-3">Choose Your Payment Method</h5>
                        
                        <div class="payment-gateways">
                            @forelse($activeGateways as $gateway)
                            <div class="gateway-option mb-3" data-gateway="{{ $gateway->name }}">
                                <div class="card gateway-card h-100" style="cursor: pointer;">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-auto">
                                                <div class="form-check">
                                                    <input class="form-check-input gateway-radio" 
                                                           type="radio" 
                                                           name="payment_gateway" 
                                                           value="{{ $gateway->id }}" 
                                                           id="gateway_{{ $gateway->id }}"
                                                           {{ $gateway->is_default ? 'checked' : '' }}>
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                @if($gateway->logo_url)
                                                    <img src="{{ $gateway->logo_url }}" 
                                                         alt="{{ $gateway->display_name }}" 
                                                         class="gateway-logo"
                                                         style="max-height: 40px; max-width: 80px;">
                                                @else
                                                    <div class="gateway-icon bg-primary text-white rounded d-flex align-items-center justify-content-center" 
                                                         style="width: 50px; height: 40px;">
                                                        <i class="fas fa-credit-card"></i>
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="col">
                                                <h6 class="mb-1">{{ $gateway->display_name }}</h6>
                                                @if($gateway->description)
                                                    <small class="text-muted">{{ $gateway->description }}</small>
                                                @endif
                                                
                                                <!-- Supported currencies -->
                                                @if($gateway->supported_currencies && count($gateway->supported_currencies) > 0)
                                                    <div class="mt-2">
                                                        <small class="text-muted">Supports: </small>
                                                        @foreach(array_slice($gateway->supported_currencies, 0, 5) as $currency)
                                                            <span class="badge bg-light text-dark me-1">{{ $currency }}</span>
                                                        @endforeach
                                                        @if(count($gateway->supported_currencies) > 5)
                                                            <span class="badge bg-light text-dark">+{{ count($gateway->supported_currencies) - 5 }} more</span>
                                                        @endif
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="col-auto">
                                                @if($gateway->name === 'razorpay')
                                                    <div class="payment-methods">
                                                        <small class="text-muted d-block">Accepts:</small>
                                                        <div class="d-flex gap-1 mt-1">
                                                            <i class="fab fa-cc-visa text-primary" title="Visa"></i>
                                                            <i class="fab fa-cc-mastercard text-warning" title="Mastercard"></i>
                                                            <i class="fas fa-university text-info" title="Net Banking"></i>
                                                            <i class="fas fa-mobile-alt text-success" title="UPI"></i>
                                                        </div>
                                                    </div>
                                                @elseif($gateway->name === 'paypal')
                                                    <div class="payment-methods">
                                                        <small class="text-muted d-block">Accepts:</small>
                                                        <div class="d-flex gap-1 mt-1">
                                                            <i class="fab fa-paypal text-primary" title="PayPal"></i>
                                                            <i class="fab fa-cc-visa text-primary" title="Visa"></i>
                                                            <i class="fab fa-cc-mastercard text-warning" title="Mastercard"></i>
                                                        </div>
                                                    </div>
                                                @elseif($gateway->name === 'qr_bank_transfer')
                                                    <div class="payment-methods">
                                                        <small class="text-muted d-block">Method:</small>
                                                        <div class="d-flex gap-1 mt-1">
                                                            <i class="fas fa-qrcode text-dark" title="QR Code"></i>
                                                            <i class="fas fa-university text-info" title="Bank Transfer"></i>
                                                        </div>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @empty
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                No payment methods are currently available. Please contact support.
                            </div>
                            @endforelse
                        </div>

                        <!-- Gateway-specific information -->
                        <div id="gateway-info" class="mt-4" style="display: none;">
                            <div class="alert alert-info">
                                <div id="gateway-info-content"></div>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="terms_accepted" name="terms_accepted" required>
                            <label class="form-check-label" for="terms_accepted">
                                I agree to the <a href="#" target="_blank">Terms and Conditions</a> and <a href="#" target="_blank">Privacy Policy</a>
                            </label>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ route('plans.public') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Plans
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg" id="proceed-btn" disabled>
                                <i class="fas fa-lock me-2"></i>
                                Proceed to Payment
                                <span class="spinner-border spinner-border-sm ms-2" style="display: none;"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Payment Security Information -->
            <div class="row mt-4">
                <div class="col-md-4 text-center">
                    <div class="security-feature">
                        <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                        <h6>SSL Encrypted</h6>
                        <small class="text-muted">256-bit SSL encryption protects your data</small>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <div class="security-feature">
                        <i class="fas fa-lock fa-2x text-primary mb-2"></i>
                        <h6>Secure Processing</h6>
                        <small class="text-muted">PCI DSS compliant payment processing</small>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <div class="security-feature">
                        <i class="fas fa-user-shield fa-2x text-info mb-2"></i>
                        <h6>Privacy Protected</h6>
                        <small class="text-muted">Your information is never stored or shared</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.gateway-card {
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.gateway-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,123,255,0.1);
}

.gateway-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.gateway-logo {
    object-fit: contain;
}

.payment-methods i {
    font-size: 1.2em;
}

.security-feature {
    padding: 1rem;
}

.order-summary {
    border-left: 4px solid #007bff;
}

.security-info {
    border-left: 4px solid #28a745;
}

@media (max-width: 768px) {
    .gateway-card .row {
        text-align: center;
    }
    
    .gateway-card .col-auto,
    .gateway-card .col {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .payment-methods {
        margin-top: 0.5rem;
    }
}
</style>
@endpush@push('
scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const gatewayCards = document.querySelectorAll('.gateway-card');
    const gatewayRadios = document.querySelectorAll('.gateway-radio');
    const proceedBtn = document.getElementById('proceed-btn');
    const termsCheckbox = document.getElementById('terms_accepted');
    const paymentForm = document.getElementById('payment-form');
    const gatewayInfo = document.getElementById('gateway-info');
    const gatewayInfoContent = document.getElementById('gateway-info-content');

    // Gateway information content
    const gatewayInfoData = {
        'paypal': {
            title: 'PayPal Payment',
            content: 'You will be redirected to PayPal to complete your payment securely. You can pay with your PayPal account or credit/debit card.',
            icon: 'fab fa-paypal'
        },
        'razorpay': {
            title: 'Razorpay Payment',
            content: 'Pay securely with credit/debit cards, net banking, UPI, or digital wallets. All transactions are processed through Razorpay\'s secure platform.',
            icon: 'fas fa-credit-card'
        },
        'qr_bank_transfer': {
            title: 'QR Code Bank Transfer',
            content: 'Scan the QR code with your banking app to make a direct bank transfer. You\'ll need to upload payment proof for verification.',
            icon: 'fas fa-qrcode'
        }
    };

    // Initialize
    updateProceedButton();
    
    // Set initial selection if default gateway exists
    const defaultGateway = document.querySelector('.gateway-radio:checked');
    if (defaultGateway) {
        selectGateway(defaultGateway);
    }

    // Gateway card click handlers
    gatewayCards.forEach(card => {
        card.addEventListener('click', function() {
            const radio = this.querySelector('.gateway-radio');
            if (radio) {
                radio.checked = true;
                selectGateway(radio);
            }
        });
    });

    // Radio button change handlers
    gatewayRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                selectGateway(this);
            }
        });
    });

    // Terms checkbox handler
    termsCheckbox.addEventListener('change', updateProceedButton);

    // Form submission handler
    paymentForm.addEventListener('submit', function(e) {
        const selectedGateway = document.querySelector('.gateway-radio:checked');
        
        if (!selectedGateway) {
            e.preventDefault();
            showAlert('warning', 'Please select a payment method');
            return;
        }

        if (!termsCheckbox.checked) {
            e.preventDefault();
            showAlert('warning', 'Please accept the terms and conditions');
            return;
        }

        // Show loading state
        proceedBtn.disabled = true;
        proceedBtn.querySelector('.spinner-border').style.display = 'inline-block';
        proceedBtn.innerHTML = proceedBtn.innerHTML.replace('Proceed to Payment', 'Processing...');
    });

    function selectGateway(radio) {
        // Update card selection visual state
        gatewayCards.forEach(card => card.classList.remove('selected'));
        const selectedCard = radio.closest('.gateway-card');
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }

        // Show gateway-specific information
        const gatewayName = radio.closest('.gateway-option').dataset.gateway;
        showGatewayInfo(gatewayName);
        
        updateProceedButton();
    }

    function showGatewayInfo(gatewayName) {
        const info = gatewayInfoData[gatewayName];
        if (info) {
            gatewayInfoContent.innerHTML = `
                <div class="d-flex align-items-start">
                    <i class="${info.icon} fa-2x text-primary me-3 mt-1"></i>
                    <div>
                        <h6 class="mb-2">${info.title}</h6>
                        <p class="mb-0">${info.content}</p>
                    </div>
                </div>
            `;
            gatewayInfo.style.display = 'block';
        } else {
            gatewayInfo.style.display = 'none';
        }
    }

    function updateProceedButton() {
        const selectedGateway = document.querySelector('.gateway-radio:checked');
        const termsAccepted = termsCheckbox.checked;
        
        proceedBtn.disabled = !(selectedGateway && termsAccepted);
    }

    function showAlert(type, message) {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            <i class="fas fa-${type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Insert at top of card body
        const cardBody = document.querySelector('.card-body');
        cardBody.insertBefore(alertDiv, cardBody.firstChild);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    // Handle responsive behavior
    function handleResponsive() {
        const isMobile = window.innerWidth < 768;
        
        gatewayCards.forEach(card => {
            const row = card.querySelector('.row');
            if (isMobile) {
                row.classList.add('text-center');
            } else {
                row.classList.remove('text-center');
            }
        });
    }

    // Initial responsive check
    handleResponsive();
    
    // Listen for window resize
    window.addEventListener('resize', handleResponsive);

    // Add smooth scrolling for mobile
    if (window.innerWidth < 768) {
        gatewayCards.forEach(card => {
            card.addEventListener('click', function() {
                setTimeout(() => {
                    this.scrollIntoView({ 
                        behavior: 'smooth', 
                        block: 'center' 
                    });
                }, 100);
            });
        });
    }
});

// Utility function for currency formatting
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

// Handle back button for better UX
window.addEventListener('pageshow', function(event) {
    if (event.persisted) {
        // Reset form state if page was cached
        const proceedBtn = document.getElementById('proceed-btn');
        if (proceedBtn) {
            proceedBtn.disabled = false;
            proceedBtn.querySelector('.spinner-border').style.display = 'none';
            proceedBtn.innerHTML = proceedBtn.innerHTML.replace('Processing...', 'Proceed to Payment');
        }
    }
});
</script>
@endpush