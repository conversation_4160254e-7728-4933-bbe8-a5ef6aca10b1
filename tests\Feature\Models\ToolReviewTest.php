<?php

use App\Models\Tool;
use App\Models\ToolReview;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('ToolReview Model', function () {
    
    describe('Factory and Creation', function () {
        it('can be created using factory', function () {
            $toolReview = ToolReview::factory()->create();
            
            expect($toolReview)->toBeInstanceOf(ToolReview::class)
                ->and($toolReview->exists)->toBeTrue();
        });

        it('can be created with custom attributes', function () {
            $tool = Tool::factory()->create();
            $user = User::factory()->create();
            
            $toolReview = ToolReview::factory()->create([
                'tool_id' => $tool->id,
                'user_id' => $user->id,
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'review' => 'This is a great tool!',
                'is_approved' => true,
                'rating' => 5
            ]);

            expect($toolReview->tool_id)->toBe($tool->id)
                ->and($toolReview->user_id)->toBe($user->id)
                ->and($toolReview->name)->toBe('John Doe')
                ->and($toolReview->email)->toBe('<EMAIL>')
                ->and($toolReview->review)->toBe('This is a great tool!')
                ->and($toolReview->is_approved)->toBeTrue()
                ->and($toolReview->rating)->toBe(5);
        });

        it('can be created with factory states', function () {
            $approvedReview = ToolReview::factory()->approved()->create();
            $unapprovedReview = ToolReview::factory()->unapproved()->create();
            $ratedReview = ToolReview::factory()->rating(4)->create();
            
            expect($approvedReview->is_approved)->toBeTrue()
                ->and($unapprovedReview->is_approved)->toBeFalse()
                ->and($ratedReview->rating)->toBe(4);
        });
    });

    describe('Fillable Attributes', function () {
        it('allows mass assignment of fillable attributes', function () {
            $tool = Tool::factory()->create();
            $user = User::factory()->create();
            
            $attributes = [
                'tool_id' => $tool->id,
                'user_id' => $user->id,
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'review' => 'Excellent tool, very useful!',
                'is_approved' => true,
                'rating' => 4
            ];

            $toolReview = ToolReview::create($attributes);

            expect($toolReview->tool_id)->toBe($tool->id)
                ->and($toolReview->user_id)->toBe($user->id)
                ->and($toolReview->name)->toBe('Jane Smith')
                ->and($toolReview->email)->toBe('<EMAIL>')
                ->and($toolReview->review)->toBe('Excellent tool, very useful!')
                ->and($toolReview->is_approved)->toBeTrue()
                ->and($toolReview->rating)->toBe(4);
        });
    });

    describe('Casts', function () {
        it('casts is_approved to boolean', function () {
            $toolReview = ToolReview::factory()->create(['is_approved' => 1]);
            
            expect($toolReview->is_approved)->toBeTrue()
                ->and($toolReview->is_approved)->toBeBool();
        });

        it('casts rating to integer', function () {
            $toolReview = ToolReview::factory()->create(['rating' => '4']);
            
            expect($toolReview->rating)->toBe(4)
                ->and($toolReview->rating)->toBeInt();
        });

        it('casts created_at and updated_at to datetime', function () {
            $toolReview = ToolReview::factory()->create();
            
            expect($toolReview->created_at)->toBeInstanceOf(\Illuminate\Support\Carbon::class)
                ->and($toolReview->updated_at)->toBeInstanceOf(\Illuminate\Support\Carbon::class);
        });
    });

    describe('Relationships', function () {
        it('belongs to a tool', function () {
            $tool = Tool::factory()->create();
            $toolReview = ToolReview::factory()->create(['tool_id' => $tool->id]);
            
            expect($toolReview->tool)->toBeInstanceOf(Tool::class)
                ->and($toolReview->tool->id)->toBe($tool->id);
        });

        it('belongs to a user', function () {
            $user = User::factory()->create();
            $toolReview = ToolReview::factory()->create(['user_id' => $user->id]);
            
            expect($toolReview->user)->toBeInstanceOf(User::class)
                ->and($toolReview->user->id)->toBe($user->id);
        });

        it('can have a null user relationship', function () {
            $toolReview = ToolReview::factory()->create(['user_id' => null]);
            
            expect($toolReview->user)->toBeNull();
        });
    });

    describe('Database Table', function () {
        it('uses correct table name', function () {
            $toolReview = new ToolReview();
            
            expect($toolReview->getTable())->toBe('tool_reviews');
        });
    });

    describe('Model Integration', function () {
        it('can create, update, and delete tool reviews', function () {
            // Create
            $toolReview = ToolReview::factory()->create(['review' => 'Original Review']);
            expect($toolReview->review)->toBe('Original Review');
            
            // Update
            $toolReview->update(['review' => 'Updated Review']);
            expect($toolReview->fresh()->review)->toBe('Updated Review');
            
            // Delete
            $toolReviewId = $toolReview->id;
            $toolReview->delete();
            expect(ToolReview::find($toolReviewId))->toBeNull();
        });

        it('handles null values appropriately', function () {
            $toolReview = ToolReview::factory()->create([
                'name' => null,
                'email' => null,
                'rating' => null,
            ]);

            expect($toolReview->name)->toBeNull()
                ->and($toolReview->email)->toBeNull()
                ->and($toolReview->rating)->toBeNull();
        });

        it('maintains data integrity with boolean fields', function () {
            $toolReview = ToolReview::factory()->create([
                'is_approved' => false,
            ]);

            // Test that booleans are properly stored and retrieved
            expect($toolReview->fresh()->is_approved)->toBeFalse();
        });

        it('maintains user_id when user is deleted (no cascade)', function () {
            $user = User::factory()->create();
            $toolReview = ToolReview::factory()->create(['user_id' => $user->id]);

            $user->delete(); // This will soft delete the user

            // Since there's no foreign key constraint, user_id should remain unchanged
            expect($toolReview->fresh()->user_id)->toBe($user->id);
        });
    });

    describe('Edge Cases', function () {
        it('handles very long review text', function () {
            $longReview = str_repeat('This is a very long review. ', 100); // Create a long string
            $toolReview = ToolReview::factory()->create(['review' => $longReview]);
            
            expect($toolReview->fresh()->review)->toBe($longReview);
        });

        it('handles rating edge values', function () {
            $minRating = ToolReview::factory()->create(['rating' => 1]);
            $maxRating = ToolReview::factory()->create(['rating' => 5]);
            
            expect($minRating->rating)->toBe(1)
                ->and($maxRating->rating)->toBe(5);
        });

        it('cascades deletion when parent tool is deleted', function () {
            $tool = Tool::factory()->create();
            $toolReview = ToolReview::factory()->create(['tool_id' => $tool->id]);
            
            $reviewId = $toolReview->id;
            $tool->delete(); // This should cascade delete the review
            
            expect(ToolReview::find($reviewId))->toBeNull();
        });
    });
});