<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\PaymentGateway;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PaymentAnalyticsController extends Controller
{
    /**
     * Display payment analytics dashboard
     */
    public function index(Request $request)
    {
        $dateRange = $this->getDateRange($request);
        
        $analytics = [
            'overview' => $this->getOverviewStats($dateRange),
            'gatewayStats' => $this->getGatewayStats($dateRange),
            'revenueData' => $this->getRevenueData($dateRange),
            'trendData' => $this->getTrendData($dateRange),
            'topGateways' => $this->getTopGateways($dateRange),
        ];
        
        return view('admin.payment-analytics.index', compact('analytics', 'dateRange'));
    }

    /**
     * Get gateway-wise statistics
     */
    public function gatewayStats(Request $request)
    {
        $dateRange = $this->getDateRange($request);
        $gatewayId = $request->get('gateway_id');
        
        $query = Payment::with('gateway')
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']]);
            
        if ($gatewayId) {
            $query->where('gateway_id', $gatewayId);
        }
        
        $stats = $query->select([
                'gateway_id',
                DB::raw('COUNT(*) as total_transactions'),
                DB::raw('SUM(CASE WHEN payment_status = "completed" THEN 1 ELSE 0 END) as successful_transactions'),
                DB::raw('SUM(CASE WHEN payment_status = "failed" THEN 1 ELSE 0 END) as failed_transactions'),
                DB::raw('SUM(CASE WHEN payment_status = "completed" THEN amount ELSE 0 END) as total_revenue'),
                DB::raw('SUM(CASE WHEN payment_status = "completed" THEN gateway_fee ELSE 0 END) as total_fees'),
                DB::raw('AVG(CASE WHEN payment_status = "completed" THEN amount ELSE NULL END) as avg_transaction_amount'),
                DB::raw('ROUND((SUM(CASE WHEN payment_status = "completed" THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 2) as success_rate')
            ])
            ->groupBy('gateway_id')
            ->get();
            
        return response()->json($stats);
    }

    /**
     * Get revenue reports
     */
    public function revenueReport(Request $request)
    {
        $dateRange = $this->getDateRange($request);
        $groupBy = $request->get('group_by', 'day'); // day, week, month
        
        $dateFormat = match($groupBy) {
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            default => '%Y-%m-%d'
        };
        
        $revenue = Payment::where('payment_status', 'completed')
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->select([
                DB::raw("DATE_FORMAT(created_at, '$dateFormat') as period"),
                DB::raw('SUM(amount) as total_revenue'),
                DB::raw('SUM(gateway_fee) as total_fees'),
                DB::raw('SUM(net_amount) as net_revenue'),
                DB::raw('COUNT(*) as transaction_count'),
                'currency'
            ])
            ->groupBy('period', 'currency')
            ->orderBy('period')
            ->get();
            
        return response()->json($revenue);
    }

    /**
     * Get payment trends analysis
     */
    public function paymentTrends(Request $request)
    {
        $dateRange = $this->getDateRange($request);
        
        $trends = Payment::with('gateway')
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->select([
                DB::raw('DATE(created_at) as date'),
                'gateway_id',
                'payment_status',
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(amount) as total_amount')
            ])
            ->groupBy('date', 'gateway_id', 'payment_status')
            ->orderBy('date')
            ->get();
            
        return response()->json($trends);
    }

    /**
     * Export analytics data
     */
    public function export(Request $request)
    {
        $format = $request->get('format', 'csv'); // csv, pdf
        $dateRange = $this->getDateRange($request);
        
        $data = $this->getExportData($dateRange);
        
        if ($format === 'pdf') {
            return $this->exportToPdf($data, $dateRange);
        }
        
        return $this->exportToCsv($data, $dateRange);
    }

    /**
     * Get overview statistics
     */
    private function getOverviewStats($dateRange)
    {
        $stats = Payment::whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->select([
                DB::raw('COUNT(*) as total_transactions'),
                DB::raw('SUM(CASE WHEN payment_status = "completed" THEN 1 ELSE 0 END) as successful_transactions'),
                DB::raw('SUM(CASE WHEN payment_status = "failed" THEN 1 ELSE 0 END) as failed_transactions'),
                DB::raw('SUM(CASE WHEN payment_status = "pending" THEN 1 ELSE 0 END) as pending_transactions'),
                DB::raw('SUM(CASE WHEN payment_status = "completed" THEN amount ELSE 0 END) as total_revenue'),
                DB::raw('SUM(CASE WHEN payment_status = "completed" THEN gateway_fee ELSE 0 END) as total_fees'),
                DB::raw('SUM(CASE WHEN payment_status = "completed" THEN net_amount ELSE 0 END) as net_revenue'),
                DB::raw('ROUND((SUM(CASE WHEN payment_status = "completed" THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 2) as overall_success_rate')
            ])
            ->first();
            
        return $stats;
    }

    /**
     * Get gateway-wise statistics
     */
    private function getGatewayStats($dateRange)
    {
        return Payment::with('gateway')
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->select([
                'gateway_id',
                DB::raw('COUNT(*) as total_transactions'),
                DB::raw('SUM(CASE WHEN payment_status = "completed" THEN 1 ELSE 0 END) as successful_transactions'),
                DB::raw('SUM(CASE WHEN payment_status = "completed" THEN amount ELSE 0 END) as total_revenue'),
                DB::raw('SUM(CASE WHEN payment_status = "completed" THEN gateway_fee ELSE 0 END) as total_fees'),
                DB::raw('ROUND((SUM(CASE WHEN payment_status = "completed" THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 2) as success_rate')
            ])
            ->groupBy('gateway_id')
            ->orderBy('total_revenue', 'desc')
            ->get();
    }

    /**
     * Get revenue data for charts
     */
    private function getRevenueData($dateRange)
    {
        return Payment::where('payment_status', 'completed')
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->select([
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(amount) as daily_revenue'),
                DB::raw('COUNT(*) as daily_transactions')
            ])
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }

    /**
     * Get trend data for analysis
     */
    private function getTrendData($dateRange)
    {
        return Payment::whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->select([
                DB::raw('DATE(created_at) as date'),
                'payment_status',
                DB::raw('COUNT(*) as count')
            ])
            ->groupBy('date', 'payment_status')
            ->orderBy('date')
            ->get();
    }

    /**
     * Get top performing gateways
     */
    private function getTopGateways($dateRange)
    {
        return PaymentGateway::withCount(['payments' => function ($query) use ($dateRange) {
                $query->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
                      ->where('payment_status', 'completed');
            }])
            ->with(['payments' => function ($query) use ($dateRange) {
                $query->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
                      ->where('payment_status', 'completed')
                      ->select('gateway_id', DB::raw('SUM(amount) as total_revenue'))
                      ->groupBy('gateway_id');
            }])
            ->orderBy('payments_count', 'desc')
            ->limit(5)
            ->get();
    }

    /**
     * Get date range from request
     */
    private function getDateRange(Request $request)
    {
        $start = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $end = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        
        return [
            'start' => Carbon::parse($start)->startOfDay(),
            'end' => Carbon::parse($end)->endOfDay(),
        ];
    }

    /**
     * Get data for export
     */
    private function getExportData($dateRange)
    {
        return Payment::with(['gateway', 'order'])
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->select([
                'id',
                'order_id',
                'gateway_id',
                'gateway_payment_id',
                'amount',
                'currency',
                'gateway_fee',
                'net_amount',
                'payment_status',
                'payment_method',
                'created_at',
                'paid_at'
            ])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Export data to CSV
     */
    private function exportToCsv($data, $dateRange)
    {
        $filename = 'payment_analytics_' . $dateRange['start']->format('Y-m-d') . '_to_' . $dateRange['end']->format('Y-m-d') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];
        
        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Payment ID',
                'Order ID',
                'Gateway',
                'Gateway Payment ID',
                'Amount',
                'Currency',
                'Gateway Fee',
                'Net Amount',
                'Status',
                'Payment Method',
                'Created At',
                'Paid At'
            ]);
            
            // CSV data
            foreach ($data as $payment) {
                fputcsv($file, [
                    $payment->id,
                    $payment->order_id,
                    $payment->gateway->display_name ?? 'N/A',
                    $payment->gateway_payment_id,
                    $payment->amount,
                    $payment->currency,
                    $payment->gateway_fee,
                    $payment->net_amount,
                    $payment->payment_status,
                    $payment->payment_method,
                    $payment->created_at->format('Y-m-d H:i:s'),
                    $payment->paid_at ? $payment->paid_at->format('Y-m-d H:i:s') : 'N/A'
                ]);
            }
            
            fclose($file);
        };
        
        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export data to PDF
     */
    private function exportToPdf($data, $dateRange)
    {
        // This would require a PDF library like DomPDF or similar
        // For now, return a simple response
        return response()->json([
            'message' => 'PDF export functionality requires PDF library implementation',
            'data_count' => $data->count(),
            'date_range' => [
                'start' => $dateRange['start']->format('Y-m-d'),
                'end' => $dateRange['end']->format('Y-m-d')
            ]
        ]);
    }
}