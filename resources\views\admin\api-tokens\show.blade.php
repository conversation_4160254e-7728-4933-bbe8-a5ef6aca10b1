<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-text-primary-light dark:text-text-primary-dark leading-tight">
                {{ __('API Token Details') }}
            </h2>
            <a href="{{ route('admin.api-tokens.index') }}" class="inline-flex items-center px-4 py-2 bg-bg-secondary-light dark:bg-bg-secondary-dark border border-border-light dark:border-border-dark rounded-md font-semibold text-xs text-text-primary-light dark:text-text-primary-dark uppercase tracking-widest hover:bg-bg-secondary-light-hover dark:hover:bg-bg-secondary-dark-hover focus:outline-none focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 disabled:opacity-25 transition">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                {{ __('Back to List') }}
            </a>
        </div>
    </x-slot>

<div class="bg-bg-light dark:bg-bg-dark rounded-lg shadow-md overflow-hidden mb-6 border border-border-light dark:border-border-dark">
    <div class="px-6 py-4 bg-primary-light dark:bg-primary-dark text-white">
        <h3 class="text-lg font-semibold">Token Information</h3>
    </div>
    <div class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <div class="mb-4">
                <h4 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Token Name</h4>
                <p class="text-text-primary-light dark:text-text-primary-dark">{{ $token->name }}</p>
            </div>
            <div class="mb-4">
                <h4 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Token ID</h4>
                <p class="text-text-primary-light dark:text-text-primary-dark">{{ $token->id }}</p>
            </div>
            <div class="mb-4">
                <h4 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Created</h4>
                <p class="text-text-primary-light dark:text-text-primary-dark">{{ $token->created_at->format('M d, Y h:i A') }}</p>
            </div>
        </div>
        <div>
            <div class="mb-4">
                <h4 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Last Used</h4>
                <p class="text-text-primary-light dark:text-text-primary-dark">{{ $token->last_used_at ? $token->last_used_at->format('M d, Y h:i A') : 'Never' }}</p>
            </div>
            <div class="mb-4">
                <h4 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Status</h4>
                <p>
                    @php
                        $isExpired = $token->created_at->addDays(\App\Models\User::TOKEN_EXPIRY_DAYS) < now();
                    @endphp
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $isExpired ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' : 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' }}">
                        {{ $isExpired ? 'Expired' : 'Active' }}
                    </span>
                </p>
            </div>
            <div class="mb-4">
                <h4 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Expires</h4>
                <p class="text-text-primary-light dark:text-text-primary-dark">{{ $token->created_at->addDays(\App\Models\User::TOKEN_EXPIRY_DAYS)->format('M d, Y h:i A') }}</p>
            </div>
        </div>
    </div>
</div>

<div class="bg-bg-light dark:bg-bg-dark rounded-lg shadow-md overflow-hidden mb-6 border border-border-light dark:border-border-dark">
    <div class="px-6 py-4 bg-primary-light dark:bg-primary-dark text-white">
        <h3 class="text-lg font-semibold">User Information</h3>
    </div>
    <div class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <div class="mb-4">
                <h4 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Name</h4>
                <p class="text-text-primary-light dark:text-text-primary-dark">{{ $token->tokenable->name }}</p>
            </div>
            <div class="mb-4">
                <h4 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Email</h4>
                <p class="text-text-primary-light dark:text-text-primary-dark">{{ $token->tokenable->email }}</p>
            </div>
            <div class="mb-4">
                <h4 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Role</h4>
                <p class="text-text-primary-light dark:text-text-primary-dark">{{ ucfirst($token->tokenable->role) }}</p>
            </div>
        </div>
        <div>
            <div class="mb-4">
                <h4 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Status</h4>
                <p>
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $token->tokenable->status === 'active' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' }}">
                        {{ ucfirst($token->tokenable->status) }}
                    </span>
                </p>
            </div>
            <div class="mb-4">
                <h4 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Current Plan</h4>
                <p class="text-text-primary-light dark:text-text-primary-dark">{{ $token->tokenable->getCurrentPlan() }}</p>
            </div>
            <div class="mb-4">
                <h4 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-1">Remaining API Requests</h4>
                <p class="text-text-primary-light dark:text-text-primary-dark">{{ $token->tokenable->getRemainingRequests() }}</p>
            </div>
        </div>
    </div>
</div>

<div class="bg-bg-light dark:bg-bg-dark rounded-lg shadow-md overflow-hidden mb-6 border border-border-light dark:border-border-dark">
    <div class="px-6 py-4 bg-primary-light dark:bg-primary-dark text-white">
        <h3 class="text-lg font-semibold">Usage Statistics</h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="bg-bg-secondary-light dark:bg-bg-secondary-dark p-4 rounded-lg border border-border-light dark:border-border-dark">
                <h4 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Total Requests</h4>
                <p class="mt-1 text-2xl font-bold text-text-primary-light dark:text-text-primary-dark">{{ $usageStats['total_requests'] }}</p>
            </div>
            <div class="bg-bg-secondary-light dark:bg-bg-secondary-dark p-4 rounded-lg border border-border-light dark:border-border-dark">
                <h4 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Successful Requests</h4>
                <p class="mt-1 text-2xl font-bold text-green-600 dark:text-green-400">{{ $usageStats['successful_requests'] }}</p>
            </div>
            <div class="bg-bg-secondary-light dark:bg-bg-secondary-dark p-4 rounded-lg border border-border-light dark:border-border-dark">
                <h4 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Failed Requests</h4>
                <p class="mt-1 text-2xl font-bold text-red-600 dark:text-red-400">{{ $usageStats['failed_requests'] }}</p>
            </div>
        </div>

        @if(count($usageStats['endpoints']) > 0)
        <div class="mt-6">
            <h3 class="text-md font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Most Used Endpoints</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-border-light dark:divide-border-dark">
                    <thead class="bg-bg-secondary-light dark:bg-bg-secondary-dark">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Endpoint</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Count</th>
                        </tr>
                    </thead>
                    <tbody class="bg-bg-light dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                        @foreach($usageStats['endpoints'] as $endpoint)
                            <tr class="hover:bg-bg-secondary-light dark:hover:bg-bg-secondary-dark">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-text-primary-light dark:text-text-primary-dark">{{ $endpoint->endpoint }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark">{{ $endpoint->count }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        @endif
    </div>
</div>

<div class="bg-bg-light dark:bg-bg-dark rounded-lg shadow-md overflow-hidden mb-6 border border-border-light dark:border-border-dark">
    <div class="px-6 py-4 bg-primary-light dark:bg-primary-dark text-white">
        <h3 class="text-lg font-semibold">Recent API Requests</h3>
    </div>
    <div class="p-6">
        @if(count($recentRequests) > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-border-light dark:divide-border-dark">
                    <thead class="bg-bg-secondary-light dark:bg-bg-secondary-dark">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Endpoint</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Method</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">IP Address</th>
                        </tr>
                    </thead>
                    <tbody class="bg-bg-light dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                        @foreach($recentRequests as $request)
                            <tr class="hover:bg-bg-secondary-light dark:hover:bg-bg-secondary-dark">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark">{{ $request->created_at->format('M d, Y h:i:s A') }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark">{{ $request->endpoint }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark">{{ $request->method }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $request->status >= 200 && $request->status < 300 ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' }}">
                                        {{ $request->status }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark">{{ $request->ip_address }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <p class="text-text-secondary-light dark:text-text-secondary-dark">No API requests have been made with this token yet.</p>
        @endif
    </div>
</div>

<div class="mt-6 flex justify-end">
    <form method="POST" action="{{ route('admin.api-tokens.destroy', $token->id) }}">
        @csrf
        @method('DELETE')
        <button type="submit" class="inline-flex items-center px-4 py-2 bg-red-600 dark:bg-red-700 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 dark:hover:bg-red-800 active:bg-red-700 dark:active:bg-red-800 focus:outline-none focus:border-red-700 dark:focus:border-red-800 focus:ring focus:ring-red-300 dark:focus:ring-red-700/30 disabled:opacity-25 transition" onclick="return confirm('Are you sure you want to revoke this token?')">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            {{ __('Revoke Token') }}
        </button>
    </form>
</div>
</x-app-layout>