<?php $__env->startSection('title', 'Orders'); ?>

<?php $__env->startSection('page-title', 'Orders'); ?>

<?php $__env->startSection('content'); ?>
    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">All Orders</h2>
            </div>

            <?php if(session('success')): ?>
                <div
                    class="mb-4 bg-green-100 dark:bg-green-900/20 border-l-4 border-green-500 dark:border-green-700 text-green-700 dark:text-green-200 p-4">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-border-light dark:divide-border-dark hidden md:table">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Order Number</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">User</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider hidden md:table-cell">Plan</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider hidden md:table-cell">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider hidden md:table-cell">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                        <?php $__empty_1 = true; $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-text-primary-light dark:text-text-primary-dark"><?php echo e($order->order_number); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark"><?php echo e($order->user->name); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark hidden md:table-cell"><?php echo e($order->plan_id); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark hidden md:table-cell">₹<?php echo e(number_format($order->amount, 2)); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e($order->status === 'completed' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' : ($order->status === 'cancelled' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400' : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400')); ?>"><?php echo e(ucfirst($order->status)); ?></span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark hidden md:table-cell"><?php echo e($order->created_at->format('M d, Y')); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="<?php echo e(route('admin.orders.show', $order)); ?>" class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light mr-3">View</a>
                                    <form action="<?php echo e(route('admin.orders.destroy', $order)); ?>" method="POST" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to delete this order?')">Delete</button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="px-6 py-4 text-center text-text-secondary-light dark:text-text-secondary-dark">No orders found.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
                <!-- Mobile Card View -->
                <div class="md:hidden">
                    <?php $__empty_1 = true; $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="mb-4 p-4 bg-white dark:bg-bg-dark rounded-lg shadow border border-border-light dark:border-border-dark">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-semibold text-text-primary-light dark:text-text-primary-dark">Order #<?php echo e($order->order_number); ?></span>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e($order->status === 'completed' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' : ($order->status === 'cancelled' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400' : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400')); ?>"><?php echo e(ucfirst($order->status)); ?></span>
                            </div>
                            <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1"><span class="font-medium">User:</span> <?php echo e($order->user->name); ?></div>
                            <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1"><span class="font-medium">Plan:</span> <?php echo e($order->plan_id); ?></div>
                            <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-1"><span class="font-medium">Amount:</span> ₹<?php echo e(number_format($order->amount, 2)); ?></div>
                            <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark mb-2"><span class="font-medium">Date:</span> <?php echo e($order->created_at->format('M d, Y')); ?></div>
                            <div class="flex space-x-4">
                                <a href="<?php echo e(route('admin.orders.show', $order)); ?>" class="text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light">View</a>
                                <form action="<?php echo e(route('admin.orders.destroy', $order)); ?>" method="POST" class="inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to delete this order?')">Delete</button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="p-4 text-center text-text-secondary-light dark:text-text-secondary-dark">No orders found.</div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="mt-4">
                <?php echo e($orders->links()); ?>

            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/admin/orders/index.blade.php ENDPATH**/ ?>