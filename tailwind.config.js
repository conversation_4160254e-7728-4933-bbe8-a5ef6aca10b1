import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';
import typography from '@tailwindcss/typography';

/** @type {import('tailwindcss').Config} */
export default {
    darkMode: 'class',
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        './resources/js/**/*.js',
        './app/Http/Controllers/**/*.php',  // Include controllers that might generate HTML
    ],

    theme: {
        extend: {
            colors: {
                'primary-light': '#0a58ca',
                'primary-dark': '#3d8bfd',
                'accent-light': '#ffb703',
                'accent-dark': '#ffda6a',
                'bg-light': '#f9fafb',
                'bg-dark': '#0f172a',
                'text-primary-light': '#111827',
                'text-primary-dark': '#e5e7eb',
                'text-secondary-light': '#6b7280',
                'text-secondary-dark': '#9ca3af',
                'border-light': '#e5e7eb',
                'border-dark': '#334155'
            }
        }
    },
    plugins: [forms, typography],
};


