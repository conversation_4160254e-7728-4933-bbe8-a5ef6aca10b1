<?php

namespace App\Services\Payment;

use App\Models\Order;
use App\Models\Payment;
use App\Models\PaymentProof;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class QRBankTransferService extends BasePaymentGatewayService
{
    private array $bankDetails;

    public function __construct($gateway)
    {
        $this->gateway = $gateway;
        $this->config = $gateway->getCredentials();
        
        // Validate required configuration before calling parent
        $requiredFields = ['account_name', 'account_number', 'ifsc_code'];
        foreach ($requiredFields as $field) {
            if (empty($this->config[$field])) {
                throw new PaymentGatewayException("Missing required bank transfer configuration: {$field}");
            }
        }
        
        // Now check if gateway is configured (this will pass since we validated above)
        if (!$gateway->isConfigured()) {
            throw new PaymentGatewayException("Gateway {$gateway->name} is not properly configured");
        }
        
        $this->bankDetails = [
            'bank_name' => $this->config['bank_name'] ?? '',
            'account_name' => $this->config['account_name'] ?? '',
            'account_number' => $this->config['account_number'] ?? '',
            'ifsc_code' => $this->config['ifsc_code'] ?? '',
            'branch_name' => $this->config['branch_name'] ?? '',
            'currency' => $this->config['currency'] ?? 'INR',
        ];
    }

    /**
     * Create a payment for the given order.
     */
    public function createPayment(Order $order): PaymentResponse
    {
        try {
            $this->validateOrder($order);

            // Create payment record
            $payment = $this->createPaymentRecord($order, [
                'payment_status' => Payment::STATUS_PENDING,
                'payment_method' => Payment::METHOD_QR_BANK_TRANSFER,
            ]);

            // Generate payment reference
            $paymentReference = $this->generatePaymentReference($order);

            // Generate QR code data
            $qrData = $this->generateQRCodeData($order, $payment, $paymentReference);

            // Update payment record with QR data
            $this->updatePaymentRecord($payment, [
                'payment_details' => [
                    'qr_data' => $qrData,
                    'bank_details' => $this->bankDetails,
                    'payment_reference' => $paymentReference,
                ],
            ]);

            $this->logGatewayInteraction('create_payment', [
                'order_id' => $order->id,
                'payment_id' => $payment->id,
                'payment_reference' => $paymentReference,
            ]);

            return PaymentResponse::success([
                'payment_id' => $payment->id,
                'order_id' => $payment->id,
                'status' => 'pending',
                'message' => 'QR code generated successfully. Please complete the bank transfer and upload payment proof.',
                'currency' => $order->currency,
                'metadata' => [
                    'qr_data' => $qrData,
                    'bank_details' => $this->bankDetails,
                    'payment_reference' => $paymentReference,
                    'upload_url' => url("/payment/{$payment->id}/upload-proof"),
                ],
            ]);

        } catch (\Exception $e) {
            if (isset($payment)) {
                $this->handleFailedPayment($payment, $e->getMessage());
            }
            throw $this->handleGatewayException($e, 'create_payment');
        }
    }

    /**
     * Verify a payment by its ID (manual verification).
     */
    public function verifyPayment(string $paymentId): PaymentResponse
    {
        try {
            $payment = Payment::where('id', $paymentId)
                            ->orWhere('gateway_payment_id', $paymentId)
                            ->first();

            if (!$payment) {
                return PaymentResponse::error('Payment not found');
            }

            // Check if payment proof exists
            $paymentProof = $payment->latestPaymentProof;

            if (!$paymentProof) {
                return PaymentResponse::success([
                    'payment_id' => $payment->id,
                    'status' => 'pending',
                    'message' => 'Waiting for payment proof upload',
                    'currency' => $payment->order->currency,
                ]);
            }

            // Return current verification status
            $status = match($paymentProof->verification_status) {
                PaymentProof::STATUS_APPROVED => Payment::STATUS_COMPLETED,
                PaymentProof::STATUS_REJECTED => Payment::STATUS_FAILED,
                default => Payment::STATUS_PENDING,
            };

            return PaymentResponse::success([
                'payment_id' => $payment->id,
                'status' => $status,
                'message' => $this->getVerificationMessage($paymentProof),
                'currency' => $payment->order->currency,
                'metadata' => [
                    'proof_status' => $paymentProof->verification_status,
                    'verification_notes' => $paymentProof->verification_notes,
                    'verified_at' => $paymentProof->verified_at?->toISOString(),
                ],
            ]);

        } catch (\Exception $e) {
            throw $this->handleGatewayException($e, 'verify_payment');
        }
    }

    /**
     * Handle webhook notifications (not applicable for QR bank transfer).
     */
    public function handleWebhook(Request $request): WebhookResponse
    {
        // QR bank transfer doesn't use webhooks
        return WebhookResponse::error('Webhooks not supported for QR bank transfer');
    }

    /**
     * Refund a payment (manual process for bank transfer).
     */
    public function refundPayment(string $paymentId, float $amount): RefundResponse
    {
        try {
            $payment = Payment::where('id', $paymentId)
                            ->orWhere('gateway_payment_id', $paymentId)
                            ->first();

            if (!$payment || !$payment->canBeRefunded()) {
                return RefundResponse::error('Payment cannot be refunded');
            }

            // For bank transfer, refund is a manual process
            // Update payment record to indicate refund initiated
            $refundId = 'REFUND_' . $payment->id . '_' . time();
            
            $this->updatePaymentRecord($payment, [
                'refund_id' => $refundId,
                'payment_status' => Payment::STATUS_REFUNDED,
                'admin_notes' => "Refund initiated for amount: {$amount} {$payment->currency}",
            ]);

            $this->logGatewayInteraction('refund_payment', [
                'payment_id' => $paymentId,
                'refund_amount' => $amount,
                'refund_id' => $refundId,
            ]);

            return RefundResponse::success([
                'refund_id' => $refundId,
                'payment_id' => $payment->id,
                'refund_amount' => $amount,
                'currency' => $payment->currency,
                'status' => 'initiated',
                'message' => 'Refund initiated. Manual bank transfer required.',
            ]);

        } catch (\Exception $e) {
            throw $this->handleGatewayException($e, 'refund_payment');
        }
    }

    /**
     * Get the current status of a payment.
     */
    public function getPaymentStatus(string $paymentId): PaymentStatus
    {
        try {
            $payment = Payment::where('id', $paymentId)
                            ->orWhere('gateway_payment_id', $paymentId)
                            ->first();

            if (!$payment) {
                return PaymentStatus::error('Payment not found');
            }

            return PaymentStatus::success([
                'payment_id' => $payment->id,
                'status' => $payment->payment_status,
                'amount' => $payment->amount,
                'currency' => $payment->currency,
                'message' => $this->getPaymentStatusMessage($payment),
                'paid_at' => $payment->paid_at,
                'updated_at' => $payment->updated_at,
            ]);

        } catch (\Exception $e) {
            throw $this->handleGatewayException($e, 'get_payment_status');
        }
    }

    /**
     * Test the gateway connection (always returns true for bank transfer).
     */
    public function testConnection(): bool
    {
        // Validate bank details configuration
        return !empty($this->bankDetails['account_name']) && 
               !empty($this->bankDetails['account_number']) && 
               !empty($this->bankDetails['ifsc_code']);
    }

    /**
     * Calculate gateway fee (no fee for bank transfer).
     */
    public function calculateFee(float $amount, string $currency): float
    {
        return 0.0; // No gateway fee for bank transfer
    }

    /**
     * Upload payment proof for verification.
     */
    public function uploadPaymentProof(Payment $payment, $file, array $metadata = []): PaymentProof
    {
        try {
            // Validate file
            $this->validateProofFile($file);

            // Store file
            $filePath = $this->storeProofFile($file, $payment);

            // Create payment proof record
            $paymentProof = PaymentProof::create([
                'payment_id' => $payment->id,
                'file_path' => $filePath,
                'file_name' => $file->getClientOriginalName(),
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'verification_status' => PaymentProof::STATUS_PENDING,
            ]);

            // Update payment record
            $this->updatePaymentRecord($payment, [
                'payment_proof' => $filePath,
            ]);

            // Log the upload
            $this->logGatewayInteraction('upload_proof', [
                'payment_id' => $payment->id,
                'proof_id' => $paymentProof->id,
                'file_name' => $file->getClientOriginalName(),
            ]);

            // TODO: Send notification to admin for verification
            // This would be implemented in a notification service

            return $paymentProof;

        } catch (\Exception $e) {
            throw $this->handleGatewayException($e, 'upload_proof');
        }
    }

    /**
     * Admin verification of payment proof.
     */
    public function verifyPaymentProof(PaymentProof $paymentProof, bool $approved, string $notes = '', int $adminId = null): bool
    {
        try {
            $payment = $paymentProof->payment;

            if ($approved) {
                // Approve payment proof
                $paymentProof->markAsApproved($adminId, $notes);
                
                // Mark payment as completed
                $this->handleSuccessfulPayment($payment, [
                    'verification_method' => 'manual',
                    'verified_by' => $adminId,
                    'verification_notes' => $notes,
                ]);

                $this->logGatewayInteraction('approve_proof', [
                    'payment_id' => $payment->id,
                    'proof_id' => $paymentProof->id,
                    'admin_id' => $adminId,
                ]);

            } else {
                // Reject payment proof
                $paymentProof->markAsRejected($adminId, $notes);
                
                // Mark payment as failed
                $this->handleFailedPayment($payment, $notes ?: 'Payment proof rejected', [
                    'verification_method' => 'manual',
                    'verified_by' => $adminId,
                    'verification_notes' => $notes,
                ]);

                $this->logGatewayInteraction('reject_proof', [
                    'payment_id' => $payment->id,
                    'proof_id' => $paymentProof->id,
                    'admin_id' => $adminId,
                    'reason' => $notes,
                ]);
            }

            return true;

        } catch (\Exception $e) {
            Log::error('Payment proof verification failed', [
                'proof_id' => $paymentProof->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Generate QR code data for bank transfer.
     */
    public function generateQRCodeData(Order $order, Payment $payment = null, string $reference = null): string
    {
        // UPI QR code format for Indian bank transfers
        $upiData = [
            'pa' => $this->bankDetails['account_number'] . '@' . strtolower($this->bankDetails['ifsc_code']),
            'pn' => $this->bankDetails['account_name'],
            'am' => number_format($order->amount, 2, '.', ''),
            'cu' => strtoupper($this->bankDetails['currency']),
            'tn' => "Payment for Order #{$order->id}",
            'tr' => $reference,
        ];

        // Build UPI URL
        $upiUrl = 'upi://pay?' . http_build_query($upiData);

        return $upiUrl;
    }

    /**
     * Validate uploaded proof file.
     */
    public function validateProofFile($file): void
    {
        $allowedMimes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
        $maxSize = 5 * 1024 * 1024; // 5MB

        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new PaymentGatewayException('Invalid file type. Only JPEG, PNG, and PDF files are allowed.');
        }

        if ($file->getSize() > $maxSize) {
            throw new PaymentGatewayException('File size too large. Maximum size is 5MB.');
        }
    }

    /**
     * Store proof file securely.
     */
    public function storeProofFile($file, Payment $payment): string
    {
        $directory = 'payment-proofs/' . date('Y/m');
        $filename = $payment->id . '_' . time() . '.' . $file->getClientOriginalExtension();
        
        return $file->storeAs($directory, $filename, 'private');
    }

    /**
     * Get verification message based on proof status.
     */
    private function getVerificationMessage(PaymentProof $paymentProof): string
    {
        return match($paymentProof->verification_status) {
            PaymentProof::STATUS_APPROVED => 'Payment proof approved. Payment completed successfully.',
            PaymentProof::STATUS_REJECTED => 'Payment proof rejected. ' . ($paymentProof->verification_notes ?: 'Please contact support.'),
            default => 'Payment proof uploaded. Waiting for admin verification.',
        };
    }

    /**
     * Get payment status message.
     */
    private function getPaymentStatusMessage(Payment $payment): string
    {
        if ($payment->payment_status === Payment::STATUS_PENDING) {
            $paymentProof = $payment->latestPaymentProof;
            if ($paymentProof) {
                return 'Payment proof uploaded. Waiting for verification.';
            }
            return 'Waiting for payment proof upload.';
        }

        return match($payment->payment_status) {
            Payment::STATUS_COMPLETED => 'Payment completed successfully.',
            Payment::STATUS_FAILED => 'Payment failed. ' . ($payment->failed_reason ?: 'Please contact support.'),
            Payment::STATUS_REFUNDED => 'Payment refunded.',
            default => 'Payment status unknown.',
        };
    }

    /**
     * Get bank details for display.
     */
    public function getBankDetails(): array
    {
        return $this->bankDetails;
    }

    /**
     * Format amount for display.
     */
    public function formatAmountForDisplay(float $amount, string $currency): string
    {
        $symbol = match(strtoupper($currency)) {
            'INR' => '₹',
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            default => $currency . ' '
        };
        
        return $symbol . number_format($amount, 2);
    }

    /**
     * Generate payment instructions for customer.
     */
    public function getPaymentInstructions(Payment $payment): array
    {
        $paymentDetails = $payment->payment_details ?? [];
        $reference = $paymentDetails['payment_reference'] ?? $payment->id;

        return [
            'title' => 'Bank Transfer Payment Instructions',
            'steps' => [
                '1. Scan the QR code with your banking app or UPI app',
                '2. Verify the payment details (amount, account name)',
                '3. Complete the bank transfer',
                '4. Take a screenshot or photo of the transaction confirmation',
                '5. Upload the payment proof using the upload button below',
                '6. Wait for admin verification (usually within 24 hours)',
            ],
            'bank_details' => $this->bankDetails,
            'payment_reference' => $reference,
            'amount' => $payment->getFormattedAmount(),
            'important_notes' => [
                'Please ensure the payment reference is included in the transfer',
                'Keep the transaction receipt for your records',
                'Upload clear, readable proof of payment',
                'Contact support if you face any issues',
            ],
            'support_info' => [
                'email' => '<EMAIL>',
                'phone' => '******-567-8900',
                'hours' => 'Monday to Friday, 9 AM to 6 PM',
            ],
        ];
    }
}