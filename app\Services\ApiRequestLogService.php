<?php

namespace App\Services;

use App\Models\ApiRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ApiRequestLogService
{
    /**
     * @var ApiRequest
     */
    private $apiRequestModel;

    public function __construct(ApiRequest $apiRequestModel)
    {
        $this->apiRequestModel = $apiRequestModel;
    }

    /**
     * Log an API request and its response
     *
     * @param Request $request
     * @param JsonResponse $response
     * @return bool
     */
    public function logRequest(Request $request, JsonResponse $response): bool
    {
        try {
            $this->apiRequestModel->create([
                'user_id' => auth()->id(),
                'personal_access_token_id' => $request->user()?->currentAccessToken()?->id,
                'endpoint' => $request->path(),
                'method' => $request->method(),
                'status' => $response->getStatusCode(),
                'ip_address' => $request->ip(),
                'request_data' => $request->all(),
                'response_data' => $response->getData(true),
            ]);

            return true;
        } catch (\Exception $e) {
            // Log the error but don't throw it to prevent breaking the API response
            \Log::error('Failed to log API request: ' . $e->getMessage());
            return false;
        }
    }
} 