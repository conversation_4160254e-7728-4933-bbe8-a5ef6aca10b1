<?php

use App\Models\User;
use App\Models\PersonalAccessToken;
use App\Models\ApiRequest;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->admin = User::factory()->create([
        'role' => User::ROLE_ADMIN,
        'status' => User::STATUS_ACTIVE
    ]);
    
    $this->user = User::factory()->create([
        'role' => User::ROLE_USER,
        'status' => User::STATUS_ACTIVE
    ]);
});

test('admin can view api tokens index page', function () {
    $response = $this->actingAs($this->admin)
        ->get(route('admin.api-tokens.index'));

    $response->assertStatus(200)
        ->assertViewIs('admin.api-tokens.index')
        ->assertViewHas(['tokens', 'users', 'tokenStats']);
});

test('non admin users cannot access api tokens index page', function () {
    $response = $this->actingAs($this->user)
        ->get(route('admin.api-tokens.index'));

    $response->assertStatus(403);
});

test('admin can view api token details', function () {
    $token = PersonalAccessToken::factory()->create([
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);

    $response = $this->actingAs($this->admin)
        ->get(route('admin.api-tokens.show', $token->id));

    $response->assertStatus(200)
        ->assertViewIs('admin.api-tokens.show')
        ->assertViewHas(['token', 'recentRequests', 'usageStats']);
});

test('admin can search api tokens', function () {
    $token1 = PersonalAccessToken::factory()->create([
        'name' => 'Test Token 1',
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);

    $token2 = PersonalAccessToken::factory()->create([
        'name' => 'Another Token',
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);

    $response = $this->actingAs($this->admin)
        ->get(route('admin.api-tokens.index', ['search' => 'Test']));

    $response->assertStatus(200)
        ->assertViewIs('admin.api-tokens.index')
        ->assertViewHas('tokens', function ($tokens) {
            return $tokens->count() === 1 && $tokens->first()->name === 'Test Token 1';
        });
});

test('admin can filter api tokens by user', function () {
    $user2 = User::factory()->create();
    
    $token1 = PersonalAccessToken::factory()->create([
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);

    $token2 = PersonalAccessToken::factory()->create([
        'tokenable_id' => $user2->id,
        'tokenable_type' => User::class
    ]);

    $response = $this->actingAs($this->admin)
        ->get(route('admin.api-tokens.index', ['user_id' => $this->user->id]));

    $response->assertStatus(200)
        ->assertViewIs('admin.api-tokens.index')
        ->assertViewHas('tokens', function ($tokens) {
            return $tokens->count() === 1 && $tokens->first()->tokenable_id === $this->user->id;
        });
});

test('admin can revoke api token', function () {
    $token = PersonalAccessToken::factory()->create([
        'name' => 'Test Token',
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);

    $response = $this->actingAs($this->admin)
        ->delete(route('admin.api-tokens.destroy', $token->id));

    $response->assertRedirect(route('admin.api-tokens.index'))
        ->assertSessionHas('success', "Token 'Test Token' for user '{$this->user->name}' has been revoked.");

    $this->assertDatabaseMissing('personal_access_tokens', [
        'id' => $token->id
    ]);
});

test('token stats are correctly calculated', function () {
    // Delete existing tokens to start with a clean slate
    PersonalAccessToken::query()->delete();
    ApiRequest::query()->delete();
    
    // Create some tokens
    $token1 = PersonalAccessToken::factory()->create([
        'created_at' => now()->subDays(5),
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);
    
    $token2 = PersonalAccessToken::factory()->create([
        'created_at' => now()->subDays(User::TOKEN_EXPIRY_DAYS + 1),
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);

    // Create some API requests
    ApiRequest::factory()->count(3)->create([
        'personal_access_token_id' => $token1->id
    ]);

    $response = $this->actingAs($this->admin)
        ->get(route('admin.api-tokens.index'));

    $response->assertStatus(200)
        ->assertViewHas('tokenStats');
        
    $tokenStats = $response->viewData('tokenStats');
    
    expect($tokenStats['total_tokens'])->toBe(2);
    expect($tokenStats['active_tokens'])->toBe(1);
    expect($tokenStats['expired_tokens'])->toBe(1);
    expect($tokenStats['total_requests'])->toBe(3);
    expect($tokenStats['users_with_tokens'])->toBe(1);
});

test('token details show correct usage statistics', function () {
    $token = PersonalAccessToken::factory()->create([
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);

    // Create successful and failed requests
    ApiRequest::factory()->count(3)->create([
        'personal_access_token_id' => $token->id,
        'status' => 200
    ]);

    ApiRequest::factory()->count(2)->create([
        'personal_access_token_id' => $token->id,
        'status' => 400
    ]);

    $response = $this->actingAs($this->admin)
        ->get(route('admin.api-tokens.show', $token->id));

    $response->assertStatus(200)
        ->assertViewHas('usageStats', function ($stats) {
            return $stats['total_requests'] === 5
                && $stats['successful_requests'] === 3
                && $stats['failed_requests'] === 2;
        });
});

test('admin can view api token stats page', function () {
    // Delete existing tokens to start with a clean slate
    PersonalAccessToken::query()->delete();
    ApiRequest::query()->delete();
    
    // Create tokens with different statuses
    $activeToken = PersonalAccessToken::factory()->create([
        'created_at' => now()->subDays(5),
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);
    
    $expiredToken = PersonalAccessToken::factory()->create([
        'created_at' => now()->subDays(User::TOKEN_EXPIRY_DAYS + 1),
        'tokenable_id' => $this->user->id,
        'tokenable_type' => User::class
    ]);

    // Create API requests with different statuses
    ApiRequest::factory()->count(3)->create([
        'personal_access_token_id' => $activeToken->id,
        'status' => 200 // Successful
    ]);

    ApiRequest::factory()->count(2)->create([
        'personal_access_token_id' => $activeToken->id,
        'status' => 400 // Failed
    ]);

    $response = $this->actingAs($this->admin)
        ->get(route('admin.api-tokens.stats'));

    $response->assertStatus(200)
        ->assertViewIs('admin.api-tokens.stats')
        ->assertViewHas('stats');
        
    // Get the stats from the view
    $stats = $response->viewData('stats');
    
    // Assert the stats have the expected structure and values
    expect($stats)->toHaveKeys([
        'total_tokens',
        'active_tokens',
        'expired_tokens',
        'total_requests',
        'successful_requests',
        'failed_requests',
        'users_with_tokens'
    ]);
    
    expect($stats['total_tokens'])->toBe(2);
    expect($stats['active_tokens'])->toBe(1);
    expect($stats['expired_tokens'])->toBe(1);
    expect($stats['total_requests'])->toBe(5);
    expect($stats['successful_requests'])->toBe(3);
    expect($stats['failed_requests'])->toBe(2);
    expect($stats['users_with_tokens'])->toBe(1);
    
    // Assert the stats have the expected values
    expect($stats['total_tokens'])->toBe(2);
    expect($stats['active_tokens'])->toBe(1);
    expect($stats['expired_tokens'])->toBe(1);
    expect($stats['total_requests'])->toBe(5);
    expect($stats['successful_requests'])->toBe(3);
    expect($stats['failed_requests'])->toBe(2);
    expect($stats['users_with_tokens'])->toBe(1);
});