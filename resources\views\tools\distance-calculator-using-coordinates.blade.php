@extends('layouts.app')

@section('json-ld')
    {{-- @include($activeTemplate.'pincodes.json-ld.webpage') 
    @include($activeTemplate.'pincodes.json-ld.breadcrumbs')
    @include($activeTemplate.'pincodes.json-ld.organization') --}}
@endsection

@section('content')
    <x-breadcrumb :segments="$breadcrumbs" :pageTitle="$pageTitle" :metaDescription="$metaDescription" />

    <section class="py-16 bg-bg-light dark:bg-bg-dark transition-colors duration-300">
        <div class="container max-w-6xl mx-auto px-4">
            <div class="flex flex-wrap -mx-4 justify-center">
                <div class="w-full lg:w-2/3 px-4">
                    <div class="space-y-8">
                        <div class="container mt-10" x-data="{
                            formData: {
                                from_lat: 19.0760,
                                from_lng: 72.8777,
                                to_lat: 18.5204,
                                to_lng: 73.8567,
                                _token: '{{ csrf_token() }}'
                            },
                            result: null,
                            error: null,
                            async calculateDistance() {
                                try {
                                    const response = await fetch('{{ route('calculate.distance.coordinates') }}', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json',
                                            'Accept': 'application/json'
                                        },
                                        body: JSON.stringify(this.formData)
                                    });
                                    if (!response.ok) throw new Error('Network response was not ok');
                                    this.result = await response.json();
                                    this.error = null;
                                } catch (e) {
                                    this.error = 'An error occurred. Please try again.';
                                    this.result = null;
                                }
                            }
                        }">
                            <h2 class="text-3xl font-bold text-text-primary-light dark:text-text-primary-dark transition-colors duration-300">Latitude Longitude Distance Calculator</h2>
                            <p class="text-sm mb-6 text-text-secondary-light dark:text-text-secondary-dark transition-colors duration-300">Map Coordinates Distance Calculator</p>

                            <!-- Added descriptive content -->
                            <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark p-6 mb-8 transition-all duration-300">
                                <h2 class="text-xl font-semibold mb-4 text-text-primary-light dark:text-text-primary-dark transition-colors duration-300">Calculate Distance Between Two Points on Earth</h2>
                                <div class="text-text-secondary-light dark:text-text-secondary-dark transition-colors duration-300">
                                    <p class="mb-4">This calculator helps you find the exact distance between any two
                                        points on Earth using their latitude and longitude coordinates. It uses the
                                        Haversine formula to calculate the shortest distance over the Earth's surface,
                                        giving you results in kilometers, miles, and nautical miles.</p>

                                    <div class="bg-primary-light/10 dark:bg-primary-dark/10 p-4 rounded-lg mb-4 border border-primary-light/20 dark:border-primary-dark/20 transition-all duration-300">
                                        <h3 class="text-lg font-medium text-primary-light dark:text-primary-dark mb-2 transition-colors duration-300">How to Use:</h3>
                                        <ol class="list-decimal list-inside space-y-2 text-primary-light dark:text-primary-dark transition-colors duration-300">
                                            <li>Enter the latitude and longitude of your starting point</li>
                                            <li>Enter the coordinates of your destination point</li>
                                            <li>Click "Calculate Distance" to get the results</li>
                                        </ol>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                        <div>
                                            <h3 class="font-medium mb-2 text-text-primary-light dark:text-text-primary-dark transition-colors duration-300">Features:</h3>
                                            <ul class="list-disc list-inside space-y-1 text-text-secondary-light dark:text-text-secondary-dark transition-colors duration-300">
                                                <li>Accurate distance calculation</li>
                                                <li>Multiple units (km, miles, nautical miles)</li>
                                                <li>Initial bearing calculation</li>
                                                <li>Midpoint coordinates</li>
                                            </ul>
                                        </div>
                                        <div>
                                            <h3 class="font-medium mb-2 text-text-primary-light dark:text-text-primary-dark transition-colors duration-300">Default Coordinates:</h3>
                                            <p class="text-text-secondary-light dark:text-text-secondary-dark transition-colors duration-300">Starting Point: Mumbai (19.0760°N, 72.8777°E)<br>
                                                Destination: Pune (18.5204°N, 73.8567°E)</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- End of added content -->

                            <form @submit.prevent="calculateDistance" class="space-y-6 bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark p-6 transition-all duration-300">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="from_lat" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2 transition-colors duration-300">From
                                            Latitude:</label>
                                        <input type="number" step="any"
                                            class="w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark transition-all duration-300"
                                            id="from_lat" x-model="formData.from_lat" required>
                                    </div>
                                    <div>
                                        <label for="from_lng" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2 transition-colors duration-300">From
                                            Longitude:</label>
                                        <input type="number" step="any"
                                            class="w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark transition-all duration-300"
                                            id="from_lng" x-model="formData.from_lng" required>
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="to_lat" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2 transition-colors duration-300">To
                                            Latitude:</label>
                                        <input type="number" step="any"
                                            class="w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark transition-all duration-300"
                                            id="to_lat" x-model="formData.to_lat" required>
                                    </div>
                                    <div>
                                        <label for="to_lng" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2 transition-colors duration-300">To
                                            Longitude:</label>
                                        <input type="number" step="any"
                                            class="w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark transition-all duration-300"
                                            id="to_lng" x-model="formData.to_lng" required>
                                    </div>
                                </div>
                                <button type="submit"
                                    class="w-full bg-primary-light hover:bg-primary-light/90 dark:bg-primary-dark dark:hover:bg-primary-dark/90 text-white font-bold py-3 px-4 rounded-md transition-all duration-300 ease-in-out transform hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl">
                                    Calculate Distance
                                </button>
                            </form>

                            <template x-if="error">
                                <div class="mt-6 bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 dark:border-red-400 p-4 rounded-r-md transition-all duration-300">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-red-400 dark:text-red-300 transition-colors duration-300" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd"
                                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                                    clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm text-red-700 dark:text-red-200 transition-colors duration-300" x-text="error"></p>
                                        </div>
                                    </div>
                                </div>
                            </template>

                            <template x-if="result">
                                <div class="mt-6 bg-white dark:bg-bg-dark shadow-lg rounded-lg overflow-hidden border border-border-light dark:border-border-dark transition-all duration-300">
                                    <div class="bg-primary-light dark:bg-primary-dark text-white px-6 py-4 transition-all duration-300">
                                        <h5 class="text-xl font-semibold mb-0">Distance Calculation Results</h5>
                                    </div>
                                    <div class="p-6">
                                        <h6 class="text-text-secondary-light dark:text-text-secondary-dark mb-4 transition-colors duration-300">The distance between the two points is:</h6>
                                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
                                            <div class="bg-accent-light/10 dark:bg-accent-dark/10 border border-accent-light/20 dark:border-accent-dark/20 rounded-lg p-4 transition-all duration-300">
                                                <h5 class="text-center font-semibold text-text-primary-light dark:text-text-primary-dark transition-colors duration-300" x-text="`${result.distance.km} km`">
                                                </h5>
                                            </div>
                                            <div class="bg-accent-light/10 dark:bg-accent-dark/10 border border-accent-light/20 dark:border-accent-dark/20 rounded-lg p-4 transition-all duration-300">
                                                <h5 class="text-center font-semibold text-text-primary-light dark:text-text-primary-dark transition-colors duration-300"
                                                    x-text="`${result.distance.miles} miles`"></h5>
                                            </div>
                                            <div class="bg-accent-light/10 dark:bg-accent-dark/10 border border-accent-light/20 dark:border-accent-dark/20 rounded-lg p-4 transition-all duration-300">
                                                <h5 class="text-center font-semibold text-text-primary-light dark:text-text-primary-dark transition-colors duration-300"
                                                    x-text="`${result.distance.nautical_miles} nautical miles`"></h5>
                                            </div>
                                        </div>
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 bg-bg-light dark:bg-bg-dark/50 rounded-lg p-4 border border-border-light dark:border-border-dark transition-all duration-300">
                                            <div class="space-y-2">
                                                <p class="text-text-primary-light dark:text-text-primary-dark transition-colors duration-300"><span class="font-semibold text-primary-light dark:text-primary-dark">From:</span> <span
                                                        x-text="result.from.formatted" class="text-text-secondary-light dark:text-text-secondary-dark"></span></p>
                                                <p class="text-text-primary-light dark:text-text-primary-dark transition-colors duration-300"><span class="font-semibold text-primary-light dark:text-primary-dark">To:</span> <span
                                                        x-text="result.to.formatted" class="text-text-secondary-light dark:text-text-secondary-dark"></span></p>
                                            </div>
                                            <div class="space-y-2">
                                                <p class="text-text-primary-light dark:text-text-primary-dark transition-colors duration-300"><span class="font-semibold text-primary-light dark:text-primary-dark">Initial bearing:</span> <span
                                                        x-text="`${result.bearing}°`" class="text-text-secondary-light dark:text-text-secondary-dark"></span></p>
                                                <p class="text-text-primary-light dark:text-text-primary-dark transition-colors duration-300"><span class="font-semibold text-primary-light dark:text-primary-dark">Midpoint:</span> <span
                                                        x-text="`${result.midpoint.latitude}, ${result.midpoint.longitude}`" class="text-text-secondary-light dark:text-text-secondary-dark"></span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <div class="w-full lg:w-1/3 px-4">
                    <div class="sticky top-20">
                        @include('pincodes.partials.sidebar')
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Reviews Section -->
    <div class="container max-w-6xl mx-auto px-4 py-8">
        <x-tool-reviews
            :reviews="$reviews"
            :tool="$tool"
            :hasMoreReviews="$hasMoreReviews ?? false"
            :totalReviewsCount="$totalReviewsCount ?? 0"
        />
    </div>
@endsection