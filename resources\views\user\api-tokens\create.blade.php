@extends('layouts.user')

@section('title', 'Create API Token')

@section('content')
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-bg-dark overflow-hidden shadow-sm dark:shadow-md dark:shadow-gray-800/30 sm:rounded-lg border border-border-light dark:border-border-dark">
                <div class="p-6">
                    <div class="mb-6">
                        <h2 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">
                            {{ __('Create API Token') }}
                        </h2>
                        <p class="mt-1 text-sm text-text-secondary-light dark:text-text-secondary-dark">
                            {{ __('Create a new API token to access the API. Make sure to copy your token now. You won\'t be able to see it again!') }}
                        </p>
                    </div>

                    <form method="POST" action="{{ route('tokens.store') }}" class="space-y-6">
                        @csrf

                        <div>
                            <x-input-label for="name" :value="__('Token Name')" />
                            <x-text-input id="name" name="name" type="text" class="mt-1 block w-full" :value="old('name')" required autofocus />
                            <x-input-error class="mt-2" :messages="$errors->get('name')" />
                        </div>

                        @error('subscription')
                            <div class="rounded-md bg-red-50 dark:bg-red-900/20 p-4 border border-red-200 dark:border-red-800">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400 dark:text-red-500" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-red-700 dark:text-red-300">{{ $message }}</p>
                                    </div>
                                </div>
                            </div>
                        @enderror

                        @error('token_limit')
                            <div class="rounded-md bg-red-50 dark:bg-red-900/20 p-4 border border-red-200 dark:border-red-800">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400 dark:text-red-500" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-red-700 dark:text-red-300">{{ $message }}</p>
                                    </div>
                                </div>
                            </div>
                        @enderror

                        <div class="flex items-center gap-4">
                            <x-primary-button>{{ __('Create Token') }}</x-primary-button>
                            <a href="{{ route('user.api-tokens.index') }}" class="text-sm text-text-secondary-light dark:text-text-secondary-dark hover:text-text-primary-light dark:hover:text-text-primary-dark transition-colors duration-200">
                                {{ __('Cancel') }}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection