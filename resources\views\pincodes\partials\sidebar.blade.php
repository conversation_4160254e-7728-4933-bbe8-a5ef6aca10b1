<div class="w-full">
    <div class="w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-2xl p-6 border border-border-light dark:border-border-dark transition-all duration-300"
         x-data="searchForm()" x-init="init()">
        <h3 class="text-2xl font-bold text-center mb-8 text-text-primary-light dark:text-text-primary-dark">
            Search Pincode by Post Office Name OR Pincode
        </h3>
        <form id="locationForm" @submit.prevent class="space-y-6">
            <!-- State Selection -->
            <div>
                <label for="state" class="block text-sm font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">
                    Select State:
                </label>
                <select name="state" id="state" x-model="stateId" @change="fetchDistricts()"
                    class="w-full rounded-lg border-2 border-border-light dark:border-border-dark shadow-sm py-3 px-4 bg-white dark:bg-gray-700 text-text-primary-light dark:text-text-primary-dark focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark transition-all duration-200 hover:border-primary-light dark:hover:border-primary-dark">
                    <option value="">Select State</option>
                    @foreach ($m_states as $state)
                        <option value="{{ $state->id }}">{{ ucfirst($state->name) }}</option>
                    @endforeach
                </select>
            </div>
            <!-- District Selection -->
            <div>
                <label for="district" class="block text-sm font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">
                    Select District:
                </label>
                <select name="district" id="district" x-model="districtId" @change="handleDistrictChange()"
                    class="w-full rounded-lg border-2 border-border-light dark:border-border-dark shadow-sm py-3 px-4 bg-white dark:bg-gray-700 text-text-primary-light dark:text-text-primary-dark focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark transition-all duration-200 hover:border-primary-light dark:hover:border-primary-dark disabled:opacity-50 disabled:cursor-not-allowed"
                    :disabled="!districts.length || isLoading">
                    <option value="">Select District</option>
                    <template x-for="district in districts" :key="district.id">
                        <option :value="district.name"
                            x-text="district.name.charAt(0).toUpperCase() + district.name.slice(1)"></option>
                    </template>
                </select>
            </div>
            <!-- Post Office Input -->
            <div>
                <label for="post_office" class="block text-sm font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">
                    Enter Post Office Name:
                </label>
                <input type="text" name="post_office" id="post_office" x-model.debounce.300ms="postOffice"
                    @input="handleInputToggle('postOffice')" :disabled="!districtId || isLoading"
                    class="w-full rounded-lg border-2 border-border-light dark:border-border-dark shadow-sm py-3 px-4 bg-white dark:bg-gray-700 text-text-primary-light dark:text-text-primary-dark placeholder-text-secondary-light dark:placeholder-text-secondary-dark focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark transition-all duration-200 hover:border-primary-light dark:hover:border-primary-dark disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder="Ex. Nagpur Central">
            </div>
            <!-- OR Divider -->
            <div class="flex items-center justify-center my-6">
                <div class="w-full border-t-2 border-border-light dark:border-border-dark"></div>
                <span class="bg-white dark:bg-gray-800 px-6 text-sm text-text-secondary-light dark:text-text-secondary-dark font-semibold">
                    OR
                </span>
                <div class="w-full border-t-2 border-border-light dark:border-border-dark"></div>
            </div>
            <!-- Pincode Input -->
            <div>
                <label for="pin_code" class="block text-sm font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">
                    Enter Pincode Number:
                </label>
                <input type="text" name="pin_code" id="pin_code" x-model.debounce.300ms="pinCode"
                    @input="handleInputToggle('pinCode')" :disabled="!districtId || isLoading"
                    class="w-full rounded-lg border-2 border-border-light dark:border-border-dark shadow-sm py-3 px-4 bg-white dark:bg-gray-700 text-text-primary-light dark:text-text-primary-dark placeholder-text-secondary-light dark:placeholder-text-secondary-dark focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark transition-all duration-200 hover:border-primary-light dark:hover:border-primary-dark disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder="Ex. 441901">
            </div>
        </form>

        <!-- Sort and Filter Options -->
        <div x-show="searchResults.length > 0" class="mt-6 space-y-4" style="display: none;">
            <div class="flex flex-col sm:flex-row gap-3">
                <div class="flex-1">
                    <label class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">
                        Sort by:
                    </label>
                    <select x-model="sortBy" @change="sortResults()"
                        class="w-full rounded-lg border border-border-light dark:border-border-dark py-2 px-3 bg-white dark:bg-gray-700 text-text-primary-light dark:text-text-primary-dark text-sm focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                        <option value="name_asc">Name A-Z</option>
                        <option value="name_desc">Name Z-A</option>
                        <option value="pincode_asc">Pincode Low-High</option>
                        <option value="pincode_desc">Pincode High-Low</option>
                    </select>
                </div>
                <div class="flex-1">
                    <label class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">
                        Results per page:
                    </label>
                    <select x-model="resultsPerPage" @change="updatePagination()"
                        class="w-full rounded-lg border border-border-light dark:border-border-dark py-2 px-3 bg-white dark:bg-gray-700 text-text-primary-light dark:text-text-primary-dark text-sm focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                        <option value="5">5</option>
                        <option value="10">10</option>
                        <option value="15">15</option>
                        <option value="20">20</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="results" class="mt-8">
            <template x-if="paginatedResults.length">
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <h4 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">
                            Search Results (<span x-text="searchResults.length"></span>):
                        </h4>
                        <button @click="clearResults()" 
                            class="text-sm text-text-secondary-light dark:text-text-secondary-dark hover:text-primary-light dark:hover:text-primary-dark transition-colors">
                            Clear
                        </button>
                    </div>
                    
                    <template x-for="result in paginatedResults" :key="result.id">
                        <div class="border-2 border-border-light dark:border-border-dark rounded-lg p-4 bg-bg-light dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 hover:border-primary-light dark:hover:border-primary-dark hover:shadow-md">
                            <div class="space-y-2">
                                <p class="flex flex-wrap items-center gap-2">
                                    <span class="font-semibold text-text-primary-light dark:text-text-primary-dark text-sm">
                                        Post Office:
                                    </span>
                                    <a :href="'/pincodes/' + result.state + '/' + result.district + '/' + result.name"
                                        class="text-primary-light dark:text-primary-dark hover:text-accent-light dark:hover:text-accent-dark font-medium underline decoration-2 underline-offset-2 transition-colors duration-200 text-sm"
                                        x-text="result.name.charAt(0).toUpperCase() + result.name.slice(1)"></a>
                                </p>
                                <p class="flex flex-wrap items-center gap-2">
                                    <span class="font-semibold text-text-primary-light dark:text-text-primary-dark text-sm">
                                        Pincode:
                                    </span>
                                    <a :href="'/pincodes/' + result.state + '/' + result.district + '/postal-code/' + result.pincode"
                                        class="text-primary-light dark:text-primary-dark hover:text-accent-light dark:hover:text-accent-dark font-medium underline decoration-2 underline-offset-2 transition-colors duration-200 text-sm"
                                        x-text="result.pincode"></a>
                                </p>
                            </div>
                        </div>
                    </template>

                    <!-- Pagination -->
                    <div x-show="totalPages > 1" class="flex justify-center items-center space-x-2 mt-4">
                        <button @click="currentPage = Math.max(1, currentPage - 1)" 
                            :disabled="currentPage === 1"
                            class="px-3 py-1 rounded border border-border-light dark:border-border-dark bg-white dark:bg-gray-700 text-text-primary-light dark:text-text-primary-dark disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors text-sm">
                            ←
                        </button>
                        
                        <template x-for="page in visiblePages" :key="page">
                            <button @click="currentPage = page" 
                                :class="currentPage === page ? 'bg-primary-light dark:bg-primary-dark text-white' : 'bg-white dark:bg-gray-700 text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-600'"
                                class="px-3 py-1 rounded border border-border-light dark:border-border-dark transition-colors text-sm"
                                x-text="page">
                            </button>
                        </template>
                        
                        <button @click="currentPage = Math.min(totalPages, currentPage + 1)" 
                            :disabled="currentPage === totalPages"
                            class="px-3 py-1 rounded border border-border-light dark:border-border-dark bg-white dark:bg-gray-700 text-text-primary-light dark:text-text-primary-dark disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors text-sm">
                            →
                        </button>
                    </div>
                </div>
            </template>
            
            <template x-if="!searchResults.length && (postOffice || pinCode) && !isLoading">
                <div class="text-center py-8">
                    <div class="bg-accent-light/10 dark:bg-accent-dark/10 rounded-lg p-6 border border-accent-light/20 dark:border-accent-dark/20">
                        <p class="text-text-secondary-light dark:text-text-secondary-dark text-lg">
                            No results found for your search.
                        </p>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark text-sm mt-2">
                            Try adjusting your search terms or selecting a different district.
                        </p>
                    </div>
                </div>
            </template>
        </div>
    </div>
</div>

<!-- Alpine.js Script -->
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('searchForm', () => ({
            stateId: '',
            districtId: '',
            districts: [],
            searchResults: [],
            paginatedResults: [],
            postOffice: '',
            pinCode: '',
            isLoading: false,
            searchTimeout: null,
            sortBy: 'name_asc',
            resultsPerPage: 10,
            currentPage: 1,
            totalPages: 1,

            init() {
                // Initialize any required data
            },

            get visiblePages() {
                const pages = [];
                const start = Math.max(1, this.currentPage - 2);
                const end = Math.min(this.totalPages, this.currentPage + 2);
                
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                return pages;
            },

            async fetchDistricts() {
                if (!this.stateId) {
                    this.resetFields();
                    return;
                }

                try {
                    this.isLoading = true;
                    const response = await fetch(`/get-districts/${this.stateId}`);
                    if (!response.ok) throw new Error('Network response was not ok');
                    this.districts = await response.json();
                } catch (error) {
                    console.error('Error fetching districts:', error);
                    this.districts = [];
                } finally {
                    this.isLoading = false;
                }
            },

            resetFields() {
                this.districtId = '';
                this.postOffice = '';
                this.pinCode = '';
                this.districts = [];
                this.searchResults = [];
                this.paginatedResults = [];
                this.currentPage = 1;
            },

            handleDistrictChange() {
                this.postOffice = '';
                this.pinCode = '';
                this.searchResults = [];
                this.paginatedResults = [];
                this.currentPage = 1;
            },

            handleInputToggle(field) {
                if (field === 'postOffice') {
                    this.pinCode = '';
                } else {
                    this.postOffice = '';
                }
                this.debounceSearch(field === 'postOffice' ? this.postOffice : this.pinCode);
            },

            debounceSearch(query) {
                if (this.searchTimeout) {
                    clearTimeout(this.searchTimeout);
                }

                this.searchTimeout = setTimeout(() => {
                    this.performSearch(query);
                }, 300);
            },

            async performSearch(query) {
                if (!query || !this.stateId || !this.districtId) return;

                try {
                    this.isLoading = true;
                    const response = await fetch('/post-office-search', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        },
                        body: JSON.stringify({
                            query: query,
                            stateId: this.stateId,
                            districtId: this.districtId,
                            searchType: this.pinCode ? 'pincode' : 'postoffice'
                        })
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.message || 'Network response was not ok');
                    }

                    const data = await response.json();
                    this.searchResults = Array.isArray(data) ? data : [];
                    this.currentPage = 1;
                    this.sortResults();

                } catch (error) {
                    console.error('Error performing search:', error);
                    this.searchResults = [];
                    this.paginatedResults = [];
                } finally {
                    this.isLoading = false;
                }
            },

            sortResults() {
                let sorted = [...this.searchResults];
                
                switch (this.sortBy) {
                    case 'name_asc':
                        sorted.sort((a, b) => a.name.localeCompare(b.name));
                        break;
                    case 'name_desc':
                        sorted.sort((a, b) => b.name.localeCompare(a.name));
                        break;
                    case 'pincode_asc':
                        sorted.sort((a, b) => parseInt(a.pincode) - parseInt(b.pincode));
                        break;
                    case 'pincode_desc':
                        sorted.sort((a, b) => parseInt(b.pincode) - parseInt(a.pincode));
                        break;
                }
                
                this.searchResults = sorted;
                this.updatePagination();
            },

            updatePagination() {
                this.totalPages = Math.ceil(this.searchResults.length / this.resultsPerPage);
                this.currentPage = Math.min(this.currentPage, this.totalPages || 1);
                
                const start = (this.currentPage - 1) * this.resultsPerPage;
                const end = start + this.resultsPerPage;
                this.paginatedResults = this.searchResults.slice(start, end);
            },

            clearResults() {
                this.searchResults = [];
                this.paginatedResults = [];
                this.postOffice = '';
                this.pinCode = '';
                this.currentPage = 1;
            }
        }));
    });
</script>

<!-- Loading Indicator -->
<div x-show="isLoading" class="fixed inset-0 bg-black/50 dark:bg-black/70 flex items-center justify-center z-50 backdrop-blur-sm"
    style="display: none;" x-cloak>
    <div class="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-2xl border border-border-light dark:border-border-dark max-w-sm w-full mx-4">
        <div class="flex flex-col items-center space-y-4">
            <div class="animate-spin rounded-full h-12 w-12 border-4 border-primary-light dark:border-primary-dark border-t-transparent"></div>
            <p class="text-text-primary-light dark:text-text-primary-dark font-medium">Searching...</p>
        </div>
    </div>
</div>

