<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'BharatPostalInfo - Post Office Details',
                'type' => 'text',
                'group' => 'general',
                'description' => 'The name of your website',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'site_description',
                'value' => 'Find and verify Indian postal codes easily',
                'type' => 'textarea',
                'group' => 'general',
                'description' => 'A brief description of your website',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'site_logo',
                'value' => null,
                'type' => 'image',
                'group' => 'general',
                'description' => 'Website logo',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'site_favicon',
                'value' => null,
                'type' => 'image',
                'group' => 'general',
                'description' => 'Website favicon',
                'is_public' => true,
                'is_encrypted' => false
            ],
            // System Settings
            [
                'key' => 'maintenance_mode',
                'value' => false,
                'type' => 'boolean',
                'group' => 'system',
                'description' => 'Enable/disable maintenance mode',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'login_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'system',
                'description' => 'Enable/disable user login system',
                'is_public' => true,
                'is_encrypted' => false
            ],
            // SEO Settings
            [
                'key' => 'meta_description',
                'value' => 'Find and verify Indian postal codes easily. Search by state, district, or pincode.',
                'type' => 'textarea',
                'group' => 'seo',
                'description' => 'Default meta description',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'meta_keywords',
                'value' => 'pincode, postal code, india post, post office, zip code',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Default meta keywords',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'is_cookie_consent_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'system',
                'description' => 'Cookie consent enable/disable',
                'is_public' => true,
                'is_encrypted' => false
            ],
            // Schema Markup Settings
            [
                'key' => 'schema_organization_name',
                'value' => 'NSK Multiservices Kosbi',
                'type' => 'text',
                'group' => 'schema_markup',
                'description' => 'Organization name for JSON-LD schema',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'schema_organization_url',
                'value' => 'https://pincodes.nskmultiservices.in',
                'type' => 'text',
                'group' => 'schema_markup',
                'description' => 'Organization URL for JSON-LD schema',
                'is_public' => true,
                'is_encrypted' => false
            ],
            // Content settings
            [
                'key' => 'short_about_footer',
                'value' => 'The most comprehensive pincode directory for India. Find accurate postal codes, locality information, and more for all your addressing needs.',
                'type' => 'text',
                'group' => 'content_settings',
                'description' => 'About Us Footer',
                'is_public' => true,
                'is_encrypted' => false
            ],
            // Social media
            [
                'key' => 'facebook_link',
                'value' => 'https://www.facebook.com/yourpage',
                'type' => 'text',
                'group' => 'social_media',
                'description' => 'Facebook Link',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'instagram_link',
                'value' => 'https://www.instagram.com/yourprofile',
                'type' => 'text',
                'group' => 'social_media',
                'description' => 'Instagram Link',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'twitter_link',
                'value' => 'https://www.twitter.com/yourhandle',
                'type' => 'text',
                'group' => 'social_media',
                'description' => 'Twitter Link',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'youtube_link',
                'value' => 'https://www.youtube.com/yourchannel',
                'type' => 'text',
                'group' => 'social_media',
                'description' => 'YouTube Link',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'linkedin_link',
                'value' => 'https://www.linkedin.com/yourchannel',
                'type' => 'text',
                'group' => 'social_media',
                'description' => 'Linkedin Link',
                'is_public' => true,
                'is_encrypted' => false
            ],
            // Contact settings
            [
                'key' => 'contact_header',
                'value' => 'Contact Our Team',
                'type' => 'text',
                'group' => 'contact_settings',
                'description' => 'Contact Heading',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'contact_content',
                'value' => "We're here to help. Reach out to us with any questions or inquiries.",
                'type' => 'text',
                'group' => 'contact_settings',
                'description' => 'Contact text content',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'contact_get_in_touch',
                'value' => 'Get in Touch',
                'type' => 'text',
                'group' => 'contact_settings',
                'description' => 'Heading',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'contact_address',
                'value' => 'Panchshil Square, Shedepar Road, Deori, Gondia, Maharashtra - 441901',
                'type' => 'text',
                'group' => 'contact_settings',
                'description' => 'Contact Address',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'contact_phone',
                'value' => '+91 9834754391',
                'type' => 'text',
                'group' => 'contact_settings',
                'description' => 'Contact Phone',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'text',
                'group' => 'contact_settings',
                'description' => 'Contact Email',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'contact_email_alternate',
                'value' => '<EMAIL>',
                'type' => 'text',
                'group' => 'contact_settings',
                'description' => 'Email 2',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'contact_biz_hours_1',
                'value' => 'Monday - Friday: 9:00 AM - 5:00 PM',
                'type' => 'text',
                'group' => 'contact_settings',
                'description' => 'Business hours line 1',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'contact_biz_hours_2',
                'value' => 'Saturday: 10:00 AM - 2:00 PM',
                'type' => 'text',
                'group' => 'contact_settings',
                'description' => 'Business hours line 2',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'contact_biz_hours_3',
                'value' => 'Sunday: Closed',
                'type' => 'text',
                'group' => 'contact_settings',
                'description' => 'Business hours line 3',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'contact_map_iframe',
                'value' => '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d14892.265984590847!2d80.3699113346558!3d21.070006200000034!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a2b93d7e3c3f30f%3A0xb28c3f09fb355e0e!2sDeori%2C%20Maharashtra!5e0!3m2!1sen!2sin!4v1733267259751!5m2!1sen!2sin" width="100%" height="450" style="border:0;" allowfullscreen="" loading="lazy"></iframe>',
                'type' => 'text',
                'group' => 'contact_settings',
                'description' => 'Location Map iFrame',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'contact_send_message',
                'value' => 'Send Us a Message',
                'type' => 'text',
                'group' => 'contact_settings',
                'description' => 'Contact Form Heading',
                'is_public' => true,
                'is_encrypted' => false
            ],
            [
                'key' => 'contact_subjects',
                'value' => '["","",""]',
                'type' => 'json',
                'group' => 'contact_settings',
                'description' => 'Email Subject for Contact Form',
                'is_public' => true,
                'is_encrypted' => false
            ],
            
        ];


        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}

// php artisan db:seed --class=SettingsTableSeeder
