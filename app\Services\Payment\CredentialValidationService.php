<?php

namespace App\Services\Payment;

use App\Models\PaymentGateway;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class CredentialValidationService
{
    private CredentialEncryptionService $encryptionService;

    public function __construct(CredentialEncryptionService $encryptionService)
    {
        $this->encryptionService = $encryptionService;
    }

    /**
     * Validate gateway credentials by testing API connection
     *
     * @param PaymentGateway $gateway
     * @return array
     */
    public function validateGatewayCredentials(PaymentGateway $gateway): array
    {
        $cacheKey = "gateway_validation_{$gateway->id}_" . md5(serialize($gateway->configuration));
        
        // Cache validation results for 5 minutes to avoid excessive API calls
        return Cache::remember($cacheKey, 300, function () use ($gateway) {
            return $this->performValidation($gateway);
        });
    }

    /**
     * Perform actual credential validation
     *
     * @param PaymentGateway $gateway
     * @return array
     */
    private function performValidation(PaymentGateway $gateway): array
    {
        try {
            switch ($gateway->name) {
                case 'paypal':
                    return $this->validatePayPalCredentials($gateway);
                case 'razorpay':
                    return $this->validateRazorpayCredentials($gateway);
                case 'stripe':
                    return $this->validateStripeCredentials($gateway);
                case 'qr_bank_transfer':
                    return $this->validateQRBankTransferCredentials($gateway);
                default:
                    return $this->validateGenericCredentials($gateway);
            }
        } catch (\Exception $e) {
            Log::error('Credential validation failed', [
                'gateway_id' => $gateway->id,
                'gateway_name' => $gateway->name,
                'error' => $e->getMessage()
            ]);

            return [
                'valid' => false,
                'message' => 'Validation failed: ' . $e->getMessage(),
                'errors' => ['exception' => $e->getMessage()],
                'tested_at' => now()
            ];
        }
    }

    /**
     * Validate PayPal credentials
     *
     * @param PaymentGateway $gateway
     * @return array
     */
    private function validatePayPalCredentials(PaymentGateway $gateway): array
    {
        $config = $gateway->configuration;
        $environment = $config['environment'] ?? 'sandbox';
        
        $clientId = $config['client_id'] ?? '';
        $clientSecret = $config['client_secret'] ?? '';

        if (empty($clientId) || empty($clientSecret)) {
            return [
                'valid' => false,
                'message' => 'PayPal Client ID and Client Secret are required',
                'errors' => ['missing_credentials' => 'client_id or client_secret'],
                'tested_at' => now()
            ];
        }

        // Test PayPal API connection
        $baseUrl = $environment === 'live' 
            ? 'https://api.paypal.com' 
            : 'https://api.sandbox.paypal.com';

        try {
            $response = Http::timeout(10)
                ->withBasicAuth($clientId, $clientSecret)
                ->post("{$baseUrl}/v1/oauth2/token", [
                    'grant_type' => 'client_credentials'
                ]);

            if ($response->successful()) {
                $data = $response->json();
                return [
                    'valid' => true,
                    'message' => 'PayPal credentials are valid',
                    'details' => [
                        'environment' => $environment,
                        'token_type' => $data['token_type'] ?? 'Bearer',
                        'expires_in' => $data['expires_in'] ?? 0
                    ],
                    'tested_at' => now()
                ];
            } else {
                return [
                    'valid' => false,
                    'message' => 'PayPal API authentication failed',
                    'errors' => [
                        'status_code' => $response->status(),
                        'response' => $response->json()
                    ],
                    'tested_at' => now()
                ];
            }
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'message' => 'PayPal API connection failed: ' . $e->getMessage(),
                'errors' => ['connection_error' => $e->getMessage()],
                'tested_at' => now()
            ];
        }
    }

    /**
     * Validate Razorpay credentials
     *
     * @param PaymentGateway $gateway
     * @return array
     */
    private function validateRazorpayCredentials(PaymentGateway $gateway): array
    {
        $config = $gateway->configuration;
        
        $keyId = $config['key_id'] ?? '';
        $keySecret = $config['key_secret'] ?? '';

        if (empty($keyId) || empty($keySecret)) {
            return [
                'valid' => false,
                'message' => 'Razorpay Key ID and Key Secret are required',
                'errors' => ['missing_credentials' => 'key_id or key_secret'],
                'tested_at' => now()
            ];
        }

        // Validate key format
        if (!preg_match('/^rzp_(test|live)_[A-Za-z0-9]{14}$/', $keyId)) {
            return [
                'valid' => false,
                'message' => 'Invalid Razorpay Key ID format',
                'errors' => ['invalid_format' => 'key_id format is incorrect'],
                'tested_at' => now()
            ];
        }

        // Test Razorpay API connection
        try {
            $response = Http::timeout(10)
                ->withBasicAuth($keyId, $keySecret)
                ->get('https://api.razorpay.com/v1/payments', [
                    'count' => 1
                ]);

            if ($response->successful()) {
                return [
                    'valid' => true,
                    'message' => 'Razorpay credentials are valid',
                    'details' => [
                        'environment' => str_contains($keyId, 'test') ? 'test' : 'live',
                        'key_id' => substr($keyId, 0, 12) . '...'
                    ],
                    'tested_at' => now()
                ];
            } else {
                return [
                    'valid' => false,
                    'message' => 'Razorpay API authentication failed',
                    'errors' => [
                        'status_code' => $response->status(),
                        'response' => $response->json()
                    ],
                    'tested_at' => now()
                ];
            }
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'message' => 'Razorpay API connection failed: ' . $e->getMessage(),
                'errors' => ['connection_error' => $e->getMessage()],
                'tested_at' => now()
            ];
        }
    }

    /**
     * Validate Stripe credentials
     *
     * @param PaymentGateway $gateway
     * @return array
     */
    private function validateStripeCredentials(PaymentGateway $gateway): array
    {
        $config = $gateway->configuration;
        
        $secretKey = $config['secret_key'] ?? '';
        $publishableKey = $config['publishable_key'] ?? '';

        if (empty($secretKey)) {
            return [
                'valid' => false,
                'message' => 'Stripe Secret Key is required',
                'errors' => ['missing_credentials' => 'secret_key'],
                'tested_at' => now()
            ];
        }

        // Validate key format
        if (!preg_match('/^sk_(test|live)_[A-Za-z0-9]{24,}$/', $secretKey)) {
            return [
                'valid' => false,
                'message' => 'Invalid Stripe Secret Key format',
                'errors' => ['invalid_format' => 'secret_key format is incorrect'],
                'tested_at' => now()
            ];
        }

        // Test Stripe API connection
        try {
            $response = Http::timeout(10)
                ->withToken($secretKey)
                ->get('https://api.stripe.com/v1/balance');

            if ($response->successful()) {
                return [
                    'valid' => true,
                    'message' => 'Stripe credentials are valid',
                    'details' => [
                        'environment' => str_contains($secretKey, 'test') ? 'test' : 'live',
                        'has_publishable_key' => !empty($publishableKey)
                    ],
                    'tested_at' => now()
                ];
            } else {
                return [
                    'valid' => false,
                    'message' => 'Stripe API authentication failed',
                    'errors' => [
                        'status_code' => $response->status(),
                        'response' => $response->json()
                    ],
                    'tested_at' => now()
                ];
            }
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'message' => 'Stripe API connection failed: ' . $e->getMessage(),
                'errors' => ['connection_error' => $e->getMessage()],
                'tested_at' => now()
            ];
        }
    }

    /**
     * Validate QR Bank Transfer credentials
     *
     * @param PaymentGateway $gateway
     * @return array
     */
    private function validateQRBankTransferCredentials(PaymentGateway $gateway): array
    {
        $config = $gateway->configuration;
        
        $required = [
            'bank_name' => 'Bank Name',
            'account_name' => 'Account Holder Name',
            'account_number' => 'Account Number',
            'ifsc_code' => 'IFSC Code'
        ];

        $missing = [];
        $errors = [];

        foreach ($required as $field => $label) {
            if (empty($config[$field])) {
                $missing[] = $label;
            }
        }

        if (!empty($missing)) {
            return [
                'valid' => false,
                'message' => 'Required bank details are missing: ' . implode(', ', $missing),
                'errors' => ['missing_fields' => $missing],
                'tested_at' => now()
            ];
        }

        // Validate IFSC code format
        $ifscCode = $config['ifsc_code'];
        if (!preg_match('/^[A-Z]{4}0[A-Z0-9]{6}$/', $ifscCode)) {
            $errors[] = 'Invalid IFSC code format';
        }

        // Validate account number (basic check)
        $accountNumber = $config['account_number'];
        if (!preg_match('/^[0-9]{9,18}$/', $accountNumber)) {
            $errors[] = 'Invalid account number format';
        }

        if (!empty($errors)) {
            return [
                'valid' => false,
                'message' => 'Bank details validation failed',
                'errors' => ['validation_errors' => $errors],
                'tested_at' => now()
            ];
        }

        return [
            'valid' => true,
            'message' => 'QR Bank Transfer configuration is valid',
            'details' => [
                'bank_name' => $config['bank_name'],
                'account_holder' => $config['account_name'],
                'ifsc_code' => $ifscCode
            ],
            'tested_at' => now()
        ];
    }

    /**
     * Validate generic gateway credentials
     *
     * @param PaymentGateway $gateway
     * @return array
     */
    private function validateGenericCredentials(PaymentGateway $gateway): array
    {
        $config = $gateway->configuration;

        if (empty($config)) {
            return [
                'valid' => false,
                'message' => 'Gateway configuration is empty',
                'errors' => ['empty_configuration' => true],
                'tested_at' => now()
            ];
        }

        // Check for common sensitive fields
        $hasSensitiveData = false;
        foreach ($config as $key => $value) {
            if ($this->encryptionService->isSensitiveField($key) && !empty($value)) {
                $hasSensitiveData = true;
                break;
            }
        }

        return [
            'valid' => $hasSensitiveData,
            'message' => $hasSensitiveData 
                ? 'Gateway configuration appears valid' 
                : 'No sensitive credentials found in configuration',
            'details' => [
                'has_credentials' => $hasSensitiveData,
                'config_keys' => array_keys($config)
            ],
            'tested_at' => now()
        ];
    }

    /**
     * Validate all active gateways
     *
     * @return array
     */
    public function validateAllGateways(): array
    {
        $results = [];
        $gateways = PaymentGateway::where('is_active', true)->get();

        foreach ($gateways as $gateway) {
            $results[] = [
                'gateway_id' => $gateway->id,
                'gateway_name' => $gateway->name,
                'display_name' => $gateway->display_name,
                'validation' => $this->validateGatewayCredentials($gateway)
            ];
        }

        return $results;
    }

    /**
     * Clear validation cache for a gateway
     *
     * @param PaymentGateway $gateway
     */
    public function clearValidationCache(PaymentGateway $gateway): void
    {
        $pattern = "gateway_validation_{$gateway->id}_*";
        Cache::flush(); // In production, you might want to use a more specific cache clearing method
    }

    /**
     * Get validation summary for dashboard
     *
     * @return array
     */
    public function getValidationSummary(): array
    {
        $gateways = PaymentGateway::where('is_active', true)->get();
        $summary = [
            'total' => $gateways->count(),
            'valid' => 0,
            'invalid' => 0,
            'untested' => 0,
            'details' => []
        ];

        foreach ($gateways as $gateway) {
            $validation = $this->validateGatewayCredentials($gateway);
            
            if ($validation['valid']) {
                $summary['valid']++;
            } else {
                $summary['invalid']++;
            }

            $summary['details'][] = [
                'gateway' => $gateway->display_name,
                'valid' => $validation['valid'],
                'message' => $validation['message']
            ];
        }

        return $summary;
    }
}