<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            // Gateway integration fields
            $table->foreignId('gateway_id')->nullable()->after('order_id')->constrained('payment_gateways')->onDelete('set null');
            $table->string('gateway_payment_id')->nullable()->after('payment_id')->comment('Payment ID from gateway');
            $table->string('gateway_order_id')->nullable()->after('gateway_payment_id')->comment('Order ID from gateway');
            
            // Currency and fee tracking
            $table->decimal('exchange_rate', 10, 6)->default(1.000000)->after('currency')->comment('Exchange rate used for conversion');
            $table->decimal('gateway_fee', 10, 2)->default(0.00)->after('exchange_rate')->comment('Gateway processing fee');
            $table->decimal('net_amount', 10, 2)->nullable()->after('gateway_fee')->comment('Amount after gateway fee');
            
            // Enhanced tracking fields
            $table->json('webhook_data')->nullable()->after('payment_details')->comment('Webhook payload data');
            $table->string('payment_proof')->nullable()->after('webhook_data')->comment('Payment proof file path for QR transfers');
            $table->text('admin_notes')->nullable()->after('payment_proof')->comment('Admin verification notes');
            $table->text('failed_reason')->nullable()->after('admin_notes')->comment('Payment failure reason');
            $table->string('refund_id')->nullable()->after('failed_reason')->comment('Refund reference ID');
            
            // Verification tracking
            $table->timestamp('verified_at')->nullable()->after('paid_at')->comment('When payment was verified');
            $table->foreignId('verified_by')->nullable()->after('verified_at')->constrained('users')->onDelete('set null')->comment('Admin who verified payment');
            
            // Indexes for performance
            $table->index('gateway_payment_id', 'idx_payments_gateway_payment_id');
            $table->index('payment_status', 'idx_payments_status');
            $table->index('payment_method', 'idx_payments_method');
            $table->index(['gateway_id', 'payment_status'], 'idx_payments_gateway_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex('idx_payments_gateway_payment_id');
            $table->dropIndex('idx_payments_status');
            $table->dropIndex('idx_payments_method');
            $table->dropIndex('idx_payments_gateway_status');
            
            // Drop foreign key constraints
            $table->dropForeign(['gateway_id']);
            $table->dropForeign(['verified_by']);
            
            // Drop columns
            $table->dropColumn([
                'gateway_id',
                'gateway_payment_id',
                'gateway_order_id',
                'exchange_rate',
                'gateway_fee',
                'net_amount',
                'webhook_data',
                'payment_proof',
                'admin_notes',
                'failed_reason',
                'refund_id',
                'verified_at',
                'verified_by'
            ]);
        });
    }
};
