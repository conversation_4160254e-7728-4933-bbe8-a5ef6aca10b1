<?php

namespace App\Services\Payment;

use App\Models\PaymentGateway;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use Illuminate\Support\Facades\Log;

class PaymentGatewayFactory
{
    /**
     * Gateway service class mappings.
     */
    private static array $gatewayServices = [
        'paypal' => PayPalGatewayService::class,
        'razorpay' => RazorpayGatewayService::class,
        'qr_bank_transfer' => QRBankTransferService::class,
    ];

    /**
     * Create a payment gateway service instance.
     */
    public static function create(PaymentGateway $gateway): PaymentGatewayServiceInterface
    {
        if (!$gateway->is_active) {
            throw new PaymentGatewayException("Gateway '{$gateway->name}' is not active");
        }

        if (!isset(self::$gatewayServices[$gateway->name])) {
            throw new PaymentGatewayException("Gateway service not found for '{$gateway->name}'");
        }

        $serviceClass = self::$gatewayServices[$gateway->name];

        if (!class_exists($serviceClass)) {
            throw new PaymentGatewayException("Gateway service class '{$serviceClass}' does not exist");
        }

        try {
            $service = new $serviceClass($gateway);

            if (!$service instanceof PaymentGatewayServiceInterface) {
                throw new PaymentGatewayException("Gateway service must implement PaymentGatewayServiceInterface");
            }

            Log::info("Payment gateway service created", [
                'gateway' => $gateway->name,
                'service_class' => $serviceClass,
            ]);

            return $service;

        } catch (\Exception $e) {
            Log::error("Failed to create payment gateway service", [
                'gateway' => $gateway->name,
                'service_class' => $serviceClass,
                'error' => $e->getMessage(),
            ]);

            throw new PaymentGatewayException(
                "Failed to create gateway service for '{$gateway->name}': " . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * Create service by gateway name.
     */
    public static function createByName(string $gatewayName): PaymentGatewayServiceInterface
    {
        $gateway = PaymentGateway::where('name', $gatewayName)
                                ->where('is_active', true)
                                ->first();

        if (!$gateway) {
            throw new PaymentGatewayException("Active gateway not found: {$gatewayName}");
        }

        return self::create($gateway);
    }

    /**
     * Create service by gateway ID.
     */
    public static function createById(int $gatewayId): PaymentGatewayServiceInterface
    {
        $gateway = PaymentGateway::where('id', $gatewayId)
                                ->where('is_active', true)
                                ->first();

        if (!$gateway) {
            throw new PaymentGatewayException("Active gateway not found with ID: {$gatewayId}");
        }

        return self::create($gateway);
    }

    /**
     * Register a new gateway service.
     */
    public static function register(string $gatewayName, string $serviceClass): void
    {
        if (!class_exists($serviceClass)) {
            throw new PaymentGatewayException("Service class does not exist: {$serviceClass}");
        }

        $reflection = new \ReflectionClass($serviceClass);
        if (!$reflection->implementsInterface(PaymentGatewayServiceInterface::class)) {
            throw new PaymentGatewayException("Service class must implement PaymentGatewayServiceInterface");
        }

        self::$gatewayServices[$gatewayName] = $serviceClass;

        Log::info("Payment gateway service registered", [
            'gateway' => $gatewayName,
            'service_class' => $serviceClass,
        ]);
    }

    /**
     * Get all registered gateway services.
     */
    public static function getRegisteredServices(): array
    {
        return self::$gatewayServices;
    }

    /**
     * Check if a gateway service is registered.
     */
    public static function isRegistered(string $gatewayName): bool
    {
        return isset(self::$gatewayServices[$gatewayName]);
    }

    /**
     * Get available gateway names.
     */
    public static function getAvailableGateways(): array
    {
        return array_keys(self::$gatewayServices);
    }

    /**
     * Test all registered gateway services.
     */
    public static function testAllServices(): array
    {
        $results = [];

        foreach (self::$gatewayServices as $gatewayName => $serviceClass) {
            try {
                $gateway = PaymentGateway::where('name', $gatewayName)
                                        ->where('is_active', true)
                                        ->first();

                if (!$gateway) {
                    $results[$gatewayName] = [
                        'status' => 'inactive',
                        'message' => 'Gateway not found or inactive',
                    ];
                    continue;
                }

                $service = self::create($gateway);
                $connectionTest = $service->testConnection();

                $results[$gatewayName] = [
                    'status' => $connectionTest ? 'success' : 'failed',
                    'message' => $connectionTest ? 'Connection successful' : 'Connection failed',
                    'service_class' => $serviceClass,
                ];

            } catch (\Exception $e) {
                $results[$gatewayName] = [
                    'status' => 'error',
                    'message' => $e->getMessage(),
                    'service_class' => $serviceClass,
                ];
            }
        }

        return $results;
    }
}