<?php

namespace Database\Factories;

use App\Models\VillagePincode;
use App\Models\State;
use App\Models\District;
use App\Models\PinCode;
use Illuminate\Database\Eloquent\Factories\Factory;

class VillagePincodeFactory extends Factory
{
    protected $model = VillagePincode::class;

    public function definition()
    {
        $state = State::factory()->create();
        $district = District::factory()->create(['state_id' => $state->id]);
        $pincode = PinCode::factory()->create([
            'state' => $state->name,
            'district' => $district->name
        ]);
        
        return [
            'state_id' => $state->id,
            'state_name_en' => $state->name,
            'district_code' => $this->faker->numerify('####'),
            'district_name_en' => $district->name,
            'subdistrict_code' => $this->faker->numerify('####'),
            'subdistrict_name_en' => $this->faker->city,
            'village_code' => $this->faker->numerify('######'),
            'village_name_en' => $this->faker->city . ' Village',
            'pincode' => $pincode->pincode
        ];
    }
}