<?php if(isset($landingPage['latest-blog-posts']) && $landingPage['latest-blog-posts']['active']): ?>
        <section class="py-20 bg-white dark:bg-bg-dark">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center max-w-3xl mx-auto mb-16">
                    <span
                        class="inline-block px-6 py-3 rounded-full bg-primary-light/10 dark:bg-primary-dark/10 border border-primary-light dark:border-primary-dark text-primary-light dark:text-primary-dark text-sm font-semibold tracking-wider uppercase mb-6 backdrop-blur-sm">
                        Blog
                    </span>
                    <h2 class="text-4xl md:text-5xl font-extrabold mb-6 text-primary-light dark:text-primary-dark">
                        <?php echo e($landingPage['latest-blog-posts']['content']['heading'] ?? 'Latest from our blog'); ?>

                    </h2>
                    <p class="text-xl text-text-secondary-light dark:text-text-secondary-dark leading-relaxed">
                        <?php echo e($landingPage['latest-blog-posts']['content']['subheading'] ?? 'Learn more about postal codes, address verification, and logistics in India'); ?>

                    </p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php $__empty_1 = true; $__currentLoopData = $latestPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <a href="<?php echo e(route('blog.show', $post->slug)); ?>"
                            class="block group focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark rounded-2xl transition-shadow duration-300">
                            <article
                                class="card-hover group bg-white dark:bg-bg-dark rounded-2xl shadow-lg overflow-hidden border border-border-light dark:border-border-dark">
                                <div class="relative">
                                    <img src="<?php echo e($post->featured_image ? asset('storage/' . $post->featured_image) : '/api/placeholder/600/400'); ?>"
                                        alt="<?php echo e($post->title); ?>"
                                        class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500">
                                    <?php if($post->category): ?>
                                        <div
                                            class="absolute top-4 left-4 bg-primary-light dark:bg-primary-dark text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg">
                                            <?php echo e($post->category->name); ?>

                                        </div>
                                    <?php endif; ?>
                                    <div
                                        class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    </div>
                                </div>
                                <div class="p-6">
                                    <h3
                                        class="text-xl font-bold text-primary-light dark:text-primary-dark mb-3 group-hover:text-primary-dark dark:group-hover:text-primary-light transition-colors duration-300 line-clamp-2">
                                        <?php echo e($post->title); ?>

                                    </h3>
                                    <p class="text-text-secondary-light dark:text-text-secondary-dark mb-4 line-clamp-3">
                                        <?php echo e(Str::limit($post->excerpt ?? strip_tags($post->content), 120)); ?>

                                    </p>
                                    <?php if($post->tags->count() > 0): ?>
                                        <div class="flex flex-wrap gap-2 mb-4">
                                            <?php $__currentLoopData = $post->tags->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span
                                                    class="inline-block px-3 py-1 bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark text-xs rounded-full border border-primary-light/20 dark:border-primary-dark/20 font-medium">
                                                    <?php echo e($tag->name); ?>

                                                </span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($post->tags->count() > 2): ?>
                                                <span
                                                    class="inline-block px-3 py-1 bg-accent-light/10 dark:bg-accent-dark/10 text-accent-light dark:text-accent-dark text-xs rounded-full border border-accent-light/20 dark:border-accent-dark/20 font-medium">
                                                    +<?php echo e($post->tags->count() - 2); ?> more
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    <div
                                        class="flex items-center text-sm text-text-secondary-light dark:text-text-secondary-dark mb-4">
                                        <svg class="w-4 h-4 mr-2 text-primary-light dark:text-primary-dark" fill="none"
                                            stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                        <span><?php echo e($post->published_at ? $post->published_at->format('M j, Y') : 'Draft'); ?></span>
                                        <span class="mx-2 text-accent-light dark:text-accent-dark">•</span>
                                        <span
                                            class="text-accent-light dark:text-accent-dark font-medium"><?php echo e($post->reading_time); ?>

                                            min read</span>
                                    </div>
                                    <div
                                        class="flex items-center pt-3 border-t border-border-light dark:border-border-dark">
                                        <img src="<?php echo e($post->author && $post->author->profile_photo_path ? asset('storage/' . $post->author->profile_photo_path) : '/api/placeholder/40/40'); ?>"
                                            alt="<?php echo e($post->author->name ?? 'Author'); ?>"
                                            class="w-8 h-8 rounded-full mr-3 ring-2 ring-primary-light/20 dark:ring-primary-dark/20">
                                        <span
                                            class="text-sm font-medium text-primary-light dark:text-primary-dark"><?php echo e($post->author->name ?? 'Anonymous'); ?></span>
                                    </div>
                                </div>
                            </article>
                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <!-- Fallback blog post -->
                        <article
                            class="card-hover group block bg-white dark:bg-bg-dark rounded-2xl shadow-lg overflow-hidden border border-border-light dark:border-border-dark">
                            <div class="relative">
                                <img src="/api/placeholder/600/400" alt="Blog Post Image"
                                    class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500">
                                <div
                                    class="absolute top-4 left-4 bg-primary-light dark:bg-primary-dark text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg">
                                    Example
                                </div>
                                <div
                                    class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                </div>
                            </div>
                            <div class="p-6">
                                <h3
                                    class="text-xl font-bold text-primary-light dark:text-primary-dark mb-3 group-hover:text-primary-dark dark:group-hover:text-primary-light transition-colors duration-300 line-clamp-2">
                                    Example Blog Post
                                </h3>
                                <p class="text-text-secondary-light dark:text-text-secondary-dark mb-4 line-clamp-3">
                                    This is a fallback blog post. Add posts to see dynamic content here.
                                </p>
                                <div class="flex flex-wrap gap-2 mb-4">
                                    <span
                                        class="inline-block px-3 py-1 bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark text-xs rounded-full border border-primary-light/20 dark:border-primary-dark/20 font-medium">
                                        Example Tag
                                    </span>
                                </div>
                                <div
                                    class="flex items-center text-sm text-text-secondary-light dark:text-text-secondary-dark mb-4">
                                    <svg class="w-4 h-4 mr-2 text-primary-light dark:text-primary-dark" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    <span>Apr 10, 2025</span>
                                    <span class="mx-2 text-accent-light dark:text-accent-dark">•</span>
                                    <span class="text-accent-light dark:text-accent-dark font-medium">5 min read</span>
                                </div>
                                <div class="flex items-center pt-3 border-t border-border-light dark:border-border-dark">
                                    <img src="/api/placeholder/40/40" alt="Author Avatar"
                                        class="w-8 h-8 rounded-full mr-3 ring-2 ring-primary-light/20 dark:ring-primary-dark/20">
                                    <span class="text-sm font-medium text-primary-light dark:text-primary-dark">Example
                                        Author</span>
                                </div>
                            </div>
                        </article>
                    <?php endif; ?>
                </div>
                <div class="mt-12 text-center">
                    <a href="/blog"
                        class="inline-flex items-center px-8 py-4 bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark hover:bg-primary-light/20 dark:hover:bg-primary-dark/20 border border-primary-light dark:border-primary-dark text-base font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                        View all articles
                        <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                    </a>
                </div>
            </div>
        </section>
    <?php endif; ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/home/<USER>/blog-section.blade.php ENDPATH**/ ?>