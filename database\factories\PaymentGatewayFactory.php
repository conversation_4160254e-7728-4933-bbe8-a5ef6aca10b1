<?php

namespace Database\Factories;

use App\Models\PaymentGateway;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PaymentGateway>
 */
class PaymentGatewayFactory extends Factory
{
    protected $model = PaymentGateway::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $gatewayTypes = ['razorpay', 'paypal', 'qr_bank_transfer'];
        $gatewayType = $this->faker->randomElement($gatewayTypes);

        return [
            'name' => $gatewayType,
            'display_name' => $this->getDisplayName($gatewayType),
            'description' => $this->getDescription($gatewayType),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
            'is_default' => false, // Will be set explicitly when needed
            'configuration' => $this->getConfiguration($gatewayType),
            'supported_currencies' => $this->getSupportedCurrencies($gatewayType),
            'sort_order' => $this->faker->numberBetween(0, 10),
            'webhook_url' => $this->getWebhookUrl($gatewayType),
            'webhook_secret' => $this->faker->sha256(),
            'logo_url' => "/images/gateways/{$gatewayType}.png",
        ];
    }

    /**
     * Create a Razorpay gateway
     */
    public function razorpay(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'razorpay',
            'display_name' => 'Razorpay',
            'description' => 'Pay with UPI, Cards, Net Banking, and Wallets',
            'configuration' => [
                'key_id' => 'rzp_test_' . $this->faker->regexify('[A-Za-z0-9]{14}'),
                'key_secret' => $this->faker->regexify('[A-Za-z0-9]{24}'),
                'webhook_secret' => $this->faker->sha256(),
                'mode' => 'test'
            ],
            'supported_currencies' => ['INR'],
            'webhook_url' => '/webhook/razorpay',
            'logo_url' => '/images/gateways/razorpay.png',
        ]);
    }

    /**
     * Create a PayPal gateway
     */
    public function paypal(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'paypal',
            'display_name' => 'PayPal',
            'description' => 'Pay securely with PayPal',
            'configuration' => [
                'client_id' => 'AY' . $this->faker->regexify('[A-Za-z0-9]{78}'),
                'client_secret' => 'E' . $this->faker->regexify('[A-Za-z0-9]{79}'),
                'mode' => 'sandbox',
                'webhook_id' => $this->faker->regexify('[A-Z0-9]{17}')
            ],
            'supported_currencies' => ['USD', 'EUR', 'GBP'],
            'webhook_url' => '/webhook/paypal',
            'logo_url' => '/images/gateways/paypal.png',
        ]);
    }

    /**
     * Create a QR Bank Transfer gateway
     */
    public function qrBankTransfer(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'qr_bank_transfer',
            'display_name' => 'Bank Transfer (QR)',
            'description' => 'Pay via bank transfer using QR code',
            'configuration' => [
                'bank_name' => $this->faker->company() . ' Bank',
                'account_name' => $this->faker->company() . ' Ltd',
                'account_number' => $this->faker->numerify('##########'),
                'ifsc_code' => $this->faker->regexify('[A-Z]{4}0[A-Z0-9]{6}'),
                'branch_name' => $this->faker->city() . ' Branch',
                'upi_id' => strtolower($this->faker->userName()) . '@' . $this->faker->randomElement(['paytm', 'googlepay', 'phonepe'])
            ],
            'supported_currencies' => ['INR', 'USD'],
            'webhook_url' => null, // QR Bank Transfer doesn't use webhooks
            'logo_url' => '/images/gateways/bank-transfer.png',
        ]);
    }

    /**
     * Create an active gateway
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Create an inactive gateway
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a default gateway
     */
    public function default(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_default' => true,
            'is_active' => true,
        ]);
    }

    /**
     * Create a gateway with test configuration
     */
    public function testMode(): static
    {
        return $this->state(function (array $attributes) {
            $config = $attributes['configuration'] ?? [];
            $config['mode'] = 'test';
            
            return ['configuration' => $config];
        });
    }

    /**
     * Create a gateway with production configuration
     */
    public function productionMode(): static
    {
        return $this->state(function (array $attributes) {
            $config = $attributes['configuration'] ?? [];
            $config['mode'] = 'production';
            
            return ['configuration' => $config];
        });
    }

    /**
     * Get display name for gateway type
     */
    private function getDisplayName(string $gatewayType): string
    {
        return match ($gatewayType) {
            'razorpay' => 'Razorpay',
            'paypal' => 'PayPal',
            'qr_bank_transfer' => 'Bank Transfer (QR)',
            default => ucfirst($gatewayType)
        };
    }

    /**
     * Get description for gateway type
     */
    private function getDescription(string $gatewayType): string
    {
        return match ($gatewayType) {
            'razorpay' => 'Pay with UPI, Cards, Net Banking, and Wallets',
            'paypal' => 'Pay securely with PayPal',
            'qr_bank_transfer' => 'Pay via bank transfer using QR code',
            default => "Pay using {$gatewayType}"
        };
    }

    /**
     * Get configuration for gateway type
     */
    private function getConfiguration(string $gatewayType): array
    {
        return match ($gatewayType) {
            'razorpay' => [
                'key_id' => 'rzp_test_' . $this->faker->regexify('[A-Za-z0-9]{14}'),
                'key_secret' => $this->faker->regexify('[A-Za-z0-9]{24}'),
                'webhook_secret' => $this->faker->sha256(),
                'mode' => 'test'
            ],
            'paypal' => [
                'client_id' => 'AY' . $this->faker->regexify('[A-Za-z0-9]{78}'),
                'client_secret' => 'E' . $this->faker->regexify('[A-Za-z0-9]{79}'),
                'mode' => 'sandbox',
                'webhook_id' => $this->faker->regexify('[A-Z0-9]{17}')
            ],
            'qr_bank_transfer' => [
                'bank_name' => $this->faker->company() . ' Bank',
                'account_name' => $this->faker->company() . ' Ltd',
                'account_number' => $this->faker->numerify('##########'),
                'ifsc_code' => $this->faker->regexify('[A-Z]{4}0[A-Z0-9]{6}'),
                'branch_name' => $this->faker->city() . ' Branch',
                'upi_id' => strtolower($this->faker->userName()) . '@' . $this->faker->randomElement(['paytm', 'googlepay', 'phonepe'])
            ],
            default => []
        };
    }

    /**
     * Get supported currencies for gateway type
     */
    private function getSupportedCurrencies(string $gatewayType): array
    {
        return match ($gatewayType) {
            'razorpay' => ['INR'],
            'paypal' => ['USD', 'EUR', 'GBP'],
            'qr_bank_transfer' => ['INR', 'USD'],
            default => ['USD']
        };
    }

    /**
     * Get webhook URL for gateway type
     */
    private function getWebhookUrl(string $gatewayType): ?string
    {
        return match ($gatewayType) {
            'razorpay' => '/webhook/razorpay',
            'paypal' => '/webhook/paypal',
            'qr_bank_transfer' => null, // QR Bank Transfer doesn't use webhooks
            default => "/webhook/{$gatewayType}"
        };
    }
}