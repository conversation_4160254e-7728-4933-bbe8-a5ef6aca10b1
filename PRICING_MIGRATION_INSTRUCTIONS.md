# Pricing Section Migration Instructions

This guide explains how to migrate from landing page content-based pricing to Plan model-based pricing.

## 🎯 What Was Changed

### ✅ **Template Updates**
- **File**: `resources/views/home/<USER>/pricing-section.blade.php`
- **Change**: Now uses `$plans` variable from Plan model instead of `$landingPage['pricing']['content']['plans']`
- **Benefits**: Real-time pricing data, admin can manage through Plan admin panel

### ✅ **Database Changes**
- **Migration**: `database/migrations/2024_07_31_000000_add_pricing_fields_to_plans_table.php`
- **New Fields**: `currency`, `billing_cycle`, `sort_order`
- **Purpose**: Make Plan model compatible with pricing section requirements

### ✅ **Seeder Updates**
- **File**: `database/seeders/PlanSeeder.php`
- **Change**: Updated to include new fields and match landing page pricing structure
- **Plans**: Free (₹0), Professional (₹999/month), Enterprise (Custom)

### ✅ **Controller Updates**
- **File**: `app/Http/Controllers/SiteController.php`
- **Change**: Plans now ordered by `sort_order` then `price`
- **Result**: Consistent plan ordering

### ✅ **Landing Page Seeder Cleanup**
- **File**: `database/seeders/LandingPageSeeder.php`
- **Change**: Removed hardcoded plans data (now uses Plan model)
- **Kept**: Header content (badge, heading, subheading, etc.)

## 🚀 Migration Steps

### Step 1: Run Migration
```bash
php artisan migrate
```

### Step 2: Update Plan Data
```bash
php artisan db:seed --class=PlanSeeder
```

### Step 3: Clear Cache (if using cache)
```bash
php artisan cache:clear
```

### Step 4: Verify Changes
1. Visit your landing page
2. Check that pricing section shows plans from database
3. Verify admin can manage plans through Plan admin panel

## 📊 **Plan Structure**

### **Database Fields:**
- `name` - Plan name (e.g., "Professional")
- `slug` - URL-friendly identifier
- `price` - Price in decimal (null for custom pricing)
- `currency` - Currency symbol (₹, $, etc.)
- `billing_cycle` - Billing period (monthly, yearly, custom, forever)
- `request_limit` - API request limit
- `description` - Plan description
- `is_active` - Whether plan is active
- `features` - JSON array of features
- `sort_order` - Display order

### **Template Usage:**
```php
@forelse($plans ?? [] as $index => $plan)
    <h3>{{ $plan->name }}</h3>
    <div>{{ $plan->currency }}{{ number_format($plan->price, 0) }}</div>
    <p>{{ $plan->description }}</p>
    @foreach($plan->features as $feature)
        <li>{{ $feature }}</li>
    @endforeach
@endforelse
```

## 🎛️ **Admin Management**

### **Plan Admin Panel:**
- **Location**: `/admin/plans`
- **Features**: Create, edit, delete plans
- **Real-time**: Changes appear immediately on landing page
- **Validation**: Proper validation for all fields

### **Landing Page Content:**
- **Location**: `/admin/landing-page`
- **Pricing Section**: Only header content (badge, heading, subheading)
- **Plans Data**: Managed through Plan model

## 🔧 **Benefits**

### ✅ **Separation of Concerns**
- **Pricing Data**: Managed through dedicated Plan model
- **Content Data**: Managed through landing page system
- **Clean Architecture**: Each system handles its responsibility

### ✅ **Real-time Updates**
- **Plan Changes**: Immediate reflection on landing page
- **No Cache Issues**: Direct database queries
- **Admin Friendly**: Dedicated plan management interface

### ✅ **Scalability**
- **Multiple Plans**: Easy to add/remove plans
- **Complex Features**: Rich feature arrays
- **Pricing Logic**: Can add complex pricing calculations

## 🧪 **Testing**

### **Verify Plan Display:**
1. Create a new plan in admin panel
2. Check it appears on landing page
3. Deactivate a plan and verify it's hidden

### **Verify Content Management:**
1. Edit pricing section header in landing page admin
2. Verify changes appear on frontend
3. Confirm plans data is separate

### **Verify Ordering:**
1. Change `sort_order` values in database
2. Verify plans reorder on landing page
3. Test with different price values

## 🔄 **Rollback (if needed)**

If you need to rollback to the old system:

1. **Restore Template**: Revert pricing section template to use `$landingPage['pricing']['content']['plans']`
2. **Restore Seeder**: Add back plans data to LandingPageSeeder
3. **Remove Migration**: `php artisan migrate:rollback`

## 📝 **Notes**

- **Popular Badge**: Automatically assigned to middle plan or plans with slug 'professional'/'premium'
- **Custom Pricing**: Plans with `price = null` show "Custom" text
- **Free Plans**: Plans with `price = 0` show special "Get Started Free" button
- **Currency**: Defaults to ₹ but can be customized per plan
- **Features**: Stored as JSON array for flexibility

## 🎯 **Result**

✅ **Pricing data is now fully managed through the Plan model**
✅ **Admin can manage plans through dedicated interface**
✅ **Landing page content system only handles header content**
✅ **Clean separation between pricing data and content data**
✅ **Real-time updates without cache issues**
