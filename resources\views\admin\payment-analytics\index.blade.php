@extends('admin.layouts.app')

@section('title', 'Payment Analytics')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <div class="d-flex">
                        <div class="input-group me-2">
                            <input type="date" class="form-control" id="start_date" value="{{ request('start_date', now()->subDays(30)->format('Y-m-d')) }}">
                        </div>
                        <div class="input-group me-2">
                            <input type="date" class="form-control" id="end_date" value="{{ request('end_date', now()->format('Y-m-d')) }}">
                        </div>
                        <button type="button" class="btn btn-primary" id="filter-btn">
                            <i class="mdi mdi-filter"></i> Filter
                        </button>
                        <div class="dropdown ms-2">
                            <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="mdi mdi-download"></i> Export
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" id="export-csv">Export CSV</a></li>
                                <li><a class="dropdown-item" href="#" id="export-pdf">Export PDF</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <h4 class="page-title">Payment Analytics</h4>
            </div>
        </div>
    </div>

    <!-- Overview Stats -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-muted mb-3 lh-1 d-block text-truncate">Total Transactions</span>
                            <h4 class="mb-3">
                                <span class="counter-value" data-target="{{ $analytics['overview']->total_transactions ?? 0 }}">0</span>
                            </h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-primary">
                                <span class="avatar-title bg-primary rounded-circle">
                                    <i class="mdi mdi-credit-card-outline font-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-muted mb-3 lh-1 d-block text-truncate">Successful Payments</span>
                            <h4 class="mb-3">
                                <span class="counter-value" data-target="{{ $analytics['overview']->successful_transactions ?? 0 }}">0</span>
                            </h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-success">
                                <span class="avatar-title bg-success rounded-circle">
                                    <i class="mdi mdi-check-circle-outline font-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-muted mb-3 lh-1 d-block text-truncate">Total Revenue</span>
                            <h4 class="mb-3">
                                ₹<span class="counter-value" data-target="{{ $analytics['overview']->total_revenue ?? 0 }}">0</span>
                            </h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-info">
                                <span class="avatar-title bg-info rounded-circle">
                                    <i class="mdi mdi-currency-inr font-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <span class="text-muted mb-3 lh-1 d-block text-truncate">Success Rate</span>
                            <h4 class="mb-3">
                                <span class="counter-value" data-target="{{ $analytics['overview']->overall_success_rate ?? 0 }}">0</span>%
                            </h4>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="avatar-sm rounded-circle bg-warning">
                                <span class="avatar-title bg-warning rounded-circle">
                                    <i class="mdi mdi-chart-line font-24"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Revenue Chart -->
        <div class="col-xl-8">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="header-title">Revenue Trends</h4>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                Daily
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" data-period="day">Daily</a></li>
                                <li><a class="dropdown-item" href="#" data-period="week">Weekly</a></li>
                                <li><a class="dropdown-item" href="#" data-period="month">Monthly</a></li>
                            </ul>
                        </div>
                    </div>
                    <div id="revenue-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>

        <!-- Gateway Performance -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Gateway Performance</h4>
                    <div id="gateway-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Gateway Statistics Table -->
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title">Gateway Statistics</h4>
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Gateway</th>
                                    <th>Total Transactions</th>
                                    <th>Successful</th>
                                    <th>Success Rate</th>
                                    <th>Total Revenue</th>
                                    <th>Gateway Fees</th>
                                    <th>Net Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($analytics['gatewayStats'] as $stat)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0 me-2">
                                                @if($stat->gateway)
                                                    <img src="{{ $stat->gateway->logo_url ?? '/images/default-gateway.png' }}" 
                                                         alt="{{ $stat->gateway->display_name }}" 
                                                         class="rounded" width="32" height="32">
                                                @endif
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-0">{{ $stat->gateway->display_name ?? 'Unknown Gateway' }}</h6>
                                                <small class="text-muted">{{ $stat->gateway->name ?? 'N/A' }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ number_format($stat->total_transactions) }}</td>
                                    <td>{{ number_format($stat->successful_transactions) }}</td>
                                    <td>
                                        <span class="badge bg-{{ $stat->success_rate >= 80 ? 'success' : ($stat->success_rate >= 60 ? 'warning' : 'danger') }}">
                                            {{ number_format($stat->success_rate, 2) }}%
                                        </span>
                                    </td>
                                    <td>₹{{ number_format($stat->total_revenue, 2) }}</td>
                                    <td>₹{{ number_format($stat->total_fees, 2) }}</td>
                                    <td>₹{{ number_format($stat->total_revenue - $stat->total_fees, 2) }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center">No payment data available for the selected period</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts
    initRevenueChart();
    initGatewayChart();
    
    // Filter functionality
    document.getElementById('filter-btn').addEventListener('click', function() {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;
        
        const url = new URL(window.location);
        url.searchParams.set('start_date', startDate);
        url.searchParams.set('end_date', endDate);
        
        window.location.href = url.toString();
    });
    
    // Export functionality
    document.getElementById('export-csv').addEventListener('click', function(e) {
        e.preventDefault();
        exportData('csv');
    });
    
    document.getElementById('export-pdf').addEventListener('click', function(e) {
        e.preventDefault();
        exportData('pdf');
    });
});

function initRevenueChart() {
    const revenueData = @json($analytics['revenueData']);
    
    const options = {
        series: [{
            name: 'Revenue',
            data: revenueData.map(item => ({
                x: item.date,
                y: parseFloat(item.daily_revenue || 0)
            }))
        }],
        chart: {
            type: 'area',
            height: 350,
            toolbar: {
                show: false
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        xaxis: {
            type: 'datetime'
        },
        yaxis: {
            labels: {
                formatter: function(val) {
                    return '₹' + val.toFixed(0);
                }
            }
        },
        fill: {
            type: 'gradient',
            gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.3,
                stops: [0, 90, 100]
            }
        },
        colors: ['#727cf5']
    };
    
    const chart = new ApexCharts(document.querySelector("#revenue-chart"), options);
    chart.render();
}

function initGatewayChart() {
    const gatewayStats = @json($analytics['gatewayStats']);
    
    const options = {
        series: gatewayStats.map(stat => parseFloat(stat.total_revenue || 0)),
        chart: {
            type: 'donut',
            height: 350
        },
        labels: gatewayStats.map(stat => stat.gateway ? stat.gateway.display_name : 'Unknown'),
        colors: ['#727cf5', '#0acf97', '#fa5c7c', '#ffbc00', '#39afd1'],
        legend: {
            position: 'bottom'
        },
        plotOptions: {
            pie: {
                donut: {
                    size: '70%'
                }
            }
        }
    };
    
    const chart = new ApexCharts(document.querySelector("#gateway-chart"), options);
    chart.render();
}

function exportData(format) {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    const url = new URL('{{ route("admin.payment-analytics.export") }}', window.location.origin);
    url.searchParams.set('format', format);
    url.searchParams.set('start_date', startDate);
    url.searchParams.set('end_date', endDate);
    
    window.open(url.toString(), '_blank');
}

// Counter animation
document.querySelectorAll('.counter-value').forEach(function(counter) {
    const target = parseInt(counter.getAttribute('data-target'));
    const increment = target / 200;
    let current = 0;
    
    const timer = setInterval(function() {
        current += increment;
        if (current >= target) {
            counter.textContent = target.toLocaleString();
            clearInterval(timer);
        } else {
            counter.textContent = Math.ceil(current).toLocaleString();
        }
    }, 10);
});
</script>
@endpush