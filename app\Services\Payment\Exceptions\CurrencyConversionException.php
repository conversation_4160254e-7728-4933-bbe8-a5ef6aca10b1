<?php

namespace App\Services\Payment\Exceptions;

use Exception;

class CurrencyConversionException extends Exception
{
    /**
     * The currency code that caused the exception
     */
    protected ?string $currencyCode = null;

    /**
     * Additional context information
     */
    protected array $context = [];

    /**
     * Create a new currency conversion exception
     */
    public function __construct(
        string $message, 
        ?string $currencyCode = null, 
        array $context = [], 
        int $code = 0, 
        ?Exception $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->currencyCode = $currencyCode;
        $this->context = $context;
    }

    /**
     * Get the currency code that caused the exception
     */
    public function getCurrencyCode(): ?string
    {
        return $this->currencyCode;
    }

    /**
     * Get additional context information
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Convert to array for logging
     */
    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'currency_code' => $this->currencyCode,
            'context' => $this->context,
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'trace' => $this->getTraceAsString()
        ];
    }

    /**
     * Create an exception for invalid currency code
     */
    public static function invalidCurrency(string $currencyCode, array $context = []): self
    {
        return new self(
            "Invalid currency code: {$currencyCode}",
            $currencyCode,
            $context
        );
    }

    /**
     * Create an exception for unavailable exchange rate
     */
    public static function rateUnavailable(string $fromCurrency, string $toCurrency, array $context = []): self
    {
        return new self(
            "Exchange rate not available for {$fromCurrency} to {$toCurrency}",
            "{$fromCurrency}_{$toCurrency}",
            $context
        );
    }

    /**
     * Create an exception for API fetch failure
     */
    public static function apiFetchFailure(string $message = 'Failed to fetch exchange rates from API', array $context = []): self
    {
        return new self(
            $message,
            null,
            $context
        );
    }

    /**
     * Create an exception for negative amount
     */
    public static function negativeAmount(float $amount, string $currency, array $context = []): self
    {
        return new self(
            "Amount must be non-negative",
            $currency,
            array_merge(['amount' => $amount], $context)
        );
    }
}