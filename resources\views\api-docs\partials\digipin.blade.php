{{-- Digipin API Documentation --}}
<section id="digipin-api" class="mb-12">
    <h2 class="text-xl md:text-2xl font-bold text-indigo-600 dark:text-indigo-400 mb-6">Digipin API Endpoints</h2>

    <!-- Generate Digipin -->
    <div class="mb-10 pb-8 border-b border-slate-200 dark:border-slate-700">
        <h3 class="text-lg md:text-xl font-bold text-slate-800 dark:text-slate-200 mb-3">1. Generate Digipin</h3>
        <p class="text-sm md:text-base text-slate-700 dark:text-slate-300 leading-relaxed mb-4">
            Generate a DIGIPIN from latitude and longitude coordinates.
        </p>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div class="bg-slate-50 dark:bg-slate-900/50 p-3 rounded-lg">
                <span class="font-semibold text-slate-900 dark:text-white text-sm">Endpoint:</span>
                <div class="mt-1 text-emerald-600 dark:text-emerald-400 font-mono text-xs md:text-sm break-all">
                    POST /api/digipin/generate
                </div>
            </div>
            <div class="bg-slate-50 dark:bg-slate-900/50 p-3 rounded-lg">
                <span class="font-semibold text-slate-900 dark:text-white text-sm">Method:</span>
                <div class="mt-1 text-blue-600 dark:text-blue-400 font-mono text-xs md:text-sm">POST</div>
            </div>
        </div>
        <h4 class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-4 mb-2">Request Body:</h4>
        <div class="bg-slate-50 dark:bg-slate-900/50 rounded-lg p-3 md:p-4 mb-4">
            <ul class="list-disc pl-4 md:pl-5 space-y-2 text-slate-700 dark:text-slate-300">
                <li class="text-sm md:text-base"><span class="font-semibold">latitude</span> (float, required): Latitude value.</li>
                <li class="text-sm md:text-base"><span class="font-semibold">longitude</span> (float, required): Longitude value.</li>
            </ul>
        </div>
        <h4 class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">Request Example:</h4>
        <div class="bg-slate-900 rounded-lg overflow-hidden mb-4">
            <div class="bg-slate-800 px-3 py-2 text-xs text-slate-300 border-b border-slate-700">HTTP Request</div>
            <pre class="language-http p-3 md:p-4 text-xs md:text-sm overflow-x-auto"><code class="whitespace-pre-wrap break-all">POST /api/digipin/generate
Content-Type: application/json
Authorization: Bearer YOUR_API_KEY

{
  "latitude": 19.123456,
  "longitude": 73.987654
}</code></pre>
        </div>
        <h4 class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">Response Example:</h4>
        <div class="bg-slate-900 rounded-lg overflow-hidden">
            <div class="bg-slate-800 px-3 py-2 text-xs text-slate-300 border-b border-slate-700">JSON Response</div>
            <pre class="language-json p-3 md:p-4 text-xs md:text-sm overflow-x-auto max-h-96 overflow-y-auto"><code class="whitespace-pre-wrap break-words">{
  "success": true,
  "data": {
    "digipin": "A1B2C3D4",
    "latitude": 19.123456,
    "longitude": 73.987654
  }
}</code></pre>
        </div>
        <h4 class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">Error Response:</h4>
        <div class="bg-slate-900 rounded-lg overflow-hidden">
            <pre class="language-json p-3 md:p-4 text-xs md:text-sm overflow-x-auto max-h-96 overflow-y-auto"><code class="whitespace-pre-wrap break-words">{
  "success": false,
  "message": "Error message here."
}</code></pre>
        </div>
    </div>

    <!-- Decode Digipin -->
    <div class="mb-10 pb-8 border-b border-slate-200 dark:border-slate-700">
        <h3 class="text-lg md:text-xl font-bold text-slate-800 dark:text-slate-200 mb-3">2. Decode Digipin</h3>
        <p class="text-sm md:text-base text-slate-700 dark:text-slate-300 leading-relaxed mb-4">
            Decode a DIGIPIN to retrieve latitude and longitude coordinates.
        </p>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div class="bg-slate-50 dark:bg-slate-900/50 p-3 rounded-lg">
                <span class="font-semibold text-slate-900 dark:text-white text-sm">Endpoint:</span>
                <div class="mt-1 text-emerald-600 dark:text-emerald-400 font-mono text-xs md:text-sm break-all">
                    POST /api/digipin/decode
                </div>
            </div>
            <div class="bg-slate-50 dark:bg-slate-900/50 p-3 rounded-lg">
                <span class="font-semibold text-slate-900 dark:text-white text-sm">Method:</span>
                <div class="mt-1 text-blue-600 dark:text-blue-400 font-mono text-xs md:text-sm">POST</div>
            </div>
        </div>
        <h4 class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-4 mb-2">Request Body:</h4>
        <div class="bg-slate-50 dark:bg-slate-900/50 rounded-lg p-3 md:p-4 mb-4">
            <ul class="list-disc pl-4 md:pl-5 space-y-2 text-slate-700 dark:text-slate-300">
                <li class="text-sm md:text-base"><span class="font-semibold">digipin</span> (string, required): The DIGIPIN code to decode.</li>
            </ul>
        </div>
        <h4 class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">Request Example:</h4>
        <div class="bg-slate-900 rounded-lg overflow-hidden mb-4">
            <div class="bg-slate-800 px-3 py-2 text-xs text-slate-300 border-b border-slate-700">HTTP Request</div>
            <pre class="language-http p-3 md:p-4 text-xs md:text-sm overflow-x-auto"><code class="whitespace-pre-wrap break-all">POST /api/digipin/decode
Content-Type: application/json
Authorization: Bearer YOUR_API_KEY

{
  "digipin": "A1B2C3D4"
}</code></pre>
        </div>
        <h4 class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">Response Example:</h4>
        <div class="bg-slate-900 rounded-lg overflow-hidden">
            <div class="bg-slate-800 px-3 py-2 text-xs text-slate-300 border-b border-slate-700">JSON Response</div>
            <pre class="language-json p-3 md:p-4 text-xs md:text-sm overflow-x-auto max-h-96 overflow-y-auto"><code class="whitespace-pre-wrap break-words">{
  "success": true,
  "data": {
    "digipin": "A1B2C3D4",
    "latitude": 19.123456,
    "longitude": 73.987654
  }
}</code></pre>
        </div>
        <h4 class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">Error Response:</h4>
        <div class="bg-slate-900 rounded-lg overflow-hidden">
            <pre class="language-json p-3 md:p-4 text-xs md:text-sm overflow-x-auto max-h-96 overflow-y-auto"><code class="whitespace-pre-wrap break-words">{
  "success": false,
  "message": "Invalid DIGIPIN format"
}</code></pre>
        </div>
    </div>

    <!-- Digipin Info -->
    <div class="mb-10">
        <h3 class="text-lg md:text-xl font-bold text-slate-800 dark:text-slate-200 mb-3">3. Get Digipin Info</h3>
        <p class="text-sm md:text-base text-slate-700 dark:text-slate-300 leading-relaxed mb-4">
            Retrieve information and configuration details about the DIGIPIN system.
        </p>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div class="bg-slate-50 dark:bg-slate-900/50 p-3 rounded-lg">
                <span class="font-semibold text-slate-900 dark:text-white text-sm">Endpoint:</span>
                <div class="mt-1 text-emerald-600 dark:text-emerald-400 font-mono text-xs md:text-sm break-all">
                    GET /api/digipin/info
                </div>
            </div>
            <div class="bg-slate-50 dark:bg-slate-900/50 p-3 rounded-lg">
                <span class="font-semibold text-slate-900 dark:text-white text-sm">Method:</span>
                <div class="mt-1 text-blue-600 dark:text-blue-400 font-mono text-xs md:text-sm">GET</div>
            </div>
        </div>
        <h4 class="text-base md:text-lg font-semibold text-slate-800 dark:text-slate-200 mt-6 mb-2">Response Example:</h4>
        <div class="bg-slate-900 rounded-lg overflow-hidden">
            <div class="bg-slate-800 px-3 py-2 text-xs text-slate-300 border-b border-slate-700">JSON Response</div>
            <pre class="language-json p-3 md:p-4 text-xs md:text-sm overflow-x-auto max-h-96 overflow-y-auto"><code class="whitespace-pre-wrap break-words">{
  "success": true,
  "data": {
    "description": "DIGIPIN is a geocoding system for encoding latitude and longitude into a short code.",
    "bounds": {
      "minLatitude": -90,
      "maxLatitude": 90,
      "minLongitude": -180,
      "maxLongitude": 180
    },
    "grid": "1km x 1km",
    "validCharacters": "ABCDEFGHJKLMNPQRSTUVWXYZ23456789",
    "endpoints": {
      "generate": "/api/digipin/generate",
      "decode": "/api/digipin/decode",
      "info": "/api/digipin/info"
    }
  }
}</code></pre>
        </div>
    </div>
</section> 