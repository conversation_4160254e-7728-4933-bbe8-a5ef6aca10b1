<?php

namespace Tests\Feature\Services;

use App\Models\ApiRequest;
use App\Models\User;
use App\Services\ApiRequestLogService;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Mockery;
use Mockery\MockInterface;
use Tests\TestCase;

it('logs a request for an authenticated user with a token successfully', function () {
    $user = User::factory()->create();
    $token = $user->createToken('test-token')->accessToken;
    $user->withAccessToken($token);

    $this->actingAs($user);

    $requestData = ['param' => 'value'];
    $responseData = ['data' => 'success'];

    $request = Mockery::mock(Request::class);
    $request->shouldReceive('user')->andReturn($user);
    $request->shouldReceive('path')->andReturn('api/test');
    $request->shouldReceive('method')->andReturn('GET');
    $request->shouldReceive('ip')->andReturn('127.0.0.1');
    $request->shouldReceive('all')->andReturn($requestData);

    $response = new JsonResponse($responseData, 200);

    $service = app(ApiRequestLogService::class);
    $result = $service->logRequest($request, $response);

    expect($result)->toBeTrue();
    $this->assertDatabaseHas('api_requests', [
        'user_id' => $user->id,
        'personal_access_token_id' => $token->id,
        'endpoint' => 'api/test',
        'method' => 'GET',
        'status' => 200,
        'ip_address' => '127.0.0.1',
        'request_data' => json_encode($requestData),
        'response_data' => json_encode($responseData),
    ]);
});

it('logs a request for an authenticated user without a token successfully', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $requestData = ['foo' => 'bar'];
    $responseData = ['message' => 'created'];

    $request = Mockery::mock(Request::class);
    $request->shouldReceive('user')->andReturn($user);
    $request->shouldReceive('path')->andReturn('api/auth-test');
    $request->shouldReceive('method')->andReturn('POST');
    $request->shouldReceive('ip')->andReturn('*********');
    $request->shouldReceive('all')->andReturn($requestData);

    $response = new JsonResponse($responseData, 201);

    $service = app(ApiRequestLogService::class);
    $result = $service->logRequest($request, $response);

    expect($result)->toBeTrue();
    $this->assertDatabaseHas('api_requests', [
        'user_id' => $user->id,
        'personal_access_token_id' => null,
        'endpoint' => 'api/auth-test',
        'method' => 'POST',
        'status' => 201,
        'ip_address' => '*********',
        'request_data' => json_encode($requestData),
        'response_data' => json_encode($responseData),
    ]);
});

it('logs a request for a guest user successfully', function () {
    $requestData = [];
    $responseData = ['data' => 'public'];

    $request = Mockery::mock(Request::class);
    $request->shouldReceive('user')->andReturn(null);
    $request->shouldReceive('path')->andReturn('api/guest');
    $request->shouldReceive('method')->andReturn('GET');
    $request->shouldReceive('ip')->andReturn('*********');
    $request->shouldReceive('all')->andReturn($requestData);

    $response = new JsonResponse($responseData, 200);

    $service = app(ApiRequestLogService::class);
    $result = $service->logRequest($request, $response);

    expect($result)->toBeTrue();
    $this->assertDatabaseHas('api_requests', [
        'user_id' => null,
        'personal_access_token_id' => null,
        'endpoint' => 'api/guest',
        'method' => 'GET',
        'status' => 200,
        'ip_address' => '*********',
        'request_data' => json_encode($requestData),
        'response_data' => json_encode($responseData),
    ]);
});

it('handles exception during logging and returns false', function () {
    Log::shouldReceive('error')
        ->once()
        ->with('Failed to log API request: Database error');

    $mock = $this->mock(ApiRequest::class, function (MockInterface $mock) {
        $mock->shouldReceive('create')->andThrow(new Exception('Database error'));
    });
    
    $request = Mockery::mock(Request::class);
    $request->shouldReceive('user')->andReturn(null);
    $request->shouldReceive('path')->andReturn('api/fail');
    $request->shouldReceive('method')->andReturn('GET');
    $request->shouldReceive('ip')->andReturn('127.0.0.1');
    $request->shouldReceive('all')->andReturn([]);
    $response = new JsonResponse([], 500);

    $service = app(ApiRequestLogService::class);

    $result = $service->logRequest($request, $response);

    expect($result)->toBeFalse();
    $this->assertDatabaseCount('api_requests', 0);
}); 