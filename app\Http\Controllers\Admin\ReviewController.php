<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Review;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ReviewController extends Controller
{
    /**
     * Display a listing of all reviews.
     */
    public function index(): View
    {
        $reviews = Review::with(['user', 'pincode'])
            ->latest()
            ->paginate(20);

        return view('admin.reviews.index', compact('reviews'));
    }

    /**
     * Show the form for editing the specified review.
     */
    public function edit(Review $review): View
    {
        return view('admin.reviews.edit', compact('review'));
    }

    /**
     * Update the specified review in storage.
     */
    public function update(Request $request, Review $review)
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,approved,rejected',
            'review' => 'required|string|min:10|max:1000',
        ]);

        $review->update($validated);

        return redirect()->route('admin.reviews.index')
            ->with('status', 'Review updated successfully.');
    }

    /**
     * Remove the specified review from storage.
     */
    public function destroy(Review $review)
    {
        $review->delete();

        return redirect()->route('admin.reviews.index')
            ->with('status', 'Review deleted successfully.');
    }

    /**
     * Approve the specified review.
     */
    public function approve(Review $review)
    {
        $review->update(['status' => 'approved']);

        return redirect()->route('admin.reviews.index')
            ->with('status', 'Review approved successfully.');
    }

    /**
     * Reject the specified review.
     */
    public function reject(Review $review)
    {
        $review->update(['status' => 'rejected']);

        return redirect()->route('admin.reviews.index')
            ->with('status', 'Review rejected successfully.');
    }
} 