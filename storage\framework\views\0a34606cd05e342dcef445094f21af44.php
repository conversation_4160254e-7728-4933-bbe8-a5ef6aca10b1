<?php $__env->startSection('title', 'Import Errors - ' . $import->filename); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white dark:bg-bg-dark shadow-md rounded-lg overflow-hidden border border-border-light dark:border-border-dark">
        <div class="px-6 py-4 border-b border-border-light dark:border-border-dark flex justify-between items-center">
            <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">
                Import Errors for <?php echo e($import->filename); ?>

            </h3>
            <a href="<?php echo e(route('admin.pincodes.import')); ?>" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-text-primary-dark px-4 py-2 rounded-md text-sm font-medium flex items-center gap-2 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
                Back to Import
            </a>
        </div>
        <div class="p-6">
            <div class="mb-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="text-base font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">Import Summary</h5>
                    <table class="min-w-full text-sm">
                        <tr>
                            <td class="font-medium text-text-secondary-light dark:text-text-secondary-dark pr-2">File:</td>
                            <td class="text-text-primary-light dark:text-text-primary-dark"><?php echo e($import->filename); ?></td>
                        </tr>
                        <tr>
                            <td class="font-medium text-text-secondary-light dark:text-text-secondary-dark pr-2">Status:</td>
                            <td>
                                <span class="px-2 py-0.5 rounded-full text-xs font-semibold
                                    <?php echo e($import->status === 'completed' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' :
                                       ($import->status === 'processing' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400')); ?>">
                                    <?php echo e(ucfirst($import->status)); ?>

                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td class="font-medium text-text-secondary-light dark:text-text-secondary-dark pr-2">Total Records:</td>
                            <td class="text-text-primary-light dark:text-text-primary-dark"><?php echo e($import->total_records ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <td class="font-medium text-text-secondary-light dark:text-text-secondary-dark pr-2">Successful:</td>
                            <td class="text-green-600 dark:text-green-400"><?php echo e($import->successful_records ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <td class="font-medium text-text-secondary-light dark:text-text-secondary-dark pr-2">Failed:</td>
                            <td class="text-red-600 dark:text-red-400"><?php echo e($import->failed_records ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <td class="font-medium text-text-secondary-light dark:text-text-secondary-dark pr-2">Created:</td>
                            <td class="text-text-primary-light dark:text-text-primary-dark"><?php echo e($import->created_at->format('M d, Y H:i:s')); ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <?php if(!empty($importErrors)): ?>
                <h5 class="text-base font-semibold text-text-primary-light dark:text-text-primary-dark mb-2 mt-6">Error Details</h5>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-border-light dark:divide-border-dark bg-white dark:bg-bg-dark rounded-lg">
                        <thead class="bg-gray-50 dark:bg-gray-800">
                            <tr>
                                <th class="px-4 py-2 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Row</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Error</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Data</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-border-light dark:divide-border-dark">
                            <?php $__currentLoopData = $importErrors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td class="px-4 py-2 text-sm text-text-primary-light dark:text-text-primary-dark"><?php echo e($error['row'] ?? 'N/A'); ?></td>
                                    <td class="px-4 py-2 text-sm text-red-600 dark:text-red-400"><?php echo e($error['message'] ?? $error['error'] ?? 'Unknown error'); ?></td>
                                    <td class="px-4 py-2 text-sm"><code class="text-text-secondary-light dark:text-text-secondary-dark"><?php echo e($error['data'] ?? 'N/A'); ?></code></td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-200 px-4 py-3 rounded flex items-center gap-2 mt-6">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01" />
                    </svg>
                    No detailed error information available for this import.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/admin/pincodes/import-errors.blade.php ENDPATH**/ ?>