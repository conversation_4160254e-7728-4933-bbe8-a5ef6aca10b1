<?php

namespace App\Http\Controllers;

use App\Models\PinCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Barryvdh\DomPDF\Facade\Pdf;

class PincodeDistanceController extends Controller
{
    /**
     * Handle the Excel file upload and process it
     */
    public function upload(Request $request)
    {
              
        $request->validate([
            'excel_file' => 'required|file|mimes:xlsx,xls,csv',
        ]);

        // Store the uploaded file
        $file = $request->file('excel_file');
        $filePath = $file->store('temp');
        
        // Process the Excel file
        $fullPath = Storage::path($filePath);
        
        // Use a chunk reader to handle large files
        $reader = IOFactory::createReaderForFile($fullPath);
        $reader->setReadDataOnly(true);
        $spreadsheet = $reader->load($fullPath);
        $worksheet = $spreadsheet->getActiveSheet();
        
        // Get the highest row and column
        $highestRow = $worksheet->getHighestRow();
        $highestColumn = $worksheet->getHighestColumn();
        
        // Skip header row
        $headerRow = $worksheet->rangeToArray('A1:' . $highestColumn . '1', null, true, false)[0];
        
        // Create a temporary file to store results instead of using session
        $resultsFile = 'pincode_results_' . time() . '.json';
        $resultsPath = storage_path('app/temp/' . $resultsFile);
        
        // Make sure the temp directory exists
        $tempDir = storage_path('app/temp');
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }
        
        // Initialize results array with headers
        $results = [
            'headers' => $headerRow,
            'data' => []
        ];
        
        // Process each row in chunks to avoid memory issues
        $chunkSize = 100; // Process 100 rows at a time
        $processedCount = 0;
        $successCount = 0;
        
        for ($startRow = 2; $startRow <= $highestRow; $startRow += $chunkSize) {
            $endRow = min($startRow + $chunkSize - 1, $highestRow);
            
            $dataRows = $worksheet->rangeToArray('A' . $startRow . ':' . $highestColumn . $endRow, null, true, false);
            
            foreach ($dataRows as $rowIndex => $row) {
                $processedCount++;
                $currentRow = $startRow + $rowIndex;
                
                // Using column B (index 1) as source and D (index 3) as destination
                $sourcePincode = $row[1] ?? null;  // Column B
                $destinationPincode = $row[3] ?? null;  // Column D
                
                if (!$sourcePincode || !$destinationPincode) {
                    continue; // Skip rows with missing data
                }
                
                // Get coordinates for source and destination
                $sourceLocation = PinCode::where('pincode', $sourcePincode)->first();
                $destinationLocation = PinCode::where('pincode', $destinationPincode)->first();
                
                // Store all rows, even if we can't calculate distance
                $resultRow = [
                    'original_row' => $row,  // Store complete original row
                    'source_pincode' => $sourcePincode,
                    'destination_pincode' => $destinationPincode,
                ];
                
                if ($sourceLocation && $destinationLocation) {
                    // Calculate distance
                    $distance = $this->calculateDistance(
                        $sourceLocation->latitude,
                        $sourceLocation->longitude,
                        $destinationLocation->latitude,
                        $destinationLocation->longitude
                    );
                    
                    // Add location data and distance
                    $resultRow['source_city'] = $sourceLocation->district;
                    // Add location data and distance
                    $resultRow['source_city'] = $sourceLocation->district;
                    $resultRow['source_state'] = $sourceLocation->state;
                    $resultRow['destination_city'] = $destinationLocation->district;
                    $resultRow['destination_state'] = $destinationLocation->state;
                    $resultRow['distance'] = $distance;
                    $successCount++;
                } else {
                    // Set default values for missing data
                    $resultRow['source_city'] = 'Not found';
                    $resultRow['source_state'] = 'Not found';
                    $resultRow['destination_city'] = 'Not found';
                    $resultRow['destination_state'] = 'Not found';
                    $resultRow['distance'] = null;
                }
                
                $results['data'][] = $resultRow;
            }
            
            // Free memory
            unset($dataRows);
            gc_collect_cycles();
        }
        
        // Save results to file
        file_put_contents($resultsPath, json_encode($results));
        
        // Store only the file path in session
        session(['pincode_results_file' => $resultsFile]);
        
        // Clean up the temporary file
        Storage::delete($filePath);
        
        return view('tools.distance.result', ['results' => $results['data']]);
    }
    
    /**
     * Download the results as Excel file
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\RedirectResponse
     */
    public function downloadExcel()
    { 
        // Get results from file instead of session
        $resultsFile = session('pincode_results_file');
        
        if (!$resultsFile) {
            return redirect()->back()->with('error', 'No results found. Please upload a file first.');
        }
        
        $resultsPath = storage_path('app/temp/' . $resultsFile);
        
        if (!file_exists($resultsPath)) {
            return redirect()->back()->with('error', 'Results file not found. Please upload a file again.');
        }
        
        $results = json_decode(file_get_contents($resultsPath), true);
        
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // Add headers from original file
        $headers = ['A', 'B', 'C', 'D', 'E'];
        foreach ($headers as $index => $column) {
            if (isset($results['headers'][$index])) {
                $sheet->setCellValue($column . '1', $results['headers'][$index]);
            }
        }
        
        // Add data
        $row = 2;
        foreach ($results['data'] as $result) {
            // Copy original row data
            foreach ($result['original_row'] as $colIndex => $value) {
                $sheet->setCellValue(chr(65 + $colIndex) . $row, $value);
            }
            
            // Add calculated distance to column E
            $sheet->setCellValue('E' . $row, $result['distance']);
            
            $row++;
        }
        
        $writer = new Xlsx($spreadsheet);
        $filename = 'pincode_distances_' . date('YmdHis') . '.xlsx';
        
        // Make sure the temp directory exists
        $tempDir = storage_path('app/temp');
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }
        
        $tempPath = $tempDir . '/' . $filename;
        
        try {
            $writer->save($tempPath);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error generating Excel file: ' . $e->getMessage());
        }
        
        return response()->download($tempPath, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ])->deleteFileAfterSend(true);
    }
    
    /**
     * Download the results as PDF file
     *
     */
    public function downloadPdf()
    {
        // Get results from file instead of session
        $resultsFile = session('pincode_results_file');
        if (!$resultsFile) {
            return redirect()->back()->with('error', 'No results found. Please upload a file first.');
        }
        
        $resultsPath = storage_path('app/temp/' . $resultsFile);
        if (!file_exists($resultsPath)) {
            return redirect()->back()->with('error', 'Results file not found. Please upload a file again.');
        }
        
        $results = json_decode(file_get_contents($resultsPath), true);
        
        $pdf = PDF::loadView('tools.distance.pdf', ['results' => $results['data']]);
        
        return $pdf->download('pincode_distances_' . date('YmdHis') . '.pdf');
    }
    
    /**
     * Calculate the distance between two coordinates using Haversine formula
     *
     * @param float $lat1 Source latitude
     * @param float $lng1 Source longitude
     * @param float $lat2 Destination latitude
     * @param float $lng2 Destination longitude
     * @return float Distance in kilometers
     */
    private function calculateDistance($lat1, $lng1, $lat2, $lng2)
    {
        // Earth's radius in kilometers
        $earthRadius = 6371;
        
        $latDiff = deg2rad($lat2 - $lat1);
        $lngDiff = deg2rad($lng2 - $lng1);
        
        $a = sin($latDiff / 2) * sin($latDiff / 2) +
            cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
            sin($lngDiff / 2) * sin($lngDiff / 2);
            
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        
        return $earthRadius * $c;
    }
    
    /**
     * Get nearest post office location
     *
     * @param float $latitude User's latitude
     * @param float $longitude User's longitude
     * @return \Illuminate\Http\JsonResponse
     */
    // public function getNearestPostOfficeLocation($latitude, $longitude)
    // {
    //     $userLat = $latitude;
    //     $userLng = $longitude;

    //     // Define the bounding box for limiting the number of records
    //     $distance = 10; // Distance in kilometers
    //     $latRange = $distance / 111; // 1 degree latitude ~ 111 km
    //     $lngRange = $distance / (111 * cos(deg2rad($userLat)));

    //     // Using the Haversine formula to calculate distances
    //     $nearestLocation = PinCode::select(DB::raw("*, 
    //         (6371 * acos(cos(radians($userLat)) 
    //         * cos(radians(latitude)) 
    //         * cos(radians(longitude) - radians($userLng)) 
    //         + sin(radians($userLat)) 
    //         * sin(radians(latitude)))) AS distance"))
    //         ->whereBetween('latitude', [$userLat - $latRange, $userLat + $latRange])
    //         ->whereBetween('longitude', [$userLng - $lngRange, $userLng + $lngRange])
    //         ->orderBy('distance')
    //         ->first();

    //     if (!$nearestLocation) {
    //         return response()->json(['error' => 'No post office found nearby'], 404);
    //     }

    //     // Get all post offices with the same pincode
    //     $samePostOffices = PinCode::select(DB::raw("*, 
    //         (6371 * acos(cos(radians($userLat)) 
    //         * cos(radians(latitude)) 
    //         * cos(radians(longitude) - radians($userLng)) 
    //         + sin(radians($userLat)) 
    //         * sin(radians(latitude)))) AS distance"))
    //         ->where('pincode', $nearestLocation->pincode)
    //         ->orderBy('distance')
    //         ->get();

    //     return response()->json([
    //         'postalCode' => $nearestLocation->pincode,
    //         'city' => ucfirst($nearestLocation->district),
    //         'state' => ucfirst($nearestLocation->state),
    //         'nearestDistance' => $nearestLocation->distance,
    //         'postOffices' => $samePostOffices->map(function($office) {
    //             return [
    //                 'name' => ucfirst($office->name),
    //                 'distance' => $office->distance,
    //                 'latitude' => $office->latitude,
    //                 'longitude' => $office->longitude
    //             ];
    //         })
    //     ]);
    // }
    
}