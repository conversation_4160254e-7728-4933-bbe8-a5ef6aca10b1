<div class="mb-3">
    <label class="form-label">Key ID *</label>
    <input type="text" class="form-control" name="configuration[key_id]" 
           value="{{ $config['key_id'] ?? '' }}" required
           placeholder="rzp_test_xxxxxxxxxx">
    <small class="text-muted">Your Razorpay Key ID from the dashboard</small>
</div>

<div class="mb-3">
    <label class="form-label">Key Secret *</label>
    <div class="input-group">
        <input type="password" class="form-control" name="configuration[key_secret]" 
               value="{{ $config['key_secret'] ?? '' }}" required
               placeholder="Enter your key secret">
        <button type="button" class="btn btn-outline-secondary toggle-password">
            <i class="mdi mdi-eye"></i>
        </button>
    </div>
    <small class="text-muted">Your Razorpay Key Secret (keep this secure)</small>
</div>

<div class="mb-3">
    <label class="form-label">Environment</label>
    <select class="form-select" name="configuration[environment]">
        <option value="sandbox" {{ ($config['environment'] ?? 'sandbox') === 'sandbox' ? 'selected' : '' }}>
            Sandbox (Test)
        </option>
        <option value="production" {{ ($config['environment'] ?? 'sandbox') === 'production' ? 'selected' : '' }}>
            Production (Live)
        </option>
    </select>
</div>

<div class="mb-3">
    <label class="form-label">Default Currency</label>
    <select class="form-select" name="configuration[default_currency]">
        <option value="INR" {{ ($config['default_currency'] ?? 'INR') === 'INR' ? 'selected' : '' }}>INR - Indian Rupee</option>
        <option value="USD" {{ ($config['default_currency'] ?? 'INR') === 'USD' ? 'selected' : '' }}>USD - US Dollar</option>
        <option value="EUR" {{ ($config['default_currency'] ?? 'INR') === 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
    </select>
</div>

<div class="row">
    <div class="col-md-6 mb-3">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" name="configuration[auto_capture]" 
                   value="1" {{ ($config['auto_capture'] ?? true) ? 'checked' : '' }}>
            <label class="form-check-label">Auto Capture Payments</label>
        </div>
        <small class="text-muted">Automatically capture payments after authorization</small>
    </div>
    <div class="col-md-6 mb-3">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" name="configuration[enable_international]" 
                   value="1" {{ ($config['enable_international'] ?? false) ? 'checked' : '' }}>
            <label class="form-check-label">Enable International Payments</label>
        </div>
        <small class="text-muted">Allow payments from international cards</small>
    </div>
</div>

<div class="alert alert-info">
    <h6 class="alert-heading">
        <i class="mdi mdi-information me-2"></i>
        Razorpay Setup Instructions:
    </h6>
    <ol class="mb-0 small">
        <li>Log in to your <a href="https://dashboard.razorpay.com/" target="_blank">Razorpay Dashboard</a></li>
        <li>Go to Settings → API Keys</li>
        <li>Generate or copy your Key ID and Key Secret</li>
        <li>Configure webhook URL in Settings → Webhooks</li>
        <li>Enable required payment methods in Settings → Configuration</li>
    </ol>
</div>