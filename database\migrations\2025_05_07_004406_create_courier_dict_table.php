<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('courier_dict', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('term');
            $table->text('description')->nullable();
            $table->longText('long_description')->nullable();
            $table->string('tag')->nullable();
            $table->string('slug')->unique();
            $table->timestamps();
            $table->softDeletes();
            
            // Adding index for search functionality with length limit for text column
            $table->index(['term', 'tag']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('courier_dict');
    }
};

// php artisan migrate --path=database/migrations/2025_05_07_004406_create_courier_dict_table.php
