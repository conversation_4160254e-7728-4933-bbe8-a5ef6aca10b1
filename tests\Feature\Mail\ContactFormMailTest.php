<?php

use App\Mail\ContactFormMail;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use function Pest\Faker\faker;

// Test ContactFormMail without static mocking to avoid interference
uses()->group('mail', 'isolated');

test('contact form mail has correct data', function () {
        $data = [
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'Test Message',
        ];

        $mail = new ContactFormMail($data);

    expect($mail->data)->toBe($data);
});

test('contact form mail has correct envelope', function () {
        $data = [
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'Test Message',
        ];

        $mail = new ContactFormMail($data);
        $envelope = $mail->envelope();

    expect($envelope)->toBeInstanceOf(Envelope::class)
        ->and($envelope->subject)->toBe("New Contact Form Submission: {$data['subject']}")
        ->and($envelope->tags)->toBe(['contact-form'])
        ->and($envelope->metadata)->toHaveKey('contact_form_id')
        ->and($envelope->metadata['contact_form_id'])->toStartWith('contact_');
});

test('contact form mail has correct content', function () {
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'Test Message',
        ];

        $mail = new ContactFormMail($data);
        $content = $mail->content();

    expect($content)->toBeInstanceOf(Content::class)
        ->and($content->view)->toBe('emails.contact-form')
        ->and($content->with)->toHaveKey('name')
        ->and($content->with['name'])->toBe($data['name'])
        ->and($content->with['email'])->toBe($data['email'])
        ->and($content->with['subject'])->toBe($data['subject'])
        ->and($content->with['message'])->toBe($data['message']);
    // Note: We're not testing siteName since it depends on Setting::get() which we're not mocking
});

test('contact form mail has empty attachments', function () {
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'Test Message',
        ];

        $mail = new ContactFormMail($data);
        $attachments = $mail->attachments();

    expect($attachments)->toBeArray()->toBeEmpty();
});

test('contact form mail handles missing data gracefully', function () {
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'message' => 'Test Message',
        ];

        $mail = new ContactFormMail($data);

    expect($mail)->toBeInstanceOf(ContactFormMail::class)
        ->and($mail->envelope()->subject)->toBe('New Contact Form Submission: ');
    // Note: We're not testing siteName since it depends on Setting::get()
});

test('contact form mail handles empty data gracefully', function () {
        $data = [
            'name' => '',
            'email' => '',
            'subject' => '',
            'message' => '',
        ];

        $mail = new ContactFormMail($data);
        $envelope = $mail->envelope();
        $content = $mail->content();

    expect($envelope->subject)->toBe('New Contact Form Submission: ')
        ->and($content->with['name'])->toBe('')
        ->and($content->with['email'])->toBe('')
        ->and($content->with['subject'])->toBe('')
        ->and($content->with['message'])->toBe('');
});