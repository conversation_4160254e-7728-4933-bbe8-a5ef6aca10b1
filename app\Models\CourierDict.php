<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class CourierDict extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $table = 'courier_dict';
    protected $primaryKey = 'id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'term',
        'description',
        'long_description',
        'tag',
        'slug'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->slug = static::generateUniqueSlug($model->term);
        });

        static::updating(function ($model) {
            if ($model->isDirty('term')) {
                $model->slug = static::generateUniqueSlug($model->term);
            }
        });
    }

    protected static function generateUniqueSlug($term)
    {
        $slug = Str::slug($term);
        if (DB::getDriverName() === 'sqlite') {
            // Use LIKE for SQLite as it doesn't support REGEXP
            $count = static::where('slug', 'LIKE', "{$slug}%")->count();
        } else {
            // Use RLIKE for MySQL
            $count = static::whereRaw("slug RLIKE '^{$slug}(-[0-9]+)?$'")->count();
        }
        
        return $count ? "{$slug}-{$count}" : $slug;
    }

    // Get related terms based on tag
    public function relatedTerms()
    {
        return $this->where('tag', $this->tag)
            ->where('id', '!=', $this->id)
            ->limit(2)
            ->get();
    }

    public function randomRelatedTerms()
    {
        return $this->where('id', '!=', $this->id)
            ->inRandomOrder()
            ->limit(10)
            ->get();
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function($q) use ($search) {
            $q->where('term', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere(function($subQ) use ($search) {
                  // This subquery ensures we only match by tag if the search term
                  // is NOT found in the term or description, making it "exclusive".
                  $subQ->where('tag', $search)
                       ->where('term', 'NOT LIKE', "%{$search}%")
                       ->where('description', 'NOT LIKE', "%{$search}%");
              });
        });
    }
}