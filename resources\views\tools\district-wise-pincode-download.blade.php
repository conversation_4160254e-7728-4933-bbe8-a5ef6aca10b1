@extends('layouts.app')

@section('meta')
    <meta name="description"
        content="Download free Indian postal pincode data for all states and districts in Excel and PDF formats. Complete directory with post office names, branch types, and delivery status information.">
    <meta name="keywords"
        content="Indian pincode directory, postal code download, India PIN codes, pincode database, state pincode list, district PIN codes, post office directory">
    <link rel="canonical" href="{{ url()->current() }}">
    <meta property="og:title" content="Indian Pincode Directory - Download Complete Postal Code Data">
    <meta property="og:description"
        content="Access comprehensive pincode data for all Indian states and districts. Free download in Excel and PDF formats with post office details and delivery status.">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:type" content="website">
@endsection

@include('layouts.partials.tools-json-ld')

@push('structured-data')
    <script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "Indian Pincode Directory - Download Complete Postal Code Data",
  "description": "Access comprehensive pincode data for all Indian states and districts. Free download in Excel and PDF formats with post office details and delivery status.",
  "mainEntity": {
    "@type": "DataCatalog",
    "name": "Indian Postal Pincode Database",
    "about": "India Post PIN codes",
    "dataset": {
      "@type": "Dataset",
      "name": "Indian Pincode Data",
      "description": "Complete directory of Indian post office PIN codes with state, district, branch type, and delivery status information",
      "distribution": [
        {
          "@type": "DataDownload",
          "encodingFormat": "xlsx",
          "contentUrl": "{{ route('pincodes.downloadNow') }}"
        },
        {
          "@type": "DataDownload",
          "encodingFormat": "pdf",
          "contentUrl": "{{ route('pincodes.downloadNow') }}"
        }
      ]
    }
  }
}
</script>
@endpush

@section('content')
    <x-breadcrumb :segments="$breadcrumbs" :pageTitle="$pageTitle" />
    <div class="container max-w-6xl mx-auto px-4 py-8">
        <div class="flex flex-wrap -mx-4">
            <div class="w-full lg:w-2/3 px-4">
                <div class="bg-white dark:bg-bg-dark rounded-lg shadow-lg p-6 mb-8" x-data="pincodeData()" x-init="init()">
                    <h2 class="text-3xl font-bold mb-4 text-text-primary-light dark:text-text-primary-dark">Indian Pincode Directory: Download Complete Postal
                        Code Data by State &amp; District</h2>

                    <div class="mb-6">
                        <p class="text-text-secondary-light dark:text-text-secondary-dark text-lg">Search, browse and download accurate Indian PIN codes for all
                            states, districts and post offices. Available in Excel and PDF formats. Last updated:
                            {{ date('F Y') }}.</p>
                    </div>

                    <div class="grid md:grid-cols-2 gap-8 mb-8">
                        <div>
                            <h2 class="text-2xl font-semibold mb-3 text-text-primary-light dark:text-text-primary-dark">Complete Indian Postal Code Database</h2>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark leading-relaxed mb-4">
                                Access our comprehensive directory of Indian postal PIN codes covering all
                                <strong>{{ $m_states->count() ?? '36' }} states and union territories</strong>
                                across India. Our database includes:
                            </p>
                            <ul class="list-disc pl-5 mb-4 text-text-secondary-light dark:text-text-secondary-dark">
                                <li>Post office names with exact PIN codes</li>
                                <li>Complete district and state information</li>
                                <li>Branch type classification (Head Office, Sub Office, etc.)</li>
                                <li>Delivery status and coverage information</li>
                            </ul>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark">
                                Perfect for businesses, researchers, and organizations that need reliable postal code data
                                for shipping, address verification, or market analysis.
                            </p>
                        </div>
                        <div>
                            <div class="bg-bg-light dark:bg-gray-800 rounded-lg p-6">
                                <h3 class="text-xl font-semibold mb-3 text-text-primary-light dark:text-text-primary-dark">Find Pincodes by Location</h3>
                                <form @submit.prevent="fetchPincodes(1)" class="space-y-4">
                                    <div>
                                        <label for="state"
                                            class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">State:</label>
                                        <select x-model="state" id="state" @change="loadDistricts()"
                                            class="w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark transition duration-150 ease-in-out">
                                            <option value="">Select State</option>
                                            @foreach ($m_states as $state)
                                                <option value="{{ $state->id }}">{{ $state->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div>
                                        <label for="district"
                                            class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">District:</label>
                                        <select x-model="district" id="district"
                                            class="w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark transition duration-150 ease-in-out"
                                            :disabled="!state">
                                            <option value="">Select District</option>
                                            <template x-for="dist in districts" :key="dist.id">
                                                <option :value="dist.id" x-text="dist.name"></option>
                                            </template>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="pincode" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-1">Pincode
                                            (Optional):</label>
                                        <input type="text" x-model="pincode" id="pincode"
                                            class="w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark transition duration-150 ease-in-out"
                                            placeholder="Enter 6-digit pincode (e.g., 110001)" :disabled="!state">
                                    </div>
                                    <button type="submit"
                                        class="w-full bg-primary-light dark:bg-primary-dark hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-md transition duration-300 transform hover:scale-105">
                                        Search Pincodes
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="mt-8" x-show="pincodes.length > 0">
                        <h2 class="text-2xl font-semibold mb-4 text-text-primary-light dark:text-text-primary-dark">Pincode Search Results</h2>
                        <div class="downloadable_result space-y-4">
                            <div class="flex justify-between items-center mb-4">
                                <button @click="fetchPincodes(currentPage - 1)" :disabled="currentPage <= 1"
                                    :class="{
                                        'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed': currentPage <= 1,
                                        'bg-primary-light dark:bg-primary-dark hover:bg-blue-700 dark:hover:bg-blue-600 text-white': currentPage > 1
                                    }"
                                    class="px-4 py-2 border border-border-light dark:border-border-dark text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                                    Previous
                                </button>

                                <span class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Page <span x-text="currentPage"></span> of <span
                                        x-text="totalPages"></span></span>

                                <button @click="fetchPincodes(currentPage + 1)" :disabled="currentPage >= totalPages"
                                    :class="{
                                        'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed': currentPage >= totalPages,
                                        'bg-primary-light dark:bg-primary-dark hover:bg-blue-700 dark:hover:bg-blue-600 text-white': currentPage < totalPages
                                    }"
                                    class="px-4 py-2 border border-border-light dark:border-border-dark text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                                    Next
                                </button>
                            </div>

                            <div class="overflow-x-auto bg-white dark:bg-bg-dark rounded-lg shadow">
                                <table class="min-w-full divide-y divide-border-light dark:divide-border-dark" x-show="pincodes.length">
                                    <thead class="bg-bg-light dark:bg-gray-800">
                                        <tr>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                                Post Office Name</th>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                                Pincode</th>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                                District</th>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                                State</th>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                                Branch Type</th>
                                            <th
                                                class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">
                                                Delivery Status</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-bg-dark divide-y divide-border-light dark:divide-border-dark">
                                        <template x-for="pincode in pincodes" :key="pincode.id">
                                            <tr class="hover:bg-bg-light dark:hover:bg-gray-800 transition duration-150 ease-in-out">
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark"
                                                    x-text="pincode.name"></td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark"
                                                    x-text="pincode.pincode"></td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark"
                                                    x-text="pincode.district"></td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark"
                                                    x-text="pincode.state"></td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark"
                                                    x-text="pincode.branch_type"></td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-text-primary-light dark:text-text-primary-dark"
                                                    x-text="pincode.delivery_status"></td>
                                            </tr>
                                        </template>
                                    </tbody>
                                </table>
                            </div>

                            <div class="download-form-container relative rounded-lg overflow-hidden mt-8">
                                <div class="max-w-lg mx-auto" x-data="{ format: 'excel', downloading: false }">
                                    <!-- Download Form Card -->
                                    <div class="download-form-container relative rounded-xl overflow-hidden shadow-2xl">
                                        <!-- Background with animated gradient -->
                                        <div
                                            class="absolute inset-0 bg-gradient-to-br from-purple-600 via-indigo-600 to-blue-500 opacity-90">
                                            <div class="absolute inset-0 bg-pattern opacity-10"></div>
                                        </div>

                                        <!-- Card Content -->
                                        <div class="relative p-8">
                                            <div class="flex items-center mb-6">
                                                <div class="rounded-full bg-white bg-opacity-20 p-3 mr-4">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white"
                                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                                                    </svg>
                                                </div>
                                                <h3 class="text-2xl font-bold text-white">Download Pincode Data</h3>
                                            </div>

                                            <div class="bg-white bg-opacity-95 p-6 rounded-lg shadow-inner">
                                                <div class="space-y-5">
                                                    <div>
                                                        <label for="format"
                                                            class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">File
                                                            Format</label>
                                                        <div class="relative">
                                                            <select x-model="format"
                                                                class="w-full pl-4 pr-10 py-3 bg-bg-light dark:bg-bg-dark border border-border-light dark:border-border-dark text-text-primary-light dark:text-text-primary-dark rounded-lg appearance-none focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-transparent transition duration-200">
                                                                <option value="excel">Excel (.xlsx)</option>
                                                                <option value="pdf">PDF Document</option>
                                                            </select>
                                                            <div
                                                                class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                                                <svg class="h-5 w-5 text-text-secondary-light dark:text-text-secondary-dark"
                                                                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                                                    fill="currentColor">
                                                                    <path fill-rule="evenodd"
                                                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                                                        clip-rule="evenodd" />
                                                                </svg>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="flex items-center text-sm text-text-secondary-light dark:text-text-secondary-dark mb-2">
                                                        <template x-if="format === 'excel'">
                                                            <div class="flex items-center">
                                                                <svg class="w-4 h-4 mr-1 text-green-600"
                                                                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                                                    fill="currentColor">
                                                                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                                                                    <path fill-rule="evenodd"
                                                                        d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z"
                                                                        clip-rule="evenodd" />
                                                                </svg>
                                                                <span>All data with formatting preserved</span>
                                                            </div>
                                                        </template>
                                                        <template x-if="format === 'pdf'">
                                                            <div class="flex items-center">
                                                                <svg class="w-4 h-4 mr-1 text-red-600"
                                                                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                                                    fill="currentColor">
                                                                    <path fill-rule="evenodd"
                                                                        d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z"
                                                                        clip-rule="evenodd" />
                                                                </svg>
                                                                <span>Perfect for printing and sharing</span>
                                                            </div>
                                                        </template>
                                                    </div>

                                                    <button
                                                        @click="downloading = true; downloadFile(); setTimeout(() => downloading = false, 2000)"
                                                        class="w-full flex items-center justify-center bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 text-white font-medium py-3 px-4 rounded-lg transition duration-300 transform hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 shadow-lg">
                                                        <template x-if="!downloading">
                                                            <div class="flex items-center">
                                                                <svg class="w-5 h-5 mr-2"
                                                                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                                                    fill="currentColor">
                                                                    <path fill-rule="evenodd"
                                                                        d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                                                                        clip-rule="evenodd" />
                                                                </svg>
                                                                Download Complete Data
                                                            </div>
                                                        </template>
                                                        <template x-if="downloading">
                                                            <div class="flex items-center">
                                                                <svg class="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                                                                    xmlns="http://www.w3.org/2000/svg" fill="none"
                                                                    viewBox="0 0 24 24">
                                                                    <circle class="opacity-25" cx="12"
                                                                        cy="12" r="10" stroke="currentColor"
                                                                        stroke-width="4"></circle>
                                                                    <path class="opacity-75" fill="currentColor"
                                                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                                                    </path>
                                                                </svg>
                                                                Processing...
                                                            </div>
                                                        </template>
                                                    </button>
                                                </div>

                                                <div class="mt-5 text-center">
                                                    <a href="#"
                                                        class="text-sm text-indigo-600 hover:text-indigo-800 transition duration-150">Need
                                                        help? Contact support</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-10 border-t pt-8 border-border-light dark:border-border-dark">
                        <h2 class="text-2xl font-semibold mb-4 text-text-primary-light dark:text-text-primary-dark">About Indian Postal PIN Codes</h2>
                        <div class="text-text-secondary-light dark:text-text-secondary-dark space-y-4">
                            <p>
                                Postal Index Number (PIN) codes are 6-digit numeric codes that are essential for mail
                                delivery across India.
                                The first digit represents one of the nine postal zones, while the second digit indicates
                                the sub-zone.
                                The remaining digits narrow down to the specific post office.
                            </p>
                            <p>
                                Our comprehensive database covers all postal PIN codes across India, including metropolitan
                                cities,
                                towns, and rural areas. Whether you need pincode information for Delhi, Mumbai, Kolkata,
                                Chennai,
                                Bangalore, Hyderabad, or any other location in India, our tool provides accurate and
                                up-to-date data.
                            </p>
                            <p>
                                This data is particularly useful for e-commerce businesses, logistics companies, market
                                researchers,
                                address verification services, and anyone who needs reliable postal code information for
                                India.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-full lg:w-1/3 px-4">
                <div class="bg-white dark:bg-bg-dark rounded-lg shadow-lg p-6 mt-6">
                    <h2 class="text-2xl font-bold mb-4 text-text-primary-light dark:text-text-primary-dark">Useful Resources</h2>
                    <ul class="space-y-2">
                        <li>
                            <a href="#"
                                class="text-primary-light dark:text-primary-dark hover:text-blue-800 dark:hover:text-blue-400 transition duration-150 ease-in-out">
                                How to Use Pincode Data for Business
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="text-primary-light dark:text-primary-dark hover:text-blue-800 dark:hover:text-blue-400 transition duration-150 ease-in-out">
                                Pincode FAQs
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="text-primary-light dark:text-primary-dark hover:text-blue-800 dark:hover:text-blue-400 transition duration-150 ease-in-out">
                                Indian Postal System Guide
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="text-primary-light dark:text-primary-dark hover:text-blue-800 dark:hover:text-blue-400 transition duration-150 ease-in-out">
                                Contact Support
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="bg-bg-light dark:bg-gray-800 rounded-lg shadow-lg p-6 mt-6">
                    <h2 class="text-2xl font-bold mb-4 text-text-primary-light dark:text-text-primary-dark">Did You Know?</h2>
                    <div class="text-text-secondary-light dark:text-text-secondary-dark leading-relaxed space-y-4">
                        <p>
                            The Indian Postal Service manages over 155,000 post offices across the country, making it the
                            most
                            widely distributed postal system in the world!
                        </p>
                        <p>
                            The PIN code system was introduced in India on August 15, 1972. The first digit of the PIN code
                            represents the region: 1 (North), 2 (West), 3 (West), 4 (North), 5 (North), 6 (East), 7 (East),
                            8 (South), and 9 (APS).
                        </p>
                    </div>
                </div>

                <div class="bg-white dark:bg-bg-dark rounded-lg shadow-lg p-6 mt-6">
                    <h2 class="text-2xl font-bold mb-4 text-text-primary-light dark:text-text-primary-dark">Why Use Our PIN Code Data?</h2>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7">
                                </path>
                            </svg>
                            <span class="text-text-secondary-light dark:text-text-secondary-dark">Regularly updated with the latest information</span>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7">
                                </path>
                            </svg>
                            <span class="text-text-secondary-light dark:text-text-secondary-dark">Complete coverage of all Indian states and territories</span>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7">
                                </path>
                            </svg>
                            <span class="text-text-secondary-light dark:text-text-secondary-dark">Detailed information about each post office</span>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7">
                                </path>
                            </svg>
                            <span class="text-text-secondary-light dark:text-text-secondary-dark">Free download in multiple formats</span>
                        </li>
                    </ul>
                </div>

                <div class="bg-white dark:bg-bg-dark rounded-lg shadow-lg p-6 mt-6">
                    <h2 class="text-2xl font-bold mb-4 text-text-primary-light dark:text-text-primary-dark">Other Available Tools</h2>
                    @php
                        $randomTools = App\Models\Tool::published()->inRandomOrder()->limit(5)->get();
                    @endphp
                    <ul class="space-y-2">
                        @foreach ($randomTools as $tool)
                            <li>
                                <a href="{{ route('tools.show', ['slug' => $tool->slug]) }}"
                                    class="text-blue-600 hover:text-blue-800 transition duration-150 ease-in-out">
                                    {{ $tool->name }}
                                </a>
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>

        <!-- Reviews Section -->
        <div class="mt-8">
            <x-tool-reviews
                :reviews="$reviews"
                :tool="$tool"
                :hasMoreReviews="$hasMoreReviews ?? false"
                :totalReviewsCount="$totalReviewsCount ?? 0"
            />
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function pincodeData() {
            return {
                state: '',
                district: '',
                pincode: '',
                pincodes: [],
                districts: [],
                currentPage: 1,
                totalPages: 1,
                perPage: 15,
                format: 'excel',

                init() {
                    this.loadDistricts();
                },

                loadDistricts() {
                    if (this.state) {
                        fetch(`/get-districts/${this.state}`)
                            .then(response => response.json())
                            .then(data => {
                                this.districts = data;
                            });
                    } else {
                        this.districts = [];
                    }
                },

                async fetchPincodes(page) {
                    if (!this.state) return;

                    const response = await fetch('{{ route('getdownloadabledata.pincodes') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            state: this.state,
                            district: this.district,
                            pincode: this.pincode,
                            page: page
                        })
                    }).then(res => res.json());

                    if (response.pincodes.length > 0) {
                        this.pincodes = response.pincodes;
                        this.currentPage = response.currentPage;
                        this.totalPages = Math.ceil(response.total / response.perPage);
                    } else {
                        this.pincodes = [];
                    }
                },

                async downloadFile() {
                    const response = await fetch('{{ route('pincodes.downloadNow') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            format: this.format
                        })
                    });

                    const blob = await response.blob();
                    const filename = response.headers.get('Content-Disposition')
                        ?.split('filename=')[1]
                        ?.replace(/['"]/g, '') || `indian-pincodes.${this.format === 'excel' ? 'xlsx' : 'pdf'}`;

                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                }
            }
        }
    </script>
@endpush
