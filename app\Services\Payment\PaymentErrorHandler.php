<?php

namespace App\Services\Payment;

use App\Services\Payment\Exceptions\PaymentGatewayException;
use App\Services\Payment\Responses\ErrorResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Notification;
use Exception;
use Throwable;

class PaymentErrorHandler
{
    /**
     * Error rate tracking cache key prefix
     */
    private const ERROR_RATE_PREFIX = 'payment_error_rate:';

    /**
     * Error rate monitoring window (in minutes)
     */
    private const MONITORING_WINDOW = 60;

    /**
     * Critical error threshold (errors per hour)
     */
    private const CRITICAL_THRESHOLD = 10;

    /**
     * Warning error threshold (errors per hour)
     */
    private const WARNING_THRESHOLD = 5;

    /**
     * Handle payment gateway exception
     */
    public function handleException(Throwable $exception, array $context = []): ErrorResponse
    {
        // Convert to PaymentGatewayException if needed
        if (!$exception instanceof PaymentGatewayException) {
            $exception = $this->convertToPaymentException($exception, $context);
        }

        // Create error response
        $errorResponse = ErrorResponse::fromException($exception);

        // Add additional context
        $errorResponse->withContext(array_merge($context, [
            'user_id' => auth()->id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'request_id' => request()->header('X-Request-ID', uniqid())
        ]));

        // Log the error
        $this->logError($exception, $errorResponse);

        // Track error rates
        $this->trackErrorRate($exception->getGatewayName(), $exception->getSeverity());

        // Send notifications for critical errors
        if ($exception->getSeverity() === 'critical') {
            $this->notifyCriticalError($exception, $errorResponse);
        }

        // Check for error rate alerts
        $this->checkErrorRateAlerts($exception->getGatewayName());

        return $errorResponse;
    }

    /**
     * Handle generic exception and convert to payment exception
     */
    public function handleGenericException(Exception $exception, string $gatewayName = '', array $context = []): ErrorResponse
    {
        $paymentException = $this->convertToPaymentException($exception, $context, $gatewayName);
        return $this->handleException($paymentException, $context);
    }

    /**
     * Convert generic exception to PaymentGatewayException
     */
    private function convertToPaymentException(Throwable $exception, array $context = [], string $gatewayName = ''): PaymentGatewayException
    {
        $message = $exception->getMessage();
        $gatewayCode = $this->determineGatewayCode($exception);

        // Determine gateway name from context if not provided
        if (empty($gatewayName)) {
            $gatewayName = $context['gateway_name'] ?? 'unknown';
        }

        return new PaymentGatewayException(
            $message,
            $gatewayName,
            $gatewayCode,
            $message,
            array_merge($context, [
                'original_exception' => get_class($exception),
                'original_code' => $exception->getCode(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine()
            ])
        );
    }

    /**
     * Determine gateway error code from exception
     */
    private function determineGatewayCode(Throwable $exception): string
    {
        $message = strtolower($exception->getMessage());

        // Network-related errors
        if (str_contains($message, 'curl') || str_contains($message, 'connection') || str_contains($message, 'timeout')) {
            return 'NETWORK_ERROR';
        }

        // Authentication errors
        if (str_contains($message, 'unauthorized') || str_contains($message, 'authentication') || str_contains($message, 'api key')) {
            return 'AUTHENTICATION_ERROR';
        }

        // Validation errors
        if (str_contains($message, 'validation') || str_contains($message, 'invalid') || str_contains($message, 'required')) {
            return 'VALIDATION_ERROR';
        }

        // Rate limiting
        if (str_contains($message, 'rate limit') || str_contains($message, 'too many requests')) {
            return 'RATE_LIMIT_EXCEEDED';
        }

        // Server errors
        if (str_contains($message, 'server error') || str_contains($message, '500') || str_contains($message, 'internal error')) {
            return 'SERVER_ERROR';
        }

        return 'UNKNOWN_ERROR';
    }

    /**
     * Log error with appropriate level and context
     */
    private function logError(PaymentGatewayException $exception, ErrorResponse $errorResponse): void
    {
        $logContext = [
            'gateway_name' => $exception->getGatewayName(),
            'gateway_code' => $exception->getGatewayCode(),
            'user_id' => auth()->id(),
            'error_response' => $errorResponse->toArray(),
            'stack_trace' => $exception->getTraceAsString()
        ];

        match ($exception->getSeverity()) {
            'critical' => Log::critical($exception->getMessage(), $logContext),
            'error' => Log::error($exception->getMessage(), $logContext),
            'warning' => Log::warning($exception->getMessage(), $logContext),
            'info' => Log::info($exception->getMessage(), $logContext),
            default => Log::error($exception->getMessage(), $logContext)
        };
    }

    /**
     * Track error rates for monitoring
     */
    private function trackErrorRate(string $gatewayName, string $severity): void
    {
        $key = self::ERROR_RATE_PREFIX . $gatewayName . ':' . $severity;
        $currentHour = now()->format('Y-m-d-H');
        $hourlyKey = $key . ':' . $currentHour;

        // Increment error count for current hour
        Cache::increment($hourlyKey, 1);
        Cache::expire($hourlyKey, 3600); // Expire after 1 hour

        // Track total errors
        Cache::increment($key . ':total', 1);
    }

    /**
     * Check error rate alerts
     */
    private function checkErrorRateAlerts(string $gatewayName): void
    {
        $currentHour = now()->format('Y-m-d-H');
        $criticalKey = self::ERROR_RATE_PREFIX . $gatewayName . ':critical:' . $currentHour;
        $errorKey = self::ERROR_RATE_PREFIX . $gatewayName . ':error:' . $currentHour;

        $criticalCount = Cache::get($criticalKey, 0);
        $errorCount = Cache::get($errorKey, 0);
        $totalErrors = $criticalCount + $errorCount;

        // Check if we need to send alerts
        if ($criticalCount >= 1 && !$this->hasRecentAlert($gatewayName, 'critical')) {
            $this->sendErrorRateAlert($gatewayName, 'critical', $criticalCount);
            $this->markAlertSent($gatewayName, 'critical');
        } elseif ($totalErrors >= self::CRITICAL_THRESHOLD && !$this->hasRecentAlert($gatewayName, 'high_error_rate')) {
            $this->sendErrorRateAlert($gatewayName, 'high_error_rate', $totalErrors);
            $this->markAlertSent($gatewayName, 'high_error_rate');
        } elseif ($totalErrors >= self::WARNING_THRESHOLD && !$this->hasRecentAlert($gatewayName, 'warning_error_rate')) {
            $this->sendErrorRateAlert($gatewayName, 'warning_error_rate', $totalErrors);
            $this->markAlertSent($gatewayName, 'warning_error_rate');
        }
    }

    /**
     * Send critical error notification
     */
    private function notifyCriticalError(PaymentGatewayException $exception, ErrorResponse $errorResponse): void
    {
        $alertData = [
            'gateway_name' => $exception->getGatewayName(),
            'error_code' => $exception->getGatewayCode(),
            'message' => $exception->getMessage(),
            'user_message' => $exception->getUserMessage(),
            'context' => $exception->getContext(),
            'timestamp' => now()->toISOString(),
            'severity' => 'critical'
        ];

        // Log to admin alert channel
        Log::channel('admin-alerts')->critical('Critical payment gateway error', $alertData);

        // In a real implementation, you would send notifications here
        // Examples: Email, Slack, SMS, Push notifications, etc.
        $this->sendAdminNotification('critical_payment_error', $alertData);
    }

    /**
     * Send error rate alert
     */
    private function sendErrorRateAlert(string $gatewayName, string $alertType, int $errorCount): void
    {
        $alertData = [
            'gateway_name' => $gatewayName,
            'alert_type' => $alertType,
            'error_count' => $errorCount,
            'time_window' => '1 hour',
            'timestamp' => now()->toISOString()
        ];

        Log::channel('admin-alerts')->warning("Payment gateway error rate alert: {$alertType}", $alertData);
        $this->sendAdminNotification('error_rate_alert', $alertData);
    }

    /**
     * Check if alert was recently sent
     */
    private function hasRecentAlert(string $gatewayName, string $alertType): bool
    {
        $key = "payment_alert:{$gatewayName}:{$alertType}";
        return Cache::has($key);
    }

    /**
     * Mark alert as sent
     */
    private function markAlertSent(string $gatewayName, string $alertType): void
    {
        $key = "payment_alert:{$gatewayName}:{$alertType}";
        Cache::put($key, true, 3600); // Don't send same alert for 1 hour
    }

    /**
     * Send admin notification
     */
    private function sendAdminNotification(string $type, array $data): void
    {
        // In a real implementation, you would implement actual notification sending
        // For now, we'll just log it
        Log::channel('admin-notifications')->info("Admin notification: {$type}", $data);

        // Example implementations:
        // - Send email to administrators
        // - Send Slack message
        // - Send SMS for critical errors
        // - Push notification to admin mobile app
        // - Create admin dashboard alert
    }

    /**
     * Get error statistics for a gateway
     */
    public function getErrorStatistics(string $gatewayName, int $hours = 24): array
    {
        $stats = [
            'gateway_name' => $gatewayName,
            'time_window_hours' => $hours,
            'total_errors' => 0,
            'by_severity' => [
                'critical' => 0,
                'error' => 0,
                'warning' => 0,
                'info' => 0
            ],
            'by_hour' => [],
            'error_rate' => 0,
            'most_common_errors' => []
        ];

        $severities = ['critical', 'error', 'warning', 'info'];
        $now = now();

        for ($i = 0; $i < $hours; $i++) {
            $hour = $now->copy()->subHours($i)->format('Y-m-d-H');
            $hourStats = ['hour' => $hour, 'total' => 0];

            foreach ($severities as $severity) {
                $key = self::ERROR_RATE_PREFIX . $gatewayName . ':' . $severity . ':' . $hour;
                $count = Cache::get($key, 0);
                $stats['by_severity'][$severity] += $count;
                $stats['total_errors'] += $count;
                $hourStats[$severity] = $count;
                $hourStats['total'] += $count;
            }

            $stats['by_hour'][] = $hourStats;
        }

        // Calculate error rate (errors per hour)
        $stats['error_rate'] = $hours > 0 ? round($stats['total_errors'] / $hours, 2) : 0;

        return $stats;
    }

    /**
     * Clear error statistics for a gateway
     */
    public function clearErrorStatistics(string $gatewayName): void
    {
        $pattern = self::ERROR_RATE_PREFIX . $gatewayName . ':*';
        
        // In a real implementation, you would use Redis SCAN or similar
        // For now, we'll clear known keys
        $severities = ['critical', 'error', 'warning', 'info'];
        $now = now();

        for ($i = 0; $i < 168; $i++) { // Clear last week
            $hour = $now->copy()->subHours($i)->format('Y-m-d-H');
            foreach ($severities as $severity) {
                $key = self::ERROR_RATE_PREFIX . $gatewayName . ':' . $severity . ':' . $hour;
                Cache::forget($key);
            }
        }

        Cache::forget(self::ERROR_RATE_PREFIX . $gatewayName . ':total');
    }

    /**
     * Get recovery suggestions for an error
     */
    public function getRecoverySuggestions(PaymentGatewayException $exception): array
    {
        $suggestions = $exception->getRecoveryOptions();

        // Add context-specific suggestions
        if ($exception->isNetworkError()) {
            $suggestions = array_merge($suggestions, [
                'Check internet connectivity',
                'Try again in a few minutes',
                'Contact support if issue persists'
            ]);
        }

        if ($exception->isConfigurationError()) {
            $suggestions = array_merge($suggestions, [
                'Contact system administrator',
                'Check gateway configuration',
                'Verify API credentials'
            ]);
        }

        if ($exception->isUserInputError()) {
            $suggestions = array_merge($suggestions, [
                'Verify payment information',
                'Try a different payment method',
                'Contact your bank if card issues persist'
            ]);
        }

        return array_unique($suggestions);
    }
}