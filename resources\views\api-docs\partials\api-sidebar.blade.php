<div class="hidden lg:block w-64 flex-shrink-0">
    <div
        class="sticky top-24 bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 border border-slate-200 dark:border-slate-700">
        <h3 class="text-lg font-bold text-slate-800 dark:text-white mb-4">Documentation</h3>
        <nav class="space-y-1">
            <a href="#introduction"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Introduction</a>
            <a href="#base-url"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Base
                URL</a>
            <a href="#authentication"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Authentication</a>
            <a href="#rate-limiting"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Rate
                Limiting</a>
            <a href="#endpoints"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Endpoints</a>
            <a href="#error-handling"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Error
                Handling</a>
            <a href="#changelog"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Changelog</a>
            <a href="#support"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Support</a>
            <a href="#demo"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Live
                Demo</a>
            <a href="#postman-test"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">
                Postman Test
            </a>
            <a href="#best-practices"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Best
                Practices</a>
        </nav>
    </div>
</div>

<!-- Mobile Sidebar Toggle -->
<div class="lg:hidden sticky top-4 z-10">
    <button id="sidebarToggle"
        class="w-full flex items-center justify-between bg-white dark:bg-slate-800 p-4 rounded-lg shadow-md border border-slate-200 dark:border-slate-700">
        <span class="font-medium text-slate-800 dark:text-white">Documentation Menu</span>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-slate-500" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
    </button>
    <div id="mobileSidebar"
        class="hidden mt-2 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-slate-200 dark:border-slate-700 p-4">
        <nav class="space-y-1">
            <a href="#introduction"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Introduction</a>
            <a href="#base-url"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Base
                URL</a>
            <a href="#authentication"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Authentication</a>
            <a href="#rate-limiting"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Rate
                Limiting</a>
            <a href="#endpoints"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Endpoints</a>
            <a href="#error-handling"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Error
                Handling</a>
            <a href="#changelog"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Changelog</a>
            <a href="#support"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Support</a>
            <a href="#demo"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Live
                Demo</a>
            <a href="#postman-test"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">
                Postman Test
            </a>
            <a href="#best-practices"
                class="block py-2 px-3 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white transition">Best
                Practices</a>
        </nav>
    </div>
</div>
