<?php

use App\Models\District;
use App\Models\State;
use App\Models\PinCode;
use App\Models\PostOffice;

test('district can be created', function () {
    $district = District::factory()->create([
        'name' => 'Test District',
        'alt_name' => 'Test Alt Name',
        'official_site' => 'https://test-district.gov'
    ]);

    expect($district)->toBeInstanceOf(District::class)
        ->and($district->name)->toBe('Test District')
        ->and($district->alt_name)->toBe('Test Alt Name')
        ->and($district->official_site)->toBe('https://test-district.gov');
});

test('district has state relationship', function () {
    $state = State::factory()->create();
    $district = District::factory()->create([
        'state_id' => $state->id
    ]);

    expect($district->state)->toBeInstanceOf(State::class)
        ->and($district->state->id)->toBe($state->id);
});

test('district has post offices relationship', function () {
    $district = District::factory()->create();
    $postOffice = PostOffice::factory()->create([
        'district_id' => $district->id
    ]);

    expect($district->postOffices)->toHaveCount(1)
        ->and($district->postOffices->first())->toBeInstanceOf(PostOffice::class);
});

test('district has pincodes relationship', function () {
    $district = District::factory()->create([
        'name' => 'Test District'
    ]);
    $pincode = PinCode::factory()->create([
        'district' => 'Test District'
    ]);

    expect($district->pincodes)->toHaveCount(1)
        ->and($district->pincodes->first())->toBeInstanceOf(PinCode::class);
});

test('district has correct table name', function () {
    $district = new District();
    
    expect($district->getTable())->toBe('pin_districts');
});

test('district has correct fillable attributes', function () {
    $district = new District();
    $fillable = $district->getFillable();
    
    expect($fillable)->toContain('name')
        ->toContain('state_id')
        ->toContain('alt_name')
        ->toContain('official_site');
});

test('district returns default featured image url', function () {
    $district = District::factory()->create();
    
    expect($district->featured_image_url)->toBe(asset('images/default-district.jpg'));
});