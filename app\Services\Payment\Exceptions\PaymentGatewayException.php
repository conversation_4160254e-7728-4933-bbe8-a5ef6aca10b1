<?php

namespace App\Services\Payment\Exceptions;

use Exception;
use Throwable;

class PaymentGatewayException extends Exception
{
    protected string $gatewayName;
    protected ?string $gatewayCode;
    protected ?string $gatewayMessage;
    protected array $context;
    protected ?string $userMessage;
    protected array $recoveryOptions;

    public function __construct(
        string $message,
        string $gatewayName = '',
        ?string $gatewayCode = null,
        ?string $gatewayMessage = null,
        array $context = [],
        ?string $userMessage = null,
        array $recoveryOptions = [],
        int $code = 0,
        ?Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        
        $this->gatewayName = $gatewayName;
        $this->gatewayCode = $gatewayCode;
        $this->gatewayMessage = $gatewayMessage;
        $this->context = $context;
        $this->userMessage = $userMessage ?? $this->generateUserFriendlyMessage();
        $this->recoveryOptions = $recoveryOptions;
    }

    /**
     * Get the gateway name
     */
    public function getGatewayName(): string
    {
        return $this->gatewayName;
    }

    /**
     * Get the gateway-specific error code
     */
    public function getGatewayCode(): ?string
    {
        return $this->gatewayCode;
    }

    /**
     * Get the gateway-specific error message
     */
    public function getGatewayMessage(): ?string
    {
        return $this->gatewayMessage;
    }

    /**
     * Get additional context information
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Get user-friendly error message
     */
    public function getUserMessage(): string
    {
        return $this->userMessage;
    }

    /**
     * Get recovery options for the user
     */
    public function getRecoveryOptions(): array
    {
        return $this->recoveryOptions;
    }

    /**
     * Check if this is a retryable error
     */
    public function isRetryable(): bool
    {
        $retryableCodes = [
            'NETWORK_ERROR',
            'TIMEOUT',
            'RATE_LIMIT_EXCEEDED',
            'TEMPORARY_FAILURE',
            'SERVICE_UNAVAILABLE',
            'INTERNAL_ERROR'
        ];

        return in_array($this->gatewayCode, $retryableCodes) || 
               $this->isNetworkError() || 
               $this->isTemporaryError();
    }

    /**
     * Check if this is a network-related error
     */
    public function isNetworkError(): bool
    {
        $networkKeywords = ['network', 'connection', 'timeout', 'unreachable', 'dns'];
        $message = strtolower($this->getMessage());
        
        foreach ($networkKeywords as $keyword) {
            if (str_contains($message, $keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if this is a temporary error
     */
    public function isTemporaryError(): bool
    {
        $temporaryKeywords = ['temporary', 'try again', 'retry', 'unavailable', 'maintenance'];
        $message = strtolower($this->getMessage());
        
        foreach ($temporaryKeywords as $keyword) {
            if (str_contains($message, $keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if this is a configuration error
     */
    public function isConfigurationError(): bool
    {
        $configKeywords = ['configuration', 'credential', 'api key', 'secret', 'invalid key'];
        $message = strtolower($this->getMessage());
        
        foreach ($configKeywords as $keyword) {
            if (str_contains($message, $keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if this is a user input error
     */
    public function isUserInputError(): bool
    {
        $userInputCodes = [
            'INVALID_AMOUNT',
            'INVALID_CURRENCY',
            'CARD_DECLINED',
            'INSUFFICIENT_FUNDS',
            'INVALID_CARD',
            'EXPIRED_CARD'
        ];

        return in_array($this->gatewayCode, $userInputCodes);
    }

    /**
     * Get error severity level
     */
    public function getSeverity(): string
    {
        if ($this->isConfigurationError()) {
            return 'critical';
        }

        if ($this->isUserInputError()) {
            return 'info';
        }

        if ($this->isRetryable()) {
            return 'warning';
        }

        return 'error';
    }

    /**
     * Convert to array for logging
     */
    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'gateway_name' => $this->gatewayName,
            'gateway_code' => $this->gatewayCode,
            'gateway_message' => $this->gatewayMessage,
            'user_message' => $this->userMessage,
            'context' => $this->context,
            'recovery_options' => $this->recoveryOptions,
            'is_retryable' => $this->isRetryable(),
            'severity' => $this->getSeverity(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'trace' => $this->getTraceAsString()
        ];
    }

    /**
     * Generate user-friendly error message
     */
    private function generateUserFriendlyMessage(): string
    {
        if ($this->isUserInputError()) {
            return match ($this->gatewayCode) {
                'CARD_DECLINED' => 'Your card was declined. Please try a different payment method or contact your bank.',
                'INSUFFICIENT_FUNDS' => 'Insufficient funds in your account. Please check your balance and try again.',
                'INVALID_CARD' => 'The card information provided is invalid. Please check and try again.',
                'EXPIRED_CARD' => 'Your card has expired. Please use a different card.',
                'INVALID_AMOUNT' => 'The payment amount is invalid. Please check and try again.',
                'INVALID_CURRENCY' => 'The selected currency is not supported. Please choose a different currency.',
                default => 'There was an issue with your payment information. Please check and try again.'
            };
        }

        if ($this->isNetworkError() || $this->isTemporaryError()) {
            return 'We\'re experiencing temporary connectivity issues. Please try again in a few moments.';
        }

        if ($this->isConfigurationError()) {
            return 'We\'re experiencing technical difficulties. Our team has been notified and is working to resolve this issue.';
        }

        return 'An unexpected error occurred during payment processing. Please try again or contact support if the issue persists.';
    }

    /**
     * Create a network error exception
     */
    public static function networkError(string $gatewayName, string $details = '', array $context = []): self
    {
        return new self(
            "Network error communicating with {$gatewayName}: {$details}",
            $gatewayName,
            'NETWORK_ERROR',
            $details,
            $context,
            null,
            ['Try again in a few moments', 'Check your internet connection', 'Contact support if issue persists']
        );
    }

    /**
     * Create a configuration error exception
     */
    public static function configurationError(string $gatewayName, string $details = '', array $context = []): self
    {
        return new self(
            "Configuration error for {$gatewayName}: {$details}",
            $gatewayName,
            'CONFIGURATION_ERROR',
            $details,
            $context,
            null,
            ['Contact administrator', 'Check gateway configuration']
        );
    }

    /**
     * Create a validation error exception
     */
    public static function validationError(string $gatewayName, string $details = '', array $context = []): self
    {
        return new self(
            "Validation error for {$gatewayName}: {$details}",
            $gatewayName,
            'VALIDATION_ERROR',
            $details,
            $context,
            null,
            ['Check payment information', 'Verify amount and currency', 'Try a different payment method']
        );
    }

    /**
     * Create a payment declined exception
     */
    public static function paymentDeclined(string $gatewayName, string $reason = '', array $context = []): self
    {
        return new self(
            "Payment declined by {$gatewayName}: {$reason}",
            $gatewayName,
            'PAYMENT_DECLINED',
            $reason,
            $context,
            null,
            ['Try a different payment method', 'Contact your bank', 'Check account balance']
        );
    }

    /**
     * Create a timeout error exception
     */
    public static function timeoutError(string $gatewayName, string $details = '', array $context = []): self
    {
        return new self(
            "Timeout error with {$gatewayName}: {$details}",
            $gatewayName,
            'TIMEOUT',
            $details,
            $context,
            null,
            ['Try again', 'Check internet connection', 'Contact support if issue persists']
        );
    }

    /**
     * Create a rate limit exceeded exception
     */
    public static function rateLimitExceeded(string $gatewayName, string $details = '', array $context = []): self
    {
        return new self(
            "Rate limit exceeded for {$gatewayName}: {$details}",
            $gatewayName,
            'RATE_LIMIT_EXCEEDED',
            $details,
            $context,
            null,
            ['Wait a few minutes and try again', 'Contact support if urgent']
        );
    }
}