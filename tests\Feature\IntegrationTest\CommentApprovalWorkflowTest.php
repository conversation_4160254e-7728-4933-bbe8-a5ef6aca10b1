<?php

use App\Models\BlogPost;
use App\Models\BlogPostCategory;
use App\Models\Comment;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;

uses(RefreshDatabase::class);

describe('Comment Approval Workflow', function () {
    beforeEach(function () {
        // Create test data
        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->user = User::factory()->create(['role' => 'user']);
        $this->category = BlogPostCategory::factory()->create();
        $this->post = BlogPost::factory()->create([
            'user_id' => $this->user->id,
            'blog_post_category_id' => $this->category->id,
            'is_published' => true,
            'published_at' => now(),
        ]);
    });

    it('completes the full comment approval workflow', function () {
        // Step 1: User submits a comment
        $commentContent = 'This is a test comment for the blog post.';
        
        $response = $this->actingAs($this->user)
            ->post(route('comments.store', $this->post), [
                'content' => $commentContent,
            ]);

        $response->assertRedirect()
            ->assertSessionHas('success', 'Comment added successfully');

        // Verify comment was created with pending status
        $this->assertDatabaseHas('comments', [
            'content' => $commentContent,
            'blog_post_id' => $this->post->id,
            'user_id' => $this->user->id,
            'is_approved' => false,
            'rejected_reason' => null,
            'moderated_at' => null,
            'moderated_by' => null,
        ]);

        $comment = Comment::where('content', $commentContent)->first();
        expect($comment)->not->toBeNull()
            ->and($comment->is_approved)->toBeFalse()
            ->and($comment->author->id)->toBe($this->user->id);

        // Step 2: Verify comment is NOT visible on frontend (pending status)
        $frontendResponse = $this->get(route('blog.show', $this->post->slug));
        $frontendResponse->assertStatus(200)
            ->assertViewIs('blog-post.show')
            ->assertDontSee($commentContent); // Comment should not be visible

        // Step 3: Admin views pending comments
        $adminCommentsResponse = $this->actingAs($this->admin)
            ->get(route('admin.comments.index', ['status' => 'pending']));

        $adminCommentsResponse->assertStatus(200)
            ->assertViewIs('admin.comments.index')
            ->assertViewHas('comments')
            ->assertSee($commentContent);

        // Step 4: Admin approves the comment
        $approveResponse = $this->actingAs($this->admin)
            ->patch(route('admin.comments.approve', $comment), [
                '_token' => csrf_token(),
                '_method' => 'PATCH'
            ]);

        $approveResponse->assertRedirect(route('admin.comments.index'))
            ->assertSessionHas('success', 'Comment approved successfully.');

        // Verify comment was approved
        $comment->refresh();
        expect($comment->is_approved)->toBeTrue()
            ->and($comment->rejected_reason)->toBeNull()
            ->and($comment->moderated_at)->not->toBeNull()
            ->and($comment->moderated_by)->toBe($this->admin->id);

        $this->assertDatabaseHas('comments', [
            'id' => $comment->id,
            'is_approved' => true,
            'rejected_reason' => null,
            'moderated_by' => $this->admin->id,
        ]);

        // Step 5: Verify comment is NOW visible on frontend (approved status)
        $frontendResponseAfterApproval = $this->get(route('blog.show', $this->post->slug));
        $frontendResponseAfterApproval->assertStatus(200)
            ->assertViewIs('blog-post.show')
            ->assertSee($commentContent) // Comment should now be visible
            ->assertSee($this->user->name); // Author name should be visible
    });

    it('handles comment rejection workflow', function () {
        // Step 1: User submits a comment
        $commentContent = 'This comment should be rejected.';
        
        $this->actingAs($this->user)
            ->post(route('comments.store', $this->post), [
                'content' => $commentContent,
            ]);

        $comment = Comment::where('content', $commentContent)->first();

        // Step 2: Admin rejects the comment
        $rejectReason = 'Inappropriate content';
        $rejectResponse = $this->actingAs($this->admin)
            ->patch(route('admin.comments.reject', $comment), [
                'rejected_reason' => $rejectReason,
                '_token' => csrf_token(),
                '_method' => 'PATCH'
            ]);

        $rejectResponse->assertRedirect(route('admin.comments.index'))
            ->assertSessionHas('success', 'Comment rejected successfully.');

        // Verify comment was rejected
        $comment->refresh();
        expect($comment->is_approved)->toBeFalse()
            ->and($comment->rejected_reason)->toBe($rejectReason)
            ->and($comment->moderated_at)->not->toBeNull()
            ->and($comment->moderated_by)->toBe($this->admin->id);

        // Step 3: Verify rejected comment is NOT visible on frontend
        $frontendResponse = $this->get(route('blog.show', $this->post->slug));
        $frontendResponse->assertStatus(200)
            ->assertDontSee($commentContent);
    });

    it('handles nested comments (replies) approval workflow', function () {
        // Step 1: User submits a parent comment
        $parentCommentContent = 'This is a parent comment.';
        
        $this->actingAs($this->user)
            ->post(route('comments.store', $this->post), [
                'content' => $parentCommentContent,
            ]);

        $parentComment = Comment::where('content', $parentCommentContent)->first();

        // Step 2: Admin approves parent comment
        $this->actingAs($this->admin)
            ->patch(route('admin.comments.approve', $parentComment), [
                '_token' => csrf_token(),
                '_method' => 'PATCH'
            ]);

        // Step 3: Another user submits a reply
        $replyUser = User::factory()->create();
        $replyContent = 'This is a reply to the parent comment.';
        
        $this->actingAs($replyUser)
            ->post(route('comments.store', $this->post), [
                'content' => $replyContent,
                'parent_id' => $parentComment->id,
            ]);

        $reply = Comment::where('content', $replyContent)->first();
        expect($reply->parent_id)->toBe($parentComment->id)
            ->and($reply->is_approved)->toBeFalse();

        // Step 4: Verify only parent comment is visible (reply is pending)
        $frontendResponse = $this->get(route('blog.show', $this->post->slug));
        $frontendResponse->assertStatus(200)
            ->assertSee($parentCommentContent)
            ->assertDontSee($replyContent);

        // Step 5: Admin approves the reply
        $this->actingAs($this->admin)
            ->patch(route('admin.comments.approve', $reply), [
                '_token' => csrf_token(),
                '_method' => 'PATCH'
            ]);

        // Step 6: Verify both parent and reply are now visible
        $frontendResponseAfterApproval = $this->get(route('blog.show', $this->post->slug));
        $frontendResponseAfterApproval->assertStatus(200)
            ->assertSee($parentCommentContent)
            ->assertSee($replyContent);
    });

    it('filters comments correctly in admin panel', function () {
        // Create comments with different statuses
        $approvedComment = Comment::factory()->create([
            'content' => 'Approved comment',
            'blog_post_id' => $this->post->id,
            'user_id' => $this->user->id,
            'is_approved' => true,
            'moderated_by' => $this->admin->id,
            'moderated_at' => now(),
        ]);

        $pendingComment = Comment::factory()->create([
            'content' => 'Pending comment',
            'blog_post_id' => $this->post->id,
            'user_id' => $this->user->id,
            'is_approved' => false,
            'rejected_reason' => null,
        ]);

        $rejectedComment = Comment::factory()->create([
            'content' => 'Rejected comment',
            'blog_post_id' => $this->post->id,
            'user_id' => $this->user->id,
            'is_approved' => false,
            'rejected_reason' => 'Spam',
            'moderated_by' => $this->admin->id,
            'moderated_at' => now(),
        ]);

        // Test approved filter
        $this->actingAs($this->admin)
            ->get(route('admin.comments.index', ['status' => 'approved']))
            ->assertStatus(200)
            ->assertSee('Approved comment')
            ->assertDontSee('Pending comment')
            ->assertDontSee('Rejected comment');

        // Test pending filter
        $this->actingAs($this->admin)
            ->get(route('admin.comments.index', ['status' => 'pending']))
            ->assertStatus(200)
            ->assertDontSee('Approved comment')
            ->assertSee('Pending comment')
            ->assertDontSee('Rejected comment');

        // Test rejected filter
        $this->actingAs($this->admin)
            ->get(route('admin.comments.index', ['status' => 'rejected']))
            ->assertStatus(200)
            ->assertDontSee('Approved comment')
            ->assertDontSee('Pending comment')
            ->assertSee('Rejected comment');
    });

    it('validates comment submission requirements', function () {
        // Test empty content
        $response = $this->actingAs($this->user)
            ->post(route('comments.store', $this->post), [
                'content' => '',
            ]);

        $response->assertSessionHasErrors('content');

        // Test content too long
        $longContent = str_repeat('a', 301); // Exceeds 300 character limit
        $response = $this->actingAs($this->user)
            ->post(route('comments.store', $this->post), [
                'content' => $longContent,
            ]);

        $response->assertSessionHasErrors('content');

        // Test script injection prevention
        $scriptContent = 'Normal content <script>alert("xss")</script>';
        $response = $this->actingAs($this->user)
            ->post(route('comments.store', $this->post), [
                'content' => $scriptContent,
            ]);

        $response->assertSessionHasErrors('content');
    });

    it('requires authentication for comment submission', function () {
        // Test unauthenticated user cannot submit comment
        $response = $this->post(route('comments.store', $this->post), [
            'content' => 'Test comment',
        ]);

        $response->assertRedirect(); // Should redirect to login
    });

    it('requires admin privileges for comment moderation', function () {
        // Create a comment
        $comment = Comment::factory()->create([
            'blog_post_id' => $this->post->id,
            'user_id' => $this->user->id,
            'is_approved' => false,
        ]);

        // Test regular user cannot approve comments
        $response = $this->actingAs($this->user)
            ->patch(route('admin.comments.approve', $comment), [
                '_token' => csrf_token(),
                '_method' => 'PATCH'
            ]);

        $response->assertStatus(403); // Forbidden

        // Test admin can approve comments
        $response = $this->actingAs($this->admin)
            ->patch(route('admin.comments.approve', $comment), [
                '_token' => csrf_token(),
                '_method' => 'PATCH'
            ]);

        $response->assertRedirect(); // Success
    });

    it('handles comment deletion workflow', function () {
        // Create and approve a comment
        $comment = Comment::factory()->create([
            'blog_post_id' => $this->post->id,
            'user_id' => $this->user->id,
            'is_approved' => true,
        ]);

        // Verify comment is visible
        $this->get(route('blog.show', $this->post->slug))
            ->assertSee($comment->content);

        // Admin deletes the comment
        $this->actingAs($this->admin)
            ->delete(route('admin.comments.destroy', $comment))
            ->assertRedirect(route('admin.comments.index'))
            ->assertSessionHas('success', 'Comment deleted successfully.');

        // Verify comment is no longer visible
        $this->get(route('blog.show', $this->post->slug))
            ->assertDontSee($comment->content);

        // Verify comment is deleted from database
        $this->assertDatabaseMissing('comments', ['id' => $comment->id]);
    });

    it('tracks moderation history correctly', function () {
        // Create a comment
        $comment = Comment::factory()->create([
            'blog_post_id' => $this->post->id,
            'user_id' => $this->user->id,
            'is_approved' => false,
        ]);

        // Admin rejects the comment
        $rejectReason = 'Inappropriate content';
        $this->actingAs($this->admin)
            ->patch(route('admin.comments.reject', $comment), [
                'rejected_reason' => $rejectReason,
                '_token' => csrf_token(),
                '_method' => 'PATCH'
            ]);

        $comment->refresh();
        expect($comment->moderated_by)->toBe($this->admin->id)
            ->and($comment->moderated_at)->not->toBeNull()
            ->and($comment->rejected_reason)->toBe($rejectReason);

        // Admin later approves the same comment
        $this->actingAs($this->admin)
            ->patch(route('admin.comments.approve', $comment), [
                '_token' => csrf_token(),
                '_method' => 'PATCH'
            ]);

        $comment->refresh();
        expect($comment->is_approved)->toBeTrue()
            ->and($comment->rejected_reason)->toBeNull()
            ->and($comment->moderated_by)->toBe($this->admin->id)
            ->and($comment->moderated_at)->not->toBeNull();
    });
}); 