<?php

namespace App\Services\Payment;

use App\Models\Order;
use App\Models\Payment;
use App\Models\PaymentGateway;
use App\Models\WebhookLog;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

abstract class BasePaymentGatewayService implements PaymentGatewayServiceInterface
{
    protected PaymentGateway $gateway;
    protected array $config;

    public function __construct(PaymentGateway $gateway)
    {
        $this->gateway = $gateway;
        $this->config = $gateway->getCredentials();
        
        if (!$gateway->isConfigured()) {
            throw new PaymentGatewayException("Gateway {$gateway->name} is not properly configured");
        }
    }

    /**
     * Get the gateway configuration.
     */
    public function getGatewayConfig(): array
    {
        return $this->config;
    }

    /**
     * Get supported currencies for this gateway.
     */
    public function getSupportedCurrencies(): array
    {
        return $this->gateway->supported_currencies;
    }

    /**
     * Calculate gateway fee for the given amount.
     * Default implementation returns 0, override in specific gateways.
     */
    public function calculateFee(float $amount, string $currency): float
    {
        return 0.0;
    }

    /**
     * Create a payment record in the database.
     */
    protected function createPaymentRecord(Order $order, array $data = []): Payment
    {
        $paymentData = array_merge([
            'order_id' => $order->id,
            'gateway_id' => $this->gateway->id,
            'amount' => $order->amount,
            'currency' => $order->currency ?? 'USD',
            'payment_status' => Payment::STATUS_PENDING,
            'payment_method' => $this->gateway->name,
            'exchange_rate' => 1.0,
            'gateway_fee' => $this->calculateFee($order->amount, $order->currency ?? 'USD'),
        ], $data);

        $payment = Payment::create($paymentData);
        $payment->calculateNetAmount();

        return $payment;
    }

    /**
     * Update payment record with gateway response.
     */
    protected function updatePaymentRecord(Payment $payment, array $data): Payment
    {
        $payment->update($data);
        return $payment->fresh();
    }

    /**
     * Log webhook event.
     */
    protected function logWebhookEvent(Request $request, string $eventType, ?Payment $payment = null): WebhookLog
    {
        return WebhookLog::create([
            'gateway_id' => $this->gateway->id,
            'payment_id' => $payment?->id,
            'webhook_id' => $request->header('X-Webhook-Id') ?? uniqid(),
            'event_type' => $eventType,
            'payload' => $request->all(),
            'signature' => $request->header('X-Signature') ?? $request->header('X-Razorpay-Signature'),
            'status' => 'pending',
        ]);
    }

    /**
     * Verify webhook signature.
     * Override in specific gateway implementations.
     */
    protected function verifyWebhookSignature(Request $request): bool
    {
        // Default implementation - override in specific gateways
        return true;
    }

    /**
     * Handle successful payment.
     */
    protected function handleSuccessfulPayment(Payment $payment, array $gatewayData = []): void
    {
        $payment->markAsCompleted($gatewayData['payment_id'] ?? null, $gatewayData);
        
        // Activate the order/plan
        if ($payment->order) {
            $payment->order->markAsCompleted();
        }

        Log::info("Payment completed successfully", [
            'payment_id' => $payment->id,
            'gateway' => $this->gateway->name,
            'amount' => $payment->amount,
            'currency' => $payment->currency,
        ]);
    }

    /**
     * Handle failed payment.
     */
    protected function handleFailedPayment(Payment $payment, string $reason = null, array $gatewayData = []): void
    {
        $payment->markAsFailed($reason, $gatewayData);

        // Mark the order as failed too
        if ($payment->order) {
            $payment->order->markAsFailed();
        }

        Log::warning("Payment failed", [
            'payment_id' => $payment->id,
            'gateway' => $this->gateway->name,
            'reason' => $reason,
            'amount' => $payment->amount,
            'currency' => $payment->currency,
        ]);
    }

    /**
     * Validate order before processing.
     */
    protected function validateOrder(Order $order): void
    {
        if (!$order) {
            throw new PaymentGatewayException("Order not found");
        }

        if ($order->amount <= 0) {
            throw new PaymentGatewayException("Invalid order amount");
        }

        if (!$this->gateway->supportsCurrency($order->currency ?? 'USD')) {
            throw new PaymentGatewayException("Currency not supported by gateway");
        }
    }

    /**
     * Generate unique payment reference.
     */
    public function generatePaymentReference(Order $order): string
    {
        return $this->gateway->name . '_' . $order->id . '_' . time();
    }

    /**
     * Format amount for gateway (some gateways require cents, others require decimal).
     */
    protected function formatAmountForGateway(float $amount, string $currency): int|float
    {
        // Default implementation returns amount as-is
        // Override in specific gateways if needed (e.g., Razorpay uses paise)
        return $amount;
    }

    /**
     * Parse amount from gateway response.
     */
    protected function parseAmountFromGateway(int|float $amount, string $currency): float
    {
        // Default implementation returns amount as-is
        // Override in specific gateways if needed
        return (float) $amount;
    }

    /**
     * Get gateway-specific error message.
     */
    protected function getGatewayErrorMessage(array $gatewayResponse): string
    {
        // Default implementation
        return $gatewayResponse['error']['description'] ?? 
               $gatewayResponse['message'] ?? 
               'Payment processing failed';
    }

    /**
     * Get gateway-specific error code.
     */
    protected function getGatewayErrorCode(array $gatewayResponse): ?string
    {
        // Default implementation
        return $gatewayResponse['error']['code'] ?? 
               $gatewayResponse['error_code'] ?? 
               null;
    }

    /**
     * Test the gateway connection and credentials.
     * Override in specific gateway implementations.
     */
    public function testConnection(): bool
    {
        // Default implementation - override in specific gateways
        return true;
    }

    /**
     * Log gateway interaction.
     */
    protected function logGatewayInteraction(string $action, array $data, ?string $response = null): void
    {
        Log::info("Gateway interaction", [
            'gateway' => $this->gateway->name,
            'action' => $action,
            'data' => $data,
            'response' => $response,
        ]);
    }

    /**
     * Handle gateway exception.
     */
    protected function handleGatewayException(\Exception $e, string $context = ''): PaymentGatewayException
    {
        Log::error("Gateway exception", [
            'gateway' => $this->gateway->name,
            'context' => $context,
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
        ]);

        return new PaymentGatewayException(
            "Gateway error: " . $e->getMessage(),
            $e->getCode(),
            $e
        );
    }
}