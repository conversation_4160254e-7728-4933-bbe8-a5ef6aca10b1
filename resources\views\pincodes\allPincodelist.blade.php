@extends('layouts.app')

@section('json-ld')
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "BharatPostalInfo - All pincode List",
            "description": "{{ $metaDescription }}",
            "publisher": {
                "@type": "Organization",
                "name": "NSK Multiservices Kosbi"
            },
            "url": "{{ url()->current() }}",
            "datePublished": "2024-07-04",
            "dateModified": "2024-07-04"
        }
    </script>

    @if (!empty($breadcrumbs))
        <script type="application/ld+json">
            {
                "@context": "https://schema.org",
                "@type": "BreadcrumbList",
                "itemListElement": [
                    @foreach ($breadcrumbs as $index => $breadcrumb)
                        {
                            "@type": "ListItem",
                            "position": {{ $index + 1 }},
                            "name": "{{ $breadcrumb['name'] }}",
                            "item": "{{ $breadcrumb['url'] }}"
                        } @if (!$loop->last) , @endif
                    @endforeach
                ]
            }
        </script>
    @endif

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "NSK Multiservices Kosbi",
            "url": "{{ url()->current() }}",
            "logo": "http://fingerprint.nskmultiservices.in/assets/images/general/favicon.png",
            "contactPoint": {
                "@type": "ContactPoint",
                "telephone": "+91-9834754391",
                "contactType": "Customer Service"
            },
            "sameAs": [
                "https://www.linkedin.com/in/nsk-multiservices/",
                "https://www.instagram.com/nskmultiservices/",
                "https://x.com/digi_nsk",
                "https://www.facebook.com/nskmultiservices/"
            ]
        }
    </script>
@endsection

@section('content')
    <x-breadcrumb :segments="$breadcrumbs" :pageTitle="$pageTitle" :metaDescription="$metaDescription" />
    
    <div class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-3">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                    <h2 class="text-2xl font-bold mb-6 text-gray-800 dark:text-gray-200">
                        {{ $pageTitle }}
                    </h2>
                    
                    <!-- Search Box -->
                    <div class="mb-6">
                        <input type="text" id="pincodeSearch" 
                               class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                               placeholder="Search by pincode, name, or district...">
                    </div>

                    <!-- Table with Responsive Design -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    @foreach(['Pincode', 'Post Office', 'District', 'State', 'Details'] as $header)
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            {{ $header }}
                                        </th>
                                    @endforeach
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700" id="pincodeTableBody">
                                @foreach($pincodes as $pincode)
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <a href="{{ url("/pincodes/{$pincode->state}/{$pincode->district}/postal-code/{$pincode->pincode}") }}"
                                               class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                                {{ $pincode->pincode }}
                                            </a>
                                        </td>
                                        <td class="px-6 py-4">
                                            <a href="{{ url("/pincodes/{$pincode->state}/{$pincode->district}/{$pincode->name}") }}"
                                               class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                                {{ $pincode->name }}
                                            </a>
                                        </td>
                                        <td class="px-6 py-4">
                                            <a href="{{ url("/pincodes/{$pincode->state}/{$pincode->district}") }}"
                                               class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                                {{ $pincode->district }}
                                            </a>
                                        </td>
                                        <td class="px-6 py-4">
                                            <a href="{{ url("/pincodes/{$pincode->state}") }}"
                                               class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                                {{ $pincode->state }}
                                            </a>
                                        </td>
                                        <td class="px-6 py-4">
                                            <button onclick="showDetails('{{ $pincode->id }}')"
                                                    class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-md text-sm">
                                                View More
                                            </button>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $pincodes->links() }}
                    </div>
                </div>
            </div>

            <!-- Sticky Sidebar -->
            <div class="lg:col-span-1">
                <div class="sticky top-20">
                    @include('pincodes.partials.sidebar')
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Details -->
    <div id="detailsModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full">
                <div class="p-6" id="modalContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('pincodeSearch');
    const tableBody = document.getElementById('pincodeTableBody');
    const rows = tableBody.getElementsByTagName('tr');

    searchInput.addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        
        Array.from(rows).forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });

    // Close modal when clicking outside
    const modal = document.getElementById('detailsModal');
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.classList.add('hidden');
        }
    });
});

function showDetails(pincodeId) {
    const modal = document.getElementById('detailsModal');
    const content = document.getElementById('modalContent');
    
    // Show loading state
    content.innerHTML = '<div class="text-center">Loading...</div>';
    modal.classList.remove('hidden');

    // Fetch details
    fetch(`/api/pincode/${pincodeId}`)
        .then(response => response.json())
        .then(data => {
            content.innerHTML = `
                <div class="space-y-4">
                    <h3 class="text-lg font-bold">${data.name}</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-600">Circle: ${data.circle}</p>
                            <p class="text-sm text-gray-600">Division: ${data.division}</p>
                            <p class="text-sm text-gray-600">Region: ${data.region}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">District: ${data.district}</p>
                            <p class="text-sm text-gray-600">State: ${data.state}</p>
                            <p class="text-sm text-gray-600">Pincode: ${data.pincode}</p>
                        </div>
                    </div>
                    <button onclick="document.getElementById('detailsModal').classList.add('hidden')"
                            class="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
                        Close
                    </button>
                </div>
            `;
        })
        .catch(error => {
            content.innerHTML = '<div class="text-red-500">Error loading details</div>';
        });
}
</script>
@endpush
