@extends('layouts.app')

@include('layouts.partials.tools-json-ld')

@section('content')
    <x-breadcrumb :segments="$breadcrumbs" :pageTitle="$pageTitle" />
    <div class="container max-w-6xl mx-auto py-8 px-4 bg-bg-light dark:bg-bg-dark transition-colors duration-300">
        <div class="flex flex-wrap -mx-4">
            <div class="w-full lg:w-2/3 px-4">
                <h2 class="text-3xl font-bold mb-6 text-text-primary-light dark:text-text-primary-dark">Shipping Cost
                    Calculator</h2>
                <div class="mb-8">
                    <p class="mb-4 text-text-secondary-light dark:text-text-secondary-dark">Calculating shipping costs can
                        be a daunting task, especially with the various factors
                        that come into play. Our Shipping Cost Calculator is designed to simplify the process for you,
                        allowing you to estimate shipping fees easily based on your package specifications.</p>

                    <h2 class="text-xl font-semibold mb-2 text-text-primary-light dark:text-text-primary-dark">Input
                        Requirements</h2>
                    <p class="mb-4 text-text-secondary-light dark:text-text-secondary-dark">To get started, please provide
                        the following information:</p>

                    <ul class="list-disc pl-5 mb-4 space-y-2 text-text-secondary-light dark:text-text-secondary-dark">
                        <li><strong class="text-text-primary-light dark:text-text-primary-dark">Package Weight:</strong>
                            Enter the weight of your package in kilograms. If you are
                            unsure, weigh it accurately for the best estimate.</li>
                        <li><strong class="text-text-primary-light dark:text-text-primary-dark">Package Dimensions:</strong>
                            Fill in the length, width, and height of the package in
                            centimeters. This helps us calculate the volumetric weight, which is crucial for pricing.</li>
                        <li><strong class="text-text-primary-light dark:text-text-primary-dark">Courier Service:</strong>
                            Choose your preferred courier service from the dropdown menu.
                            Each service has different rates, and the calculator will apply the appropriate cost based on
                            your selection.</li>
                    </ul>
                </div>
                <div
                    class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-border-light dark:border-border-dark mb-8 transition-colors duration-300">
                    <div
                        class="bg-gray-50 dark:bg-slate-700 p-4 rounded-t-lg border-b border-border-light dark:border-border-dark transition-colors duration-300">
                        <h2 class="text-xl font-semibold mb-2 text-text-primary-light dark:text-text-primary-dark">Get
                            Started Now!</h2>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark">Fill out the form below to find
                            out how much it will cost to ship your package. Whether you're
                            sending a gift, making a sale, or shipping business items, knowing your shipping costs in
                            advance can help you plan better.</p>
                    </div>
                    <div class="p-6">
                        <form id="shippingForm" class="space-y-4">
                            <div>
                                <label for="weight"
                                    class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Package
                                    Weight
                                    (kg):</label>
                                <input type="number"
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-slate-700 text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors duration-300"
                                    id="weight" required>
                            </div>
                            <div>
                                <label for="length"
                                    class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Length
                                    (cm):</label>
                                <input type="number"
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-slate-700 text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors duration-300"
                                    id="length" required>
                            </div>
                            <div>
                                <label for="width"
                                    class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Width
                                    (cm):</label>
                                <input type="number"
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-slate-700 text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors duration-300"
                                    id="width" required>
                            </div>
                            <div>
                                <label for="height"
                                    class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Height
                                    (cm):</label>
                                <input type="number"
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-slate-700 text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors duration-300"
                                    id="height" required>
                            </div>
                            <div>
                                <label for="courier"
                                    class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Select
                                    Courier
                                    Service:</label>
                                <select
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-slate-700 text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors duration-300"
                                    id="courier" required>
                                    <option value="80">India Post</option>
                                    <option value="100">Blue Dart</option>
                                    <option value="90">DTDC</option>
                                </select>
                            </div>
                            <button type="submit"
                                class="w-full bg-primary-light dark:bg-primary-dark hover:bg-primary-light/90 dark:hover:bg-primary-dark/90 text-white font-bold py-2 px-4 rounded-md focus:outline-none focus:shadow-outline transition-all duration-300 ease-in-out transform hover:scale-[1.02] active:scale-[0.98]">Calculate
                                Cost</button>
                        </form>

                        <div class="mt-6">
                            <div id="result"
                                class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark"></div>
                        </div>

                        <p class="mt-4 text-sm text-text-secondary-light dark:text-text-secondary-dark italic">If you have
                            any questions or need further assistance,
                            feel free to contact our customer support team!</p>
                    </div>
                </div>

                <div class="bg-primary-light/10 dark:bg-primary-dark/10 border-l-4 border-primary-light dark:border-primary-dark text-primary-light dark:text-primary-dark p-4 mb-8 rounded-r-md transition-colors duration-300"
                    role="alert">
                    <h4 class="font-bold mb-2">Disclaimer</h4>
                    <p class="text-text-primary-light dark:text-text-primary-dark">Please note that the estimated shipping
                        costs provided by this tool are for informational purposes
                        only and may vary based on the courier's final pricing, additional surcharges, or changes in rates.
                        Always check with your selected courier for the most accurate rates.</p>
                </div>

                <div
                    class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-border-light dark:border-border-dark mb-8 transition-colors duration-300">
                    <div
                        class="bg-gray-50 dark:bg-slate-700 p-4 rounded-t-lg border-b border-border-light dark:border-border-dark transition-colors duration-300">
                        <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">Understanding
                            Shipping Cost & Calculation</h2>
                    </div>
                    <div class="p-6">
                        <p class="mb-4 text-text-secondary-light dark:text-text-secondary-dark">Shipping costs can vary
                            widely based on several factors:</p>
                        <ul class="list-disc pl-5 mb-4 space-y-2 text-text-secondary-light dark:text-text-secondary-dark">
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">Weight:</strong> Heavier
                                packages generally incur higher shipping costs.</li>
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">Dimensions:</strong>
                                Larger packages may also result in higher costs due to volumetric weight
                                considerations.</li>
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">Courier Rates:</strong>
                                Different courier services have varying pricing structures, which can affect
                                your overall shipping expense.</li>
                        </ul>
                        <p class="mb-4 text-text-secondary-light dark:text-text-secondary-dark">Once you submit your package
                            details, our calculator will:</p>
                        <ul class="list-disc pl-5 space-y-2 text-text-secondary-light dark:text-text-secondary-dark">
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">Calculate Volumetric
                                    Weight:</strong> This accounts for the size of the package, ensuring fair
                                pricing for bulky but lightweight items.</li>
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">Determine the Final
                                    Weight:</strong> The greater of the actual weight and volumetric weight will be
                                used.</li>
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">Calculate the Estimated
                                    Shipping Cost:</strong> The cost is derived based on the final weight and the
                                selected courier service's rates.</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="w-full lg:w-1/3 px-4">
                <div class="sticky top-20">
                    @include('pincodes.partials.sidebar')
                </div>
            </div>
        </div>

        <!-- Reviews Section -->
        <div class="mt-8">
            <x-tool-reviews
                :reviews="$reviews"
                :tool="$tool"
                :hasMoreReviews="$hasMoreReviews ?? false"
                :totalReviewsCount="$totalReviewsCount ?? 0"
            />
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        document.getElementById('shippingForm').addEventListener('submit', function (e) {
            e.preventDefault();

            const weight = parseFloat(document.getElementById('weight').value);
            const length = parseFloat(document.getElementById('length').value);
            const width = parseFloat(document.getElementById('width').value);
            const height = parseFloat(document.getElementById('height').value);
            const courierRate = parseFloat(document.getElementById('courier').value);

            const volumetricWeight = (length * width * height) / 5000;
            const finalWeight = Math.max(weight, volumetricWeight);

            const shippingCost = finalWeight * courierRate;

            document.getElementById('result').innerText = `Estimated Shipping Cost: ₹${shippingCost.toFixed(2)}`;
        });
    </script>
@endpush