<?php

require_once 'vendor/autoload.php';

// Load Laravel application
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\LandingPageSection;
use App\Models\LandingPageContent;

echo "Testing Pricing Section Seeder...\n\n";

// Test if pricing section exists
$pricingSection = LandingPageSection::where('slug', 'pricing')->first();

if ($pricingSection) {
    echo "✅ Pricing section found:\n";
    echo "   - Name: {$pricingSection->name}\n";
    echo "   - Slug: {$pricingSection->slug}\n";
    echo "   - Active: " . ($pricingSection->is_active ? 'Yes' : 'No') . "\n";
    echo "   - Sort Order: {$pricingSection->sort_order}\n\n";
    
    // Get pricing content
    $pricingContent = LandingPageContent::where('section_id', $pricingSection->id)->get();
    
    echo "📋 Pricing content items:\n";
    foreach ($pricingContent as $content) {
        echo "   - {$content->key}: ";
        if ($content->key === 'plans') {
            $plans = is_string($content->value) ? json_decode($content->value, true) : $content->value;
            echo "\n";
            foreach ($plans as $index => $plan) {
                echo "     Plan " . ($index + 1) . ": {$plan['name']} - ";
                if ($plan['price'] === null) {
                    echo "Custom pricing";
                } elseif ($plan['price'] == 0) {
                    echo "Free";
                } else {
                    echo "{$plan['currency']}{$plan['price']}/{$plan['billing_period']}";
                }
                echo " (Popular: " . ($plan['popular'] ? 'Yes' : 'No') . ")\n";
                echo "       Features: " . count($plan['features']) . " features\n";
            }
        } elseif ($content->key === 'features_comparison') {
            $features = is_string($content->value) ? json_decode($content->value, true) : $content->value;
            echo count($features) . " comparison features\n";
        } else {
            echo strlen($content->value) > 50 ? substr($content->value, 0, 50) . '...' : $content->value;
            echo "\n";
        }
    }
    
    echo "\n✅ Pricing section seeded successfully!\n";
    echo "📊 Total content items: " . $pricingContent->count() . "\n";
    
} else {
    echo "❌ Pricing section not found in database!\n";
}

// Test if the section order is correct
echo "\n📋 All sections in order:\n";
$allSections = LandingPageSection::orderBy('sort_order')->get();
foreach ($allSections as $section) {
    echo "   {$section->sort_order}. {$section->name} ({$section->slug})\n";
}

echo "\n🎉 Test completed!\n";