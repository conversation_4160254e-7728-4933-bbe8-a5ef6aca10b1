<div class="border-b border-border-light dark:border-border-dark pb-6 last:border-b-0 last:pb-0">
    <div class="flex items-start justify-between mb-3">
        <div class="flex-1">
            <div class="flex items-center mb-2">
                <div class="w-10 h-10 bg-primary-light/10 dark:bg-primary-dark/10 rounded-full flex items-center justify-center mr-3">
                    <span class="text-primary-light dark:text-primary-dark font-semibold text-sm">
                        {{ strtoupper(substr($review->user ? $review->user->name : $review->name, 0, 1)) }}
                    </span>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">
                        {{ $review->user ? $review->user->name : $review->name }}
                    </h3>
                    <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">
                        {{ $review->created_at->format('F j, Y \a\t g:i A') }}
                    </p>
                </div>
            </div>
        </div>
        
        @if($review->rating)
            <div class="flex items-center ml-4">
                <div class="flex items-center">
                    @for($i = 1; $i <= 5; $i++)
                        <svg class="w-5 h-5 {{ $i <= $review->rating ? 'text-accent-light dark:text-accent-dark' : 'text-text-secondary-light/40 dark:text-text-secondary-dark/40' }}" 
                             fill="currentColor" 
                             viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                    @endfor
                </div>
                <span class="ml-2 text-sm text-text-secondary-light dark:text-text-secondary-dark">{{ $review->rating }}/5</span>
            </div>
        @endif
    </div>
    
    <div class="prose max-w-none text-text-primary-light dark:text-text-primary-dark leading-relaxed">
        {{ $review->comment }}
    </div>
</div>
