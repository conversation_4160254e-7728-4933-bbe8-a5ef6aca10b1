<?php $__env->startSection('content'); ?>
    <!-- Scroll Progress Indicator -->
    <div class="scroll-indicator" id="scrollIndicator"></div>
    <!-- Interactive Cursor -->
    <div class="interactive-cursor" id="cursor"></div>

    
    <!-- Animated Hero Section -->
    <?php echo $__env->make('home.partials.hero-section', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Features Section -->

    <?php echo $__env->make('home.partials.search-section', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <?php echo $__env->make('home.partials.features-section', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Stats Section -->

    <?php echo $__env->make('home.partials.stats-section', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Tools Section -->
    <?php echo $__env->make('home.partials.tools-section', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <?php echo $__env->make('home.partials.testimonials-section', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Pricing/Plans Section -->
    
    <?php echo $__env->make('home.partials.pricing-section', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Latest Blog Posts -->
    <?php echo $__env->make('home.partials.blog-section', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- FAQ Accordion Section -->
    <?php echo $__env->make('home.partials.faq-section', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- CTA Section -->
    <?php echo $__env->make('home.partials.cta-section', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css" />
    <style>
        /* Core Animation Classes - Only used ones */
        .card-hover {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .card-hover:hover {
            transform: translateY(-5px) scale(1.03);
            box-shadow: 0 8px 32px 0 rgba(10, 88, 202, 0.15);
        }

        /* Essential Animations */
        .animate-float-complex {
            animation: float-complex 6s ease-in-out infinite;
        }

        .animate-float-gentle {
            animation: float-gentle 4s ease-in-out infinite;
        }

        .animate-bounce-slow {
            animation: bounce-slow 3s ease-in-out infinite;
        }

        .animate-bounce-gentle {
            animation: bounce-gentle 2s ease-in-out infinite;
        }

        .animate-pulse-slow {
            animation: pulse-slow 4s ease-in-out infinite;
        }

        .animate-pulse-glow {
            animation: pulse-glow 2s ease-in-out infinite;
        }

        .animate-spin-slow {
            animation: spin-slow 8s linear infinite;
        }

        .animate-wiggle {
            animation: wiggle 2s ease-in-out infinite;
        }

        .animate-slide-in-left {
            animation: slide-in-left 1s ease-out;
        }

        .animate-slide-in-right {
            animation: slide-in-right 1s ease-out;
        }

        .animate-slide-in-bottom {
            animation: slide-in-bottom 1s ease-out;
        }

        .animate-fade-in-up {
            animation: fade-in-up 1s ease-out;
        }

        .animate-pulse-border {
            animation: pulse-border 3s ease-in-out infinite;
        }

        /* Keyframes */
        @keyframes float-complex {
            0%, 100% { transform: translateY(0) rotate(0deg) scale(1); }
            25% { transform: translateY(-8px) rotate(1deg) scale(1.02); }
            50% { transform: translateY(-15px) rotate(0deg) scale(1.05); }
            75% { transform: translateY(-8px) rotate(-1deg) scale(1.02); }
        }

        @keyframes float-gentle {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-8px); }
        }

        @keyframes bounce-slow {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-20px); }
        }

        @keyframes bounce-gentle {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        @keyframes pulse-slow {
            0%, 100% { opacity: 0.7; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        @keyframes pulse-glow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(10, 88, 202, 0.3);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 0 40px rgba(10, 88, 202, 0.6);
                transform: scale(1.02);
            }
        }

        @keyframes spin-slow {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes wiggle {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(5deg); }
            75% { transform: rotate(-5deg); }
        }

        @keyframes slide-in-left {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slide-in-right {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slide-in-bottom {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fade-in-up {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse-border {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        /* Essential Effects */
        .glass-effect {
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .enhanced-glow {
            box-shadow: 0 0 20px rgba(10, 88, 202, 0.3), 0 0 40px rgba(10, 88, 202, 0.2);
            transition: all 0.3s ease;
        }

        .enhanced-glow:hover {
            box-shadow: 0 0 30px rgba(10, 88, 202, 0.5), 0 0 60px rgba(10, 88, 202, 0.3);
            transform: translateY(-2px);
        }

        .dark .enhanced-glow {
            box-shadow: 0 0 20px rgba(61, 139, 253, 0.3), 0 0 40px rgba(61, 139, 253, 0.2);
        }

        .dark .enhanced-glow:hover {
            box-shadow: 0 0 30px rgba(61, 139, 253, 0.5), 0 0 60px rgba(61, 139, 253, 0.3);
        }

        .enhanced-gradient-text {
            background: linear-gradient(135deg, #0a58ca 0%, #3d8bfd 25%, #ffb703 50%, #ffda6a 75%, #0a58ca 100%);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradient-shift 4s ease-in-out infinite;
        }

        @keyframes gradient-shift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .enhanced-blob {
            filter: blur(60px);
            animation: enhanced-morph 12s ease-in-out infinite;
        }

        @keyframes enhanced-morph {
            0%, 100% { border-radius: 50%; transform: scale(1) rotate(0deg); }
            20% { border-radius: 60% 40% 30% 70%; transform: scale(1.1) rotate(72deg); }
            40% { border-radius: 30% 70% 60% 40%; transform: scale(0.9) rotate(144deg); }
            60% { border-radius: 70% 30% 40% 60%; transform: scale(1.05) rotate(216deg); }
            80% { border-radius: 40% 60% 70% 30%; transform: scale(0.95) rotate(288deg); }
        }

        /* UI Elements */
        .scroll-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 6px;
            background: linear-gradient(90deg, #0a58ca 0%, #3d8bfd 25%, #ffb703 50%, #ffda6a 75%, #0a58ca 100%);
            background-size: 200% 100%;
            transform-origin: left;
            transform: scaleX(0);
            z-index: 1000;
            transition: transform 0.3s ease;
            animation: scroll-gradient 3s linear infinite;
            box-shadow: 0 0 10px rgba(10, 88, 202, 0.5);
        }

        @keyframes scroll-gradient {
            0% { background-position: 0% 0%; }
            100% { background-position: 200% 0%; }
        }

        .interactive-cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: radial-gradient(circle, #0a58ca 0%, #3d8bfd 50%, #ffb703 100%);
            pointer-events: none;
            z-index: 9999;
            transition: all 0.2s ease;
            mix-blend-mode: difference;
            box-shadow: 0 0 20px rgba(10, 88, 202, 0.5), 0 0 40px rgba(61, 139, 253, 0.3);
            animation: cursor-pulse 2s ease-in-out infinite;
        }

        .dark .interactive-cursor {
            background: radial-gradient(circle, #3d8bfd 0%, #0a58ca 50%, #ffda6a 100%);
            box-shadow: 0 0 20px rgba(61, 139, 253, 0.5), 0 0 40px rgba(10, 88, 202, 0.3);
        }

        @keyframes cursor-pulse {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.2); opacity: 1; }
        }

        /* Background Elements */
        .parallax-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            background: linear-gradient(120deg, var(--primary-light, #0a58ca) 0%, var(--primary-dark, #3d8bfd) 60%, var(--accent-light, #ffb703) 100%);
            transition: all 0.5s ease;
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float-around 15s infinite linear;
        }

        @keyframes float-around {
            0% { transform: translateY(100vh) rotate(0deg); }
            100% { transform: translateY(-100vh) rotate(360deg); }
        }

        .morphing-blob {
            position: absolute;
            border-radius: 50%;
            filter: blur(40px);
            animation: morph 8s ease-in-out infinite;
            opacity: 0.3;
        }

        @keyframes morph {
            0%, 100% { border-radius: 50%; transform: scale(1) rotate(0deg); }
            25% { border-radius: 60% 40% 30% 70%; transform: scale(1.1) rotate(90deg); }
            50% { border-radius: 30% 70% 60% 40%; transform: scale(0.9) rotate(180deg); }
            75% { border-radius: 70% 30% 40% 60%; transform: scale(1.05) rotate(270deg); }
        }

        .interactive-bg {
            background: radial-gradient(
                circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
                rgba(10, 88, 202, 0.2) 0%,
                rgba(61, 139, 253, 0.15) 25%,
                rgba(255, 183, 3, 0.1) 50%,
                rgba(255, 218, 106, 0.05) 75%,
                transparent 100%
            );
            transition: all 0.3s ease;
        }

        .dark .interactive-bg {
            background: radial-gradient(
                circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
                rgba(61, 139, 253, 0.25) 0%,
                rgba(10, 88, 202, 0.2) 25%,
                rgba(255, 218, 106, 0.15) 50%,
                rgba(255, 183, 3, 0.1) 75%,
                transparent 100%
            );
        }

        .typing-animation {
            overflow: hidden;
            border-right: 3px solid rgba(102, 126, 234, 0.75);
            white-space: nowrap;
            animation: typing 3s steps(30, end), blink-caret 0.75s step-end infinite;
        }

        @keyframes typing {
            from { width: 0; }
            to { width: 100%; }
        }

        @keyframes blink-caret {
            from, to { border-color: transparent; }
            50% { border-color: rgba(102, 126, 234, 0.75); }
        }

        /* Particle System */
        .particle-system {
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: radial-gradient(circle, #0a58ca, #3d8bfd);
            border-radius: 50%;
            opacity: 0.6;
            animation: particle-float 8s linear infinite;
        }

        .particle:nth-child(odd) {
            background: radial-gradient(circle, #ffb703, #ffda6a);
        }

        @keyframes particle-float {
            0% { transform: translateY(100vh) translateX(0) rotate(0deg); opacity: 0; }
            10% { opacity: 0.6; }
            90% { opacity: 0.6; }
            100% { transform: translateY(-100px) translateX(100px) rotate(360deg); opacity: 0; }
        }



        /* Responsive */
        @media (max-width: 768px) {
            .enhanced-blob {
                filter: blur(40px);
            }
            .floating-element {
                font-size: 2rem;
            }
            .particle {
                width: 2px;
                height: 2px;
            }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.3/gsap.min.js"></script>
    <script src="https://unpkg.com/countup.js@2.0.8/dist/countUp.umd.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // AOS Init
            AOS.init({
                once: true,
                duration: 900,
                offset: 60
            });

            // Animated Counters
            document.querySelectorAll('.animated-counter').forEach(function(el) {
                const count = parseInt(el.getAttribute('data-count'));
                if (!isNaN(count)) {
                    const counter = new CountUp(el, count, { duration: 2 });
                    if (!counter.error) counter.start();
                }
            });

            // Search Form
            const searchForm = document.getElementById('searchForm');
            const searchInput = document.getElementById('search');
            const typePincode = document.getElementById('type-pincode');
            const typeName = document.getElementById('type-name');
            const typeDistrict = document.getElementById('type-district');

            if (searchForm) {
                searchForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    let selectedType;
                    if (typePincode.checked) {
                        selectedType = 'pincode';
                    } else if (typeName.checked) {
                        selectedType = 'name';
                    } else if (typeDistrict.checked) {
                        selectedType = 'district';
                    } else {
                        selectedType = 'pincode'; // default
                    }
                    
                    const query = searchInput.value.trim();
                    if (!query) return;

                    let searchUrl = `/search?query=${encodeURIComponent(query)}&type=${selectedType}`;
                    
                    // Add district parameter if type is district
                    if (selectedType === 'district') {
                        searchUrl += `&district=${encodeURIComponent(query)}`;
                    }

                    window.location.href = searchUrl;
                });

                // Update placeholder text based on selected type
                [typePincode, typeName, typeDistrict].forEach(radio => {
                    radio.addEventListener('change', function() {
                        const placeholder = this.value === 'pincode' ? 'Enter pincode' :
                                         this.value === 'name' ? 'Enter post office name' :
                                         'Enter district name';
                        searchInput.placeholder = placeholder;
                    });
                });
            }

            // Swiper Testimonials (only if element exists)
            const testimonialsSwiper = document.querySelector('.testimonials-swiper');
            if (testimonialsSwiper && window.Swiper) {
                new Swiper(testimonialsSwiper, {
                    loop: true,
                    autoplay: { delay: 5000 },
                    pagination: { el: '.swiper-pagination', clickable: true },
                    slidesPerView: 1,
                    spaceBetween: 32,
                    breakpoints: { 768: { slidesPerView: 2 } }
                });
            }



            // GSAP Hero Animations (only if GSAP is loaded)
            if (window.gsap) {
                const heroElements = {
                    titleSpans: document.querySelectorAll('.hero-title span'),
                    badge: document.querySelector('.hero-badge'),
                    subtitle: document.querySelector('.hero-subtitle'),
                    ctas: document.querySelector('.hero-ctas')
                };

                // Only animate if elements exist
                if (heroElements.badge || heroElements.titleSpans.length || heroElements.subtitle || heroElements.ctas) {
                    // Set initial states
                    if (heroElements.titleSpans.length) gsap.set(heroElements.titleSpans, { y: 80, opacity: 0, scale: 0.8 });
                    if (heroElements.badge) gsap.set(heroElements.badge, { y: -50, opacity: 0, scale: 0.5 });
                    if (heroElements.subtitle) gsap.set(heroElements.subtitle, { y: 60, opacity: 0 });
                    if (heroElements.ctas) gsap.set(heroElements.ctas, { y: 60, opacity: 0, scale: 0.8 });

                    // Animation timeline
                    const heroTimeline = gsap.timeline({ delay: 0.5 });
                    
                    if (heroElements.badge) {
                        heroTimeline.to(heroElements.badge, {
                            y: 0, opacity: 1, scale: 1, duration: 1, ease: 'back.out(1.7)'
                        });
                    }
                    
                    if (heroElements.titleSpans.length) {
                        heroTimeline.to(heroElements.titleSpans, {
                            y: 0, opacity: 1, scale: 1, stagger: 0.2, duration: 1.2, ease: 'power3.out'
                        }, '-=0.5');
                    }
                    
                    if (heroElements.subtitle) {
                        heroTimeline.to(heroElements.subtitle, {
                            y: 0, opacity: 1, duration: 1, ease: 'power2.out'
                        }, '-=0.8');
                    }
                    
                    if (heroElements.ctas) {
                        heroTimeline.to(heroElements.ctas, {
                            y: 0, opacity: 1, scale: 1, duration: 1, ease: 'back.out(1.4)'
                        }, '-=0.6');
                    }
                }
            }




        });
    </script>
    <script>
        // Enhanced Scroll Progress Indicator
        window.addEventListener('scroll', () => {
            const scrollProgress = window.pageYOffset / (document.documentElement.scrollHeight - window.innerHeight);
            const indicator = document.getElementById('scrollIndicator');

            if (indicator) {
                indicator.style.transform = `scaleX(${scrollProgress})`;

                // Add dynamic color based on scroll position
                const hue = scrollProgress * 360;
                indicator.style.filter = `hue-rotate(${hue}deg)`;
            }
        });

        // Enhanced Interactive Cursor
        const cursor = document.getElementById('cursor');
        let mouseX = 0, mouseY = 0;
        let cursorX = 0, cursorY = 0;
        let isHovering = false;

        document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
        });

        // Detect hover states for cursor enhancement
        document.addEventListener('mouseover', (e) => {
            if (e.target.matches('a, button, .card-hover, .hero-ctas a')) {
                isHovering = true;
                cursor.style.transform = 'scale(2)';
                cursor.style.opacity = '0.8';
            }
        });

        document.addEventListener('mouseout', (e) => {
            if (e.target.matches('a, button, .card-hover, .hero-ctas a')) {
                isHovering = false;
                cursor.style.transform = 'scale(1)';
                cursor.style.opacity = '1';
            }
        });

        function animateCursor() {
            const ease = isHovering ? 0.15 : 0.1;
            cursorX += (mouseX - cursorX) * ease;
            cursorY += (mouseY - cursorY) * ease;

            if (cursor) {
                cursor.style.left = cursorX + 'px';
                cursor.style.top = cursorY + 'px';
            }

            requestAnimationFrame(animateCursor);
        }
        animateCursor();

        // Enhanced Interactive Background
        const interactiveBg = document.getElementById('interactiveBg');
        let rafId;

        document.addEventListener('mousemove', (e) => {
            if (rafId) cancelAnimationFrame(rafId);

            rafId = requestAnimationFrame(() => {
                const x = (e.clientX / window.innerWidth) * 100;
                const y = (e.clientY / window.innerHeight) * 100;

                if (interactiveBg) {
                    interactiveBg.style.setProperty('--mouse-x', x + '%');
                    interactiveBg.style.setProperty('--mouse-y', y + '%');
                }
            });
        });

        // Intersection observer for scroll animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                }
            });
        }, { threshold: 0.1, rootMargin: '0px 0px -50px 0px' });

        // Observe elements for scroll animations
        document.querySelectorAll('.card-hover, .hero-texts > *').forEach(el => {
            observer.observe(el);
        });

        // Particle System
        function createParticleSystem() {
            const particleSystem = document.getElementById('particleSystem');
            if (!particleSystem) return;

            const particleCount = window.innerWidth > 768 ? 30 : 15; // Reduced count for performance
            
            for (let i = 0; i < particleCount; i++) {
                createParticle(particleSystem);
            }
        }

        function createParticle(container) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 8 + 's';
            particle.style.animationDuration = (8 + Math.random() * 4) + 's';
            
            const size = 2 + Math.random() * 3; // Slightly smaller particles
            particle.style.width = size + 'px';
            particle.style.height = size + 'px';
            
            container.appendChild(particle);

            // Remove and recreate particle after animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.remove();
                    createParticle(container);
                }
            }, (8 + Math.random() * 4) * 1000);
        }

        // Initialize particle system
        createParticleSystem();

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/home/<USER>/ ?>