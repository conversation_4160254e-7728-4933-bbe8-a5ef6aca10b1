<?php

use App\Models\Payment;
use App\Models\Order;
use App\Models\User;
use App\Models\Plan;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('payment can be created', function () {
    $payment = Payment::factory()->create([
        'payment_id' => 'PAY-123456',
        'payer_email' => '<EMAIL>',
        'amount' => 99.99,
        'currency' => 'USD',
        'payment_status' => Payment::STATUS_COMPLETED,
        'payment_method' => Payment::METHOD_PAYPAL,
        'payment_details' => ['key' => 'value'],
        'paid_at' => now(),
    ]);

    expect($payment)->toBeInstanceOf(Payment::class)
        ->and($payment->payment_id)->toBe('PAY-123456')
        ->and((float) $payment->amount)->toBe(99.99)
        ->and($payment->currency)->toBe('USD')
        ->and($payment->payment_status)->toBe(Payment::STATUS_COMPLETED)
        ->and($payment->payment_method)->toBe(Payment::METHOD_PAYPAL);
});

test('payment has order relationship', function () {
    $order = Order::factory()->create();
    $payment = Payment::factory()->create([
        'order_id' => $order->id
    ]);

    expect($payment->order)->toBeInstanceOf(Order::class)
        ->and($payment->order->id)->toBe($order->id);
});

test('payment has correct table name', function () {
    $payment = new Payment();
    
    expect($payment->getTable())->toBe('payments');
});

test('payment has correct fillable attributes', function () {
    $payment = new Payment();
    $fillable = $payment->getFillable();
    
    expect($fillable)->toContain('order_id')
        ->toContain('payment_id')
        ->toContain('payer_email')
        ->toContain('amount')
        ->toContain('currency')
        ->toContain('payment_status')
        ->toContain('payment_method')
        ->toContain('payment_details')
        ->toContain('paid_at');
});

test('payment amount is cast to decimal', function () {
    $payment = Payment::factory()->create([
        'amount' => 99.99
    ]);

    expect((float) $payment->amount)->toBe(99.99);
});

test('payment payment_details is cast to array', function () {
    $payment = Payment::factory()->create([
        'payment_details' => ['transaction_id' => 'txn_123456']
    ]);

    expect($payment->payment_details)->toBeArray()
        ->and($payment->payment_details)->toHaveKey('transaction_id')
        ->and($payment->payment_details['transaction_id'])->toBe('txn_123456');
});

test('payment paid_at is cast to datetime', function () {
    $date = now();
    $payment = Payment::factory()->create([
        'paid_at' => $date
    ]);

    expect($payment->paid_at)->toBeInstanceOf(\Carbon\Carbon::class);
});

test('payment status constants are defined correctly', function () {
    expect(Payment::STATUS_PENDING)->toBe('pending')
        ->and(Payment::STATUS_COMPLETED)->toBe('completed')
        ->and(Payment::STATUS_FAILED)->toBe('failed')
        ->and(Payment::STATUS_REFUNDED)->toBe('refunded');
});

test('payment method constants are defined correctly', function () {
    expect(Payment::METHOD_PAYPAL)->toBe('paypal');
});

test('payment can be created with different statuses', function () {
    $pendingPayment = Payment::factory()->pending()->create();
    $completedPayment = Payment::factory()->completed()->create();
    $failedPayment = Payment::factory()->failed()->create();
    $refundedPayment = Payment::factory()->refunded()->create();

    expect($pendingPayment->payment_status)->toBe(Payment::STATUS_PENDING)
        ->and($completedPayment->payment_status)->toBe(Payment::STATUS_COMPLETED)
        ->and($failedPayment->payment_status)->toBe(Payment::STATUS_FAILED)
        ->and($refundedPayment->payment_status)->toBe(Payment::STATUS_REFUNDED);
});

test('completed payments have paid_at date', function () {
    $payment = Payment::factory()->completed()->create();
    
    expect($payment->paid_at)->not->toBeNull()
        ->and($payment->paid_at)->toBeInstanceOf(\Carbon\Carbon::class);
});

test('payment can be created with null payment_details', function () {
    $payment = Payment::factory()->create([
        'payment_details' => null
    ]);

    expect($payment->payment_details)->toBeNull();
});