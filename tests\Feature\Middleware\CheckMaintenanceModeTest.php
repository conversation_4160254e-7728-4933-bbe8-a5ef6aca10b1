<?php

use App\Models\User;
use App\Models\Setting;
use App\Http\Middleware\CheckMaintenanceMode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class)->group('middleware');

beforeEach(function () {
    $this->middleware = new CheckMaintenanceMode();
    $this->next = function ($request) {
        return response('Next middleware called');
    };
});

it('skips maintenance check in testing environment', function () {
    App::shouldReceive('environment')
        ->with('testing')
        ->andReturn(true);

    $request = Request::create('/dashboard');

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});

it('allows access when not in maintenance mode', function () {
    App::shouldReceive('environment')
        ->with('testing')
        ->andReturn(false);

    $request = Request::create('/dashboard');

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});

it('blocks access when in maintenance mode for regular users', function () {
    App::shouldReceive('environment')
        ->with('testing')
        ->andReturn(false);

    // Set maintenance mode setting
    Setting::create([
        'key' => 'maintenance_mode',
        'value' => '1'
    ]);

    $request = Request::create('/dashboard');

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getStatusCode())->toBe(503);
    expect($response->getContent())->toContain('Under Maintenance');
});

it('allows admin users access during maintenance mode', function () {
    App::shouldReceive('environment')
        ->with('testing')
        ->andReturn(false);

    // Set maintenance mode setting
    Setting::create([
        'key' => 'maintenance_mode',
        'value' => '1'
    ]);

    $adminUser = User::factory()->create([
        'role' => 'admin',
        'status' => 'active'
    ]);

    $this->actingAs($adminUser);

    $request = Request::create('/dashboard');

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});

it('allows access to login page during maintenance mode', function () {
    App::shouldReceive('environment')
        ->with('testing')
        ->andReturn(false);

    // Set maintenance mode setting
    Setting::create([
        'key' => 'maintenance_mode',
        'value' => '1'
    ]);

    $request = Request::create('/login');

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getStatusCode())->toBe(200);
    expect($response->getContent())->toBe('Next middleware called');
});

it('allows access to admin login page during maintenance mode', function () {
    App::shouldReceive('environment')
        ->with('testing')
        ->andReturn(false);

    // Set maintenance mode setting
    Setting::create([
        'key' => 'maintenance_mode',
        'value' => '1'
    ]);

    $request = Request::create('/admin/login');

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});

it('blocks regular users from accessing protected routes during maintenance', function () {
    App::shouldReceive('environment')
        ->with('testing')
        ->andReturn(false);

    // Set maintenance mode setting
    Setting::create([
        'key' => 'maintenance_mode',
        'value' => '1'
    ]);

    $regularUser = User::factory()->create([
        'role' => 'user',
        'status' => 'active'
    ]);

    $request = Request::create('/dashboard');
    $request->setUserResolver(function () use ($regularUser) {
        return $regularUser;
    });

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getStatusCode())->toBe(503);
    expect($response->getContent())->toContain('Under Maintenance');
});

it('blocks unauthenticated users from accessing protected routes during maintenance', function () {
    App::shouldReceive('environment')
        ->with('testing')
        ->andReturn(false);

    // Set maintenance mode setting
    Setting::create([
        'key' => 'maintenance_mode',
        'value' => '1'
    ]);

    $request = Request::create('/dashboard');

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getStatusCode())->toBe(503);
    expect($response->getContent())->toContain('Under Maintenance');
});

it('returns maintenance view with 503 status code', function () {
    App::shouldReceive('environment')
        ->with('testing')
        ->andReturn(false);

    // Set maintenance mode setting
    Setting::create([
        'key' => 'maintenance_mode',
        'value' => '1'
    ]);

    $request = Request::create('/dashboard');

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getStatusCode())->toBe(503);
    expect($response->getContent())->toContain('Under Maintenance');
});

it('handles maintenance mode setting as false', function () {
    App::shouldReceive('environment')
        ->with('testing')
        ->andReturn(false);

    // Set maintenance mode setting to false
    Setting::create([
        'key' => 'maintenance_mode',
        'value' => '0'
    ]);

    $request = Request::create('/dashboard');

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});

it('handles missing maintenance mode setting', function () {
    App::shouldReceive('environment')
        ->with('testing')
        ->andReturn(false);

    // Don't create any maintenance mode setting

    $request = Request::create('/dashboard');

    $response = $this->middleware->handle($request, $this->next);

    expect($response->getContent())->toBe('Next middleware called');
});