<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\PinCode;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Validation\Rules\Password;

use App\Http\Requests\Admin\UpdateProfileRequest;

class AdminController extends Controller
{
    protected $backupService;


    /**
     * Show the admin dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function dashboard(): View
    {
        $stats = Cache::remember('admin.dashboard.stats', 3600, function () {
            return [
                'total_users' => User::count(),
                'active_users' => User::where('status', 'active')->count(),
                'total_pincodes' => PinCode::count(),
                'system_health' => [
                    'php_version' => PHP_VERSION,
                    'laravel_version' => app()->version(),
                    'disk_usage' => $this->getDiskUsage(),
                ]
            ];
        });

        return view('admin.dashboard', compact('stats'));
    }

    /**
     * Update the admin API settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateApiSettings(Request $request)
    {
        $validated = $request->validate([
            'api_enabled' => 'required|boolean',
            'rate_limit' => 'required|integer|min:1|max:1000',
        ]);

        // Update API settings in database or config
        
        return redirect()->route('admin.settings.index')
            ->with('success', 'API settings updated successfully.');
    }

    /**
     * Clear the application cache.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clearCache()
    {
        Artisan::call('cache:clear');
        Artisan::call('view:clear');
        Artisan::call('config:clear');
        return back()->with('success', 'Cache cleared successfully');
    }

    /**
     * Toggle maintenance mode.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function toggleMaintenance()
    {
        if (app()->isDownForMaintenance()) {
            Artisan::call('up');
            $message = 'Application is now live';
        } else {
            Artisan::call('down', [
                '--secret' => 'admin-' . md5(uniqid())
            ]);
            $message = 'Application is now in maintenance mode';
        }

        return back()->with('success', $message);
    }

    /**
     * Show the admin profile page.
     *
     * @return \Illuminate\View\View
     */
    public function profile(): View
    {
        $user = auth()->user();
        return view('admin.profile', compact('user'));
    }

    /**
     * Update the admin profile.
     *
     * @param  \App\Http\Requests\Admin\UpdateProfileRequest  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function updateProfile(UpdateProfileRequest $request)
    {
        $user = Auth::user();
        
        // Ensure the user is an admin
        if (!$user->isAdmin()) {
            return back()->withErrors([
                'error' => 'Unauthorized access.'
            ]);
        }
        
        // Update the profile
        $user->update($request->validated());
        
        // Return success response
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Profile updated successfully!',
                'user' => $user->fresh()
            ]);
        }

        return back()->with('success', 'Profile updated successfully!');
    }

    /**
     * Enable two-factor authentication.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function enableTwoFactor()
    {
        $user = Auth::user();
        $user->update([
            'two_factor_enabled' => true,
        ]);
        
        return redirect()->route('admin.profile.index')
            ->with('success', 'Two-factor authentication enabled successfully.');
    }

    /**
     * Disable two-factor authentication.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function disableTwoFactor()
    {
        $user = Auth::user();
        $user->update([
            'two_factor_enabled' => false,
        ]);
        
        return redirect()->route('admin.profile.index')
            ->with('success', 'Two-factor authentication disabled successfully.');
    }

    protected function getDiskUsage(): array
    {
        $diskTotal = disk_total_space(base_path());
        $diskFree = disk_free_space(base_path());
        $diskUsed = $diskTotal - $diskFree;

        return [
            'total' => $this->formatBytes($diskTotal),
            'used' => $this->formatBytes($diskUsed),
            'free' => $this->formatBytes($diskFree),
            'percentage' => round(($diskUsed / $diskTotal) * 100, 2)
        ];
    }

    protected function formatBytes($bytes, $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        return round($bytes / (1024 ** $pow), $precision) . ' ' . $units[$pow];
    }

    public function getStats()
    {
        return response()->json([
            'stats' => [
                'users' => User::count(),
                'revenue' => Order::sum('amount') ?? 0,
                'subscriptions' => 0, // No Subscription model available
                'pageViews' => 0, // No Visit model available
                'system' => [
                    'php_version' => PHP_VERSION,
                    'laravel_version' => app()->version(),
                    'memory' => [
                        'used' => $this->formatBytes(memory_get_usage(true)),
                        'total' => $this->formatBytes($this->parseMemoryLimit(ini_get('memory_limit') ?: '0')),
                        'percentage' => ini_get('memory_limit') ? round((memory_get_usage(true) / $this->parseMemoryLimit(ini_get('memory_limit'))) * 100, 2) : 0
                    ],
                    'disk' => [
                        'used' => $this->formatBytes(disk_total_space(base_path()) - disk_free_space(base_path())),
                        'total' => $this->formatBytes(disk_total_space(base_path())),
                        'percentage' => round(((disk_total_space(base_path()) - disk_free_space(base_path())) / disk_total_space(base_path())) * 100, 2)
                    ]
                ]
            ],
            'activities' => [] // No Activity model available
        ]);
    }

    protected function parseMemoryLimit($memoryLimit)
    {
        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int) substr($memoryLimit, 0, -1);
        
        switch ($unit) {
            case 'k':
                return $value * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'g':
                return $value * 1024 * 1024 * 1024;
            default:
                return $value;
        }
    }

    /**
     * Update admin password
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function updatePassword(Request $request)
    {
        // Validate the request
        $request->validate([
            'current_password' => ['required', 'string'],
            'new_password' => ['required', 'string', 'confirmed', Password::min(8)],
        ]);

        $user = Auth::user();
        
        // Ensure the user is an admin
        if (!$user->isAdmin()) {
            return back()->withErrors([
                'error' => 'Unauthorized access.'
            ]);
        }
        
        // Check if current password is correct
        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors([
                'current_password' => 'The current password is incorrect.'
            ]);
        }

        // Update the password
        $user->update([
            'password' => Hash::make($request->new_password)
        ]);

        // Return success response
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Password updated successfully!'
            ]);
        }

        return back()->with('success', 'Password updated successfully!');
    }

}
