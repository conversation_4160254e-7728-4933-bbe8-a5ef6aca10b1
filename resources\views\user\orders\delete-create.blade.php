@extends('layouts.user')

@section('title', 'Create Order')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <a href="{{ route('orders.index') }}" class="text-blue-500 hover:text-blue-700">
            &larr; Back to Orders
        </a>
    </div>

    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h1 class="text-2xl font-bold text-gray-800">Create New Order</h1>
        </div>

        <div class="p-6">
            <form action="{{ route('orders.store') }}" method="POST">
                @csrf

                <div class="mb-6">
                    <label for="plan_id" class="block text-sm font-medium text-gray-700 mb-2">Select Plan</label>
                    <select id="plan_id" name="plan_id" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md @error('plan_id') border-red-500 @enderror" required>
                        <option value="">-- Select a Plan --</option>
                        @foreach($plans as $plan)
                            <option value="{{ $plan->id }}" data-amount="{{ $plan->price }}" data-limit="{{ $plan->request_limit }}">
                                {{ $plan->name }} - ₹{{ number_format($plan->price, 2) }} ({{ number_format($plan->request_limit) }} requests)
                            </option>
                        @endforeach
                    </select>
                    @error('plan_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="mb-6">
                    <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">Amount</label>
                    <input type="number" name="amount" id="amount" step="0.01" min="0" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md @error('amount') border-red-500 @enderror" readonly>
                    @error('amount')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="mb-6">
                    <label for="request_limit" class="block text-sm font-medium text-gray-700 mb-2">Request Limit</label>
                    <input type="number" name="request_limit" id="request_limit" min="1" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md @error('request_limit') border-red-500 @enderror" readonly>
                    @error('request_limit')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="mb-6">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes (Optional)</label>
                    <textarea id="notes" name="notes" rows="3" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md @error('notes') border-red-500 @enderror">{{ old('notes') }}</textarea>
                    @error('notes')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="flex justify-end">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                        Create Order & Proceed to Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const planSelect = document.getElementById('plan_id');
        const amountInput = document.getElementById('amount');
        const requestLimitInput = document.getElementById('request_limit');

        planSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                amountInput.value = selectedOption.dataset.amount;
                requestLimitInput.value = selectedOption.dataset.limit;
            } else {
                amountInput.value = '';
                requestLimitInput.value = '';
            }
        });
    });
</script>
@endsection