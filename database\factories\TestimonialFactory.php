<?php

namespace Database\Factories;

use App\Models\Testimonial;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Testimonial>
 */
class TestimonialFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $companies = [
            'ShipFast', 'EcomXpress', 'QuickMove', 'LogiTech', 'PostalPro',
            'DeliveryHub', 'ExpressLogistics', 'FastTrack', 'SpeedPost', 'ReliableCourier'
        ];

        $positions = [
            'Logistics Manager', 'CTO', 'Operations Lead', 'CEO', 'COO',
            'Supply Chain Director', 'Operations Manager', 'Business Analyst', 'Project Manager', 'Team Lead'
        ];

        $testimonials = [
            "The most reliable pincode directory I've used. The data is always up to date and the interface is beautiful!",
            "BharatPostal Info's API integration saved us hours of manual work. Highly recommended for any business!",
            "Super fast search and beautiful animations. My team loves using this tool every day.",
            "Accurate data and excellent customer support. This has become an essential part of our daily operations.",
            "The bulk verification feature has streamlined our address validation process significantly.",
            "User-friendly interface and comprehensive coverage. Perfect for our logistics requirements.",
            "Real-time updates and reliable data make this the go-to solution for postal code information.",
            "Excellent API performance and documentation. Integration was seamless.",
            "The most comprehensive pincode database we've found. Highly accurate and regularly updated.",
            "Outstanding service and support. The team is always responsive to our needs."
        ];

        return [
            'name' => fake()->name(),
            'position' => fake()->randomElement($positions),
            'company' => fake()->randomElement($companies),
            'content' => fake()->randomElement($testimonials),
            'avatar' => null,
            'rating' => fake()->numberBetween(4, 5),
            'is_active' => true,
            'sort_order' => fake()->numberBetween(0, 100),
        ];
    }

    /**
     * Indicate that the testimonial is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
} 