<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Comment;
use App\Models\BlogPost;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CommentController extends Controller
{
    /**
     * Display a listing of all comments.
     * 
     */
    public function index(Request $request)
    {
        $query = Comment::with(['blogPost', 'author'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->has('status')) {
            if ($request->status === 'approved') {
                $query->where('is_approved', true);
            } elseif ($request->status === 'pending') {
                $query->where('is_approved', false)
                    ->whereNull('rejected_reason');
            } elseif ($request->status === 'rejected') {
                $query->whereNotNull('rejected_reason');
            }
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('content', 'like', "%{$search}%")
                    ->orWhereHas('blogPost', function ($q) use ($search) {
                        $q->where('title', 'like', "%{$search}%");
                    })
                    ->orWhereHas('author', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%");
                    });
            });
        }

        $comments = $query->paginate(15)->withQueryString();

        return view('admin.comments.index', compact('comments'));
    }

    /**
     * Show a specific comment.
     */
    public function show(Comment $comment)
    {
        $comment->load(['blogPost', 'author', 'replies']);
        return view('admin.comments.show', compact('comment'));
    }

    /**
     * Approve a comment.
     */
    public function approve(Comment $comment)
    {
        $comment->update([
            'is_approved' => true,
            'rejected_reason' => null,
            'moderated_at' => now(),
            'moderated_by' => Auth::id()
        ]);

        return redirect()->route('admin.comments.index')
            ->with('success', 'Comment approved successfully.');
    }

    /**
     * Reject a comment.
     */
    public function reject(Request $request, Comment $comment)
    {
        $request->validate([
            'rejected_reason' => 'required|string|max:255'
        ]);

        $comment->update([
            'is_approved' => false,
            'rejected_reason' => $request->rejected_reason,
            'moderated_at' => now(),
            'moderated_by' => Auth::id()
        ]);

        return redirect()->route('admin.comments.index')
            ->with('success', 'Comment rejected successfully.');
    }

    /**
     * Remove the specified comment from storage.
     */
    public function destroy(Comment $comment)
    {

        // Delete any replies first
        $comment->replies()->delete();

        $comment->delete();

        return redirect()->route('admin.comments.index')
            ->with('success', 'Comment deleted successfully.');
    }
}