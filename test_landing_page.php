<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing landing page data...\n";

$service = app(\App\Services\LandingPageService::class);
$data = $service->getLandingPageData();

echo "Sections available: " . implode(', ', array_keys($data)) . "\n";
echo "Hero heading: " . ($data['hero']['content']['heading'] ?? 'Not found') . "\n";
echo "Search section: " . (isset($data['search']) ? 'Available' : 'Missing') . "\n";
echo "Tools count: " . count($data['tools']['content']['tools'] ?? []) . "\n";
echo "Features count: " . count($data['features']['content']['features'] ?? []) . "\n";
echo "Testimonials count: " . count($data['testimonials']['content']['testimonials'] ?? []) . "\n";
echo "Test completed successfully!\n";