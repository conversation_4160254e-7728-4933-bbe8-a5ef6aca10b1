<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\DigipinDecodeRequest;
use App\Http\Requests\DigipinGenerateRequest;
use App\Services\DigipinService;
use Illuminate\Http\JsonResponse;

class DigipinController extends Controller
{
    protected DigipinService $digipinService;

    public function __construct(DigipinService $digipinService)
    {
        $this->digipinService = $digipinService;
    }

    /**
     * Generate DIGIPIN from latitude and longitude
     *
     * @param DigipinGenerateRequest $request
     * @return JsonResponse
     */
    public function generateDigipin(DigipinGenerateRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();
            $digipin = $this->digipinService->encode($validated['latitude'], $validated['longitude']);

            return response()->json([
                'success' => true,
                'data' => [
                    'digipin' => $digipin,
                    'latitude' => $validated['latitude'],
                    'longitude' => $validated['longitude']
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Decode DIGIPIN to get latitude and longitude
     *
     * @param DigipinDecodeRequest $request
     * @return JsonResponse
     */
    public function decodeDigipin(DigipinDecodeRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();

            // Validate DIGIPIN format
            if (!$this->digipinService->isValidFormat($validated['digipin'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid DIGIPIN format'
                ], 422);
            }

            $coordinates = $this->digipinService->decode($validated['digipin']);

            return response()->json([
                'success' => true,
                'data' => [
                    'digipin' => $validated['digipin'],
                    'latitude' => $coordinates['latitude'],
                    'longitude' => $coordinates['longitude']
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get DIGIPIN information and configuration
     *
     * @return JsonResponse
     */
    public function getInfo(): JsonResponse
    {
        $info = $this->digipinService->getInfo();
        
        return response()->json([
            'success' => true,
            'data' => [
                'description' => $info['description'],
                'bounds' => $info['bounds'],
                'grid' => $info['grid'],
                'validCharacters' => $info['validCharacters'],
                'endpoints' => [
                    'generate' => '/api/digipin/generate',
                    'decode' => '/api/digipin/decode',
                    'info' => '/api/digipin/info'
                ]
            ]
        ]);
    }
} 