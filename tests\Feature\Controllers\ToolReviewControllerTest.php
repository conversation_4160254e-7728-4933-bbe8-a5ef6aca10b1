<?php

use App\Models\Tool;
use App\Models\ToolReview;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create a test user
    $this->user = User::factory()->create();
});

test('guest can submit review with name and email', function () {
    $tool = Tool::factory()->published()->create();
    
    $response = $this->post(route('tools.reviews.store', $tool->id), [
        'review' => 'This is a test review',
        'name' => '<PERSON>',
        'email' => '<EMAIL>'
    ]);

    $response->assertRedirect()
        ->assertSessionHas('success', 'Review submitted successfully.');

    $this->assertDatabaseHas('tool_reviews', [
        'tool_id' => $tool->id,
        'name' => '<PERSON>',
        'email' => '<EMAIL>',
        'review' => 'This is a test review',
        'is_approved' => false
    ]);
});

test('authenticated user can submit review without name and email', function () {
    $tool = Tool::factory()->published()->create();
    
    $response = $this->actingAs($this->user)
        ->post(route('tools.reviews.store', $tool->id), [
            'review' => 'This is a test review from authenticated user'
        ]);

    $response->assertRedirect()
        ->assertSessionHas('success', 'Review submitted successfully.');

    $this->assertDatabaseHas('tool_reviews', [
        'tool_id' => $tool->id,
        'user_id' => $this->user->id,
        'review' => 'This is a test review from authenticated user',
        'is_approved' => false
    ]);
});

test('review submission requires review field', function () {
    $tool = Tool::factory()->published()->create();
    
    $response = $this->post(route('tools.reviews.store', $tool->id), [
        'name' => 'John Doe',
        'email' => '<EMAIL>'
    ]);

    $response->assertSessionHasErrors('review');
});

test('guest review submission requires name and email', function () {
    $tool = Tool::factory()->published()->create();
    
    $response = $this->post(route('tools.reviews.store', $tool->id), [
        'review' => 'This is a test review'
    ]);

    $response->assertSessionHasErrors(['name', 'email']);
});

test('email must be valid', function () {
    $tool = Tool::factory()->published()->create();
    
    $response = $this->post(route('tools.reviews.store', $tool->id), [
        'review' => 'This is a test review',
        'name' => 'John Doe',
        'email' => 'invalid-email'
    ]);

    $response->assertSessionHasErrors('email');
});

test('review cannot be submitted for non-existent tool', function () {
    $response = $this->post(route('tools.reviews.store', 999), [
        'review' => 'This is a test review',
        'name' => 'John Doe',
        'email' => '<EMAIL>'
    ]);

    $response->assertStatus(404);
});

test('view reviews page displays tool reviews', function () {
    $tool = Tool::factory()->published()->create();
    $reviews = ToolReview::factory()
        ->count(3)
        ->create([
            'tool_id' => $tool->id,
            'is_approved' => true
        ]);

    $response = $this->get(route('tools.reviews.view', $tool->slug));

    $response->assertStatus(200)
        ->assertViewIs('tools.tool-reviews')
        ->assertViewHas('tool')
        ->assertViewHas('pageTitle', $tool->name);

    foreach ($reviews as $review) {
        $response->assertSee($review->review);
    }
});

test('view reviews page returns 404 for non-existent tool', function () {
    $response = $this->get(route('tools.reviews.view', 'non-existent-tool'));

    $response->assertStatus(404);
}); 