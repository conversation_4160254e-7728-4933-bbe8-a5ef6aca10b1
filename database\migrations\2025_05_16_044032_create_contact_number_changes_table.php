<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_number_changes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pincode_id')->constrained('pin_codes')->onDelete('cascade');
            $table->string('old_number')->nullable();
            $table->string('new_number');
            $table->text('reason');
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->text('admin_notes')->nullable();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_number_changes');
    }
};
// php artisan migrate --path=database/migrations/2025_05_16_044032_create_contact_number_changes_table.php
