<?php

use App\Models\State;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create an admin user for testing
    $this->admin = User::factory()->create([
        'role' => User::ROLE_ADMIN,
        'status' => User::STATUS_ACTIVE,
    ]);
    
    // Create some test states
    $this->states = State::factory()->count(3)->create();
    
    // Setup fake storage
    Storage::fake('public');
});

test('admin can view states index page', function () {
    $response = $this->actingAs($this->admin)
        ->get(route('admin.states.index'));
    
    $response->assertStatus(200);
    $response->assertViewIs('admin.states.index');
    $response->assertViewHas('states');

    // Get the states from the view data
    $viewStates = $response->viewData('states');
    
    // Verify that the view has states
    $this->assertNotEmpty($viewStates, 'No states found in the view data');
    $this->assertGreaterThanOrEqual(1, $viewStates->count(), 'Expected at least one state in the view data');
    
    // Check that the first state has the expected attributes
    $firstState = $viewStates->first();
    $this->assertNotNull($firstState);
    $this->assertArrayHasKey('id', $firstState->toArray());
    $this->assertArrayHasKey('name', $firstState->toArray());
});

test('admin can access state create page', function () {
    $response = $this->actingAs($this->admin)
        ->get(route('admin.states.create'));
    
    $response->assertStatus(200);
    $response->assertViewIs('admin.states.create');
});

test('admin can store a new state without image', function () {
    $stateName = 'New Test State';
    
    $response = $this->actingAs($this->admin)
        ->post(route('admin.states.store'), [
            'name' => $stateName,
        ]);
    
    $response->assertRedirect(route('admin.states.index'));
    $response->assertSessionHas('success', 'State created successfully.');
    
    $this->assertDatabaseHas('pin_states', [
        'name' => $stateName,
    ]);
});

test('admin can store a new state with image', function () {
    $stateName = 'New Test State With Image';
    $file = UploadedFile::fake()->image('state.jpg');
    
    $response = $this->actingAs($this->admin)
        ->post(route('admin.states.store'), [
            'name' => $stateName,
            'featured_image' => $file,
        ]);
    
    $response->assertRedirect(route('admin.states.index'));
    $response->assertSessionHas('success', 'State created successfully.');
    
    $state = State::where('name', $stateName)->first();
    $this->assertNotNull($state);
    
    // Check if the file was stored
    $filePath = str_replace('public/', '', $state->featured_image);
    Storage::disk('public')->assertExists($filePath);
});

test('admin cannot store a state with duplicate name', function () {
    $existingState = $this->states->first();
    
    $response = $this->actingAs($this->admin)
        ->post(route('admin.states.store'), [
            'name' => $existingState->name,
        ]);
    
    $response->assertSessionHasErrors('name');
});

test('admin can access state edit page', function () {
    $state = $this->states->first();
    
    $response = $this->actingAs($this->admin)
        ->get(route('admin.states.edit', $state));
    
    $response->assertStatus(200);
    $response->assertViewIs('admin.states.edit');
    $response->assertViewHas('state');
    $response->assertSee($state->name);
});

test('admin can update a state without changing image', function () {
    $state = $this->states->first();
    $newName = 'Updated State Name';
    
    $response = $this->actingAs($this->admin)
        ->put(route('admin.states.update', $state), [
            'name' => $newName,
        ]);
    
    $response->assertRedirect(route('admin.states.index'));
    $response->assertSessionHas('success', 'State updated successfully.');
    
    $this->assertDatabaseHas('pin_states', [
        'id' => $state->id,
        'name' => $newName,
    ]);
});

test('admin can update a state and change image', function () {
    // First create a state with an image
    $state = State::factory()->create();
    $oldImagePath = 'states/old-image.jpg';
    $state->featured_image = $oldImagePath;
    $state->save();
    
    // Create a fake file in the storage
    Storage::disk('public')->put($oldImagePath, 'old image content');
    
    $newName = 'Updated State With New Image';
    $newFile = UploadedFile::fake()->image('new-state.jpg');
    
    $response = $this->actingAs($this->admin)
        ->put(route('admin.states.update', $state), [
            'name' => $newName,
            'featured_image' => $newFile,
        ]);
    
    $response->assertRedirect(route('admin.states.index'));
    $response->assertSessionHas('success', 'State updated successfully.');
    
    // Refresh the state from database
    $state->refresh();
    
    // Check if the name was updated
    $this->assertEquals($newName, $state->name);
    
    // Check if the old file was deleted
    Storage::disk('public')->assertMissing($oldImagePath);
    
    // Check if the new file was stored
    Storage::disk('public')->assertExists($state->featured_image);
});

test('admin can delete a state without image', function () {
    $state = $this->states->first();
    
    $response = $this->actingAs($this->admin)
        ->delete(route('admin.states.destroy', $state));
    
    $response->assertRedirect(route('admin.states.index'));
    $response->assertSessionHas('success', 'State deleted successfully.');
    
    $this->assertDatabaseMissing('pin_states', [
        'id' => $state->id,
    ]);
});

test('admin can delete a state with image', function () {
    // First create a state with an image
    $state = State::factory()->create();
    $imagePath = 'states/test-image.jpg';
    $state->featured_image = $imagePath;
    $state->save();
    
    // Create a fake file in the storage
    Storage::disk('public')->put($imagePath, 'test image content');
    
    $response = $this->actingAs($this->admin)
        ->delete(route('admin.states.destroy', $state));
    
    $response->assertRedirect(route('admin.states.index'));
    $response->assertSessionHas('success', 'State deleted successfully.');
    
    $this->assertDatabaseMissing('pin_states', [
        'id' => $state->id,
    ]);
    
    // Check if the file was deleted
    Storage::disk('public')->assertMissing($imagePath);
});

test('non-admin cannot access states index page', function () {
    $regularUser = User::factory()->create([
        'role' => User::ROLE_USER,
        'status' => User::STATUS_ACTIVE,
    ]);
    
    $response = $this->actingAs($regularUser)
        ->get(route('admin.states.index'));
    
    $response->assertStatus(403);
});

test('validation fails with too long state name', function () {
    $response = $this->actingAs($this->admin)
        ->from(route('admin.states.create'))
        ->post(route('admin.states.store'), [
            'name' => str_repeat('a', 256), // 256 characters, max is 255
        ]);
    
    $response->assertRedirect();
    $response->assertSessionHasErrors('name');
});

test('validation fails with invalid image type', function () {
    // Create a simple PDF file for testing
    $file = UploadedFile::fake()->create(
        'document.pdf', 
        100, // size in KB
        'application/pdf'
    );
    
    $response = $this->actingAs($this->admin)
        ->from(route('admin.states.create'))
        ->post(route('admin.states.store'), [
            'name' => 'Valid State Name',
            'featured_image' => $file,
        ]);
    
    $response->assertRedirect();
    $response->assertSessionHasErrors('featured_image');
});

test('validation fails with too large image', function () {
    $file = UploadedFile::fake()->image('large-image.jpg')->size(3000); // 3MB, max is 2MB
    
    $response = $this->actingAs($this->admin)
        ->from(route('admin.states.create'))
        ->post(route('admin.states.store'), [
            'name' => 'Valid State Name',
            'featured_image' => $file,
        ]);
    
    $response->assertRedirect();
    $response->assertSessionHasErrors('featured_image');
});