<?php

use App\Jobs\ProcessPincodeImport;
use App\Models\PinCode;
use App\Models\PincodeImport;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('ProcessPincodeImport Job', function () {
    
    it('processes CSV file successfully', function () {
        $user = User::factory()->admin()->create();
        
        $csvContent = "pincode,name,district,state,delivery_status\n110001,Connaught Place,New Delhi,Delhi,Delivery\n400001,Fort,Mumbai City,Maharashtra,Delivery";
        
        // Create a temporary file directly
        $tempFile = tempnam(sys_get_temp_dir(), 'test_csv_');
        file_put_contents($tempFile, $csvContent);
        
        $import = PincodeImport::create([
            'user_id' => $user->id,
            'filename' => 'test.csv',
            'file_path' => $tempFile,
            'status' => 'processing',
            'has_header' => true,
            'update_existing' => false,
        ]);

        $job = new ProcessPincodeImport($import);
        $job->handle();

        $import->refresh();

        expect($import->status)->toBe('completed');
        expect($import->total_records)->toBe(2);
        expect($import->successful_records)->toBe(2);
        expect($import->failed_records)->toBe(0);
        expect($import->has_errors)->toBe(false);

        $this->assertDatabaseHas('pin_codes', [
            'pincode' => '110001',
            'name' => 'Connaught Place',
            'district' => 'New Delhi',
            'state' => 'Delhi',
            'delivery_status' => 'Delivery'
        ]);

        $this->assertDatabaseHas('pin_codes', [
            'pincode' => '400001',
            'name' => 'Fort',
            'district' => 'Mumbai City',
            'state' => 'Maharashtra',
            'delivery_status' => 'Delivery'
        ]);

        // Clean up
        unlink($tempFile);
    });

    it('validates pincode format', function () {
        $user = User::factory()->admin()->create();
        
        $csvContent = "pincode,name,district,state,delivery_status\n12345,Invalid Pincode,District,State,Delivery";
        
        $tempFile = tempnam(sys_get_temp_dir(), 'test_csv_');
        file_put_contents($tempFile, $csvContent);
        
        $import = PincodeImport::create([
            'user_id' => $user->id,
            'filename' => 'test.csv',
            'file_path' => $tempFile,
            'status' => 'processing',
            'has_header' => true,
            'update_existing' => false,
        ]);

        $job = new ProcessPincodeImport($import);
        $job->handle();

        $import->refresh();

        expect($import->status)->toBe('completed');
        expect($import->total_records)->toBe(1);
        expect($import->successful_records)->toBe(0);
        expect($import->failed_records)->toBe(1);
        expect($import->has_errors)->toBe(true);

        $errors = json_decode($import->error_details, true);
        expect($errors[0]['error'])->toContain('Invalid pincode format');

        unlink($tempFile);
    });
}); 