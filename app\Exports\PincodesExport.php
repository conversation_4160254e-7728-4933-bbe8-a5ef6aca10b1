<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\HttpFoundation\StreamedResponse;

class PincodesExport
{
    protected $pincodes;

    public function __construct(Collection $pincodes)
    {
        $this->pincodes = $pincodes;
    }

    public function downloadAsExcel()
    {
        $response = new StreamedResponse(function () {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setCellValue('A1', 'Post Office');
            $sheet->setCellValue('B1', 'Pincode');
            $sheet->setCellValue('C1', 'District');
            $sheet->setCellValue('D1', 'State');
            $sheet->setCellValue('E1', 'Branch Type');
            $sheet->setCellValue('F1', 'Delivery');

            $rowNumber = 2; // Start in the second row
            foreach ($this->pincodes as $pincode) {
                $sheet->setCellValue('A' . $rowNumber, $pincode->name);
                $sheet->setCellValue('B' . $rowNumber, $pincode->pincode);
                $sheet->setCellValue('C' . $rowNumber, $pincode->district);
                $sheet->setCellValue('D' . $rowNumber, $pincode->state);
                $sheet->setCellValue('E' . $rowNumber, $pincode->branch_type);
                $sheet->setCellValue('F' . $rowNumber, $pincode->delivery_status);
                $rowNumber++;
            }

            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
        });

        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Content-Disposition', 'attachment; filename="pincodes-nskmultiservices-in.xlsx"');

        return $response;
    }
}
