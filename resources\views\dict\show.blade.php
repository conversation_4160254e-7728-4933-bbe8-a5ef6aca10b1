@extends('layouts.app')

@section('content')
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="mb-4">
            <a href="{{ route('courier_dict.index') }}"
                class="inline-flex items-center px-4 py-2 bg-gray-600 dark:bg-gray-700 text-white rounded-md hover:bg-gray-700 dark:hover:bg-gray-600 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Dictionary
            </a>
        </div>

        <div class="flex flex-col md:flex-row gap-6">
            <div class="md:w-2/3">
                <div class="bg-white dark:bg-bg-dark rounded-lg shadow overflow-hidden">
                    <div class="px-6 py-4 border-b border-border-light dark:border-border-dark">
                        <div class="flex items-center justify-between">
                            <h2 class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark">{{ $term->term }}</h2>
                            @if ($term->tag)
                                <span
                                    class="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-sm rounded-full">{{ $term->tag }}</span>
                            @endif
                        </div>
                    </div>
                    <div class="px-6 py-4">
                        <h3 class="text-lg font-medium text-text-secondary-light dark:text-text-secondary-dark mb-2">Description</h3>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark mb-6">{{ $term->description }}</p>

                        @if ($term->long_description)
                            <div class="border-t border-border-light dark:border-border-dark pt-4 mt-4">
                                <h3 class="text-lg font-medium text-text-secondary-light dark:text-text-secondary-dark mb-2">Detailed Information</h3>
                                <div class="text-text-secondary-light dark:text-text-secondary-dark whitespace-pre-line">
                                    {!! nl2br(e($term->long_description)) !!}
                                </div>
                            </div>
                        @endif
                    </div>
                    <div class="px-6 py-3 bg-bg-light dark:bg-gray-800 flex items-center justify-between">
                        {{-- <span class="text-sm text-gray-500">Term Slug: {{ $term->slug }}</span> --}}
                        <div class="flex items-center space-x-2">
                            {{-- <a href="{{ route('courier_dict.edit', $term->slug) }}" 
                           class="px-3 py-1 bg-yellow-500 text-white text-sm rounded hover:bg-yellow-600 transition-colors">
                            Edit
                        </a>
                        <form action="{{ route('courier_dict.destroy', $term->slug) }}" method="POST" class="inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600 transition-colors"
                                    onclick="return confirm('Are you sure you want to delete this term?')">
                                Delete
                            </button>
                        </form> --}}
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    @if (count($relatedTerms) > 0)
                        <div class="bg-white dark:bg-bg-dark rounded-lg shadow overflow-hidden">
                            <div class="px-6 py-3 border-b border-border-light dark:border-border-dark">
                                <h3 class="font-medium text-text-secondary-light dark:text-text-secondary-dark">Related Terms</h3>
                            </div>
                            <ul class="divide-y divide-border-light dark:divide-border-dark">
                                @foreach ($relatedTerms as $relatedTerm)
                                    <li class="px-6 py-3 hover:bg-bg-light dark:hover:bg-gray-800">
                                        <a href="{{ route('courier_dict.show', $relatedTerm->slug) }}"
                                            class="text-primary-light dark:text-primary-dark hover:text-blue-800 dark:hover:text-blue-400 hover:underline">
                                            {{ $relatedTerm->term }}
                                        </a>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    @if (count($randomRelatedTerms) > 0)
                        <div class="mt-8 bg-white dark:bg-bg-dark shadow-sm rounded-lg border border-border-light dark:border-border-dark">
                            <div class="px-4 py-3 border-b border-border-light dark:border-border-dark">
                                <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">You might also be interested in:</h3>
                            </div>
                            <div class="divide-y divide-border-light dark:divide-border-dark">
                                @foreach ($randomRelatedTerms as $randomTerm)
                                    <div class="p-4 hover:bg-bg-light dark:hover:bg-gray-800 transition duration-150">
                                        <a href="{{ route('courier_dict.show', $randomTerm->slug) }}"
                                            class="text-primary-light dark:text-primary-dark hover:text-blue-800 dark:hover:text-blue-400 font-medium text-base block mb-1 transition">
                                            {{ $randomTerm->term }}
                                        </a>
                                        <p class="text-text-secondary-light dark:text-text-secondary-dark text-sm line-clamp-2">{{ $randomTerm->description }}</p>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <div class="md:w-1/3">

            </div>
        </div>
    </div>
@endsection
