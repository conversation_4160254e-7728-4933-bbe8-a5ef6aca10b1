<?php

use App\Models\PinCode;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use function Pest\Faker\faker;

uses(RefreshDatabase::class);

// Tests for validatePincode
test('validate pincode endpoint fails without pincode', function () {
    $user = User::factory()->create();
    $response = $this->actingAs($user)->postJson('/api/validate-pincode', []);
    $response->assertStatus(422)
        ->assertJsonValidationErrors(['pincode']);
});

test('validate pincode endpoint fails with invalid pincode format', function () {
    $user = User::factory()->create();
    $response = $this->actingAs($user)->postJson('/api/validate-pincode', ['pincode' => '123']);
    $response->assertStatus(422)
        ->assertJsonValidationErrors(['pincode']);
});

test('validate pincode endpoint returns false for non-existent pincode', function () {
    $user = User::factory()->create();
    $response = $this->actingAs($user)->postJson('/api/validate-pincode', ['pincode' => '999999']);
    $response->assertStatus(200)
        ->assertJson(['valid' => false]);
});

test('validate pincode endpoint returns true for existing pincode', function () {
    $user = User::factory()->create();
    $pincode = PinCode::factory()->create(['pincode' => '123456']);

    $response = $this->actingAs($user)->postJson('/api/validate-pincode', ['pincode' => '123456']);
    $response->assertStatus(200)
        ->assertJson(['valid' => true]);
});


// Tests for calculateDistance
test('calculate distance endpoint fails without required pincodes', function () {
    $user = User::factory()->create();
    $response = $this->actingAs($user)->postJson('/api/calculate-distance', []);
    $response->assertStatus(422)
        ->assertJsonValidationErrors(['from_pincode', 'to_pincode'], 'error');
});

test('calculate distance endpoint fails with non-existent pincodes', function () {
    $user = User::factory()->create();
    $pincode = PinCode::factory()->create();

    $response = $this->actingAs($user)->postJson('/api/calculate-distance', [
        'from_pincode' => $pincode->pincode,
        'to_pincode' => '999999',
    ]);
    $response->assertStatus(422)
        ->assertJsonValidationErrors(['to_pincode'], 'error');
});

test('calculate distance endpoint successfully calculates distance', function () {
    $user = User::factory()->create();
    $fromPincode = PinCode::factory()->create([
        'pincode' => '110001',
        'latitude' => 28.6139,
        'longitude' => 77.2090,
    ]);
    $toPincode = PinCode::factory()->create([
        'pincode' => '400001',
        'latitude' => 19.0760,
        'longitude' => 72.8777,
    ]);

    $response = $this->actingAs($user)->postJson('/api/calculate-distance', [
        'from_pincode' => $fromPincode->pincode,
        'to_pincode' => $toPincode->pincode,
    ]);

    $response->assertStatus(200)
        ->assertJsonStructure([
            'from' => ['pincode', 'name', 'state'],
            'to' => ['pincode', 'name', 'state'],
            'distance' => ['km', 'miles', 'nautical_miles'],
            'bearing',
            'midpoint' => ['latitude', 'longitude'],
        ]);
}); 