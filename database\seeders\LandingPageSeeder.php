<?php

namespace Database\Seeders;

use App\Models\LandingPageSection;
use App\Models\LandingPageContent;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LandingPageSeeder extends Seeder
{
    public function run()
    {
        if (DB::getDriverName() !== 'sqlite') {
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        }

        // Clear existing data in correct order
        LandingPageContent::truncate();
        LandingPageSection::truncate();

        if (DB::getDriverName() !== 'sqlite') {
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        }

        //==========================================================================
        // HERO SECTION
        //==========================================================================
        $heroSection = LandingPageSection::firstOrCreate(
            ['slug' => 'hero'],
            [
                'name' => 'Hero Section',
                'is_active' => true,
                'sort_order' => 1,
            ]
        );

        LandingPageContent::firstOrCreate([
            'section_id' => $heroSection->id,
            'key' => 'heading',
        ], [
            'value' => '<span class="block mb-2">Discover</span>
                <span class="text-shadow-glow typing-animation block dark:accent-light accent-dark">Every Pincode</span>
                <span class="block">in India</span>',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $heroSection->id,
            'key' => 'subheading',
        ], [
            'value' => 'Instantly search, verify, and explore 155,000+ Indian pincodes with rich, verified data and interactive tools.',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $heroSection->id,
            'key' => 'cta_text',
        ], [
            'value' => 'Start Searching',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $heroSection->id,
            'key' => 'cta_link',
        ], [
            'value' => '#search-section',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $heroSection->id,
            'key' => 'second_cta_text',
        ], [
            'value' => 'See Features',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $heroSection->id,
            'key' => 'second_cta_link',
        ], [
            'value' => '#features',
            'type' => 'text',
        ]);

        LandingPageContent::create([
            'section_id' => $heroSection->id,
            'key' => 'hero_image',
            'value' => 'images/india-map.webp',
            'type' => 'image',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $heroSection->id,
            'key' => 'floating_txt_1',
        ], [
            'value' => '167K + Post Offices',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $heroSection->id,
            'key' => 'floating_txt_2',
        ], [
            'value' => '28 States & 8 UTs',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $heroSection->id,
            'key' => 'verified_by_1',
        ], [
            'value' => 'Verified by',
            'type' => 'text',
        ]);
        LandingPageContent::firstOrCreate([
            'section_id' => $heroSection->id,
            'key' => 'verified_by_2',
        ], [
            'value' => 'India Post',
            'type' => 'text',
        ]);
        //==========================================================================
        // FEATURES SECTION
        //==========================================================================
        $featuresSection = LandingPageSection::firstOrCreate([
            'slug' => 'features',
        ], [
            'name' => 'Features',
            'is_active' => true,
            'sort_order' => 4,
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $featuresSection->id,
            'key' => 'heading',
        ], [
            'value' => 'Everything you need for postal code information',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $featuresSection->id,
            'key' => 'features',
        ], [
            'value' => json_encode([
                [
                    'title' => 'Complete Coverage',
                    'description' => 'Comprehensive database covering all 155,000+ pincodes across India with detailed locality information.',
                    'icon' => '<svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" /></svg>'
                ],
                [
                    'title' => 'Verified Data',
                    'description' => 'All information is verified with official India Post records for maximum accuracy and reliability.',
                    'icon' => '<svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" /></svg>'
                ],
                [
                    'title' => 'Regular Updates',
                    'description' => 'We regularly update our database to ensure you have access to the most current postal information available in India.',
                    'icon' => '<svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" /></svg>'
                ],
                [
                    'title' => 'User Friendly',
                    'description' => 'Our easy-to-use interface makes finding pincode information simple and convenient for everyone.',
                    'icon' => '<svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" /></svg>'
                ],
                [
                    'title' => 'Rich Data',
                    'description' => 'Get detailed information including district, state, post office type, delivery status and more for each pincode.',
                    'icon' => '<svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" /></svg>'
                ],
                [
                    'title' => 'API Access',
                    'description' => 'Integrate our pincode data directly into your applications with our reliable and fast API solutions.',
                    'icon' => '<svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>'
                ],
            ]),
            'type' => 'repeater',
        ]);

        //==========================================================================
        // STATS SECTION
        //==========================================================================
        $statsSection = LandingPageSection::firstOrCreate([
            'slug' => 'stats',
        ], [
            'name' => 'Stats',
            'is_active' => true,
            'sort_order' => 3,
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $statsSection->id,
            'key' => 'heading',
        ], [
            'value' => 'Complete Coverage Across India',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $statsSection->id,
            'key' => 'states_count',
        ], [
            'value' => '36',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $statsSection->id,
            'key' => 'districts_count',
        ], [
            'value' => '700+',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $statsSection->id,
            'key' => 'delivery_offices_count',
        ], [
            'value' => '155,000+',
            'type' => 'text',
        ]);




        //==========================================================================
        // ABOUT SECTION
        //==========================================================================
        $aboutSection = LandingPageSection::create([
            'name' => 'About Us',
            'slug' => 'about',
            'is_active' => true,
            'sort_order' => 4,
        ]);

        LandingPageContent::create([
            'section_id' => $aboutSection->id,
            'key' => 'heading',
            'value' => 'Who We Are',
            'type' => 'text',
        ]);

        LandingPageContent::create([
            'section_id' => $aboutSection->id,
            'key' => 'content',
            'value' => 'We are a team of passionate experts dedicated to providing innovative solutions to businesses of all sizes. With years of experience in the industry, we understand the challenges you face and are committed to helping you overcome them.',
            'type' => 'textarea',
        ]);

        LandingPageContent::create([
            'section_id' => $aboutSection->id,
            'key' => 'image',
            'value' => 'landing-page/about-us.webp',
            'type' => 'image',
        ]);


        //==========================================================================
        // CTA SECTION
        //==========================================================================
        $ctaSection = LandingPageSection::firstOrCreate([
            'slug' => 'cta',
        ], [
            'name' => 'Call to Action',
            'is_active' => true,
            'sort_order' => 10,
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $ctaSection->id,
            'key' => 'heading',
        ], [
            'value' => 'Ready to get started?',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $ctaSection->id,
            'key' => 'subheading',
        ], [
            'value' => 'Get full access to our pincode directory with features to help you find and verify addresses across India.',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $ctaSection->id,
            'key' => 'cta_text',
        ], [
            'value' => 'Get Started Now',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $ctaSection->id,
            'key' => 'cta_link',
        ], [
            'value' => '#',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $ctaSection->id,
            'key' => 'background_color',
        ], [
            'value' => 'bg-gradient-to-r from-primary-light to-accent-light dark:from-bg-dark dark:to-primary-dark',
            'type' => 'text',
        ]);

        //==========================================================================
        // FAQ SECTION
        //==========================================================================
        $faqSection = LandingPageSection::firstOrCreate([
            'slug' => 'faq',
        ], [
            'name' => 'FAQ',
            'is_active' => true,
            'sort_order' => 9,
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $faqSection->id,
            'key' => 'heading',
        ], [
            'value' => 'Frequently Asked Questions',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $faqSection->id,
            'key' => 'faqs',
        ], [
            'value' => json_encode([
                [
                    'question' => 'How accurate is your pincode data?',
                    'answer' => 'All data is verified with India Post and updated regularly for maximum accuracy.'
                ],
                [
                    'question' => 'Can I download pincode data for my business?',
                    'answer' => 'Yes, you can download data in multiple formats or use our API for integration.'
                ],
                [
                    'question' => 'Is there a free plan available?',
                    'answer' => 'Yes, we offer a free plan with basic features. Upgrade for advanced access and API.'
                ],
            ]),
            'type' => 'repeater',
        ]);



        //==========================================================================
        // TOOLS SECTION
        //==========================================================================
        $toolsSection = LandingPageSection::firstOrCreate(
            ['slug' => 'tools'],
            [
                'name' => 'Tools',
                'is_active' => true,
                'sort_order' => 5,
            ]
        );

        LandingPageContent::firstOrCreate([
            'section_id' => $toolsSection->id,
            'key' => 'heading',
        ], [
            'value' => 'Quick Pincode Tools',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $toolsSection->id,
            'key' => 'subheading',
        ], [
            'value' => 'Access our most popular pincode search and verification tools',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $toolsSection->id,
            'key' => 'tools',
        ], [
            'value' => json_encode([
                [
                    'title' => 'Browse by State',
                    'description' => 'Explore pincodes organized by states and districts across India. Navigate through our hierarchical directory.',
                    'link' => '/pincodes',
                    'link_text' => 'View States',
                    'color' => 'primary',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />'
                ],
                [
                    'title' => 'Complete Pincode List',
                    'description' => 'Access the comprehensive list of all postal codes in India. Search, filter, and find any pincode instantly.',
                    'link' => '/india-postal-code-list',
                    'link_text' => 'View All Pincodes',
                    'color' => 'accent',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />'
                ],
                [
                    'title' => 'Download Pincodes',
                    'description' => 'Download pincode data for your business or personal use. Available in multiple formats for easy integration.',
                    'link' => '/tools/district-wise-pincode-download',
                    'link_text' => 'Download Options',
                    'color' => 'gradient',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />'
                ]
            ]),
            'type' => 'repeater',
        ]);

        // Add available icons for admin reference
        LandingPageContent::firstOrCreate([
            'section_id' => $toolsSection->id,
            'key' => 'available_icons',
        ], [
            'value' => json_encode([
                [
                    'name' => 'Map/Location',
                    'description' => 'Perfect for location-based tools',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />'
                ],
                [
                    'name' => 'Document/List',
                    'description' => 'Great for lists and documentation',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />'
                ],
                [
                    'name' => 'Download',
                    'description' => 'Ideal for download features',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />'
                ],
                [
                    'name' => 'Search',
                    'description' => 'Perfect for search functionality',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />'
                ],
                [
                    'name' => 'Database',
                    'description' => 'Great for data-related tools',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />'
                ],
                [
                    'name' => 'Globe/World',
                    'description' => 'Perfect for global/country features',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />'
                ],
                [
                    'name' => 'Chart/Analytics',
                    'description' => 'Great for analytics and reports',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />'
                ],
                [
                    'name' => 'Settings/Cog',
                    'description' => 'Perfect for configuration tools',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />'
                ],
                [
                    'name' => 'Shield/Security',
                    'description' => 'Great for security and verification',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />'
                ],
                [
                    'name' => 'Lightning/Fast',
                    'description' => 'Perfect for speed and performance',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />'
                ],
                [
                    'name' => 'Users/Team',
                    'description' => 'Great for team and collaboration tools',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />'
                ],
                [
                    'name' => 'Mail/Email',
                    'description' => 'Perfect for postal and email features',
                    'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />'
                ]
            ]),
            'type' => 'repeater',
        ]);

        //==========================================================================
        // SEARCH SECTION
        //==========================================================================
        $searchSection = LandingPageSection::firstOrCreate(
            ['slug' => 'search'],
            [
                'name' => 'Search Section',
                'is_active' => true,
                'sort_order' => 2,
            ]
        );

        LandingPageContent::firstOrCreate([
            'section_id' => $searchSection->id,
            'key' => 'heading',
        ], [
            'value' => 'Find Any Pincode In India',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $searchSection->id,
            'key' => 'subheading',
        ], [
            'value' => 'Enter a pincode, locality, district, or state to find detailed information',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $searchSection->id,
            'key' => 'popular_searches',
        ], [
            'value' => json_encode([
                ['name' => 'Delhi', 'link' => '/search?query=delhi&type=name'],
                ['name' => 'Mumbai', 'link' => '/search?query=mumbai&type=name'],
                ['name' => 'Bengaluru', 'link' => '/search?query=bengaluru&type=name'],
                ['name' => 'Hyderabad', 'link' => '/search?query=hyderabad&type=name'],
                ['name' => 'Chennai', 'link' => '/search?query=chennai&type=name'],
                ['name' => 'Kolkata', 'link' => '/search?query=kolkata&type=name']
            ]),
            'type' => 'repeater',
        ]);

        //==========================================================================
        // LATEST BLOG POSTS SECTION
        //==========================================================================
        $blogSection = LandingPageSection::firstOrCreate(
            ['slug' => 'latest-blog-posts'],
            [
                'name' => 'Latest Blog Posts',
                'is_active' => true,
                'sort_order' => 8,
            ]
        );

        LandingPageContent::firstOrCreate([
            'section_id' => $blogSection->id,
            'key' => 'heading',
        ], [
            'value' => 'Latest from Our Blog',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $blogSection->id,
            'key' => 'subheading',
        ], [
            'value' => 'Stay updated with the latest news, tips, and insights about postal services in India',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $blogSection->id,
            'key' => 'show_count',
        ], [
            'value' => '3',
            'type' => 'text',
        ]);

        //==========================================================================
        // PRICING SECTION
        //==========================================================================
        $pricingSection = LandingPageSection::firstOrCreate([
            'slug' => 'pricing',
        ], [
            'name' => 'Pricing',
            'is_active' => true,
            'sort_order' => 6,
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $pricingSection->id,
            'key' => 'heading',
        ], [
            'value' => 'Choose Your Plan',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $pricingSection->id,
            'key' => 'subheading',
        ], [
            'value' => 'Flexible plans for individuals, businesses, and enterprises',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $pricingSection->id,
            'key' => 'plans',
        ], [
            'value' => json_encode([
                [
                    'name' => 'Free',
                    'price' => 0,
                    'currency' => '₹',
                    'billing_period' => 'forever',
                    'description' => 'Perfect for personal use and small projects',
                    'features' => [
                        '100 API calls per month',
                        'Basic pincode search',
                        'Standard support',
                        'Web interface access',
                        'Basic data export'
                    ],
                    'cta_text' => 'Get Started',
                    'cta_link' => '/register',
                    'popular' => false,
                    'color' => 'gray'
                ],
                [
                    'name' => 'Professional',
                    'price' => 999,
                    'currency' => '₹',
                    'billing_period' => 'month',
                    'description' => 'Ideal for growing businesses and developers',
                    'features' => [
                        '10,000 API calls per month',
                        'Advanced search filters',
                        'Priority support',
                        'Bulk data download',
                        'API documentation',
                        'Custom integrations',
                        'Analytics dashboard'
                    ],
                    'cta_text' => 'Start Free Trial',
                    'cta_link' => '/plans',
                    'popular' => true,
                    'color' => 'primary'
                ],
                [
                    'name' => 'Enterprise',
                    'price' => null,
                    'currency' => '₹',
                    'billing_period' => 'custom',
                    'description' => 'Tailored solutions for large organizations',
                    'features' => [
                        'Unlimited API calls',
                        'Custom data solutions',
                        'Dedicated support manager',
                        'SLA guarantees',
                        'Custom integrations',
                        'Advanced analytics',
                        'White-label options',
                        'On-premise deployment'
                    ],
                    'cta_text' => 'Contact Sales',
                    'cta_link' => '/contact',
                    'popular' => false,
                    'color' => 'accent'
                ]
            ]),
            'type' => 'repeater',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $pricingSection->id,
            'key' => 'features_comparison',
        ], [
            'value' => json_encode([
                [
                    'feature' => 'API Calls per Month',
                    'free' => '100',
                    'professional' => '10,000',
                    'enterprise' => 'Unlimited'
                ],
                [
                    'feature' => 'Support Level',
                    'free' => 'Standard',
                    'professional' => 'Priority',
                    'enterprise' => 'Dedicated Manager'
                ],
                [
                    'feature' => 'Data Export',
                    'free' => 'Basic',
                    'professional' => 'Bulk Download',
                    'enterprise' => 'Custom Solutions'
                ],
                [
                    'feature' => 'SLA',
                    'free' => 'No',
                    'professional' => 'No',
                    'enterprise' => 'Yes'
                ],
                [
                    'feature' => 'Custom Integration',
                    'free' => 'No',
                    'professional' => 'Yes',
                    'enterprise' => 'Yes'
                ]
            ]),
            'type' => 'repeater',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $pricingSection->id,
            'key' => 'money_back_guarantee',
        ], [
            'value' => '30-day money-back guarantee on all paid plans',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $pricingSection->id,
            'key' => 'free_trial_text',
        ], [
            'value' => 'Start with a 14-day free trial. No credit card required.',
            'type' => 'text',
        ]);

        //==========================================================================
        // TESTIMONIALS SECTION
        //==========================================================================
        $testimonialsSection = LandingPageSection::firstOrCreate([
            'slug' => 'testimonials',
        ], [
            'name' => 'Testimonials',
            'is_active' => true,
            'sort_order' => 7,
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $testimonialsSection->id,
            'key' => 'heading',
        ], [
            'value' => 'What our users say',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $testimonialsSection->id,
            'key' => 'subheading',
        ], [
            'value' => 'Trusted by thousands of users across India for accurate pincode information',
            'type' => 'text',
        ]);

        LandingPageContent::firstOrCreate([
            'section_id' => $testimonialsSection->id,
            'key' => 'testimonials',
        ], [
            'value' => json_encode([
                [
                    'name' => 'Rajesh Kumar',
                    'designation' => 'E-commerce Business Owner',
                    'location' => 'Mumbai',
                    'content' => 'This pincode service has been a game-changer for our online delivery business. The accuracy and completeness of data helped us streamline our logistics operations significantly.',
                    'rating' => 5,
                    'avatar' => '/images/testimonials/rajesh.jpg'
                ],
                [
                    'name' => 'Priya Sharma',
                    'designation' => 'Logistics Manager',
                    'location' => 'Delhi',
                    'content' => 'We use this API for our courier service and it has never let us down. The data is always up-to-date and the API response time is excellent. Highly recommended!',
                    'rating' => 5,
                    'avatar' => '/images/testimonials/priya.jpg'
                ],
                [
                    'name' => 'Amit Patel',
                    'designation' => 'Software Developer',
                    'location' => 'Bangalore',
                    'content' => 'Integration was seamless and the documentation is very clear. The comprehensive coverage of all Indian pincodes makes this service invaluable for our application.',
                    'rating' => 5,
                    'avatar' => '/images/testimonials/amit.jpg'
                ],
                [
                    'name' => 'Sneha Reddy',
                    'designation' => 'Operations Head',
                    'location' => 'Hyderabad',
                    'content' => 'The verified data quality is outstanding. We have been using this service for over a year and it has consistently provided accurate postal information for our customers.',
                    'rating' => 5,
                    'avatar' => '/images/testimonials/sneha.jpg'
                ],
                [
                    'name' => 'Vikram Singh',
                    'designation' => 'Startup Founder',
                    'location' => 'Pune',
                    'content' => 'As a startup, we needed reliable pincode data without breaking the bank. This service provided exactly what we needed with excellent customer support.',
                    'rating' => 5,
                    'avatar' => '/images/testimonials/vikram.jpg'
                ],
                [
                    'name' => 'Meera Joshi',
                    'designation' => 'Digital Marketing Manager',
                    'location' => 'Chennai',
                    'content' => 'The user-friendly interface makes it easy for our non-technical team members to quickly find pincode information for our marketing campaigns.',
                    'rating' => 5,
                    'avatar' => '/images/testimonials/meera.jpg'
                ],
            ]),
            'type' => 'repeater',
        ]);


        // After services section, before the end of run() method
        $teamSection = LandingPageSection::firstOrCreate(
            ['slug' => 'team'],
            [
                'name' => 'Team',
                'is_active' => true,
                'sort_order' => 12,
            ]
        );

        // Add content for team section
        LandingPageContent::create([
            'section_id' => $teamSection->id,
            'key' => 'heading',
            'value' => 'Meet Our Team',
            'type' => 'text',
        ]);

        LandingPageContent::create([
            'section_id' => $teamSection->id,
            'key' => 'subheading',
            'value' => 'The talented people behind our success',
            'type' => 'text',
        ]);

        LandingPageContent::create([
            'section_id' => $teamSection->id,
            'key' => 'team_members',
            'value' => json_encode([
                [
                    'name' => 'John Smith',
                    'position' => 'CEO & Founder',
                    'bio' => 'With over 15 years of industry experience, John leads our company with vision and expertise.',
                    'photo' => 'landing-page/team/team1.png',
                    'social' => [
                        'linkedin' => 'https://linkedin.com/in/johnsmith',
                        'twitter' => 'https://twitter.com/johnsmith',
                        'github' => 'https://github.com/johnsmith'
                    ]
                ],
                [
                    'name' => 'Sarah Johnson',
                    'position' => 'CTO',
                    'bio' => 'Sarah oversees all technical aspects of the company, bringing innovation to our solutions.',
                    'photo' => 'landing-page/team/team2.png',
                    'social' => [
                        'linkedin' => 'https://linkedin.com/in/sarahjohnson',
                        'twitter' => 'https://twitter.com/sarahjohnson',
                        'github' => 'https://github.com/sarahjohnson'
                    ]
                ],
                [
                    'name' => 'Michael Chen',
                    'position' => 'Lead Developer',
                    'bio' => 'Michael is responsible for architecture and development of our core products.',
                    'photo' => 'landing-page/team/team3.png',
                    'social' => [
                        'linkedin' => 'https://linkedin.com/in/michaelchen',
                        'github' => 'https://github.com/michaelchen'
                    ]
                ],
                [
                    'name' => 'Emily Rodriguez',
                    'position' => 'UX/UI Designer',
                    'bio' => 'Emily creates beautiful, intuitive interfaces that delight our users.',
                    'photo' => 'landing-page/team/team4.png',
                    'social' => [
                        'linkedin' => 'https://linkedin.com/in/emilyrodriguez',
                        'dribbble' => 'https://dribbble.com/emilyrodriguez'
                    ]
                ]
            ]),
            'type' => 'repeater',
        ]);
    }
}

// php artisan db:seed --class=LandingPageSeeder