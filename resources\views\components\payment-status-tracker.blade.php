@props([
    'payment',
    'showDetails' => true,
    'autoRefresh' => true,
    'refreshInterval' => 30000
])

<div class="payment-status-tracker" data-payment-id="{{ $payment->id }}" 
     data-auto-refresh="{{ $autoRefresh ? 'true' : 'false' }}" 
     data-refresh-interval="{{ $refreshInterval }}">
    
    <!-- Status Header -->
    <div class="status-header d-flex align-items-center justify-content-between p-3 rounded mb-3" 
         style="background: {{ $payment->getStatusBackgroundColor() }};">
        <div class="d-flex align-items-center">
            <div class="status-icon me-3">
                @switch($payment->payment_status)
                    @case('pending')
                        <div class="spinner-border spinner-border-sm text-warning" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        @break
                    @case('completed')
                        <i class="fas fa-check-circle text-success fa-lg"></i>
                        @break
                    @case('failed')
                        <i class="fas fa-times-circle text-danger fa-lg"></i>
                        @break
                    @case('cancelled')
                        <i class="fas fa-ban text-secondary fa-lg"></i>
                        @break
                    @default
                        <i class="fas fa-clock text-muted fa-lg"></i>
                @endswitch
            </div>
            <div>
                <h6 class="mb-1 status-title">{{ $payment->getStatusDisplayName() }}</h6>
                <p class="mb-0 small text-muted status-message">{{ $payment->getStatusMessage() }}</p>
            </div>
        </div>
        
        @if($autoRefresh)
        <button type="button" class="btn btn-outline-primary btn-sm refresh-btn" title="Refresh Status">
            <i class="fas fa-sync-alt"></i>
        </button>
        @endif
    </div>

    @if($showDetails)
    <!-- Payment Details -->
    <div class="payment-details">
        <div class="row">
            <div class="col-md-6">
                <div class="detail-item mb-2">
                    <small class="text-muted">Payment Method:</small>
                    <div class="fw-medium">{{ $payment->getPaymentMethodDisplayName() }}</div>
                </div>
                <div class="detail-item mb-2">
                    <small class="text-muted">Amount:</small>
                    <div class="fw-medium">{{ $payment->currency }} {{ number_format($payment->amount, 2) }}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="detail-item mb-2">
                    <small class="text-muted">Created:</small>
                    <div class="fw-medium">{{ $payment->created_at->format('M d, Y H:i') }}</div>
                </div>
                @if($payment->paid_at)
                <div class="detail-item mb-2">
                    <small class="text-muted">Completed:</small>
                    <div class="fw-medium">{{ $payment->paid_at->format('M d, Y H:i') }}</div>
                </div>
                @endif
            </div>
        </div>

        @if($payment->gateway_payment_id)
        <div class="detail-item mt-2">
            <small class="text-muted">Transaction ID:</small>
            <div class="fw-medium">
                <code>{{ $payment->gateway_payment_id }}</code>
                <button type="button" class="btn btn-link btn-sm p-0 ms-2 copy-btn" 
                        data-copy="{{ $payment->gateway_payment_id }}" title="Copy Transaction ID">
                    <i class="fas fa-copy"></i>
                </button>
            </div>
        </div>
        @endif

        @if($payment->failed_reason)
        <div class="detail-item mt-2">
            <small class="text-muted">Failure Reason:</small>
            <div class="text-danger">{{ $payment->failed_reason }}</div>
        </div>
        @endif
    </div>

    <!-- Payment Proof Status (for QR payments) -->
    @if($payment->payment_method === 'qr_bank_transfer')
    <div class="proof-status mt-3">
        <h6 class="mb-2">
            <i class="fas fa-file-upload me-2"></i>
            Payment Proof Status
        </h6>
        
        @php
            $latestProof = $payment->paymentProofs()->latest()->first();
        @endphp
        
        @if($latestProof)
        <div class="proof-item p-3 border rounded">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="proof-icon me-3">
                        @switch($latestProof->verification_status)
                            @case('pending')
                                <i class="fas fa-clock text-warning"></i>
                                @break
                            @case('approved')
                                <i class="fas fa-check-circle text-success"></i>
                                @break
                            @case('rejected')
                                <i class="fas fa-times-circle text-danger"></i>
                                @break
                        @endswitch
                    </div>
                    <div>
                        <div class="fw-medium">{{ $latestProof->file_name }}</div>
                        <small class="text-muted">
                            {{ ucfirst($latestProof->verification_status) }} • 
                            Uploaded {{ $latestProof->created_at->diffForHumans() }}
                        </small>
                    </div>
                </div>
                <div class="proof-actions">
                    <a href="{{ route('user.payment.proof.download', $latestProof) }}" 
                       class="btn btn-outline-primary btn-sm" title="Download">
                        <i class="fas fa-download"></i>
                    </a>
                </div>
            </div>
            
            @if($latestProof->verification_notes)
            <div class="proof-notes mt-2 p-2 bg-light rounded">
                <small class="text-muted">Admin Notes:</small>
                <div class="small">{{ $latestProof->verification_notes }}</div>
            </div>
            @endif
        </div>
        @else
        <div class="no-proof text-center p-3 border border-dashed rounded">
            <i class="fas fa-upload fa-2x text-muted mb-2"></i>
            <p class="text-muted mb-0">No payment proof uploaded yet</p>
        </div>
        @endif
    </div>
    @endif
    @endif

    <!-- Progress Timeline (for detailed view) -->
    @if($showDetails && $payment->payment_method === 'qr_bank_transfer')
    <div class="payment-timeline mt-4">
        <h6 class="mb-3">
            <i class="fas fa-history me-2"></i>
            Payment Timeline
        </h6>
        
        <div class="timeline">
            <!-- Order Created -->
            <div class="timeline-item completed">
                <div class="timeline-marker">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="timeline-content">
                    <h6 class="mb-1">Order Created</h6>
                    <small class="text-muted">{{ $payment->order->created_at->format('M d, Y H:i') }}</small>
                </div>
            </div>

            <!-- Payment Initiated -->
            <div class="timeline-item completed">
                <div class="timeline-marker">
                    <i class="fas fa-qrcode"></i>
                </div>
                <div class="timeline-content">
                    <h6 class="mb-1">QR Code Generated</h6>
                    <small class="text-muted">{{ $payment->created_at->format('M d, Y H:i') }}</small>
                </div>
            </div>

            <!-- Payment Proof -->
            @if($latestProof)
            <div class="timeline-item completed">
                <div class="timeline-marker">
                    <i class="fas fa-upload"></i>
                </div>
                <div class="timeline-content">
                    <h6 class="mb-1">Payment Proof Uploaded</h6>
                    <small class="text-muted">{{ $latestProof->created_at->format('M d, Y H:i') }}</small>
                </div>
            </div>
            @endif

            <!-- Verification -->
            <div class="timeline-item {{ $payment->payment_status === 'completed' ? 'completed' : ($latestProof ? 'active' : 'pending') }}">
                <div class="timeline-marker">
                    @if($payment->payment_status === 'completed')
                        <i class="fas fa-check"></i>
                    @elseif($latestProof)
                        <div class="spinner-border spinner-border-sm" role="status"></div>
                    @else
                        <i class="fas fa-clock"></i>
                    @endif
                </div>
                <div class="timeline-content">
                    <h6 class="mb-1">Payment Verification</h6>
                    <small class="text-muted">
                        @if($payment->payment_status === 'completed')
                            Verified {{ $payment->verified_at?->format('M d, Y H:i') }}
                        @elseif($latestProof)
                            Under review...
                        @else
                            Waiting for payment proof
                        @endif
                    </small>
                </div>
            </div>

            <!-- Completion -->
            <div class="timeline-item {{ $payment->payment_status === 'completed' ? 'completed' : 'pending' }}">
                <div class="timeline-marker">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="timeline-content">
                    <h6 class="mb-1">Order Completed</h6>
                    <small class="text-muted">
                        @if($payment->payment_status === 'completed')
                            {{ $payment->paid_at?->format('M d, Y H:i') }}
                        @else
                            Pending verification
                        @endif
                    </small>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

@push('styles')
<style>
.payment-status-tracker .status-header {
    border-left: 4px solid currentColor;
}

.payment-status-tracker .timeline {
    position: relative;
    padding-left: 2rem;
}

.payment-status-tracker .timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.payment-status-tracker .timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.payment-status-tracker .timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: #fff;
    border: 2px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
}

.payment-status-tracker .timeline-item.completed .timeline-marker {
    background: #28a745;
    border-color: #28a745;
    color: white;
}

.payment-status-tracker .timeline-item.active .timeline-marker {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.payment-status-tracker .timeline-item.pending .timeline-marker {
    background: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
}

.payment-status-tracker .copy-btn {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.payment-status-tracker .copy-btn:hover {
    opacity: 1;
}

.payment-status-tracker .proof-item {
    transition: all 0.3s ease;
}

.payment-status-tracker .proof-item:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .payment-status-tracker .timeline {
        padding-left: 1.5rem;
    }
    
    .payment-status-tracker .timeline-marker {
        left: -1.5rem;
        width: 1.5rem;
        height: 1.5rem;
    }
    
    .payment-status-tracker .timeline::before {
        left: 0.75rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const tracker = document.querySelector('.payment-status-tracker');
    if (!tracker) return;
    
    const paymentId = tracker.dataset.paymentId;
    const autoRefresh = tracker.dataset.autoRefresh === 'true';
    const refreshInterval = parseInt(tracker.dataset.refreshInterval) || 30000;
    
    // Copy functionality
    document.querySelectorAll('.copy-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const textToCopy = this.dataset.copy;
            navigator.clipboard.writeText(textToCopy).then(() => {
                // Show success feedback
                const icon = this.querySelector('i');
                const originalClass = icon.className;
                icon.className = 'fas fa-check text-success';
                
                setTimeout(() => {
                    icon.className = originalClass;
                }, 2000);
            });
        });
    });
    
    // Refresh functionality
    const refreshBtn = tracker.querySelector('.refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            refreshPaymentStatus();
        });
    }
    
    // Auto-refresh
    if (autoRefresh) {
        setInterval(refreshPaymentStatus, refreshInterval);
    }
    
    function refreshPaymentStatus() {
        const refreshIcon = tracker.querySelector('.refresh-btn i');
        if (refreshIcon) {
            refreshIcon.classList.add('fa-spin');
        }
        
        fetch(`/payment/status/${paymentId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStatusDisplay(data);
                }
            })
            .catch(error => {
                console.error('Status refresh error:', error);
            })
            .finally(() => {
                if (refreshIcon) {
                    refreshIcon.classList.remove('fa-spin');
                }
            });
    }
    
    function updateStatusDisplay(data) {
        // Update status based on response
        // This would update the UI elements based on the new status
        if (data.status === 'completed') {
            // Redirect to success page or update UI
            window.location.reload();
        }
    }
});
</script>
@endpush