<?php if(isset($landingPage['tools']) && $landingPage['tools']['active']): ?>
        <section id="tools"
            class="py-20 bg-gradient-to-b from-bg-light to-white dark:from-bg-dark dark:to-gray-800 transition-colors duration-300">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center max-w-3xl mx-auto mb-16">
                    <span
                        class="inline-block px-3 py-1 rounded-full bg-primary-light text-white dark:bg-primary-dark dark:text-white text-xs font-semibold tracking-wider uppercase mb-3"><?php echo e($landingPage['tools']['content']['badge_text'] ?? 'Tools'); ?></span>
                    <h2
                        class="text-3xl md:text-4xl font-extrabold mb-4 text-text-primary-light dark:text-text-primary-dark">
                        <?php echo e($landingPage['tools']['content']['heading'] ?? 'Quick Pincode Tools'); ?></h2>
                    <p class="text-lg text-text-secondary-light dark:text-text-secondary-dark"><?php echo e($landingPage['tools']['content']['subheading'] ?? 'Access our most popular pincode search and verification tools'); ?></p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <?php $__currentLoopData = $landingPage['tools']['content']['tools'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tool): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 card-hover border border-border-light dark:border-border-dark">
                            <?php if($tool['color'] === 'primary'): ?>
                                <div class="h-3 bg-gradient-to-r from-primary-light to-accent-light dark:from-primary-dark dark:to-accent-dark"></div>
                            <?php elseif($tool['color'] === 'accent'): ?>
                                <div class="h-3 bg-accent-light dark:bg-accent-dark"></div>
                            <?php else: ?>
                                <div class="h-3 bg-gradient-to-r from-accent-light to-primary-light dark:from-accent-dark dark:to-primary-dark"></div>
                            <?php endif; ?>
                            
                            <div class="p-6">
                                <div class="flex items-center justify-center h-12 w-12 rounded-full <?php echo e($tool['color'] === 'accent' ? 'bg-accent-light/10 dark:bg-accent-dark/20 text-accent-light dark:text-accent-dark' : 'bg-primary-light/10 dark:bg-primary-dark/20 text-primary-light dark:text-primary-dark'); ?> mb-5">
                                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <?php echo $tool['icon'] ?? '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />'; ?>

                                    </svg>
                                </div>
                                <h3 class="text-xl font-bold text-text-primary-light dark:text-text-primary-dark mb-2">
                                    <?php echo e($tool['title']); ?>

                                </h3>
                                <p class="text-text-secondary-light dark:text-text-secondary-dark mb-6">
                                    <?php echo e($tool['description']); ?>

                                </p>
                                <a href="<?php echo e($tool['link']); ?>" class="inline-flex items-center <?php echo e($tool['color'] === 'accent' ? 'text-accent-light dark:text-accent-dark hover:text-accent-light/80 dark:hover:text-accent-dark/80' : 'text-primary-light dark:text-primary-dark hover:text-primary-light/80 dark:hover:text-primary-dark/80'); ?> transition-colors">
                                    <?php echo e($tool['link_text']); ?>

                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </section>
    <?php endif; ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/home/<USER>/tools-section.blade.php ENDPATH**/ ?>