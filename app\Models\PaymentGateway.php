<?php

namespace App\Models;

use App\Services\Payment\CredentialEncryptionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Facades\Log;

class PaymentGateway extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'display_name',
        'description',
        'is_active',
        'is_default',
        'configuration',
        'supported_currencies',
        'sort_order',
        'webhook_url',
        'webhook_secret',
        'logo_url',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'configuration' => 'array',
        'supported_currencies' => 'array',
        'sort_order' => 'integer',
    ];

    /**
     * Get the encrypted configuration attribute.
     */
    protected function configuration(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                if (empty($value)) {
                    return [];
                }

                try {
                    $decrypted = json_decode($value, true);
                    if (!$decrypted) {
                        return [];
                    }

                    $encryptionService = app(CredentialEncryptionService::class);
                    return $encryptionService->decryptConfiguration($decrypted);
                } catch (\Exception $e) {
                    Log::error('Failed to decrypt gateway configuration', [
                        'gateway_id' => $this->id,
                        'error' => $e->getMessage()
                    ]);
                    return [];
                }
            },
            set: function ($value) {
                if (!is_array($value)) {
                    return $value;
                }

                try {
                    $encryptionService = app(CredentialEncryptionService::class);
                    $encrypted = $encryptionService->encryptConfiguration($value);
                    return json_encode($encrypted);
                } catch (\Exception $e) {
                    Log::error('Failed to encrypt gateway configuration', [
                        'gateway_id' => $this->id ?? 'new',
                        'error' => $e->getMessage()
                    ]);
                    throw $e;
                }
            }
        );
    }

    /**
     * Get the encrypted webhook secret attribute.
     */
    protected function webhookSecret(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                if (empty($value)) {
                    return null;
                }

                try {
                    $encryptionService = app(CredentialEncryptionService::class);
                    return $encryptionService->decryptValue($value);
                } catch (\Exception $e) {
                    Log::error('Failed to decrypt webhook secret', [
                        'gateway_id' => $this->id,
                        'error' => $e->getMessage()
                    ]);
                    return null;
                }
            },
            set: function ($value) {
                if (empty($value)) {
                    return null;
                }

                try {
                    $encryptionService = app(CredentialEncryptionService::class);
                    return $encryptionService->encryptValue($value);
                } catch (\Exception $e) {
                    Log::error('Failed to encrypt webhook secret', [
                        'gateway_id' => $this->id ?? 'new',
                        'error' => $e->getMessage()
                    ]);
                    throw $e;
                }
            }
        );
    }

    /**
     * Scope to get only active gateways.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get gateways ordered by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('display_name');
    }

    /**
     * Get the default payment gateway.
     */
    public static function getDefault()
    {
        return static::where('is_default', true)->where('is_active', true)->first();
    }

    /**
     * Get active gateways for display.
     */
    public static function getActiveGateways()
    {
        return static::active()->ordered()->get();
    }

    /**
     * Check if gateway supports a specific currency.
     */
    public function supportsCurrency($currency)
    {
        return in_array(strtoupper($currency), array_map('strtoupper', $this->supported_currencies));
    }

    /**
     * Get configuration value by key.
     */
    public function getConfigValue($key, $default = null)
    {
        return data_get($this->configuration, $key, $default);
    }

    /**
     * Set configuration value by key.
     */
    public function setConfigValue($key, $value)
    {
        $config = $this->configuration;
        data_set($config, $key, $value);
        $this->configuration = $config;
        return $this;
    }

    /**
     * Check if gateway is properly configured.
     */
    public function isConfigured()
    {
        $config = $this->configuration;

        switch (true) {
            case str_starts_with($this->name, 'paypal'):
                return !empty($config['sandbox_client_id']) || !empty($config['live_client_id']);

            case str_starts_with($this->name, 'razorpay'):
                return !empty($config['key_id']) && !empty($config['key_secret']);

            case str_starts_with($this->name, 'qr_bank_transfer'):
                return !empty($config['account_number']) && !empty($config['account_name']);

            default:
                return !empty($config);
        }
    }

    /**
     * Get gateway credentials for API usage.
     */
    public function getCredentials()
    {
        $config = $this->configuration;

        switch ($this->name) {
            case 'paypal':
                $mode = $config['mode'] ?? 'sandbox';
                return [
                    'client_id' => $config["{$mode}_client_id"] ?? '',
                    'client_secret' => $config["{$mode}_client_secret"] ?? '',
                    'mode' => $mode,
                    'currency' => $config['currency'] ?? 'USD',
                ];

            case 'razorpay':
                return [
                    'key_id' => $config['key_id'] ?? '',
                    'key_secret' => $config['key_secret'] ?? '',
                    'webhook_secret' => $config['webhook_secret'] ?? '',
                    'currency' => $config['currency'] ?? 'INR',
                ];

            case 'qr_bank_transfer':
                return [
                    'bank_name' => $config['bank_name'] ?? '',
                    'account_name' => $config['account_name'] ?? '',
                    'account_number' => $config['account_number'] ?? '',
                    'ifsc_code' => $config['ifsc_code'] ?? '',
                    'branch_name' => $config['branch_name'] ?? '',
                    'currency' => $config['currency'] ?? 'INR',
                ];

            default:
                return $config;
        }
    }

    /**
     * Set as default gateway (unsets other defaults).
     */
    public function setAsDefault()
    {
        // Unset other defaults
        static::where('is_default', true)->update(['is_default' => false]);

        // Set this as default
        $this->update(['is_default' => true]);

        return $this;
    }

    /**
     * Toggle gateway status.
     */
    public function toggleStatus()
    {
        $this->update(['is_active' => !$this->is_active]);
        return $this;
    }

    /**
     * Get gateway logo URL with fallback.
     */
    public function getLogoUrlAttribute($value)
    {
        if ($value && file_exists(public_path($value))) {
            return asset($value);
        }

        // Fallback to default gateway icon
        return asset('images/gateways/default-gateway.png');
    }

    /**
     * Relationship with payments.
     */
    public function payments()
    {
        return $this->hasMany(Payment::class, 'gateway_id');
    }

    /**
     * Relationship with webhook logs.
     */
    public function webhookLogs()
    {
        return $this->hasMany(WebhookLog::class, 'gateway_id');
    }

    /**
     * Get gateway statistics.
     */
    public function getStats($dateFrom = null, $dateTo = null)
    {
        $query = $this->payments();

        if ($dateFrom) {
            $query->where('created_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->where('created_at', '<=', $dateTo);
        }

        return [
            'total_payments' => $query->count(),
            'successful_payments' => $query->where('payment_status', 'completed')->count(),
            'failed_payments' => $query->where('payment_status', 'failed')->count(),
            'pending_payments' => $query->where('payment_status', 'pending')->count(),
            'total_amount' => $query->where('payment_status', 'completed')->sum('amount'),
            'success_rate' => $query->count() > 0 ?
                round(($query->where('payment_status', 'completed')->count() / $query->count()) * 100, 2) : 0,
        ];
    }

    /**
     * Validate gateway configuration.
     */
    public function validateConfiguration()
    {
        $errors = [];
        $config = $this->configuration;

        switch ($this->name) {
            case 'paypal':
                $mode = $config['mode'] ?? 'sandbox';
                if (empty($config["{$mode}_client_id"])) {
                    $errors[] = ucfirst($mode) . ' Client ID is required';
                }
                if (empty($config["{$mode}_client_secret"])) {
                    $errors[] = ucfirst($mode) . ' Client Secret is required';
                }
                break;

            case 'razorpay':
                if (empty($config['key_id'])) {
                    $errors[] = 'Razorpay Key ID is required';
                }
                if (empty($config['key_secret'])) {
                    $errors[] = 'Razorpay Key Secret is required';
                }
                break;

            case 'qr_bank_transfer':
                if (empty($config['account_name'])) {
                    $errors[] = 'Account Name is required';
                }
                if (empty($config['account_number'])) {
                    $errors[] = 'Account Number is required';
                }
                if (empty($config['ifsc_code'])) {
                    $errors[] = 'IFSC Code is required';
                }
                break;
        }

        return $errors;
    }

    /**
     * Get masked configuration for display purposes
     */
    public function getMaskedConfiguration(): array
    {
        $config = $this->configuration;
        $encryptionService = app(CredentialEncryptionService::class);
        $masked = [];

        foreach ($config as $key => $value) {
            if ($encryptionService->isSensitiveField($key)) {
                $masked[$key] = $encryptionService->maskValue($value);
            } else {
                $masked[$key] = $value;
            }
        }

        return $masked;
    }

    /**
     * Validate configuration integrity
     */
    public function validateConfigurationIntegrity(): array
    {
        try {
            $rawConfig = json_decode($this->getRawOriginal('configuration'), true);
            if (!$rawConfig) {
                return [
                    'valid' => false,
                    'errors' => ['Configuration data is empty or invalid'],
                    'warnings' => []
                ];
            }

            $encryptionService = app(CredentialEncryptionService::class);
            return $encryptionService->validateConfiguration($rawConfig);
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'errors' => ['Failed to validate configuration: ' . $e->getMessage()],
                'warnings' => []
            ];
        }
    }

    /**
     * Test gateway connection with current credentials
     */
    public function testConnection(): array
    {
        try {
            $credentials = $this->getCredentials();

            // Basic validation
            $validation = $this->validateConfiguration();
            if (!empty($validation)) {
                return [
                    'success' => false,
                    'message' => 'Configuration validation failed: ' . implode(', ', $validation),
                    'details' => ['validation_errors' => $validation]
                ];
            }

            // Gateway-specific connection tests
            switch ($this->name) {
                case 'paypal':
                    return $this->testPayPalConnection($credentials);
                case 'razorpay':
                    return $this->testRazorpayConnection($credentials);
                case 'qr_bank_transfer':
                    return $this->testQRBankTransferConnection($credentials);
                default:
                    return [
                        'success' => true,
                        'message' => 'Configuration appears valid',
                        'details' => ['note' => 'No specific connection test available for this gateway type']
                    ];
            }
        } catch (\Exception $e) {
            Log::error('Gateway connection test failed', [
                'gateway_id' => $this->id,
                'gateway_name' => $this->name,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage(),
                'details' => ['exception' => get_class($e)]
            ];
        }
    }

    /**
     * Test PayPal connection
     */
    private function testPayPalConnection(array $credentials): array
    {
        if (empty($credentials['client_id']) || empty($credentials['client_secret'])) {
            return [
                'success' => false,
                'message' => 'PayPal credentials are incomplete',
                'details' => ['missing' => 'client_id or client_secret']
            ];
        }

        // Here you would implement actual PayPal API test
        // For now, return success if credentials are present
        return [
            'success' => true,
            'message' => 'PayPal configuration is valid',
            'details' => [
                'mode' => $credentials['mode'],
                'currency' => $credentials['currency']
            ]
        ];
    }

    /**
     * Test Razorpay connection
     */
    private function testRazorpayConnection(array $credentials): array
    {
        if (empty($credentials['key_id']) || empty($credentials['key_secret'])) {
            return [
                'success' => false,
                'message' => 'Razorpay credentials are incomplete',
                'details' => ['missing' => 'key_id or key_secret']
            ];
        }

        // Here you would implement actual Razorpay API test
        // For now, return success if credentials are present
        return [
            'success' => true,
            'message' => 'Razorpay configuration is valid',
            'details' => [
                'key_id' => substr($credentials['key_id'], 0, 8) . '...',
                'currency' => $credentials['currency']
            ]
        ];
    }

    /**
     * Test QR Bank Transfer connection
     */
    private function testQRBankTransferConnection(array $credentials): array
    {
        $required = ['account_name', 'account_number', 'ifsc_code'];
        $missing = [];

        foreach ($required as $field) {
            if (empty($credentials[$field])) {
                $missing[] = $field;
            }
        }

        if (!empty($missing)) {
            return [
                'success' => false,
                'message' => 'QR Bank Transfer configuration is incomplete',
                'details' => ['missing' => $missing]
            ];
        }

        return [
            'success' => true,
            'message' => 'QR Bank Transfer configuration is valid',
            'details' => [
                'bank_name' => $credentials['bank_name'] ?? 'Not specified',
                'currency' => $credentials['currency']
            ]
        ];
    }

    /**
     * Export gateway configuration for backup
     */
    public function exportConfiguration(): array
    {
        $encryptionService = app(CredentialEncryptionService::class);
        $rawConfig = json_decode($this->getRawOriginal('configuration'), true);

        return $encryptionService->exportConfiguration($rawConfig ?? []);
    }

    /**
     * Import gateway configuration from backup
     */
    public function importConfiguration(array $exportedData): bool
    {
        try {
            $encryptionService = app(CredentialEncryptionService::class);
            $configuration = $encryptionService->importConfiguration($exportedData);

            // Update the configuration
            $this->update(['configuration' => $configuration]);

            Log::info('Gateway configuration imported successfully', [
                'gateway_id' => $this->id,
                'gateway_name' => $this->name
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to import gateway configuration', [
                'gateway_id' => $this->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Rotate encryption keys for this gateway
     */
    public function rotateEncryption(): bool
    {
        try {
            $encryptionService = app(CredentialEncryptionService::class);
            $rawConfig = json_decode($this->getRawOriginal('configuration'), true);

            if (!$rawConfig) {
                return true; // Nothing to rotate
            }

            $rotated = $encryptionService->rotateEncryption($rawConfig);
            $this->update(['configuration' => $rotated]);

            Log::info('Gateway encryption rotated successfully', [
                'gateway_id' => $this->id,
                'gateway_name' => $this->name
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to rotate gateway encryption', [
                'gateway_id' => $this->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Clean up sensitive data from memory after use
     */
    public function cleanupSensitiveData(): void
    {
        $encryptionService = app(CredentialEncryptionService::class);
        $config = $this->configuration;
        $encryptionService->cleanupSensitiveData($config);
    }
}
