<?php

use App\Http\Controllers\Admin\PaymentController;
use App\Models\Payment;
use App\Models\Order;
use App\Models\User;
use App\Models\Plan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create a user for authentication (assuming admin authentication is required)
    $this->user = User::factory()->create(['role' => 'admin', 'status' => 'active']);
    $this->actingAs($this->user);
    
    // Create test data
    $this->plan = Plan::factory()->create();
    $this->order = Order::factory()->create(['plan_id' => $this->plan->id]);
    $this->payment = Payment::factory()->create(['order_id' => $this->order->id]);
});

describe('PaymentController Index Method', function () {
    it('displays payments with pagination', function () {
        // Create additional payments for pagination testing
        Payment::factory()->count(15)->create();
        
        $response = $this->get(route('admin.payments.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.payments.index');
        $response->assertViewHas('payments');
        
        // Verify pagination
        $payments = $response->viewData('payments');
        expect($payments)->toBeInstanceOf(LengthAwarePaginator::class);
        expect($payments->perPage())->toBe(10);
    });
    
    it('loads payments with correct relationships', function () {
        $response = $this->get(route('admin.payments.index'));
        
        $payments = $response->viewData('payments');
        
        // Verify relationships are loaded
        foreach ($payments as $payment) {
            expect($payment->relationLoaded('order'))->toBeTrue();
            expect($payment->order->relationLoaded('user'))->toBeTrue();
            expect($payment->order->relationLoaded('plan'))->toBeTrue();
        }
    });
    
    it('orders payments by latest first', function () {
        // Create payments with different timestamps
        $oldPayment = Payment::factory()->create(['created_at' => now()->subDays(2)]);
        $newPayment = Payment::factory()->create(['created_at' => now()]);
        
        $response = $this->get(route('admin.payments.index'));
        $payments = $response->viewData('payments');
        
        // First payment should be the newest
        expect($payments->first()->created_at->timestamp)
            ->toBeGreaterThan($payments->last()->created_at->timestamp);
    });
    
    it('handles empty payments list', function () {
        Payment::query()->delete();
        
        $response = $this->get(route('admin.payments.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.payments.index');
        
        $payments = $response->viewData('payments');
        expect($payments->count())->toBe(0);
    });
});

describe('PaymentController Show Method', function () {
    it('displays specific payment with relationships', function () {
        $response = $this->get(route('admin.payments.show', $this->payment));
        
        $response->assertStatus(200);
        $response->assertViewIs('admin.payments.show');
        $response->assertViewHas('payment');
        
        $payment = $response->viewData('payment');
        expect($payment->id)->toBe($this->payment->id);
        
        // Verify relationships are loaded
        expect($payment->relationLoaded('order'))->toBeTrue();
        expect($payment->order->relationLoaded('user'))->toBeTrue();
        expect($payment->order->relationLoaded('plan'))->toBeTrue();
    });
    
    it('handles non-existent payment', function () {
        $response = $this->get(route('admin.payments.show', 99999));
        
        $response->assertStatus(404);
    });
});

describe('PaymentController Update Method', function () {
    it('updates payment status to completed', function () {
        $response = $this->patch(route('admin.payments.update', $this->payment), [
            'payment_status' => Payment::STATUS_COMPLETED
        ]);
        
        $response->assertRedirect(route('admin.payments.show', $this->payment));
        $response->assertSessionHas('success', 'Payment status updated successfully.');
        
        $this->payment->refresh();
        expect($this->payment->payment_status)->toBe(Payment::STATUS_COMPLETED);
    });
    
    it('updates payment status to failed', function () {
        $response = $this->patch(route('admin.payments.update', $this->payment), [
            'payment_status' => Payment::STATUS_FAILED
        ]);
        
        $response->assertRedirect(route('admin.payments.show', $this->payment));
        $response->assertSessionHas('success', 'Payment status updated successfully.');
        
        $this->payment->refresh();
        expect($this->payment->payment_status)->toBe(Payment::STATUS_FAILED);
    });
    
    it('updates payment status to refunded and updates order status', function () {
        $response = $this->patch(route('admin.payments.update', $this->payment), [
            'payment_status' => Payment::STATUS_REFUNDED
        ]);
        
        $response->assertRedirect(route('admin.payments.show', $this->payment));
        $response->assertSessionHas('success', 'Payment status updated successfully.');
        
        $this->payment->refresh();
        $this->order->refresh();
        
        expect($this->payment->payment_status)->toBe(Payment::STATUS_REFUNDED);
        expect($this->order->status)->toBe(Order::STATUS_REFUNDED);
    });
    
    it('validates payment_status is required', function () {
        $response = $this->patch(route('admin.payments.update', $this->payment), []);
        
        $response->assertSessionHasErrors(['payment_status']);
    });
    
    it('validates payment_status must be valid value', function () {
        $response = $this->patch(route('admin.payments.update', $this->payment), [
            'payment_status' => 'invalid_status'
        ]);
        
        $response->assertSessionHasErrors(['payment_status']);
    });
    
    it('accepts all valid payment statuses', function () {
        $validStatuses = [
            Payment::STATUS_COMPLETED,
            Payment::STATUS_REFUNDED,
            Payment::STATUS_FAILED
        ];
        
        foreach ($validStatuses as $status) {
            $payment = Payment::factory()->create();
            
            $response = $this->patch(route('admin.payments.update', $payment), [
                'payment_status' => $status
            ]);
            
            $response->assertRedirect();
            $response->assertSessionHasNoErrors();
            
            $payment->refresh();
            expect($payment->payment_status)->toBe($status);
        }
    });
    
    it('handles non-existent payment for update', function () {
        $response = $this->patch(route('admin.payments.update', 99999), [
            'payment_status' => Payment::STATUS_COMPLETED
        ]);
        
        $response->assertStatus(404);
    });
    
    it('only updates order status when payment is refunded', function () {
        $originalOrderStatus = $this->order->status;
        
        // Update to completed (should not change order status)
        $this->patch(route('admin.payments.update', $this->payment), [
            'payment_status' => Payment::STATUS_COMPLETED
        ]);
        
        $this->order->refresh();
        expect($this->order->status)->toBe($originalOrderStatus);
        
        // Update to failed (should not change order status)
        $this->patch(route('admin.payments.update', $this->payment), [
            'payment_status' => Payment::STATUS_FAILED
        ]);
        
        $this->order->refresh();
        expect($this->order->status)->toBe($originalOrderStatus);
        
        // Update to refunded (should change order status)
        $this->patch(route('admin.payments.update', $this->payment), [
            'payment_status' => Payment::STATUS_REFUNDED
        ]);
        
        $this->order->refresh();
        expect($this->order->status)->toBe(Order::STATUS_REFUNDED);
    });
});

describe('PaymentController Integration Tests', function () {
    it('handles complete workflow from index to update', function () {
        // Start from index
        $indexResponse = $this->get(route('admin.payments.index'));
        $indexResponse->assertStatus(200);
        
        // Go to show page
        $showResponse = $this->get(route('admin.payments.show', $this->payment));
        $showResponse->assertStatus(200);
        
        // Update payment status
        $updateResponse = $this->patch(route('admin.payments.update', $this->payment), [
            'payment_status' => Payment::STATUS_REFUNDED
        ]);
        $updateResponse->assertRedirect(route('admin.payments.show', $this->payment));
        
        // Verify changes persisted
        $this->payment->refresh();
        $this->order->refresh();
        expect($this->payment->payment_status)->toBe(Payment::STATUS_REFUNDED);
        expect($this->order->status)->toBe(Order::STATUS_REFUNDED);
    });
    
    it('handles multiple payments with different statuses', function () {
        // Create payments with different statuses
        $completedPayment = Payment::factory()->create([
            'payment_status' => Payment::STATUS_COMPLETED
        ]);
        $failedPayment = Payment::factory()->create([
            'payment_status' => Payment::STATUS_FAILED
        ]);
        
        // Test index shows all payments
        $response = $this->get(route('admin.payments.index'));
        $payments = $response->viewData('payments');
        expect($payments->count())->toBeGreaterThanOrEqual(3);
        
        // Test individual payment views
        $this->get(route('admin.payments.show', $completedPayment))->assertStatus(200);
        $this->get(route('admin.payments.show', $failedPayment))->assertStatus(200);
    });
});

// Additional edge case tests
describe('PaymentController Edge Cases', function () {
    it('handles payment cascade deletion when order is deleted', function () {
        // Create a payment with an order
        $order = Order::factory()->create();
        $payment = Payment::factory()->create(['order_id' => $order->id]);
        
        // Delete the order
        $order->delete();
        
        // The payment should be deleted due to cascade
        expect(Payment::find($payment->id))->toBeNull();
        
        // Trying to access the deleted payment should return 404
        $response = $this->get(route('admin.payments.show', $payment));
        $response->assertStatus(404);
    });
    
    it('handles concurrent updates to same payment', function () {
        // Simulate concurrent updates
        $payment1 = Payment::with('order')->find($this->payment->id);
        $payment2 = Payment::with('order')->find($this->payment->id);
        
        // Update through different instances
        $payment1->update(['payment_status' => Payment::STATUS_COMPLETED]);
        
        $response = $this->patch(route('admin.payments.update', $payment2), [
            'payment_status' => Payment::STATUS_REFUNDED
        ]);
        
        // Check for successful response
        $response->assertStatus(302);
        
        // Verify final state
        $this->payment->refresh();
        expect($this->payment->payment_status)->toBe(Payment::STATUS_REFUNDED);
    });
});

// Test constants and validation rules
it('uses correct payment status constants', function () {
    // Verify that the constants exist and have expected values
    expect(defined('App\Models\Payment::STATUS_COMPLETED'))->toBeTrue();
    expect(defined('App\Models\Payment::STATUS_REFUNDED'))->toBeTrue();
    expect(defined('App\Models\Payment::STATUS_FAILED'))->toBeTrue();
    
    // Test that validation rule includes all constants
    $request = new Request(['payment_status' => Payment::STATUS_COMPLETED]);
    $validationRules = [
        'payment_status' => 'required|in:' . implode(',', [
            Payment::STATUS_COMPLETED,
            Payment::STATUS_REFUNDED,
            Payment::STATUS_FAILED
        ])
    ];
    
    $validator = validator($request->all(), $validationRules);
    expect($validator->passes())->toBeTrue();
});