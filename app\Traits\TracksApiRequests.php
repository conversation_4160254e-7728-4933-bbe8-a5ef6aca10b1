<?php

namespace App\Traits;

use App\Services\ApiRequestLogService; // Added
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

trait TracksApiRequests
{
    protected function trackApiRequest(Request $request, $response)
    {
        try {
            $user = $request->user();
            $token = $request->user()?->currentAccessToken();

            if (!$user) {
                Log::warning('No authenticated user found for API request logging');
                return;
            }

            $logService = app(\App\Services\ApiRequestLogService::class);
            $logService->logRequest($request, $response);
        } catch (\Exception $e) {
            Log::error('Failed to log API request: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => [
                    'path' => $request->path(),
                    'method' => $request->method(),
                    'user_id' => $request->user()?->id,
                ]
            ]);
        }
    }
}