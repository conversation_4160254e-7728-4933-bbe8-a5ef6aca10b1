<?php

use App\Http\Controllers\Api\PincodeController;
use App\Models\User;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\PinCode;

uses(RefreshDatabase::class);

// Mock the API request logging to prevent database errors in all tests
beforeEach(function () {
    $this->mock(\App\Services\ApiRequestLogService::class, function ($mock) {
        $mock->shouldReceive('logRequest')->andReturn(true);
    });
});

// Helper function to create and authenticate a user
function authenticatedUser($testCase = null)
{
    $user = User::factory()->create();
    $token = $user->createToken('test-token');
    
    // Get the actual token model from the database
    $tokenModel = $user->tokens()->first();
    
    Sanctum::actingAs($user, ['*']);
    
    // Set the token in the request headers for API request logging
    if ($testCase) {
        $testCase->withHeaders([
            'Authorization' => 'Bearer ' . $token->plainTextToken,
            'Accept' => 'application/json'
        ]);
    }
    
    return [
        'user' => $user,
        'token' => $tokenModel
    ];
}

// Test the default Laravel user route
test('user route returns authenticated user data', function () {
    $user = authenticatedUser($this);
    
    $response = $this->getJson('/api/user');
    
    $response->assertStatus(200)
             ->assertJson([
                 'id' => $user['user']->id,
                 'email' => $user['user']->email,
             ]);
});

test('user route requires authentication', function () {
    $response = $this->getJson('/api/user');
    
    $response->assertStatus(401)
             ->assertJsonStructure([
                 'message'
             ]);
});

// Test public routes (no authentication required)
describe('Public API Routes', function () {
    test('getCardData returns card data successfully', function () {
        $response = $this->getJson('/api/getCardData');
        
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         '*' => [
                             'id',
                             'title',
                             'description',
                             'image_url',
                             'created_at',
                             'updated_at'
                         ]
                     ]
                 ]);
    });

    test('get5Posts returns posts successfully', function () {
        $response = $this->getJson('/api/get5Posts');
        
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         '*' => [
                             'id',
                             'title',
                             'content',
                             'author',
                             'created_at',
                             'updated_at'
                         ]
                     ]
                 ]);
    });

    // test('general-setting returns general settings', function () {
    //     $response = $this->getJson('/api/general-setting');
        
    //     $response->assertStatus(200)
    //              ->assertJsonStructure([
    //                  'settings' => [
    //                      '*' => [
    //                          'key',
    //                          'value'
    //                      ]
    //                  ]
    //              ]);
    // });

    // test('get-countries returns countries list', function () {
    //     $response = $this->getJson('/api/get-countries');
        
    //     $response->assertStatus(200)
    //              ->assertJsonStructure([
    //                  'data' => [
    //                      '*' => [
    //                          'id',
    //                          'name',
    //                          'code'
    //                      ]
    //                  ]
    //              ]);
    // });

    test('public routes return proper content type', function () {
        $response = $this->getJson('/api/getCardData');
        
        $response->assertHeader('content-type', 'application/json');
    });

    test('public routes handle invalid requests gracefully', function () {
        $response = $this->getJson('/api/invalid-route');
        
        $response->assertStatus(404)
                 ->assertJsonStructure([
                     'message',
                     'error'
                 ]);
    });
});

// Test protected routes (authentication required)
describe('Protected API Routes', function () {
    beforeEach(function () {
        $auth = authenticatedUser($this);
        $this->user = $auth['user'];
        $this->token = $auth['token'];
    });

    // Pincode routes tests
    describe('Pincode Routes', function () {
        test('pincode route returns pincode data for valid pincode', function () {
            $this->mock(PincodeController::class, function ($mock) {
                $mock->shouldAllowMockingProtectedMethods()
                    ->shouldReceive('getMiddleware')
                    ->andReturn([]);
                $mock->shouldReceive('callAction')
                    ->andReturn(response()->json([
                        'data' => [
                            'pincode' => '110001',
                            'state' => 'Delhi',
                            'district' => 'Central Delhi',
                            'tehsil' => 'Delhi',
                            'post_office' => 'Connaught Place',
                            'latitude' => '28.6139',
                            'longitude' => '77.2090'
                        ]
                    ]));
            });
            $pincode = '110001';
            $response = $this->getJson("/api/pincode/{$pincode}");
            $response->assertStatus(200)
                     ->assertJsonStructure([
                         'data' => [
                             'pincode',
                             'state',
                             'district',
                             'tehsil',
                             'post_office',
                             'latitude',
                             'longitude'
                         ]
                     ]);
        });

        test('pincode route returns 404 for invalid pincode', function () {
            $this->mock(\App\Http\Controllers\Api\PincodeController::class, function ($mock) {
                $mock->shouldAllowMockingProtectedMethods()
                    ->shouldReceive('getMiddleware')
                    ->andReturn([]);
                $mock->shouldReceive('callAction')
                    ->andReturn(response()->json([
                        'message' => 'Pincode not found',
                        'error' => 'Not Found'
                    ], 404));
            });

            $invalidPincode = '000000';
            $response = $this->getJson("/api/pincode/{$invalidPincode}");
            $response->assertStatus(404)
                     ->assertJsonStructure([
                         'message',
                         'error'
                     ]);
        });

        test('pincode route validates pincode format', function () {
            // Specifically mock ApiRequestLogService for this test to ensure no DB interaction
            $this->mock(\App\Services\ApiRequestLogService::class, function ($mock) {
                $mock->shouldReceive('logRequest')->andReturn(true);
            });

            $invalidFormat = 'invalid';
            
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->token->plainTextToken,
                'Accept' => 'application/json'
            ])->getJson("/api/pincode/{$invalidFormat}");

            $response->assertStatus(404)
                     ->assertJsonStructure([
                         'status',
                         'message'
                     ]);
        });

        test('pincodes by state returns state data', function () {
            $stateName = 'Delhi';

            $this->mock(PincodeController::class, function ($mock) {
                $mock->shouldAllowMockingProtectedMethods()
                    ->shouldReceive('getMiddleware')->andReturn([]);
                $mock->shouldReceive('callAction')
                    ->andReturn(response()->json([
                        ['id' => 1, 'name' => 'Mocked Pincode State 1'],
                        ['id' => 2, 'name' => 'Mocked Pincode State 2'],
                    ]));
            });
            
            $response = $this->getJson("/api/pincodes/state/{$stateName}");
            
            $response->assertStatus(200)
                     ->assertJsonStructure([
                         '*' => [
                             'id',
                             'name'
                         ]
                     ])
                     ->assertJsonCount(2);
        });

        test('pincodes by state validates state name', function () {
            $invalidState = 'InvalidState';
            
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->token->plainTextToken,
                'Accept' => 'application/json'
            ])->getJson("/api/pincodes/state/{$invalidState}");
            
            $response->assertStatus(200)
                     ->assertJsonStructure([
                         'data' => [
                         '*' => [
                             'id',
                             'name'
                             ]
                         ]
                     ]);
        });

        test('pincodes by district returns district data', function () {
            $stateID = 1;
            $districtName = 'Central Delhi';

            $this->mock(PincodeController::class, function ($mock) {
                $mock->shouldAllowMockingProtectedMethods()
                    ->shouldReceive('getMiddleware')->andReturn([]);
                $mock->shouldReceive('callAction')
                    ->andReturn(response()->json([
                        ['id' => 1, 'name' => 'Mocked Pincode District 1'],
                        ['id' => 2, 'name' => 'Mocked Pincode District 2'],
                    ]));
            });
            
            $response = $this->getJson("/api/pincodes/district/{$stateID}/{$districtName}");
            
            $response->assertStatus(200)
                     ->assertJsonStructure([
                         '*' => [
                             'id',
                             'name'
                         ]
                     ])
                     ->assertJsonCount(2);
        });

        test('pincodes by tehsil returns tehsil data', function () {
            $stateName = 'Delhi';
            $districtName = 'Central Delhi';
            $tehsilName = 'Delhi';

            $this->mock(PincodeController::class, function ($mock) {
                $mock->shouldAllowMockingProtectedMethods()
                    ->shouldReceive('getMiddleware')->andReturn([]);
                $mock->shouldReceive('callAction')
                    ->andReturn(response()->json([
                        ['id' => 1, 'name' => 'Mocked Pincode Tehsil 1'],
                    ]));
            });
            
            $response = $this->getJson("/api/pincodes/tehsil/{$stateName}/{$districtName}/{$tehsilName}");
            
            $response->assertStatus(200)
                     ->assertJsonStructure([
                         '*' => [
                            'id',
                            'name'
                         ]
                     ])
                     ->assertJsonCount(1);
        });

        test('pincodes by post office returns post office data', function () {
            $stateName = 'Delhi';
            $districtName = 'Central Delhi';
            $postOfficeName = 'Connaught Place';

            $this->mock(PincodeController::class, function ($mock) {
                $mock->shouldAllowMockingProtectedMethods()
                    ->shouldReceive('getMiddleware')->andReturn([]);
                $mock->shouldReceive('callAction')
                    ->andReturn(response()->json([
                        ['id' => 1, 'name' => 'Mocked Pincode PostOffice 1'],
                    ]));
            });
            
            $response = $this->getJson("/api/pincodes/post/{$stateName}/{$districtName}/{$postOfficeName}");
            
            $response->assertStatus(200)
                     ->assertJsonStructure([
                         '*' => [
                             'id',
                             'name'
                         ]
                     ])
                     ->assertJsonCount(1);
        });

        test('pincode details returns detailed information', function () {
            $stateName = 'Delhi';
            $districtName = 'Central Delhi';
            $postOfficeName = 'Connaught Place';

            $this->mock(PincodeController::class, function ($mock) use ($stateName, $districtName, $postOfficeName) {
                $mock->shouldAllowMockingProtectedMethods()
                    ->shouldReceive('getMiddleware')->andReturn([]);
                $mock->shouldReceive('callAction')
                    ->andReturn(response()->json([
                        'pincode' => '110001',
                        'state' => $stateName,
                        'district' => $districtName,
                        'post_office' => $postOfficeName,
                        'detail' => 'Mocked Detail'
                    ]));
            });
            
            $response = $this->getJson("/api/pincodes/details/{$stateName}/{$districtName}/{$postOfficeName}");
            
            $response->assertStatus(200)
                     ->assertJsonStructure([
                         'pincode',
                         'state',
                         'district',
                         'post_office',
                         'detail'
                     ]);
        });
    });

    // Distance calculation routes tests
    describe('Distance Calculation Routes', function () {
        beforeEach(function () {
            // Mock the API request logging to prevent database errors
            $this->mock(\App\Services\ApiRequestLogService::class, function ($mock) {
                $mock->shouldReceive('logRequest')->andReturn(true);
            });
        });

        test('calculate distance between coordinates returns distance', function () {
            $lat1 = '28.6139';
            $long1 = '77.2090';
            $lat2 = '19.0760';
            $long2 = '72.8777';
            
            $this->mock(\App\Http\Controllers\Api\DistanceAPIController::class, function ($mock) {
                $mock->shouldAllowMockingProtectedMethods()
                    ->shouldReceive('getMiddleware')
                    ->andReturn([]);
                $mock->shouldReceive('callAction')
                    ->andReturn(response()->json([
                        'distance' => [
                            'km' => 1400.5,
                            'miles' => 870.2
                        ]
                    ]));
            });
            
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->token->plainTextToken,
                'Accept' => 'application/json'
            ])->getJson("/api/calculate-distance-between-two-coordinates/{$lat1}/{$long1}/{$lat2}/{$long2}");
            
            $response->assertStatus(200)
                     ->assertJsonStructure([
                         'distance' => [
                             'km',
                             'miles'
                         ]
                     ]);
        });

        test('calculate distance fails with invalid coordinates', function () {
            $lat1 = 'invalid';
            $long1 = '77.2090';
            $lat2 = '19.0760';
            $long2 = '72.8777';
            
            $this->mock(\App\Http\Controllers\Api\DistanceController::class, function ($mock) {
                $mock->shouldReceive('calculateDistance')
                    ->andReturn(response()->json([
                        'error' => 'Invalid coordinates'
                    ], 400));
            });
            
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->token->plainTextToken,
                'Accept' => 'application/json'
            ])->getJson("/api/calculate-distance-between-two-coordinates/{$lat1}/{$long1}/{$lat2}/{$long2}");
            
            $response->assertStatus(400)
                     ->assertJsonStructure([
                         'error'
                     ]);
        });

        test('get nearest location returns nearest location data', function () {
            $latitude = '28.6139';
            $longitude = '77.2090';

            // Create a pincode at the exact location to ensure the API finds it.
            PinCode::factory()->create([
                'latitude' => $latitude,
                'longitude' => $longitude,
            ]);
            
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $this->token->plainTextToken,
                'Accept' => 'application/json'
            ])->getJson("/api/get-nearest-location/{$latitude}/{$longitude}");
            
            $response->assertStatus(200)
                     ->assertJsonStructure([
                         'data' => [
                             'nearest_location' => [
                                 'post_office',
                         'pincode',
                         'district',
                                 'state'
                             ],
                         'distance'
                         ]
                     ]);
        });
    });

    // Pin to Pin routes tests
    describe('Pin to Pin Routes', function () {
        test('validate pincode returns validation result', function () {
            $pincode = '110001';
            
            $response = $this->getJson("/api/validate-pincode/{$pincode}");
            
            $response->assertStatus(200)
                     ->assertJsonStructure([
                         'data' => [
                             'is_valid',
                             'pincode'
                         ]
                     ]);
        });

        test('calculate distance between pincodes returns distance', function () {
            $pincode1 = '110001';
            $pincode2 = '400001';

            PinCode::factory()->create(['pincode' => $pincode1]);
            PinCode::factory()->create(['pincode' => $pincode2]);
            
            $response = $this->getJson("/api/calculate-distance-between-two-pincodes/{$pincode1}/{$pincode2}");
            
            $response->assertStatus(200)
                     ->assertJsonStructure([
                         'data' => [
                             'distance',
                             'unit'
                         ]
                     ]);
        });

        // POST route tests
        test('validate pincode via POST returns validation result', function () {
            $data = [
                'pincode' => '110001'
            ];
            
            $response = $this->postJson('/api/validate-pincode', $data);
            
            $response->assertStatus(200)
                     ->assertJsonStructure([
                         'valid'
                     ]);
        });

        test('validate pincode via POST fails with missing data', function () {
            $response = $this->postJson('/api/validate-pincode');
            
            $response->assertStatus(422)
                     ->assertJsonStructure([
                         'valid',
                         'errors'
                     ]);
        });

        test('calculate distance via POST returns distance', function () {
            $data = [
                'from_pincode' => '110001',
                'to_pincode' => '400001'
            ];

            PinCode::factory()->create(['pincode' => $data['from_pincode']]);
            PinCode::factory()->create(['pincode' => $data['to_pincode']]);
            
            $response = $this->postJson('/api/calculate-distance', $data);
            
            $response->assertStatus(200)
                     ->assertJsonStructure([
                         'from' => [
                             'pincode',
                             'name',
                             'state'
                         ],
                         'to' => [
                             'pincode',
                             'name',
                             'state'
                         ],
                         'distance' => [
                             'km',
                             'miles',
                             'nautical_miles'
                         ],
                         'bearing',
                         'midpoint' => [
                             'latitude',
                             'longitude'
                         ]
                     ]);
        });

        test('calculate distance via POST fails with invalid data', function () {
            $data = [
                'from_pincode' => 'invalid',
                'to_pincode' => '400001'
            ];
            
            $response = $this->postJson('/api/calculate-distance', $data);
            
            $response->assertStatus(422)
                     ->assertJsonStructure([
                         'error'
                     ]);
        });
    });
});

// Test error handling
describe('Error Handling', function () {
    beforeEach(function () {
        authenticatedUser($this);
    });

    test('routes handle invalid pincode format gracefully', function () {
        // Mock the API request logging to prevent database errors
        $this->mock(\App\Services\ApiRequestLogService::class, function ($mock) {
            $mock->shouldReceive('logRequest')->andReturn(true);
        });

        $response = $this->getJson('/api/pincode/invalid-format');
        
        $response->assertStatus(404)
                 ->assertJsonStructure([
                     'status',
                     'message'
                 ]);
    });

    test('routes handle invalid coordinates gracefully', function () {
        // Mock the API request logging to prevent database errors
        $this->mock(\App\Services\ApiRequestLogService::class, function ($mock) {
            $mock->shouldReceive('logRequest')->andReturn(true);
        });

        $response = $this->getJson('/api/calculate-distance-between-two-coordinates/invalid/77.2090/19.0760/72.8777');
        
        $response->assertStatus(400)
                 ->assertJsonStructure([
                     'error'
                 ]);
    });

    test('routes handle missing parameters gracefully', function () {
        // Mock the API request logging to prevent database errors
        $this->mock(\App\Services\ApiRequestLogService::class, function ($mock) {
            $mock->shouldReceive('logRequest')->andReturn(true);
        });

        $response = $this->getJson('/api/pincodes/state/');
        
        $response->assertStatus(404)
                 ->assertJsonStructure([
                     'status',
                     'message'
                 ]);
    });

    test('routes handle server errors gracefully', function () {
        // Mock the API request logging to prevent database errors
        $this->mock(\App\Services\ApiRequestLogService::class, function ($mock) {
            $mock->shouldReceive('logRequest')->andReturn(true);
        });

        $this->mock(PincodeController::class, function ($mock) {
            $mock->shouldReceive('getPincode')
                 ->andThrow(new \Exception('Service unavailable'));
        });
        
        $response = $this->getJson('/api/pincode/110001');
        // dump($response->json());
        $response->assertStatus(500)
                 ->assertJsonStructure([
                     'message'
                 ]);
    });

    test('routes handle validation errors for POST requests', function () {
        // Mock the API request logging to prevent database errors
        $this->mock(\App\Services\ApiRequestLogService::class, function ($mock) {
            $mock->shouldReceive('logRequest')->andReturn(true);
        });

        $response = $this->postJson('/api/validate-pincode', [
            'pincode' => 'invalid'
        ]);
        
        $response->assertStatus(422)
                 ->assertJsonStructure([
                     'valid',
                     'errors'
                 ]);
    });

    test('routes handle missing required fields', function () {
        // Mock the API request logging to prevent database errors
        $this->mock(\App\Services\ApiRequestLogService::class, function ($mock) {
            $mock->shouldReceive('logRequest')->andReturn(true);
        });

        $response = $this->postJson('/api/calculate-distance', [
            'from_pincode' => '110001'
            // Missing to_pincode
        ]);
        
        $response->assertStatus(422)
                 ->assertJsonStructure([
                     'error'
                 ]);
    });
});

// Test rate limiting
describe('Rate Limiting', function () {
    test('public routes have appropriate rate limits', function () {
        for ($i = 0; $i < 60; $i++) {
            $response = $this->getJson('/api/getCardData');
            if ($response->status() === 429) {
                break;
            }
        }
        
        expect($response->status())->toBeIn([200, 429]);
        if ($response->status() === 429) {
            $response->assertJsonStructure([
                'message',
                'retry_after'
            ]);
        }
    });

    test('protected routes have appropriate rate limits', function () {
        $user = authenticatedUser($this);
        
        for ($i = 0; $i < 60; $i++) {
            $response = $this->getJson('/api/pincode/110001');
            if ($response->status() === 429) {
                break;
            }
        }
        
        expect($response->status())->toBeIn([200, 429]);
        if ($response->status() === 429) {
            // dump($response->json());
            $response->assertJsonStructure([
                'message'
            ]);
        }
    });

    test('rate limit headers are present', function () {
        $response = $this->getJson('/api/getCardData');
        
        $response->assertHeader('X-RateLimit-Limit');
        $response->assertHeader('X-RateLimit-Remaining');
    });
});

// Test authentication requirements
describe('Authentication Requirements', function () {
    test('protected routes require authentication', function ($route) {
        $response = $this->getJson($route);
        
        $response->assertStatus(401)
                 ->assertJsonStructure([
                     'message'
                 ]);
    })->with([
        '/api/pincode/110001',
        '/api/pincodes/state/Delhi',
        '/api/calculate-distance-between-two-coordinates/28.6139/77.2090/19.0760/72.8777',
        '/api/get-nearest-location/28.6139/77.2090',
        '/api/validate-pincode/110001',
        '/api/calculate-distance-between-two-pincodes/110001/400001',
    ]);

    test('protected POST routes require authentication', function ($route, $data) {
        $response = $this->postJson($route, $data);
        
        $response->assertStatus(401)
                 ->assertJsonStructure([
                     'message'
                 ]);
    })->with([
        ['/api/validate-pincode', ['pincode' => '110001']],
        ['/api/calculate-distance', ['pincode1' => '110001', 'pincode2' => '400001']],
    ]);

    test('authentication token is validated', function () {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer invalid-token'
        ])->getJson('/api/pincode/110001');
        
        $response->assertStatus(401)
                 ->assertJsonStructure([
                     'message'
                 ]);
    });
});

// Test route parameter validation
describe('Route Parameter Validation', function () {
    beforeEach(function () {
        authenticatedUser($this);
    });

    test('routes validate required parameters', function ($route, $expectedStatus) {
        $response = $this->getJson($route);
        
        $response->assertStatus($expectedStatus)
                 ->assertJsonStructure([
                     'message',
                     'error'
                 ]);
    })->with([
        ['/api/pincode/', 404],
        ['/api/calculate-distance-between-two-coordinates/28.6139/', 404],
    ]);
});

// Test JSON response structure
describe('JSON Response Structure', function () {
    beforeEach(function () {
        authenticatedUser($this);
    });

    test('API routes return JSON responses', function ($route) {
        $response = $this->getJson($route);
        
        $response->assertHeader('content-type', 'application/json');
    })->with([
        '/api/getCardData',
        '/api/get5Posts',
        '/api/general-setting',
        '/api/get-countries',
    ]);
});