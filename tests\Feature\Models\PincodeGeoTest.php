<?php

use App\Models\PincodeGeo;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('it has fillable fields', function () {
    $model = new PincodeGeo();
    expect($model->getFillable())->toEqual([
        'pincode',
        'office_name',
        'division',
        'region',
        'circle',
        'geometry',
    ]);
});

test('it casts geometry to array', function () {
    $model = new PincodeGeo([
        'geometry' => [[ [77.1, 28.6], [77.2, 28.7] ]],
    ]);
    expect($model->geometry)->toBeArray();
});

test('it can scope by pincode', function () {
    $a = PincodeGeo::factory()->create(['pincode' => '110001']);
    $b = PincodeGeo::factory()->create(['pincode' => '110002']);
    PincodeGeo::factory()->create(['pincode' => '120001']);
    $results = PincodeGeo::byPincode('110')->get();
    expect($results)->toHaveCount(2)
        ->and($results[0]->pincode)->toEqual('110001');
});

test('it can create a model with geometry data', function () {
    $geometry = [
        [
            [77.1, 28.6],
            [77.2, 28.7],
            [77.3, 28.8],
        ]
    ];
    
    $pincode = PincodeGeo::factory()->create([
        'pincode' => '110001',
        'geometry' => $geometry,
    ]);
    
    expect($pincode->pincode)->toEqual('110001');
    expect($pincode->geometry)->toBeArray();
    expect($pincode->geometry[0])->toHaveCount(3);
}); 