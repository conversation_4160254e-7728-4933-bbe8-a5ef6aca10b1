@extends('layouts.app')

@section('json-ld')
    <script type="application/ld+json">
                    {
                        "@context": "https://schema.org",
                        "@type": "WebPage",
                        "name": "{{ get_setting('site_name') }}",
                        "description": "{{ $metaDescription }}",
                        "publisher": {
                            "@type": "Organization",
                            "name": "{{ get_setting('schema_organization_name') }}"
                        },
                        "url": "{{ url()->current() }}",
                        "datePublished": "{{ now()->toIso8601String() }}",
                        "dateModified": "{{ now()->toIso8601String() }}"
                    }
                </script>

    @if (!empty($breadcrumbs))
        @include('pincodes.json-ld.breadcrumbs')
    @endif
@endsection

@section('content')
    <x-breadcrumb :segments="$breadcrumbs" :pageTitle="$pageTitle" :metaDescription="$metaDescription" />
    <div class="container max-w-6xl mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-8">
            <!-- Main content column -->
            <div class="lg:col-span-8">
                <!-- Pincode Wise Post Offices -->
                <div class="bg-white dark:bg-bg-dark shadow-md rounded-lg p-6">
                    @if ($errors->has('pincode'))
                        <div class="bg-red-100 dark:bg-red-900/20 border border-red-400 dark:border-red-500 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-4">
                            {{ $errors->first('pincode') }}
                        </div>
                    @elseif ($pincodes->isNotEmpty())
                        <h2 class="text-3xl font-bold text-primary-light dark:text-primary-dark mb-4">{{ $pageTitle }}</h2>

                        <p class="text-lg text-text-secondary-light dark:text-text-secondary-dark mb-3">
                            @if ($po_count > 1)
                                    A single pincode is not limited to just one post office; a single postal code can be
                                    assigned to multiple post offices within the same region.
                                </p>
                                <p class="text-text-secondary-light dark:text-text-secondary-dark mb-3">In the case of pincode
                                    {{ $pincodes->first()->pincode }}, it is assigned to {{ $po_count }} different
                                    post offices,
                                    which include
                                    @foreach ($pincodes as $index => $po)
                                        @if ($index == $po_count - 1)
                                            and {{ ucfirst($po->name) }}.
                                        @elseif ($index == $po_count - 2)
                                            {{ ucfirst($po->name) }}
                                        @else
                                            {{ ucfirst($po->name) }},
                                        @endif
                                    @endforeach
                            @else
                                <p class="text-text-secondary-light dark:text-text-secondary-dark">Pincode {{ $pincodes->first()->pincode }} is assigned to only one post
                                    office:
                                    {{ ucfirst($pincodes->first()->name) }}.
                                </p>
                            @endif
                        </p>

                        <p class="text-text-secondary-light dark:text-text-secondary-dark mb-3">These post offices, associated with pincode
                            {{ $pincodes->first()->pincode }},
                            are located in
                            the
                            {{ $district }} district of {{ $state }} state. To learn more about each
                            individual post office,
                            click on the respective name in the list below.
                        </p>

                        <p class="text-text-secondary-light dark:text-text-secondary-dark mb-3">Additionally, you can explore user
                            reviews for each postal code to verify their accuracy and gain insights from others'
                            experiences.
                        </p>

                        <div class="mb-4">
                            <div class="bg-bg-light dark:bg-gray-800 p-4 rounded-lg">
                                <h2 class="text-xl font-semibold mb-3 text-text-primary-light dark:text-text-primary-dark">Post Offices ({{ $po_count }})</h2>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    @foreach ($pincodes->chunk(ceil($pincodes->count() / 2)) as $chunk)
                                        <div>
                                            <ul class="space-y-2">
                                                @foreach ($chunk as $index => $pincode)
                                                    <li class="bg-white dark:bg-bg-dark p-3 rounded-lg shadow-sm">
                                                        <a href="{{ url('/pincodes/' . rawurlencode($pincode->state) . '/' . rawurlencode($pincode->district) . '/' . rawurlencode($pincode->name)) }}"
                                                            class="text-primary-light dark:text-primary-dark hover:underline">
                                                            {{ $loop->parent->iteration == 1 ? $loop->iteration : $loop->iteration + ceil($pincodes->count() / 2) }}.
                                                            {{ ucfirst($pincode->name) }} -
                                                            {{ $pincode->pincode }}
                                                        </a>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        @if (count($villageList) > 0)
                            <div class="mb-4">
                                <h2 class="text-2xl font-bold mb-3 text-text-primary-light dark:text-text-primary-dark">Villages under Pincode {{ $pincode->pincode }}</h2>
                                <p class="text-text-secondary-light dark:text-text-secondary-dark mb-3">
                                    Total {{ count($villageList) }} villages come under the Pincode
                                    {{ $pincode->pincode }}. Villagers of the following villages use this pincode for
                                    postal communication.
                                </p>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                    @foreach ($villageList as $index => $village)
                                        <div class="bg-white dark:bg-bg-dark p-4 rounded-lg shadow-sm">
                                            <h5 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">{{ $village->village_name_en }}</h5>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <div class="mb-4">
                            <h2 class="text-2xl font-bold mb-3 text-text-primary-light dark:text-text-primary-dark">Location Map</h2>
                            @if (isset($pincode->latitude) && isset($pincode->longitude))
                                <div class="aspect-w-16 aspect-h-9">
                                    <iframe
                                        src="https://maps.google.com/maps?q={{ $pincode->latitude }},{{ $pincode->longitude }}&hl=es;z=14&amp;output=embed"
                                        allowfullscreen="" loading="lazy" class="w-full h-full rounded-lg"></iframe>
                                </div>
                            @else
                                <p class="text-text-secondary-light dark:text-text-secondary-dark">Location data is not available.</p>
                            @endif
                        </div>

                        <!-- DIGIPIN Finder Component -->
                        <div class="mb-4">
                            <h2 class="text-2xl font-bold mb-3 text-text-primary-light dark:text-text-primary-dark">Digital Address (DIGIPIN)</h2>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark mb-4">
                                Discover India Post's revolutionary DIGIPIN system - a precise digital addressing solution that complements traditional pincodes.
                                Get your exact 4m x 4m location code for enhanced delivery accuracy and modern addressing needs.
                            </p>
                            <x-digipin-finder />
                        </div>
                    @endif
                </div>

                @if ($relatedPincodes->isNotEmpty())
                    <div class="bg-white dark:bg-bg-dark shadow-md rounded-lg p-6 mt-4">
                        <h3 class="text-2xl font-bold mb-4 text-text-primary-light dark:text-text-primary-dark">Other Post Offices in {{ ucfirst($pincode->district) }}
                            District</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach ($relatedPincodes as $index => $pincode)
                                <div class="bg-white dark:bg-bg-dark p-4 rounded-lg shadow-sm border border-border-light dark:border-border-dark">
                                    <h5 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">
                                        <span class="bg-primary-light dark:bg-primary-dark text-white px-2 py-1 rounded-full mr-2">{{ $index + 1 }}</span>
                                        Pincode: {{ $pincode->pincode }}
                                    </h5>
                                    <ul class="space-y-1 text-text-secondary-light dark:text-text-secondary-dark">
                                        <li><strong>Name:</strong> <a
                                                href="{{ url('/pincodes/' . rawurlencode($pincode->state) . '/' . rawurlencode($pincode->district) . '/' . rawurlencode($pincode->name)) }}"
                                                class="text-primary-light dark:text-primary-dark hover:underline">{{ ucfirst($pincode->name) }}</a>
                                        </li>
                                        <li><strong>District:</strong> <a
                                                href="{{ url('/pincodes/' . rawurlencode($pincode->state) . '/' . rawurlencode($pincode->district)) }}"
                                                class="text-primary-light dark:text-primary-dark hover:underline">{{ ucfirst($pincode->district) }}</a>
                                        </li>
                                        <li><strong>Division:</strong> {{ $pincode->division }}</li>
                                        <li><strong>Circle:</strong> {{ $pincode->circle }}</li>
                                    </ul>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <div class="mt-4">
                    @include('pincodes.partials.share')
                </div>
            </div>

            <!-- Sidebar column -->
            <div class="lg:col-span-4 px-4">
                <div class="sticky top-20">
                    @include('pincodes.partials.sidebar')
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function () {
            // Smooth scroll to anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (event) {
                    event.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        window.scrollTo({
                            top: target.offsetTop - 100,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Initialize tooltips
            const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            tooltipTriggerList.forEach(tooltipTriggerEl => {
                new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
@endpush