<div class="mb-3">
    <label class="form-label">Client ID *</label>
    <input type="text" class="form-control" name="configuration[client_id]" 
           value="{{ $config['client_id'] ?? '' }}" required
           placeholder="Enter PayPal Client ID">
    <small class="text-muted">Your PayPal application Client ID</small>
</div>

<div class="mb-3">
    <label class="form-label">Client Secret *</label>
    <div class="input-group">
        <input type="password" class="form-control" name="configuration[client_secret]" 
               value="{{ $config['client_secret'] ?? '' }}" required
               placeholder="Enter PayPal Client Secret">
        <button type="button" class="btn btn-outline-secondary toggle-password">
            <i class="mdi mdi-eye"></i>
        </button>
    </div>
    <small class="text-muted">Your PayPal application Client Secret (keep this secure)</small>
</div>

<div class="mb-3">
    <label class="form-label">Environment</label>
    <select class="form-select" name="configuration[environment]">
        <option value="sandbox" {{ ($config['environment'] ?? 'sandbox') === 'sandbox' ? 'selected' : '' }}>
            Sandbox (Test)
        </option>
        <option value="live" {{ ($config['environment'] ?? 'sandbox') === 'live' ? 'selected' : '' }}>
            Live (Production)
        </option>
    </select>
</div>

<div class="mb-3">
    <label class="form-label">Default Currency</label>
    <select class="form-select" name="configuration[default_currency]">
        <option value="USD" {{ ($config['default_currency'] ?? 'USD') === 'USD' ? 'selected' : '' }}>USD - US Dollar</option>
        <option value="EUR" {{ ($config['default_currency'] ?? 'USD') === 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
        <option value="GBP" {{ ($config['default_currency'] ?? 'USD') === 'GBP' ? 'selected' : '' }}>GBP - British Pound</option>
        <option value="CAD" {{ ($config['default_currency'] ?? 'USD') === 'CAD' ? 'selected' : '' }}>CAD - Canadian Dollar</option>
    </select>
</div>

<div class="mb-3">
    <label class="form-label">Brand Name</label>
    <input type="text" class="form-control" name="configuration[brand_name]" 
           value="{{ $config['brand_name'] ?? config('app.name') }}"
           placeholder="Your business name">
    <small class="text-muted">Business name displayed on PayPal checkout</small>
</div>

<div class="row">
    <div class="col-md-6 mb-3">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" name="configuration[enable_guest_checkout]" 
                   value="1" {{ ($config['enable_guest_checkout'] ?? true) ? 'checked' : '' }}>
            <label class="form-check-label">Enable Guest Checkout</label>
        </div>
        <small class="text-muted">Allow payments without PayPal account</small>
    </div>
    <div class="col-md-6 mb-3">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" name="configuration[enable_shipping]" 
                   value="1" {{ ($config['enable_shipping'] ?? false) ? 'checked' : '' }}>
            <label class="form-check-label">Require Shipping Address</label>
        </div>
        <small class="text-muted">Collect shipping address during checkout</small>
    </div>
</div>

<div class="alert alert-info">
    <h6 class="alert-heading">
        <i class="mdi mdi-information me-2"></i>
        PayPal Setup Instructions:
    </h6>
    <ol class="mb-0 small">
        <li>Log in to <a href="https://developer.paypal.com/" target="_blank">PayPal Developer</a></li>
        <li>Create a new application or use existing one</li>
        <li>Copy the Client ID and Client Secret</li>
        <li>Configure return URLs and webhook endpoints</li>
        <li>Test with sandbox credentials before going live</li>
    </ol>
</div>