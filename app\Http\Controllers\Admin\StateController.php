<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\State;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class StateController extends Controller
{
    public function index()
    {
        $states = State::orderBy('name')->paginate(10);
        return view('admin.states.index', compact('states'));
    }

    public function create()
    {
        return view('admin.states.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:pin_states,name',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->only('name');
        
        if ($request->hasFile('featured_image')) {
            $path = $request->file('featured_image')->store('states', 'public');
            $data['featured_image'] = $path;
        }

        State::create($data);

        return redirect()->route('admin.states.index')
            ->with('success', 'State created successfully.');
    }

    public function edit(State $state)
    {
        return view('admin.states.edit', compact('state'));
    }

    public function update(Request $request, State $state)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:pin_states,name,' . $state->id,
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->only('name');
        
        if ($request->hasFile('featured_image')) {
            // Delete old image if exists
            if ($state->featured_image) {
                Storage::disk('public')->delete($state->featured_image);
            }
            $path = $request->file('featured_image')->store('states', 'public');
            $data['featured_image'] = $path;
        }

        $state->update($data);

        return redirect()->route('admin.states.index')
            ->with('success', 'State updated successfully.');
    }

    public function destroy(State $state)
    {
        if ($state->featured_image) {
            Storage::disk('public')->delete($state->featured_image);
        }
        $state->delete();

        return redirect()->route('admin.states.index')
            ->with('success', 'State deleted successfully.');
    }
}