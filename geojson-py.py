import json
import pymysql
from datetime import datetime

# Database connection settings
DB_HOST = 'localhost'
DB_USER = 'root'
DB_PASSWORD = ''
DB_NAME = 'pincodes-new'

# Path to your GeoJSON file
GEOJSON_FILE = 'all_india_pincode_boundary-19312.geojson'

# Connect to MySQL
conn = pymysql.connect(
    host=DB_HOST,
    user=DB_USER,
    password=DB_PASSWORD,
    database=DB_NAME,
    charset='utf8mb4'
)
cursor = conn.cursor()

# Load GeoJSON
with open(GEOJSON_FILE, 'r', encoding='utf-8') as f:
    geojson = json.load(f)

features = geojson['features']
print(f"Total features: {len(features)}")

# Prepare insert statement
insert_sql = """
INSERT INTO pincodes_geo
(pincode, office_name, division, region, circle, geometry, created_at, updated_at)
VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
"""

batch = []
batch_size = 100

for i, feature in enumerate(features, 1):
    props = feature['properties']
    geom = feature['geometry']
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    batch.append((
        props.get('Pincode', '').strip(),
        props.get('Office_Name', '').strip(),
        props.get('Division', '').strip(),
        props.get('Region', '').strip() or None,
        props.get('Circle', '').strip(),
        json.dumps(geom),
        now,
        now
    ))

    if len(batch) >= batch_size:
        try:
            cursor.executemany(insert_sql, batch)
            conn.commit()
            print(f"Inserted batch ending at row {i}")
        except Exception as e:
            print(f"Error inserting batch ending at row {i}: {e}")
            conn.rollback()
        batch = []

# Insert any remaining records
if batch:
    try:
        cursor.executemany(insert_sql, batch)
        conn.commit()
        print(f"Inserted final batch.")
    except Exception as e:
        print(f"Error inserting final batch: {e}")
        conn.rollback()

cursor.close()
conn.close()
print("Import completed.")
