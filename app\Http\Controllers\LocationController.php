<?php

namespace App\Http\Controllers;

use App\Models\PinCode;
use Illuminate\Http\Request;
use App\Models\State;
use App\Models\District;
use App\Models\PostOffice;
use Illuminate\Support\Facades\DB;

use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Exports\PincodesExport;
use Illuminate\Support\Facades\Log;

class LocationController extends Controller
{
    public function getDistricts($state_id)
    {
        $districts = District::where('state_id', $state_id)->get();
        return response()->json($districts);
    }

    public function getPostOffices($district_id)
    {
        $postOffices = PostOffice::where('district_id', $district_id)->get();
        return response()->json($postOffices);
    }


    public function search(Request $request)
    {
        try {
            // Validate the incoming request
            $validated = $request->validate([
                'query' => 'required|string',
                'stateId' => 'required',
                'districtId' => 'required|string',
                'searchType' => 'required|string|in:pincode,postoffice'
            ]);

            // Get the state name from ID
            $state = State::find($validated['stateId']);
            if (!$state) {
                return response()->json(['error' => 'State not found'], 404);
            }

            // Build the query
            $queryBuilder = Pincode::query()
                ->where('state', $state->name)
                ->where('district', $validated['districtId']);

            // Add search condition based on type
            if ($validated['searchType'] === 'pincode') {
                $queryBuilder->where('pincode', 'like', $validated['query'] . '%');
            } else {
                $queryBuilder->where('name', 'like', $validated['query'] . '%');
            }

            // Get results with limit
            $results = $queryBuilder
                ->select('id', 'name', 'pincode', 'state', 'district')
                ->limit(10)
                ->get();

            // Log the search attempt
            Log::info('Pincode search', [
                'query' => $validated['query'],
                'state' => $state->name,
                'district' => $validated['districtId'],
                'type' => $validated['searchType'],
                'results_count' => $results->count()
            ]);

            return response()->json($results);

        } catch (\Exception $e) {
            Log::error('Search error: ' . $e->getMessage(), [
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'An error occurred while searching',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function autoSearch(Request $request)
    {
        $query = $request->get('query', '');
        $stateId = $request->get('state_id', '');
        $districtId = $request->get('district_id', '');

        if (is_numeric($query)) {

            $places = PinCode::where('pincode', 'LIKE', "%{$query}%")
                // ->orWhere('pincode', 'LIKE', "%{$query}%")
                // ->where('state', $stateId)
                ->where('district', $districtId)
                ->pluck('pincode');
        } else {
            $places = PinCode::where('name', 'LIKE', "%{$query}%")
                // ->orWhere('pincode', 'LIKE', "%{$query}%")
                // ->where('state', $stateId)
                ->where('district', $districtId)
                ->pluck('name');
        }

        return response()->json($places);
    }

    // ----------------------------------------------------------------
    // First page for the download page
    // public function downloadPincodes()
    // {
    //     $m_states = State::get(); //for sidebar search
    //     $pageTitle = 'Download Pincodes Excel | PDF';
    //     $metaDescription = 'Download pincodes in CSV, Excel, and PDF format for all Indian states, districts, and pincodes. Search by state, district, or pincode.';  // add your meta description here.
    //     $keywords = "download all india pincode directory, download pincode, all india pincode database download";
    //     $imgPath = "assets/images/default/pincode-download.webp";

    //     setSEO($pageTitle, $metaDescription, $keywords, $imgPath);

    //     $path = 'download-pincodes';
    //     $breadcrumbController = new BreadcrumbController();
    //     $breadcrumbs = $breadcrumbController->generateBreadcrumb($path);
    //     $pincodes = false;

    //     return view('pincodes.download.downloadPincodes', compact('m_states', 'pageTitle', 'breadcrumbs', 'pincodes', 'metaDescription'));
    // }
    // Autofill for the download page
    public function getDownloadableData(Request $request)
    {
        Log::info('getDownloadableData called with params:', [
            'state' => $request->input('state'),
            'district' => $request->input('district'),
            'pincode' => $request->input('pincode')
        ]);

        $state_id = $request->input('state');
        $district = $request->input('district');
        $pincode = $request->input('pincode');
        $page = $request->input('page', 1);
        $perPage = 15;

        try {
            $query = Pincode::query();

            if ($state_id) {
                Log::debug('Filtering by state_id:', ['state_id' => $state_id]);
                $state_name = State::where('id', $state_id)->firstOrFail();
                Log::info('Found state:', ['state_name' => $state_name->name]);
                $query->where('state', $state_name->name);
            }

            if ($district) {
                // Add district name lookup similar to state
                $district_name = District::where('id', $district)->first();
                Log::debug('District lookup:', [
                    'district_id' => $district,
                    'found_name' => $district_name ? $district_name->name : 'not found'
                ]);
                $query->where('district', $district_name ? $district_name->name : $district);
            }

            if ($pincode) {
                Log::debug('Filtering by pincode:', ['pincode' => $pincode]);
                $query->where('pincode', $pincode);
            }

            // Log the raw SQL query
            Log::debug('Raw SQL:', [
                'query' => $query->toSql(),
                'bindings' => $query->getBindings()
            ]);

            $total = $query->count();
            Log::info('Total records found:', ['count' => $total]);

            $pincodes = $query->skip(($page - 1) * $perPage)
                ->take($perPage)
                ->get();

            Log::debug('Retrieved paginated results:', [
                'page' => $page,
                'perPage' => $perPage,
                'resultCount' => $pincodes->count()
            ]);

            session(['pincodes' => $pincodes]);

            $response = [
                'pincodes' => $pincodes,
                'total' => $total,
                'perPage' => $perPage,
                'currentPage' => $page
            ];

            Log::info('Successfully returning response');
            return response()->json($response);

        } catch (\Exception $e) {
            Log::error('Error in getDownloadableData:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    public function downloadNow(Request $request)
    {
        $pincodes = session('pincodes');

        $format = $request->input('format');

        if ($format === 'pdf') {
            $pdf = Pdf::loadView('pincodes.download.pdf11', ['pincodes' => $pincodes]);// name of view file
            return $pdf->download('pincodes.pdf');//filename with pdf extension
        } else {
            $export = new PincodesExport($pincodes);
            return $export->downloadAsExcel();
        }
    }

    // public function pincodeOfMyLocation()
    // {

    //     $pageTitle = 'Pincode of My Current Location';
    //     $metaDescription = 'Find the pincode of your current location easily and quickly with our online tool. Get accurate postal codes for your area and nearby regions for smooth deliveries and local services.';  // add your meta description here.
    //     $keywords = "pincode of my current location, current pincode of my location, pincode of my current location by gps, area pincode of my current location";
    //     $imgPath = "assets/images/default/pincode-download.webp";

    //     setSEO($pageTitle, $metaDescription, $keywords, $imgPath);

    //     $path = 'Pincode of My Location';
    //     $breadcrumbController = new BreadcrumbController();
    //     $breadcrumbs = $breadcrumbController->generateBreadcrumb($path);

    //     $metaDescription = "Find Pincode of your Current Location";

    //     $m_states = State::get();

    //     return view('pincodes.myLocation', compact('pageTitle', 'metaDescription', 'breadcrumbs', 'm_states'));
    // }

    public function downloadPincodesByDistrict($state_name, $district_name)
    {
        $m_states = State::get();
        $selected_state = State::select('id', 'name')->where('name', '=', $state_name)->firstOrFail();

        $m_districts = District::get();
        $selected_district = District::select('id', 'name')->where('name', '=', $district_name)->firstOrFail();

        $pageTitle = 'Download Pincodes Excel | PDF';

        $metaDescription = 'Download a comprehensive list of pincodes for ' . $district_name . ' district in ' . $state_name . ' state. Available in both PDF and Excel formats for easy reference and analysis.';
        $path = 'Download Pincode -  District Wise';

        $breadcrumbController = new BreadcrumbController();
        $breadcrumbs = $breadcrumbController->generateBreadcrumb($path);
        $pincodes = false;

        $keywords = "download pincodes, {$district_name} pincodes, {$state_name} pincodes, district pincodes";
        $imgPath = "assets/images/default/pincode-download.webp";

        setSEO($pageTitle, $metaDescription, $keywords, $imgPath);

        return view('pincodes.download.pincodeDownloadDistrict', compact('breadcrumbs', 'm_states', 'selected_state', 'm_districts', 'selected_district', 'pageTitle', 'metaDescription'));
    }

    // Pincode of my location
    public function getNearestPostOfficeLocation($latitude, $longitude)
    {
        $userLat = $latitude;
        $userLng = $longitude;

        // Define the bounding box for limiting the number of records
        $distance = 10; // Distance in kilometers
        $latRange = $distance / 111; // 1 degree latitude ~ 111 km
        $lngRange = $distance / (111 * cos(deg2rad($userLat)));

        // Using the Haversine formula to calculate distances
        $nearestLocation = PinCode::select(DB::raw("*, 
            (6371 * acos(cos(radians($userLat)) 
            * cos(radians(latitude)) 
            * cos(radians(longitude) - radians($userLng)) 
            + sin(radians($userLat)) 
            * sin(radians(latitude)))) AS distance"))
            ->whereBetween('latitude', [$userLat - $latRange, $userLat + $latRange])
            ->whereBetween('longitude', [$userLng - $lngRange, $userLng + $lngRange])
            ->orderBy('distance')
            ->first();

        if (!$nearestLocation) {
            return response()->json(['error' => 'No post office found nearby'], 404);
        }

        // Get all post offices with the same pincode
        $samePostOffices = PinCode::select(DB::raw("*, 
            (6371 * acos(cos(radians($userLat)) 
            * cos(radians(latitude)) 
            * cos(radians(longitude) - radians($userLng)) 
            + sin(radians($userLat)) 
            * sin(radians(latitude)))) AS distance"))
            ->where('pincode', $nearestLocation->pincode)
            ->orderBy('distance')
            ->get();

        return response()->json([
            'postalCode' => $nearestLocation->pincode,
            'city' => ucfirst($nearestLocation->district),
            'state' => ucfirst($nearestLocation->state),
            'nearestDistance' => $nearestLocation->distance,
            'postOffices' => $samePostOffices->map(function ($office) {
                return [
                    'name' => ucfirst($office->name),
                    'distance' => $office->distance,
                    'latitude' => $office->latitude,
                    'longitude' => $office->longitude
                ];
            })
        ]);
    }
}
