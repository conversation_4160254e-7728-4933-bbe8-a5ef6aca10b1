<?php

use App\Models\Review;
use App\Models\User;
use App\Models\PinCode;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('review can be created', function () {
    $review = Review::factory()->create([
        'pincode_id' => PinCode::factory()->create()->id,
        'user_id' => User::factory()->create()->id,
        'name' => 'Test User',
        'comment' => 'This is a test review',
        'ip_address' => '***********',
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        'status' => 'approved'
    ]);

    expect($review)->toBeInstanceOf(Review::class)
        ->and($review->name)->toBe('Test User')
        ->and($review->comment)->toBe('This is a test review')
        ->and($review->ip_address)->toBe('***********')
        ->and($review->user_agent)->toBe('Mozilla/5.0 (Windows NT 10.0; Win64; x64)')
        ->and($review->status)->toBe('approved');
});

test('review has required attributes', function () {
    $review = Review::factory()->create();

    expect($review)->toHaveKeys([
        'pincode_id',
        'user_id',
        'name',
        'comment',
        'ip_address',
        'user_agent',
        'status',
        'created_at',
        'updated_at'
    ]);
});

test('review has fillable attributes', function () {
    $expectedFillable = [
        'pincode_id', 
        'user_id', 
        'name', 
        'comment', 
        'ip_address', 
        'user_agent', 
        'status'
    ];

    expect((new Review())->getFillable())->toBe($expectedFillable);
});

test('review dates are cast to datetime', function () {
    $review = Review::factory()->create();

    expect($review->created_at)->toBeInstanceOf(\Carbon\Carbon::class)
        ->and($review->updated_at)->toBeInstanceOf(\Carbon\Carbon::class);
});

test('review belongs to user relationship', function () {
    $user = User::factory()->create();
    $review = Review::factory()->create([
        'user_id' => $user->id
    ]);

    expect($review->user)->toBeInstanceOf(User::class)
        ->and($review->user->id)->toBe($user->id);
});

test('review belongs to pincode relationship', function () {
    $pincode = PinCode::factory()->create();
    $review = Review::factory()->create([
        'pincode_id' => $pincode->id
    ]);

    expect($review->pincode)->toBeInstanceOf(PinCode::class)
        ->and($review->pincode->id)->toBe($pincode->id);
});

test('approved scope returns only approved reviews', function () {
    // Create approved reviews
    Review::factory()->count(3)->create(['status' => 'approved']);
    
    // Create pending reviews
    Review::factory()->count(2)->create(['status' => 'pending']);
    
    $approvedReviews = Review::approved()->get();
    
    expect($approvedReviews)->toHaveCount(3);
    
    $approvedReviews->each(function ($review) {
        expect($review->status)->toBe('approved');
    });
});

test('pending scope returns only pending reviews', function () {
    // Create approved reviews
    Review::factory()->count(3)->create(['status' => 'approved']);
    
    // Create pending reviews
    Review::factory()->count(2)->create(['status' => 'pending']);
    
    $pendingReviews = Review::pending()->get();
    
    expect($pendingReviews)->toHaveCount(2);
    
    $pendingReviews->each(function ($review) {
        expect($review->status)->toBe('pending');
    });
});

test('hasRecentReview returns true when recent review exists', function () {
    $pincode = PinCode::factory()->create();
    $ipAddress = '***********';
    
    // Create a review within the last hour
    Review::factory()->create([
        'pincode_id' => $pincode->id,
        'ip_address' => $ipAddress,
        'created_at' => now()->subMinutes(30)
    ]);
    
    expect(Review::hasRecentReview($pincode->id, $ipAddress))->toBeTrue();
});

test('hasRecentReview returns false when no recent review exists', function () {
    $pincode = PinCode::factory()->create();
    $ipAddress = '***********';
    
    // Create a review older than the default 60 minutes
    Review::factory()->create([
        'pincode_id' => $pincode->id,
        'ip_address' => $ipAddress,
        'created_at' => now()->subMinutes(61)
    ]);
    
    expect(Review::hasRecentReview($pincode->id, $ipAddress))->toBeFalse();
});

test('hasRecentReview respects custom time window', function () {
    $pincode = PinCode::factory()->create();
    $ipAddress = '***********';
    
    // Create a review 45 minutes ago
    Review::factory()->create([
        'pincode_id' => $pincode->id,
        'ip_address' => $ipAddress,
        'created_at' => now()->subMinutes(45)
    ]);
    
    // Should be true with 60 minute window (default)
    expect(Review::hasRecentReview($pincode->id, $ipAddress))->toBeTrue();
    
    // Should be false with 30 minute window
    expect(Review::hasRecentReview($pincode->id, $ipAddress, 30))->toBeFalse();
});

test('review can be updated', function () {
    $review = Review::factory()->create([
        'name' => 'Original Name',
        'comment' => 'Original review content',
        'status' => 'pending'
    ]);
    
    $review->update([
        'name' => 'Updated Name',
        'comment' => 'Updated review content',
        'status' => 'approved'
    ]);
    
    $review->refresh();
    
    expect($review->name)->toBe('Updated Name')
        ->and($review->comment)->toBe('Updated review content')
        ->and($review->status)->toBe('approved');
});