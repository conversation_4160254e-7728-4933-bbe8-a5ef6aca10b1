<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class CurrencyRate extends Model
{
    protected $fillable = [
        'from_currency',
        'to_currency',
        'rate',
        'source',
    ];

    protected $casts = [
        'rate' => 'decimal:6',
    ];

    /**
     * Source constants.
     */
    const SOURCE_MANUAL = 'manual';
    const SOURCE_API = 'api';
    const SOURCE_EXTERNAL = 'external';

    /**
     * Get exchange rate between two currencies.
     */
    public static function getRate($fromCurrency, $toCurrency)
    {
        // If same currency, return 1
        if (strtoupper($fromCurrency) === strtoupper($toCurrency)) {
            return 1.0;
        }

        $cacheKey = "currency_rate_{$fromCurrency}_{$toCurrency}";
        
        return Cache::remember($cacheKey, 3600, function () use ($fromCurrency, $toCurrency) {
            $rate = static::where('from_currency', strtoupper($fromCurrency))
                         ->where('to_currency', strtoupper($toCurrency))
                         ->latest()
                         ->first();
            
            return $rate ? $rate->rate : null;
        });
    }

    /**
     * Convert amount from one currency to another.
     */
    public static function convert($amount, $fromCurrency, $toCurrency)
    {
        $rate = static::getRate($fromCurrency, $toCurrency);
        
        if ($rate === null) {
            throw new \Exception("Exchange rate not found for {$fromCurrency} to {$toCurrency}");
        }
        
        return round($amount * $rate, 2);
    }

    /**
     * Set or update exchange rate.
     */
    public static function setRate($fromCurrency, $toCurrency, $rate, $source = self::SOURCE_MANUAL)
    {
        $fromCurrency = strtoupper($fromCurrency);
        $toCurrency = strtoupper($toCurrency);
        
        $currencyRate = static::updateOrCreate(
            [
                'from_currency' => $fromCurrency,
                'to_currency' => $toCurrency,
            ],
            [
                'rate' => $rate,
                'source' => $source,
            ]
        );

        // Clear cache
        Cache::forget("currency_rate_{$fromCurrency}_{$toCurrency}");
        
        return $currencyRate;
    }

    /**
     * Get all rates for a specific currency.
     */
    public static function getRatesFor($currency)
    {
        $currency = strtoupper($currency);
        
        return static::where('from_currency', $currency)
                    ->orWhere('to_currency', $currency)
                    ->get();
    }

    /**
     * Get supported currencies.
     */
    public static function getSupportedCurrencies()
    {
        $fromCurrencies = static::distinct()->pluck('from_currency');
        $toCurrencies = static::distinct()->pluck('to_currency');
        
        return $fromCurrencies->merge($toCurrencies)->unique()->sort()->values();
    }

    /**
     * Check if conversion is supported.
     */
    public static function isConversionSupported($fromCurrency, $toCurrency)
    {
        if (strtoupper($fromCurrency) === strtoupper($toCurrency)) {
            return true;
        }
        
        return static::where('from_currency', strtoupper($fromCurrency))
                    ->where('to_currency', strtoupper($toCurrency))
                    ->exists();
    }

    /**
     * Get inverse rate (if A->B exists, calculate B->A).
     */
    public static function getInverseRate($fromCurrency, $toCurrency)
    {
        $directRate = static::getRate($fromCurrency, $toCurrency);
        
        if ($directRate) {
            return $directRate;
        }
        
        // Try inverse rate
        $inverseRate = static::getRate($toCurrency, $fromCurrency);
        
        if ($inverseRate && $inverseRate > 0) {
            return round(1 / $inverseRate, 6);
        }
        
        return null;
    }

    /**
     * Update rates from external API (placeholder for future implementation).
     */
    public static function updateFromAPI($apiSource = 'external')
    {
        // This is a placeholder for future API integration
        // Could integrate with services like:
        // - exchangerate-api.com
        // - fixer.io
        // - currencylayer.com
        
        // For now, just return false to indicate no update
        return false;
    }

    /**
     * Get rate with fallback to inverse calculation.
     */
    public static function getRateWithFallback($fromCurrency, $toCurrency)
    {
        // Try direct rate first
        $rate = static::getRate($fromCurrency, $toCurrency);
        
        if ($rate !== null) {
            return $rate;
        }
        
        // Try inverse rate
        return static::getInverseRate($fromCurrency, $toCurrency);
    }

    /**
     * Convert amount with fallback to inverse rate.
     */
    public static function convertWithFallback($amount, $fromCurrency, $toCurrency)
    {
        $rate = static::getRateWithFallback($fromCurrency, $toCurrency);
        
        if ($rate === null) {
            throw new \Exception("Exchange rate not available for {$fromCurrency} to {$toCurrency}");
        }
        
        return round($amount * $rate, 2);
    }

    /**
     * Bulk update rates.
     */
    public static function bulkUpdateRates(array $rates, $source = self::SOURCE_MANUAL)
    {
        $updated = 0;
        
        foreach ($rates as $rateData) {
            if (isset($rateData['from'], $rateData['to'], $rateData['rate'])) {
                static::setRate($rateData['from'], $rateData['to'], $rateData['rate'], $source);
                $updated++;
            }
        }
        
        return $updated;
    }

    /**
     * Get rate age in hours.
     */
    public function getAgeInHours()
    {
        return $this->updated_at->diffInHours(now());
    }

    /**
     * Check if rate is stale (older than specified hours).
     */
    public function isStale($hours = 24)
    {
        return $this->getAgeInHours() > $hours;
    }

    /**
     * Get formatted rate display.
     */
    public function getFormattedRate()
    {
        return "1 {$this->from_currency} = {$this->rate} {$this->to_currency}";
    }

    /**
     * Scope for rates from a specific currency.
     */
    public function scopeFromCurrency($query, $currency)
    {
        return $query->where('from_currency', strtoupper($currency));
    }

    /**
     * Scope for rates to a specific currency.
     */
    public function scopeToCurrency($query, $currency)
    {
        return $query->where('to_currency', strtoupper($currency));
    }

    /**
     * Scope for rates by source.
     */
    public function scopeBySource($query, $source)
    {
        return $query->where('source', $source);
    }

    /**
     * Scope for manual rates.
     */
    public function scopeManual($query)
    {
        return $query->where('source', self::SOURCE_MANUAL);
    }

    /**
     * Scope for API rates.
     */
    public function scopeApi($query)
    {
        return $query->where('source', self::SOURCE_API);
    }

    /**
     * Clear all currency rate cache.
     */
    public static function clearCache()
    {
        $currencies = static::getSupportedCurrencies();
        
        foreach ($currencies as $from) {
            foreach ($currencies as $to) {
                if ($from !== $to) {
                    Cache::forget("currency_rate_{$from}_{$to}");
                }
            }
        }
    }

    /**
     * Boot method to clear cache on model events.
     */
    protected static function boot()
    {
        parent::boot();
        
        static::saved(function ($model) {
            Cache::forget("currency_rate_{$model->from_currency}_{$model->to_currency}");
        });
        
        static::deleted(function ($model) {
            Cache::forget("currency_rate_{$model->from_currency}_{$model->to_currency}");
        });
    }
}