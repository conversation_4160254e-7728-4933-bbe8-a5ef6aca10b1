<?php

namespace App\Services\Payment;

class WebhookResponse
{
    public bool $success;
    public ?string $eventType = null;
    public ?string $paymentId = null;
    public ?string $orderId = null;
    public ?string $status = null;
    public ?string $message = null;
    public ?array $payload = null;
    public ?array $actions = null;
    public bool $processed = false;

    public function __construct(bool $success = false)
    {
        $this->success = $success;
        $this->actions = [];
    }

    /**
     * Create a successful webhook response.
     */
    public static function success(array $data = []): self
    {
        $response = new self(true);
        
        if (isset($data['event_type'])) {
            $response->eventType = $data['event_type'];
        }
        
        if (isset($data['payment_id'])) {
            $response->paymentId = $data['payment_id'];
        }
        
        if (isset($data['order_id'])) {
            $response->orderId = $data['order_id'];
        }
        
        if (isset($data['status'])) {
            $response->status = $data['status'];
        }
        
        if (isset($data['message'])) {
            $response->message = $data['message'];
        }
        
        if (isset($data['payload'])) {
            $response->payload = $data['payload'];
        }
        
        if (isset($data['actions'])) {
            $response->actions = $data['actions'];
        }
        
        return $response;
    }

    /**
     * Create an error webhook response.
     */
    public static function error(string $message, ?array $payload = null): self
    {
        $response = new self(false);
        $response->message = $message;
        $response->payload = $payload;
        
        return $response;
    }

    /**
     * Set event type.
     */
    public function setEventType(string $eventType): self
    {
        $this->eventType = $eventType;
        return $this;
    }

    /**
     * Set payment ID.
     */
    public function setPaymentId(string $paymentId): self
    {
        $this->paymentId = $paymentId;
        return $this;
    }

    /**
     * Set order ID.
     */
    public function setOrderId(string $orderId): self
    {
        $this->orderId = $orderId;
        return $this;
    }

    /**
     * Set status.
     */
    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    /**
     * Set message.
     */
    public function setMessage(string $message): self
    {
        $this->message = $message;
        return $this;
    }

    /**
     * Set payload.
     */
    public function setPayload(array $payload): self
    {
        $this->payload = $payload;
        return $this;
    }

    /**
     * Add an action to be performed.
     */
    public function addAction(string $action, array $data = []): self
    {
        $this->actions[] = [
            'action' => $action,
            'data' => $data,
        ];
        return $this;
    }

    /**
     * Mark as processed.
     */
    public function markAsProcessed(): self
    {
        $this->processed = true;
        return $this;
    }

    /**
     * Check if the response is successful.
     */
    public function isSuccess(): bool
    {
        return $this->success;
    }

    /**
     * Check if the response is an error.
     */
    public function isError(): bool
    {
        return !$this->success;
    }

    /**
     * Check if webhook was processed.
     */
    public function isProcessed(): bool
    {
        return $this->processed;
    }

    /**
     * Get actions to be performed.
     */
    public function getActions(): array
    {
        return $this->actions ?? [];
    }

    /**
     * Get the response as an array.
     */
    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'event_type' => $this->eventType,
            'payment_id' => $this->paymentId,
            'order_id' => $this->orderId,
            'status' => $this->status,
            'message' => $this->message,
            'payload' => $this->payload,
            'actions' => $this->actions,
            'processed' => $this->processed,
        ];
    }

    /**
     * Convert to JSON.
     */
    public function toJson(): string
    {
        return json_encode($this->toArray());
    }
}