<div class="mb-3">
    <label class="form-label">Bank Name *</label>
    <input type="text" class="form-control" name="configuration[bank_name]" 
           value="{{ $config['bank_name'] ?? '' }}" required
           placeholder="Enter bank name">
</div>

<div class="mb-3">
    <label class="form-label">Account Holder Name *</label>
    <input type="text" class="form-control" name="configuration[account_name]" 
           value="{{ $config['account_name'] ?? '' }}" required
           placeholder="Enter account holder name">
</div>

<div class="row">
    <div class="col-md-6 mb-3">
        <label class="form-label">Account Number *</label>
        <input type="text" class="form-control" name="configuration[account_number]" 
               value="{{ $config['account_number'] ?? '' }}" required
               placeholder="Enter account number">
    </div>
    <div class="col-md-6 mb-3">
        <label class="form-label">IFSC Code *</label>
        <input type="text" class="form-control" name="configuration[ifsc_code]" 
               value="{{ $config['ifsc_code'] ?? '' }}" required
               placeholder="Enter IFSC code">
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-3">
        <label class="form-label">UPI ID</label>
        <input type="text" class="form-control" name="configuration[upi_id]" 
               value="{{ $config['upi_id'] ?? '' }}"
               placeholder="Enter UPI ID (optional)">
        <small class="text-muted">For UPI-based QR codes</small>
    </div>
    <div class="col-md-6 mb-3">
        <label class="form-label">Branch Name</label>
        <input type="text" class="form-control" name="configuration[branch_name]" 
               value="{{ $config['branch_name'] ?? '' }}"
               placeholder="Enter branch name">
    </div>
</div>

<div class="mb-3">
    <label class="form-label">Payment Instructions</label>
    <textarea class="form-control" name="configuration[instructions]" rows="3">{{ $config['instructions'] ?? 'Please transfer the exact amount to the account details provided and upload the payment proof for verification.' }}</textarea>
    <small class="text-muted">Instructions shown to customers during payment</small>
</div>

<div class="row">
    <div class="col-md-6 mb-3">
        <label class="form-label">Auto-Verification Timeout (hours)</label>
        <input type="number" class="form-control" name="configuration[verification_timeout]" 
               value="{{ $config['verification_timeout'] ?? 24 }}" min="1" max="168">
        <small class="text-muted">Time to wait before marking payment as failed</small>
    </div>
    <div class="col-md-6 mb-3">
        <label class="form-label">QR Code Size</label>
        <select class="form-select" name="configuration[qr_size]">
            <option value="200" {{ ($config['qr_size'] ?? 200) == 200 ? 'selected' : '' }}>200x200 px</option>
            <option value="250" {{ ($config['qr_size'] ?? 200) == 250 ? 'selected' : '' }}>250x250 px</option>
            <option value="300" {{ ($config['qr_size'] ?? 200) == 300 ? 'selected' : '' }}>300x300 px</option>
        </select>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-3">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" name="configuration[require_proof]" 
                   value="1" {{ ($config['require_proof'] ?? true) ? 'checked' : '' }}>
            <label class="form-check-label">Require Payment Proof</label>
        </div>
        <small class="text-muted">Customers must upload payment proof</small>
    </div>
    <div class="col-md-6 mb-3">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" name="configuration[auto_generate_qr]" 
                   value="1" {{ ($config['auto_generate_qr'] ?? true) ? 'checked' : '' }}>
            <label class="form-check-label">Auto-Generate QR Code</label>
        </div>
        <small class="text-muted">Automatically generate QR codes for payments</small>
    </div>
</div>

<div class="alert alert-warning">
    <h6 class="alert-heading">
        <i class="mdi mdi-alert me-2"></i>
        Important Notes:
    </h6>
    <ul class="mb-0 small">
        <li>Ensure all bank details are accurate to avoid payment issues</li>
        <li>QR code payments require manual verification by admin</li>
        <li>Set up proper notification system for payment proof uploads</li>
        <li>Consider compliance with local banking regulations</li>
    </ul>
</div>