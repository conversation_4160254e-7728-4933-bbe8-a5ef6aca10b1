@extends('admin.layouts.admin')

@section('title', 'API Token Management')
@section('page-title', 'API Token Management')

@section('content')
<div class="bg-bg-light dark:bg-bg-dark rounded-lg shadow-md overflow-hidden">
    <div class="p-4 sm:p-6 border-b border-border-light dark:border-border-dark">
        <div class="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
            <h2 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">API Tokens</h2>
            <div class="w-full lg:w-auto">
                <form action="{{ route('admin.api-tokens.index') }}" method="GET" class="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-4">
                    <div class="flex-1 sm:flex-none">
                        <input type="text" name="search" value="{{ request('search') }}" placeholder="Search tokens or users..." 
                            class="w-full sm:w-auto rounded-md shadow-sm border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20">
                    </div>
                    <div class="flex-1 sm:flex-none">
                        <select name="user_id" class="w-full sm:w-auto rounded-md shadow-sm border-border-light dark:border-border-dark bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20">
                            <option value="">All Users</option>
                            @foreach($users as $user)
                                <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                    {{ $user->name }} ({{ $user->email }})
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <button type="submit" class="bg-primary-light dark:bg-primary-dark hover:bg-primary-light/90 dark:hover:bg-primary-dark/90 text-white px-4 py-2 rounded-md">
                        Filter
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 p-4 sm:p-6">
        <div class="bg-bg-light dark:bg-bg-dark rounded-lg p-4 border border-border-light dark:border-border-dark">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-primary-light/10 dark:bg-primary-dark/10 flex-shrink-0">
                    <svg class="h-6 w-6 text-primary-light dark:text-primary-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                    </svg>
                </div>
                <div class="ml-4 min-w-0">
                    <h3 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Total Tokens</h3>
                    <p class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">{{ $tokenStats['total_tokens'] }}</p>
                </div>
            </div>
        </div>
        <div class="bg-bg-light dark:bg-bg-dark rounded-lg p-4 border border-border-light dark:border-border-dark">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-accent-light/10 dark:bg-accent-dark/10 flex-shrink-0">
                    <svg class="h-6 w-6 text-accent-light dark:text-accent-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <div class="ml-4 min-w-0">
                    <h3 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Active Tokens</h3>
                    <p class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">{{ $tokenStats['active_tokens'] }}</p>
                </div>
            </div>
        </div>
        <div class="bg-bg-light dark:bg-bg-dark rounded-lg p-4 border border-border-light dark:border-border-dark sm:col-span-2 lg:col-span-1">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-accent-light/10 dark:bg-accent-dark/10 flex-shrink-0">
                    <svg class="h-6 w-6 text-accent-light dark:text-accent-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div class="ml-4 min-w-0">
                    <h3 class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Total API Requests</h3>
                    <p class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">{{ $tokenStats['total_requests'] }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tokens Table -->
    <div class="p-4 sm:p-6">
        <!-- Mobile Card View (Hidden on larger screens) -->
        <div class="block lg:hidden space-y-4">
            @forelse($tokens as $token)
                <div class="bg-bg-secondary-light dark:bg-bg-secondary-dark rounded-lg p-4 border border-border-light dark:border-border-dark">
                    <div class="flex items-start justify-between mb-3">
                        <div class="min-w-0 flex-1">
                            <h3 class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark truncate">{{ $token->name }}</h3>
                            <p class="text-sm text-text-primary-light dark:text-text-primary-dark">{{ $token->tokenable->name }}</p>
                            <p class="text-xs text-text-secondary-light dark:text-text-secondary-dark">{{ $token->tokenable->email }}</p>
                        </div>
                        <div class="flex-shrink-0 ml-3">
                            @php
                                $isExpired = $token->created_at->addDays(\App\Models\User::TOKEN_EXPIRY_DAYS) < now();
                            @endphp
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $isExpired ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' : 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' }}">
                                {{ $isExpired ? 'Expired' : 'Active' }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-xs mb-4">
                        <div>
                            <span class="text-text-secondary-light dark:text-text-secondary-dark">Created:</span>
                            <div class="text-text-primary-light dark:text-text-primary-dark">{{ $token->created_at->format('M d, Y') }}</div>
                            <div class="text-text-secondary-light dark:text-text-secondary-dark">{{ $token->created_at->format('h:i A') }}</div>
                        </div>
                        <div>
                            <span class="text-text-secondary-light dark:text-text-secondary-dark">Last Used:</span>
                            <div class="text-text-primary-light dark:text-text-primary-dark">
                                {{ $token->last_used_at ? $token->last_used_at->format('M d, Y') : 'Never' }}
                            </div>
                            @if($token->last_used_at)
                                <div class="text-text-secondary-light dark:text-text-secondary-dark">{{ $token->last_used_at->format('h:i A') }}</div>
                            @endif
                        </div>
                    </div>
                    
                    <div class="flex flex-col sm:flex-row gap-2">
                        <a href="{{ route('admin.api-tokens.show', $token->id) }}" class="text-center sm:text-left bg-primary-light dark:bg-primary-dark hover:bg-primary-light/90 dark:hover:bg-primary-dark/90 text-white px-3 py-2 rounded-md text-sm flex-1">
                            View Details
                        </a>
                        <form action="{{ route('admin.api-tokens.destroy', $token->id) }}" method="POST" class="flex-1">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="w-full bg-red-500 dark:bg-red-600 hover:bg-red-600 dark:hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm" onclick="return confirm('Are you sure you want to revoke this token?')">
                                Revoke Token
                            </button>
                        </form>
                    </div>
                </div>
            @empty
                <div class="text-center py-8">
                    <p class="text-text-secondary-light dark:text-text-secondary-dark">No API tokens found</p>
                </div>
            @endforelse
        </div>

        <!-- Desktop Table View (Hidden on mobile) -->
        <div class="hidden lg:block overflow-x-auto">
            <table class="min-w-full bg-bg-light dark:bg-bg-dark">
                <thead class="bg-bg-secondary-light dark:bg-bg-secondary-dark border-b border-border-light dark:border-border-dark">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Token Name</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">User</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Created</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Last Used</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-border-light dark:divide-border-dark">
                    @forelse($tokens as $token)
                        <tr class="hover:bg-bg-secondary-light dark:hover:bg-bg-secondary-dark">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark">{{ $token->name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-text-primary-light dark:text-text-primary-dark">{{ $token->tokenable->name }}</div>
                                <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark">{{ $token->tokenable->email }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-text-primary-light dark:text-text-primary-dark">{{ $token->created_at->format('M d, Y') }}</div>
                                <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark">{{ $token->created_at->format('h:i A') }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-text-primary-light dark:text-text-primary-dark">
                                    {{ $token->last_used_at ? $token->last_used_at->format('M d, Y') : 'Never' }}
                                </div>
                                @if($token->last_used_at)
                                    <div class="text-sm text-text-secondary-light dark:text-text-secondary-dark">{{ $token->last_used_at->format('h:i A') }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @php
                                    $isExpired = $token->created_at->addDays(\App\Models\User::TOKEN_EXPIRY_DAYS) < now();
                                @endphp
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $isExpired ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' : 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' }}">
                                    {{ $isExpired ? 'Expired' : 'Active' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{{ route('admin.api-tokens.show', $token->id) }}" class="text-primary-light dark:text-primary-dark hover:text-primary-light-hover dark:hover:text-primary-dark-hover mr-3">View</a>
                                <form action="{{ route('admin.api-tokens.destroy', $token->id) }}" method="POST" class="inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to revoke this token?')">
                                        Revoke
                                    </button>
                                </form>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-text-secondary-light dark:text-text-secondary-dark text-center">
                                No API tokens found
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-2 sm:px-6 py-4 bg-bg-light dark:bg-bg-dark border-t border-border-light dark:border-border-dark">
            {{ $tokens->links() }}
        </div>
    </div>
</div>
@endsection