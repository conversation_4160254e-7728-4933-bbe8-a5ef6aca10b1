<?php

use App\Models\PersonalAccessToken;
use App\Models\User;
use App\Models\ApiRequest;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('personal access token can be created', function () {
    $token = PersonalAccessToken::factory()->create([
        'name' => 'Test Token',
        'abilities' => ['read', 'write'],
        'expires_at' => now()->addDays(30),
    ]);

    expect($token)->toBeInstanceOf(PersonalAccessToken::class)
        ->and($token->name)->toBe('Test Token')
        ->and($token->abilities)->toBe(['read', 'write'])
        ->and($token->expires_at->format('Y-m-d'))->toBe(now()->addDays(30)->format('Y-m-d'));
});

test('personal access token belongs to a tokenable model', function () {
    $user = User::factory()->create();
    $token = PersonalAccessToken::factory()->create([
        'tokenable_id' => $user->id,
        'tokenable_type' => User::class,
    ]);

    expect($token->tokenable)->toBeInstanceOf(User::class)
        ->and($token->tokenable->id)->toBe($user->id);
});

test('tokenable relationship returns MorphTo instance', function () {
    $token = new PersonalAccessToken();
    
    expect($token->tokenable())->toBeInstanceOf(\Illuminate\Database\Eloquent\Relations\MorphTo::class);
});

test('personal access token has api requests relationship', function () {
    $token = PersonalAccessToken::factory()->create();
    $apiRequest = ApiRequest::factory()->create([
        'personal_access_token_id' => $token->id
    ]);

    expect($token->apiRequests)->toHaveCount(1)
        ->and($token->apiRequests->first())->toBeInstanceOf(ApiRequest::class);
});

test('apiRequests relationship returns HasMany instance', function () {
    $token = new PersonalAccessToken();
    
    expect($token->apiRequests())->toBeInstanceOf(\Illuminate\Database\Eloquent\Relations\HasMany::class);
});

test('personal access token has correct table name', function () {
    $token = new PersonalAccessToken();
    
    expect($token->getTable())->toBe('personal_access_tokens');
});

test('personal access token has correct fillable attributes', function () {
    $expectedFillable = [
        'name',
        'token',
        'abilities',
        'expires_at',
    ];

    expect((new PersonalAccessToken())->getFillable())->toBe($expectedFillable);
});

test('personal access token has correct hidden attributes', function () {
    $expectedHidden = [
        'token',
    ];

    expect((new PersonalAccessToken())->getHidden())->toBe($expectedHidden);
});

test('personal access token has correct casts', function () {
    $expectedCasts = [
        'abilities' => 'json',
        'last_used_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    expect((new PersonalAccessToken())->getCasts())->toMatchArray($expectedCasts);
});

test('personal access token can be created with abilities', function () {
    $token = PersonalAccessToken::factory()->create([
        'abilities' => ['read', 'write', 'delete'],
    ]);

    expect($token->abilities)->toBeArray()
        ->and($token->abilities)->toBe(['read', 'write', 'delete']);
});

test('personal access token can handle null abilities', function () {
    $token = PersonalAccessToken::factory()->create([
        'abilities' => null,
    ]);

    expect($token->abilities)->toBeNull();
});

test('personal access token can track last used timestamp', function () {
    $token = PersonalAccessToken::factory()->create([
        'last_used_at' => null,
    ]);

    expect($token->last_used_at)->toBeNull();

    $lastUsedTime = now();
    $token->last_used_at = $lastUsedTime;
    $token->save();
    $token->refresh();

    expect($token->last_used_at->format('Y-m-d H:i'))->toBe($lastUsedTime->format('Y-m-d H:i'));
});

test('personal access token can be created with expiry date', function () {
    $expiryDate = now()->addDays(7);
    $token = PersonalAccessToken::factory()->create([
        'expires_at' => $expiryDate,
    ]);

    expect($token->expires_at->format('Y-m-d'))->toBe($expiryDate->format('Y-m-d'));
});

test('personal access token can be created without expiry date', function () {
    $token = PersonalAccessToken::factory()->create([
        'expires_at' => null,
    ]);

    expect($token->expires_at)->toBeNull();
});