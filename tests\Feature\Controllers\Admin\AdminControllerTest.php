<?php

use App\Models\User;
use App\Models\PinCode;
use App\Models\Comment;
use App\Models\BlogPost;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use function Pest\Laravel\{get, post, delete, actingAs};

uses(RefreshDatabase::class);

beforeEach(function () {
    // Only the admin user is needed for most tests.
    // Specific data for other tests will be created within them.
    $this->admin = User::factory()->create([
        'role' => User::ROLE_ADMIN,
        'status' => User::STATUS_ACTIVE,
    ]);
});

test('admin can access dashboard', function () {
    $this->actingAs($this->admin)
        ->get(route('admin.dashboard'))
        ->assertStatus(200)
        ->assertViewIs('admin.dashboard')
        ->assertViewHas('stats');
});

test('dashboard shows correct statistics', function () {
    // Arrange: Create specific data needed for this test.
    User::factory(5)->create(['status' => User::STATUS_ACTIVE]);
    User::factory(3)->create(['status' => User::STATUS_INACTIVE]);
    PinCode::factory(10)->create();

    // Act
    $response = $this->actingAs($this->admin)
        ->get(route('admin.dashboard'));

    // Assert
    $stats = $response->viewData('stats');
    $expectedTotalUsers = User::count();
    $expectedActiveUsers = User::where('status', User::STATUS_ACTIVE)->count();
    $expectedTotalPincodes = PinCode::count();
    
    expect($stats['total_users'])->toBe($expectedTotalUsers)
        ->and($stats['active_users'])->toBe($expectedActiveUsers)
        ->and($stats['total_pincodes'])->toBe($expectedTotalPincodes)
        ->and($stats['system_health'])->toHaveKeys(['php_version', 'laravel_version', 'disk_usage']);
});

test('dashboard statistics are cached', function () {
    // Ensure a clean cache state
    Cache::forget('admin.dashboard.stats');

    // Arrange: Create specific data for this test to ensure cache accuracy.
    User::factory(2)->create();

    // Act: First request to populate cache
    $response1 = $this->actingAs($this->admin)
        ->get(route('admin.dashboard'));
    $stats1 = $response1->viewData('stats');

    // Create a new user, which should NOT be reflected in the cached stats
    User::factory()->create();

    // Act: Second request should use cached data
    $response2 = $this->actingAs($this->admin)
        ->get(route('admin.dashboard'));
    $stats2 = $response2->viewData('stats');

    // Assert that the stats from the first and second requests are identical
    expect($stats2['total_users'])->toBe($stats1['total_users']);
});

test('admin can access profile page', function () {
    $this->actingAs($this->admin)
        ->get(route('admin.profile.index'))
        ->assertStatus(200)
        ->assertViewIs('admin.profile')
        ->assertViewHas('user');
});

test('admin can enable two factor authentication', function () {
    $this->actingAs($this->admin)
        ->post(route('admin.profile.2fa.enable'))
        ->assertRedirect(route('admin.profile.index'))
        ->assertSessionHas('success', 'Two-factor authentication enabled successfully.');
    
    expect($this->admin->fresh()->two_factor_enabled)->toBeTrue();
});

test('admin can disable two factor authentication', function () {
    // Arrange
    $this->admin->update(['two_factor_enabled' => true]);

    // Act & Assert
    $this->actingAs($this->admin)
        ->post(route('admin.profile.2fa.disable'))
        ->assertRedirect(route('admin.profile.index'))
        ->assertSessionHas('success', 'Two-factor authentication disabled successfully.');
    
    expect($this->admin->fresh()->two_factor_enabled)->toBeFalse();
});

test('admin can toggle maintenance mode', function () {
    // Ensure we start in up state, as another test could leave it down.
    Artisan::call('up');
    expect(app()->isDownForMaintenance())->toBeFalse();

    // First toggle - should go down
    $this->actingAs($this->admin)
        ->post(route('admin.maintenance.toggle'))
        ->assertRedirect()
        ->assertSessionHas('success');
    expect(app()->isDownForMaintenance())->toBeTrue();

    // Second toggle - should go up to clean up its own state
    $this->actingAs($this->admin)
        ->withoutMiddleware(\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance::class)
        ->post(route('admin.maintenance.toggle'))
        ->assertRedirect()
        ->assertSessionHas('success');
    expect(app()->isDownForMaintenance())->toBeFalse();
});

test('admin can clear application cache', function () {
    // Mock Artisan to prevent the test from actually clearing the application cache,
    // which could interfere with other tests.
    Artisan::shouldReceive('call')
        ->with('cache:clear')
        ->once()
        ->andReturn(0);
    Artisan::shouldReceive('call')
        ->with('view:clear')
        ->once()
        ->andReturn(0);
    Artisan::shouldReceive('call')
        ->with('config:clear')
        ->once()
        ->andReturn(0);

    $this->actingAs($this->admin)
        ->get(route('admin.clear-cache'))
        ->assertRedirect()
        ->assertSessionHas('success', 'Cache cleared successfully!');
});

test('non-admin users cannot access admin dashboard', function () {
    // Arrange
    $user = User::factory()->create([
        'role' => User::ROLE_USER,
        'status' => User::STATUS_ACTIVE,
    ]);

    // Act & Assert
    $this->actingAs($user)
        ->get(route('admin.dashboard'))
        ->assertStatus(403);
});

test('inactive admin cannot access admin dashboard', function () {
    // Arrange
    $inactiveAdmin = User::factory()->create([
        'role' => User::ROLE_ADMIN,
        'status' => User::STATUS_INACTIVE,
    ]);

    // Act & Assert
    $this->actingAs($inactiveAdmin)
        ->get(route('admin.dashboard'))
        ->assertStatus(403);
});

test('admin can update password with valid data', function () {
    // Arrange
    $currentPassword = 'current-password';
    $newPassword = 'new-password123';
    
    $this->admin->update([
        'password' => Hash::make($currentPassword)
    ]);

    // Act & Assert
    $this->actingAs($this->admin)
        ->put(route('admin.profile.password'), [
            'current_password' => $currentPassword,
            'new_password' => $newPassword,
            'new_password_confirmation' => $newPassword,
        ])
        ->assertRedirect()
        ->assertSessionHas('success', 'Password updated successfully!');

    // Verify password was actually changed
    expect(Hash::check($newPassword, $this->admin->fresh()->password))->toBeTrue();
});

test('admin password update fails with incorrect current password', function () {
    // Arrange
    $currentPassword = 'current-password';
    $wrongPassword = 'wrong-password';
    $newPassword = 'new-password123';
    
    $this->admin->update([
        'password' => Hash::make($currentPassword)
    ]);

    // Act & Assert
    $this->actingAs($this->admin)
        ->put(route('admin.profile.password'), [
            'current_password' => $wrongPassword,
            'new_password' => $newPassword,
            'new_password_confirmation' => $newPassword,
        ])
        ->assertRedirect()
        ->assertSessionHasErrors(['current_password' => 'The current password is incorrect.']);

    // Verify password was not changed
    expect(Hash::check($currentPassword, $this->admin->fresh()->password))->toBeTrue();
});

test('admin password update requires password confirmation', function () {
    // Arrange
    $currentPassword = 'current-password';
    $newPassword = 'new-password123';
    
    $this->admin->update([
        'password' => Hash::make($currentPassword)
    ]);

    // Act & Assert
    $this->actingAs($this->admin)
        ->put(route('admin.profile.password'), [
            'current_password' => $currentPassword,
            'new_password' => $newPassword,
            'new_password_confirmation' => 'different-password',
        ])
        ->assertRedirect()
        ->assertSessionHasErrors(['new_password']);
});

test('admin password update requires minimum password length', function () {
    // Arrange
    $currentPassword = 'current-password';
    $shortPassword = '1234567'; // 7 characters - should fail min 8 requirement
    
    $this->admin->update([
        'password' => Hash::make($currentPassword)
    ]);

    // Act & Assert
    $this->actingAs($this->admin)
        ->put(route('admin.profile.password'), [
            'current_password' => $currentPassword,
            'new_password' => $shortPassword,
            'new_password_confirmation' => $shortPassword,
        ])
        ->assertRedirect()
        ->assertSessionHasErrors(['new_password']);
});

test('admin password update returns json response when expecting json', function () {
    // Arrange
    $currentPassword = 'current-password';
    $newPassword = 'new-password123';
    
    $this->admin->update([
        'password' => Hash::make($currentPassword)
    ]);

    // Act & Assert
    $this->actingAs($this->admin)
        ->putJson(route('admin.profile.password'), [
            'current_password' => $currentPassword,
            'new_password' => $newPassword,
            'new_password_confirmation' => $newPassword,
        ])
        ->assertStatus(200)
        ->assertJson([
            'message' => 'Password updated successfully!'
        ]);
});

test('non-admin user cannot update admin password', function () {
    // Arrange
    $user = User::factory()->create([
        'role' => User::ROLE_USER,
        'status' => User::STATUS_ACTIVE,
    ]);
    
    $currentPassword = 'current-password';
    $newPassword = 'new-password123';
    
    $user->update([
        'password' => Hash::make($currentPassword)
    ]);

    // Act & Assert - Admin middleware blocks non-admin users with 403
    $this->actingAs($user)
        ->put(route('admin.profile.password'), [
            'current_password' => $currentPassword,
            'new_password' => $newPassword,
            'new_password_confirmation' => $newPassword,
        ])
        ->assertStatus(403);
});

test('admin password update requires all fields', function () {
    // Act & Assert - Test missing current_password
    $this->actingAs($this->admin)
        ->put(route('admin.profile.password'), [
            'new_password' => 'new-password123',
            'new_password_confirmation' => 'new-password123',
        ])
        ->assertRedirect()
        ->assertSessionHasErrors(['current_password']);

    // Act & Assert - Test missing new_password
    $this->actingAs($this->admin)
        ->put(route('admin.profile.password'), [
            'current_password' => 'current-password',
            'new_password_confirmation' => 'new-password123',
        ])
        ->assertRedirect()
        ->assertSessionHasErrors(['new_password']);
});

// Profile Update Tests
test('admin can update profile with valid data', function () {
    // Arrange
    $newName = 'Updated Admin Name';
    $newEmail = '<EMAIL>';

    // Act & Assert
    $this->actingAs($this->admin)
        ->put(route('admin.profile.update'), [
            'name' => $newName,
            'email' => $newEmail,
        ])
        ->assertRedirect()
        ->assertSessionHas('success', 'Profile updated successfully!');

    // Verify profile was actually updated
    $this->admin->refresh();
    expect($this->admin->name)->toBe($newName)
        ->and($this->admin->email)->toBe($newEmail);
});

test('admin profile update validates required fields', function () {
    // Act & Assert - Test missing name
    $this->actingAs($this->admin)
        ->put(route('admin.profile.update'), [
            'email' => '<EMAIL>',
        ])
        ->assertRedirect()
        ->assertSessionHasErrors(['name']);

    // Act & Assert - Test missing email
    $this->actingAs($this->admin)
        ->put(route('admin.profile.update'), [
            'name' => 'Test Name',
        ])
        ->assertRedirect()
        ->assertSessionHasErrors(['email']);
});

test('admin profile update validates email format', function () {
    // Act & Assert
    $this->actingAs($this->admin)
        ->put(route('admin.profile.update'), [
            'name' => 'Test Name',
            'email' => 'invalid-email-format',
        ])
        ->assertRedirect()
        ->assertSessionHasErrors(['email']);
});

test('admin profile update validates unique email', function () {
    // Arrange - Create another user with an email
    $existingUser = User::factory()->create([
        'email' => '<EMAIL>',
        'role' => User::ROLE_USER,
    ]);

    // Act & Assert
    $this->actingAs($this->admin)
        ->put(route('admin.profile.update'), [
            'name' => 'Test Name',
            'email' => '<EMAIL>',
        ])
        ->assertRedirect()
        ->assertSessionHasErrors(['email']);
});

test('admin can keep same email when updating profile', function () {
    // Arrange
    $originalEmail = $this->admin->email;
    $newName = 'Updated Name';

    // Act & Assert
    $this->actingAs($this->admin)
        ->put(route('admin.profile.update'), [
            'name' => $newName,
            'email' => $originalEmail,
        ])
        ->assertRedirect()
        ->assertSessionHas('success', 'Profile updated successfully!');

    // Verify profile was updated
    $this->admin->refresh();
    expect($this->admin->name)->toBe($newName)
        ->and($this->admin->email)->toBe($originalEmail);
});

test('admin profile update validates name length', function () {
    // Act & Assert - Test name too long (over 255 characters)
    $longName = str_repeat('a', 256);
    
    $this->actingAs($this->admin)
        ->put(route('admin.profile.update'), [
            'name' => $longName,
            'email' => '<EMAIL>',
        ])
        ->assertRedirect()
        ->assertSessionHasErrors(['name']);
});

test('admin profile update validates email length', function () {
    // Act & Assert - Test email too long (over 255 characters)
    $longEmail = str_repeat('a', 250) . '@example.com'; // Over 255 chars total
    
    $this->actingAs($this->admin)
        ->put(route('admin.profile.update'), [
            'name' => 'Test Name',
            'email' => $longEmail,
        ])
        ->assertRedirect()
        ->assertSessionHasErrors(['email']);
});

test('admin profile update returns json response when expecting json', function () {
    // Arrange
    $newName = 'Updated Admin Name';
    $newEmail = '<EMAIL>';

    // Act & Assert
    $response = $this->actingAs($this->admin)
        ->putJson(route('admin.profile.update'), [
            'name' => $newName,
            'email' => $newEmail,
        ])
        ->assertStatus(200)
        ->assertJson([
            'message' => 'Profile updated successfully!'
        ]);

    // Verify the response includes updated user data
    $responseData = $response->json();
    expect($responseData['user']['name'])->toBe($newName)
        ->and($responseData['user']['email'])->toBe($newEmail);
});

test('non-admin user cannot update admin profile', function () {
    // Arrange
    $user = User::factory()->create([
        'role' => User::ROLE_USER,
        'status' => User::STATUS_ACTIVE,
    ]);

    // Act & Assert - Admin middleware blocks non-admin users with 403
    $this->actingAs($user)
        ->put(route('admin.profile.update'), [
            'name' => 'Test Name',
            'email' => '<EMAIL>',
        ])
        ->assertStatus(403);
});

test('inactive admin cannot update profile', function () {
    // Arrange
    $inactiveAdmin = User::factory()->create([
        'role' => User::ROLE_ADMIN,
        'status' => User::STATUS_INACTIVE,
    ]);

    // Act & Assert - Admin middleware blocks inactive admin with 403
    $this->actingAs($inactiveAdmin)
        ->put(route('admin.profile.update'), [
            'name' => 'Test Name',
            'email' => '<EMAIL>',
        ])
        ->assertStatus(403);
}); 