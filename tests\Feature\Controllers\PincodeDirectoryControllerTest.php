<?php

use App\Models\PinCode;
use App\Models\State;
use App\Models\District;
use App\Models\PostOffice;
use App\Models\User;

test('can view list of states', function () {
    $response = $this->get(route('pincodes.states'));

    $response->assertStatus(200)
        ->assertViewIs('pincodes.1-all-state-listing')
        ->assertViewHas('statesData');
});

test('can view list of districts for a state', function () {
    $state = State::factory()->create(['name' => 'Delhi']);
    
    $response = $this->get(route('pincodes.districts', ['state' => 'Delhi']));

    $response->assertStatus(200)
        ->assertViewIs('pincodes.2-pincodes-of-single-state')
        ->assertViewHas('m_districts');
});

test('can search pincode', function () {
    $state = State::factory()->create(['name' => 'Delhi']);
    $district = District::factory()->create(['name' => 'New Delhi', 'state_id' => $state->id]);
    $postOffice = PostOffice::factory()->create(['name' => 'Connaught Place Post Office']);

    $pincode = PinCode::factory()->create([
        'pincode' => '110001',
        'state' => $state->name,
        'district' => $district->name,
        'name' => $postOffice->name,
    ]);

    $response = $this->get(route('search', ['query' => '110001']));

    $response->assertStatus(200)
        ->assertViewIs('pincodes.search-results')
        ->assertViewHas('results');
});

test('can store review for pincode', function () {
    $user = User::factory()->create();
    $pincode = PinCode::factory()->create();

    $response = $this->post(route('reviews.store'), [
        'pincode_id' => $pincode->id,
        'rating' => 5,
        'comment' => 'Great service!',
        'name' => 'Test Guest'
    ]);

    $response->assertRedirect()
        ->assertSessionHas('success');

    $this->assertDatabaseHas('reviews', [
        'pincode_id' => $pincode->id,
        'rating' => 5,
        'comment' => 'Great service!'
    ]);
});

test('can store like for pincode', function () {
    $user = User::factory()->create();
    $pincode = PinCode::factory()->create();

    $response = $this->actingAs($user)
        ->post(route('likes.store'), [
            'pincode_id' => $pincode->id
        ]);

    $response->assertRedirect()
        ->assertSessionHas('success');

    $this->assertDatabaseHas('likes', [
        'pincode_id' => $pincode->id,
        'user_id' => $user->id
    ]);
}); 