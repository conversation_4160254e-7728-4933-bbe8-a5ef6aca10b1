<?php

namespace App\Http\Controllers;

use App\Models\CourierDict;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class CourierDictController extends Controller
{
    /**
     * Display a listing of the dictionary terms.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $terms = Cache::remember('dict.all', 3600, function () {
            return CourierDict::orderBy('term')->get();
        });
        
        return view('dict.index', compact('terms'));
    }

  
    public function show($slug)
    {
        $term = CourierDict::where('slug', $slug)->firstOrFail();
        
        $relatedTerms = Cache::remember("courier_dict.related.{$slug}", 3600, function () use ($term) {
            return $term->relatedTerms();
        });
        // dd($term->relatedTerms());
        $randomRelatedTerms = Cache::remember("courier_dict.random.{$slug}", 3600, function () use ($term) {
            return $term->randomRelatedTerms();
        });
        
        return view('dict.show', compact('term', 'relatedTerms', 'randomRelatedTerms'));
    }

    public function search(Request $request)
    {
        $search = $request->input('search');
        
        $terms = CourierDict::search($search)
            ->orderBy('term')
            ->get();
            
        return view('dict.index', compact('terms', 'search'));
    }

    public function autocomplete(Request $request)
    {
        $query = $request->input('query');
        $suggestions = CourierDict::where('term', 'LIKE', "{$query}%")
            ->select('term', 'slug')
            ->limit(5)
            ->get();
            
        return response()->json($suggestions);
    }
}