<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('pincodes_geo', function (Blueprint $table) {
            $table->id();
            $table->string('pincode', 10)->index();
            $table->string('office_name');
            $table->string('division');
            $table->string('region')->nullable();
            $table->string('circle');
            $table->json('geometry');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('pincodes_geo');
    }
};