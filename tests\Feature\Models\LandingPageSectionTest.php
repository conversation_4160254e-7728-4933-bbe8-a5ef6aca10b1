<?php

use App\Models\LandingPageSection;
use App\Models\LandingPageContent;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('landing page section can be created', function () {
    $section = LandingPageSection::factory()->create([
        'name' => 'Test Section',
        'slug' => 'test-section',
        'is_active' => true,
        'sort_order' => 5
    ]);

    expect($section)->toBeInstanceOf(LandingPageSection::class)
        ->and($section->name)->toBe('Test Section')
        ->and($section->slug)->toBe('test-section')
        ->and($section->is_active)->toBeTrue()
        ->and($section->sort_order)->toBe(5);
});

test('landing page section has contents relationship', function () {
    $section = LandingPageSection::factory()->create();
    
    // Create multiple content items for this section
    $contents = LandingPageContent::factory()->count(3)->create([
        'section_id' => $section->id
    ]);
    
    expect($section->contents)->toHaveCount(3)
        ->and($section->contents->first())->toBeInstanceOf(LandingPageContent::class);
});

test('active scope returns only active sections', function () {
    // Create active and inactive sections
    LandingPageSection::factory()->active()->count(3)->create();
    LandingPageSection::factory()->inactive()->count(2)->create();
    
    $activeSections = LandingPageSection::active()->get();
    $allSections = LandingPageSection::all();
    
    expect($activeSections)->toHaveCount(3)
        ->and($allSections)->toHaveCount(5);
});

test('sections can be ordered by sort_order', function () {
    // Create sections with different sort orders
    $section1 = LandingPageSection::factory()->create(['sort_order' => 3]);
    $section2 = LandingPageSection::factory()->create(['sort_order' => 1]);
    $section3 = LandingPageSection::factory()->create(['sort_order' => 2]);
    
    $orderedSections = LandingPageSection::orderBy('sort_order')->get();
    
    expect($orderedSections->first()->id)->toBe($section2->id)
        ->and($orderedSections[1]->id)->toBe($section3->id)
        ->and($orderedSections[2]->id)->toBe($section1->id);
});

test('deleting a section cascades to its contents', function () {
    $section = LandingPageSection::factory()->create();
    
    // Create content for this section
    LandingPageContent::factory()->count(3)->create([
        'section_id' => $section->id
    ]);
    
    // Verify content exists
    expect(LandingPageContent::where('section_id', $section->id)->count())->toBe(3);
    
    // Delete the section
    $section->delete();
    
    // Verify content was deleted
    expect(LandingPageContent::where('section_id', $section->id)->count())->toBe(0);
});