<?php

use App\Models\BlogPost;
use App\Models\Comment;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('guest can submit comment with name and email', function () {
    $post = BlogPost::factory()->create(['is_published' => true]);
    
    $response = $this->post(route('comments.store', $post), [
        'content' => 'This is a guest comment',
        'guest_name' => '<PERSON>',
        'guest_email' => '<EMAIL>',
    ]);
    
    $response->assertRedirect()
        ->assertSessionHas('success', 'Comment added successfully');
    
    $this->assertDatabaseHas('comments', [
        'content' => 'This is a guest comment',
        'blog_post_id' => $post->id,
        'user_id' => null,
        'guest_name' => '<PERSON>',
        'guest_email' => '<EMAIL>',
        'is_approved' => false, // Comments require approval
    ]);
});

test('guest comment validation works correctly', function () {
    $post = BlogPost::factory()->create(['is_published' => true]);
    
    // Test missing name
    $response = $this->post(route('comments.store', $post), [
        'content' => 'Test comment',
        'guest_email' => '<EMAIL>',
    ]);
    $response->assertSessionHasErrors('guest_name');
    
    // Test missing email
    $response = $this->post(route('comments.store', $post), [
        'content' => 'Test comment',
        'guest_name' => 'John Doe',
    ]);
    $response->assertSessionHasErrors('guest_email');
    
    // Test invalid email
    $response = $this->post(route('comments.store', $post), [
        'content' => 'Test comment',
        'guest_name' => 'John Doe',
        'guest_email' => 'invalid-email',
    ]);
    $response->assertSessionHasErrors('guest_email');
});

test('guest can reply to existing comment', function () {
    $post = BlogPost::factory()->create(['is_published' => true]);
    $parentComment = Comment::factory()->create([
        'blog_post_id' => $post->id,
        'is_approved' => true,
    ]);
    
    $response = $this->post(route('comments.store', $post), [
        'content' => 'This is a guest reply',
        'guest_name' => 'Jane Doe',
        'guest_email' => '<EMAIL>',
        'parent_id' => $parentComment->id,
    ]);
    
    $response->assertRedirect()
        ->assertSessionHas('success', 'Comment added successfully');
    
    $this->assertDatabaseHas('comments', [
        'content' => 'This is a guest reply',
        'blog_post_id' => $post->id,
        'user_id' => null,
        'guest_name' => 'Jane Doe',
        'guest_email' => '<EMAIL>',
        'parent_id' => $parentComment->id,
        'is_approved' => false,
    ]);
});

test('comment model helper methods work for guest comments', function () {
    $post = BlogPost::factory()->create();
    $guestComment = Comment::factory()->guest()->create([
        'blog_post_id' => $post->id,
        'guest_name' => 'Guest User',
        'guest_email' => '<EMAIL>',
    ]);
    
    expect($guestComment->isGuestComment())->toBeTrue();
    expect($guestComment->commenter_name)->toBe('Guest User');
    expect($guestComment->commenter_email)->toBe('<EMAIL>');
    
    // Test with authenticated user comment
    $userComment = Comment::factory()->create([
        'blog_post_id' => $post->id,
    ]);
    
    expect($userComment->isGuestComment())->toBeFalse();
    expect($userComment->commenter_name)->toBe($userComment->user->name);
    expect($userComment->commenter_email)->toBe($userComment->user->email);
});

test('blog post view shows comment form for guests', function () {
    $post = BlogPost::factory()->create(['is_published' => true]);
    
    $response = $this->get(route('blog.show', $post->slug));
    
    $response->assertStatus(200)
        ->assertSee('Write a comment')
        ->assertSee('Name *')
        ->assertSee('Email *')
        ->assertSee('Comment *')
        ->assertSee('Your email won', false);
});