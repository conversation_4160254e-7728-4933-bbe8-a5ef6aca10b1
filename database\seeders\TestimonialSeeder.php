<?php

namespace Database\Seeders;

use App\Models\Testimonial;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TestimonialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $testimonials = [
            [
                'name' => '<PERSON><PERSON>',
                'position' => 'Logistics Manager',
                'company' => 'ShipFast',
                'content' => "The most reliable pincode directory I've used. The data is always up to date and the interface is beautiful!",
                'rating' => 5,
                'sort_order' => 1,
            ],
            [
                'name' => '<PERSON><PERSON>',
                'position' => 'CTO',
                'company' => 'EcomXpress',
                'content' => "BharatPostal Info's API integration saved us hours of manual work. Highly recommended for any business!",
                'rating' => 5,
                'sort_order' => 2,
            ],
            [
                'name' => '<PERSON><PERSON>',
                'position' => 'Operations Lead',
                'company' => 'QuickMove',
                'content' => "Super fast search and beautiful animations. My team loves using this tool every day.",
                'rating' => 5,
                'sort_order' => 3,
            ],
            [
                'name' => '<PERSON><PERSON><PERSON>',
                'position' => 'Supply Chain Director',
                'company' => 'LogiTech',
                'content' => "Accurate data and excellent customer support. This has become an essential part of our daily operations.",
                'rating' => 5,
                'sort_order' => 4,
            ],
            [
                'name' => 'Vikram Singh',
                'position' => 'CEO',
                'company' => 'PostalPro',
                'content' => "The bulk verification feature has streamlined our address validation process significantly.",
                'rating' => 4,
                'sort_order' => 5,
            ],
            [
                'name' => 'Anjali Desai',
                'position' => 'Operations Manager',
                'company' => 'DeliveryHub',
                'content' => "User-friendly interface and comprehensive coverage. Perfect for our logistics requirements.",
                'rating' => 5,
                'sort_order' => 6,
            ],
        ];

        foreach ($testimonials as $testimonial) {
            Testimonial::create($testimonial);
        }
    }
} 