@props([
    'gateways' => [],
    'selectedGateway' => null,
    'showDescription' => true,
    'showCurrencies' => true,
    'compact' => false
])

<div class="payment-gateway-selector">
    @forelse($gateways as $gateway)
    <div class="gateway-option mb-3" data-gateway="{{ $gateway->name }}">
        <div class="card gateway-card h-100 {{ $compact ? 'compact' : '' }}" style="cursor: pointer;">
            <div class="card-body {{ $compact ? 'p-3' : '' }}">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="form-check">
                            <input class="form-check-input gateway-radio" 
                                   type="radio" 
                                   name="payment_gateway" 
                                   value="{{ $gateway->id }}" 
                                   id="gateway_{{ $gateway->id }}"
                                   {{ ($selectedGateway && $selectedGateway == $gateway->id) || $gateway->is_default ? 'checked' : '' }}>
                        </div>
                    </div>
                    
                    <div class="col-auto">
                        @if($gateway->logo_url)
                            <img src="{{ $gateway->logo_url }}" 
                                 alt="{{ $gateway->display_name }}" 
                                 class="gateway-logo"
                                 style="max-height: {{ $compact ? '30px' : '40px' }}; max-width: {{ $compact ? '60px' : '80px' }};">
                        @else
                            <div class="gateway-icon bg-primary text-white rounded d-flex align-items-center justify-content-center" 
                                 style="width: {{ $compact ? '40px' : '50px' }}; height: {{ $compact ? '30px' : '40px' }};">
                                @switch($gateway->name)
                                    @case('paypal')
                                        <i class="fab fa-paypal"></i>
                                        @break
                                    @case('razorpay')
                                        <i class="fas fa-credit-card"></i>
                                        @break
                                    @case('qr_bank_transfer')
                                        <i class="fas fa-qrcode"></i>
                                        @break
                                    @default
                                        <i class="fas fa-credit-card"></i>
                                @endswitch
                            </div>
                        @endif
                    </div>
                    
                    <div class="col">
                        <h6 class="mb-1 {{ $compact ? 'fs-6' : '' }}">{{ $gateway->display_name }}</h6>
                        
                        @if($showDescription && $gateway->description && !$compact)
                            <small class="text-muted">{{ $gateway->description }}</small>
                        @endif
                        
                        @if($showCurrencies && $gateway->supported_currencies && count($gateway->supported_currencies) > 0 && !$compact)
                            <div class="mt-2">
                                <small class="text-muted">Supports: </small>
                                @foreach(array_slice($gateway->supported_currencies, 0, 3) as $currency)
                                    <span class="badge bg-light text-dark me-1">{{ $currency }}</span>
                                @endforeach
                                @if(count($gateway->supported_currencies) > 3)
                                    <span class="badge bg-light text-dark">+{{ count($gateway->supported_currencies) - 3 }} more</span>
                                @endif
                            </div>
                        @endif
                    </div>
                    
                    @if(!$compact)
                    <div class="col-auto">
                        <div class="payment-methods">
                            <small class="text-muted d-block">Accepts:</small>
                            <div class="d-flex gap-1 mt-1">
                                @switch($gateway->name)
                                    @case('razorpay')
                                        <i class="fab fa-cc-visa text-primary" title="Visa"></i>
                                        <i class="fab fa-cc-mastercard text-warning" title="Mastercard"></i>
                                        <i class="fas fa-university text-info" title="Net Banking"></i>
                                        <i class="fas fa-mobile-alt text-success" title="UPI"></i>
                                        @break
                                    @case('paypal')
                                        <i class="fab fa-paypal text-primary" title="PayPal"></i>
                                        <i class="fab fa-cc-visa text-primary" title="Visa"></i>
                                        <i class="fab fa-cc-mastercard text-warning" title="Mastercard"></i>
                                        @break
                                    @case('qr_bank_transfer')
                                        <i class="fas fa-qrcode text-dark" title="QR Code"></i>
                                        <i class="fas fa-university text-info" title="Bank Transfer"></i>
                                        @break
                                    @default
                                        <i class="fas fa-credit-card text-primary" title="Card Payment"></i>
                                @endswitch
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    @empty
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        No payment methods are currently available. Please contact support.
    </div>
    @endforelse
</div>

@push('styles')
<style>
.payment-gateway-selector .gateway-card {
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.payment-gateway-selector .gateway-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,123,255,0.1);
}

.payment-gateway-selector .gateway-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.payment-gateway-selector .gateway-card.compact {
    border-width: 1px;
}

.payment-gateway-selector .gateway-logo {
    object-fit: contain;
}

.payment-gateway-selector .payment-methods i {
    font-size: 1.2em;
}

@media (max-width: 768px) {
    .payment-gateway-selector .gateway-card .row {
        text-align: center;
    }
    
    .payment-gateway-selector .gateway-card .col-auto,
    .payment-gateway-selector .gateway-card .col {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .payment-gateway-selector .payment-methods {
        margin-top: 0.5rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selector = document.querySelector('.payment-gateway-selector');
    if (!selector) return;
    
    const gatewayCards = selector.querySelectorAll('.gateway-card');
    const gatewayRadios = selector.querySelectorAll('.gateway-radio');

    // Gateway card click handlers
    gatewayCards.forEach(card => {
        card.addEventListener('click', function() {
            const radio = this.querySelector('.gateway-radio');
            if (radio) {
                radio.checked = true;
                selectGateway(radio);
                
                // Trigger change event for external listeners
                radio.dispatchEvent(new Event('change', { bubbles: true }));
            }
        });
    });

    // Radio button change handlers
    gatewayRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                selectGateway(this);
            }
        });
    });

    // Set initial selection
    const defaultGateway = selector.querySelector('.gateway-radio:checked');
    if (defaultGateway) {
        selectGateway(defaultGateway);
    }

    function selectGateway(radio) {
        // Update card selection visual state
        gatewayCards.forEach(card => card.classList.remove('selected'));
        const selectedCard = radio.closest('.gateway-card');
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }
    }
});
</script>
@endpush