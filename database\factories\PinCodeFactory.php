<?php

namespace Database\Factories;

use App\Models\PinCode;
use App\Models\State;
use App\Models\District;
use Illuminate\Database\Eloquent\Factories\Factory;

class PinCodeFactory extends Factory
{
    protected $model = PinCode::class;

    public function definition()
    {
        $state = State::factory()->create();
        $district = District::factory()->create(['state_id' => $state->id]);
        
        return [
            'circle' => $this->faker->randomElement(['North', 'South', 'East', 'West', 'Central']),
            'pincode' => $this->faker->numberBetween(100000, 999999),
            'region' => $this->faker->randomElement(['Urban', 'Rural', 'Semi-Urban']),
            'division' => $this->faker->city,
            'name' => $this->faker->company . ' Post Office',
            'branch_type' => $this->faker->randomElement(['HO', 'SO', 'BO', 'PO']),
            'delivery_status' => $this->faker->randomElement(['Delivery', 'Non-Delivery']),
            'district' => $district->name,
            'state' => $state->name,
            'latitude' => $this->faker->latitude(8, 37),
            'longitude' => $this->faker->longitude(68, 97)
        ];
    }
}