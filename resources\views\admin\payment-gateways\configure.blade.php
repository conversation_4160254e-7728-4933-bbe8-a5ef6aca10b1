<form id="gateway-config-form" action="{{ route('admin.payment-gateways.update', $gateway->id) }}" method="POST">
    @csrf
    @method('PUT')
    
    <div class="row">
        <!-- Basic Information -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Basic Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Display Name *</label>
                        <input type="text" class="form-control" name="display_name" 
                               value="{{ $gateway->display_name }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="description" rows="3">{{ $gateway->description }}</textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Sort Order</label>
                            <input type="number" class="form-control" name="sort_order" 
                                   value="{{ $gateway->sort_order }}" min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Logo URL</label>
                            <input type="url" class="form-control" name="logo_url" 
                                   value="{{ $gateway->logo_url }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active" 
                                       value="1" {{ $gateway->is_active ? 'checked' : '' }}>
                                <label class="form-check-label">Active</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_default" 
                                       value="1" {{ $gateway->is_default ? 'checked' : '' }}>
                                <label class="form-check-label">Default Gateway</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Gateway Configuration -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Gateway Configuration</h6>
                </div>
                <div class="card-body">
                    @switch($gateway->name)
                        @case('paypal')
                            @include('admin.payment-gateways.config.paypal', ['config' => $gateway->configuration])
                            @break
                        @case('razorpay')
                            @include('admin.payment-gateways.config.razorpay', ['config' => $gateway->configuration])
                            @break
                        @case('stripe')
                            @include('admin.payment-gateways.config.stripe', ['config' => $gateway->configuration])
                            @break
                        @case('qr_bank_transfer')
                            @include('admin.payment-gateways.config.qr-bank-transfer', ['config' => $gateway->configuration])
                            @break
                        @default
                            <div class="alert alert-info">
                                <i class="mdi mdi-information me-2"></i>
                                No specific configuration required for this gateway type.
                            </div>
                    @endswitch
                </div>
            </div>
        </div>
    </div>
    
    <!-- Supported Currencies -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Supported Currencies</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @php
                            $currencies = [
                                'USD' => 'US Dollar', 'EUR' => 'Euro', 'GBP' => 'British Pound',
                                'INR' => 'Indian Rupee', 'JPY' => 'Japanese Yen', 'AUD' => 'Australian Dollar',
                                'CAD' => 'Canadian Dollar', 'CHF' => 'Swiss Franc', 'CNY' => 'Chinese Yuan',
                                'SGD' => 'Singapore Dollar', 'HKD' => 'Hong Kong Dollar', 'NZD' => 'New Zealand Dollar'
                            ];
                            $supportedCurrencies = $gateway->supported_currencies ?? [];
                        @endphp
                        
                        @foreach($currencies as $code => $name)
                        <div class="col-md-3 mb-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="supported_currencies[]" 
                                       value="{{ $code }}" id="currency_{{ $code }}"
                                       {{ in_array($code, $supportedCurrencies) ? 'checked' : '' }}>
                                <label class="form-check-label" for="currency_{{ $code }}">
                                    {{ $code }} - {{ $name }}
                                </label>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Webhook Configuration -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Webhook Configuration</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label class="form-label">Webhook URL</label>
                            <div class="input-group">
                                <input type="text" class="form-control" name="webhook_url" 
                                       value="{{ $gateway->webhook_url }}" readonly>
                                <button type="button" class="btn btn-outline-secondary copy-btn" 
                                        data-copy="{{ $gateway->webhook_url }}">
                                    <i class="mdi mdi-content-copy"></i>
                                </button>
                            </div>
                            <small class="text-muted">Use this URL in your gateway's webhook configuration</small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Webhook Secret</label>
                            <div class="input-group">
                                <input type="password" class="form-control" name="webhook_secret" 
                                       value="{{ $gateway->webhook_secret }}" placeholder="Enter webhook secret">
                                <button type="button" class="btn btn-outline-secondary toggle-password">
                                    <i class="mdi mdi-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div>
                    <button type="button" class="btn btn-info me-2" id="test-connection-btn">
                        <i class="mdi mdi-test-tube me-1"></i>
                        Test Connection
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="reset-config-btn">
                        <i class="mdi mdi-refresh me-1"></i>
                        Reset to Defaults
                    </button>
                </div>
                <div>
                    <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="mdi mdi-content-save me-1"></i>
                        Save Configuration
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form submission
    document.getElementById('gateway-config-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        submitBtn.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Saving...';
        submitBtn.disabled = true;
        
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => {
                    bootstrap.Modal.getInstance(document.getElementById('configureGatewayModal')).hide();
                    location.reload();
                }, 1500);
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            showAlert('error', 'Failed to save configuration');
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
    
    // Test connection
    document.getElementById('test-connection-btn').addEventListener('click', function() {
        const btn = this;
        const originalText = btn.innerHTML;
        
        btn.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Testing...';
        btn.disabled = true;
        
        // Get form data for testing
        const formData = new FormData(document.getElementById('gateway-config-form'));
        
        fetch(`{{ route('admin.payment-gateways.test', $gateway->id) }}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', 'Connection test successful!');
            } else {
                showAlert('error', data.message || 'Connection test failed');
            }
        })
        .catch(error => {
            showAlert('error', 'Failed to test connection');
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    });
    
    // Copy webhook URL
    document.querySelectorAll('.copy-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const textToCopy = this.dataset.copy;
            navigator.clipboard.writeText(textToCopy).then(() => {
                const icon = this.querySelector('i');
                const originalClass = icon.className;
                icon.className = 'mdi mdi-check text-success';
                
                setTimeout(() => {
                    icon.className = originalClass;
                }, 2000);
            });
        });
    });
    
    // Toggle password visibility
    document.querySelectorAll('.toggle-password').forEach(btn => {
        btn.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'mdi mdi-eye-off';
            } else {
                input.type = 'password';
                icon.className = 'mdi mdi-eye';
            }
        });
    });
});

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const form = document.getElementById('gateway-config-form');
    form.insertBefore(alertDiv, form.firstChild);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>