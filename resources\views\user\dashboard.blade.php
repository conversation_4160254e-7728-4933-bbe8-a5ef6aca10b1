@extends('layouts.user')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@section('content')
    <div class="space-y-6">
        <!-- Current Plan Section -->
        {{-- <div class="bg-gradient-to-r from-primary-light to-primary-dark dark:from-primary-dark dark:to-primary-light rounded-lg shadow-md dark:shadow-gray-900/20 p-6 text-white">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="mb-4 md:mb-0">
                    <h2 class="text-xl font-bold mb-1">{{ $plan->name ?? 'Free Plan' }}</h2>
                    <p class="text-blue-100 dark:text-blue-50">{{ $plan->description ?? 'Basic features included' }}</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    @if ($plan && $plan->price > 0)
                        <span class="inline-flex items-center px-3 py-1 bg-blue-700 dark:bg-blue-800 rounded-full text-sm font-medium">
                            <svg class="mr-1.5 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {{ $subscription && $subscription->ends_at ? 'Expires: ' . $subscription->ends_at->format('M d, Y') : 'Active' }}
                        </span>
                    @endif
                    <a href="{{ route('user.plans.index') }}"
                        class="inline-flex items-center justify-center px-4 py-2 bg-white dark:bg-gray-800 text-primary-light dark:text-primary-dark rounded-md font-medium text-sm hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-2 transition-colors">
                        {{ $plan && $plan->price > 0 ? 'Change Plan' : 'Upgrade Now' }}
                    </a>
                </div>
            </div>
        </div> --}}

    <!-- Create Token Modal -->
    {{-- <x-modal name="create-token" :show="false">
        <form id="create-token-form" method="POST" action="{{ route('tokens.store') }}" class="p-4 sm:p-6">
            @csrf

            <!-- Modal Header -->
            <div class="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 mx-auto bg-indigo-100 dark:bg-indigo-900/30 rounded-full">
                <svg class="w-5 h-5 sm:w-6 sm:h-6 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                </svg>
            </div>

            <h2 class="mt-3 sm:mt-4 text-base sm:text-lg font-medium text-text-primary-light dark:text-text-primary-dark text-center px-2">
                {{ __('Create New API Token') }}
            </h2>

            <p class="mt-2 text-xs sm:text-sm text-text-secondary-light dark:text-text-secondary-dark text-center px-2 leading-relaxed">
                {{ __('Create a new API token to access the API. Make sure to copy your token as you won\'t be able to see it again.') }}
            </p>

            <!-- Error Messages -->
            <div id="token-error" class="hidden mt-3 sm:mt-4">
                <div class="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-400 dark:border-red-500 p-3 sm:p-4 rounded-r-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-4 w-4 sm:h-5 sm:w-5 text-red-400 dark:text-red-500" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-2 sm:ml-3 flex-1 min-w-0">
                            <h3 class="text-xs sm:text-sm font-medium text-red-800 dark:text-red-300">
                                {{ __('There was an error creating your API token.') }}
                            </h3>
                            <div class="mt-1 sm:mt-2 text-xs sm:text-sm text-red-700 dark:text-red-400">
                                <ul id="error-list" class="list-disc pl-4 sm:pl-5 space-y-1">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Token Name Input -->
            <div class="mt-4 sm:mt-6">
                <x-input-label for="token_name" :value="__('Token Name')" class="text-sm sm:text-base" />
                <x-text-input id="token_name" name="name" type="text" class="mt-1 block w-full text-sm sm:text-base"
                    placeholder="e.g. Production Server" required maxlength="255" />
                <p class="mt-1 text-xs sm:text-sm text-text-secondary-light dark:text-text-secondary-dark leading-relaxed">
                    {{ __('Give your token a descriptive name to help you identify its purpose.') }}
                </p>
            </div>

            <!-- Warning Notice -->
            <div class="mt-4 sm:mt-6">
                <div class="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-400 dark:border-yellow-500 p-3 sm:p-4 rounded-r-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-4 w-4 sm:h-5 sm:w-5 text-yellow-400 dark:text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-2 sm:ml-3 flex-1 min-w-0">
                            <p class="text-xs sm:text-sm text-yellow-700 dark:text-yellow-300 leading-relaxed">
                                {{ __('Important: Make sure to copy your token immediately after creation. You won\'t be able to see it again!') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Actions -->
            <div class="mt-4 sm:mt-6 flex flex-col-reverse sm:flex-row sm:justify-end gap-2 sm:gap-0">
                <x-secondary-button type="button" x-on:click="$dispatch('close')"
                    class="w-full sm:w-auto justify-center sm:justify-start">
                    {{ __('Cancel') }}
                </x-secondary-button>

                <x-primary-button type="submit" id="create-token-button"
                    class="w-full sm:w-auto sm:ml-3 justify-center sm:justify-start">
                    {{ __('Create Token') }}
                </x-primary-button>
            </div>
        </form>
    </x-modal> --}}
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('create-token-form');
            const errorDiv = document.getElementById('token-error');
            const errorList = document.getElementById('error-list');
            const createButton = document.getElementById('create-token-button');

            form.addEventListener('submit', function(e) {
                e.preventDefault();

                // Reset error state
                errorDiv.classList.add('hidden');
                errorList.innerHTML = '';
                createButton.disabled = true;

                // Update button text with loading state
                const originalText = createButton.innerHTML;
                createButton.innerHTML = `
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ __('Creating...') }}
        `;

                // Get form data
                const formData = new FormData(form);

                // Send AJAX request
                fetch(form.action, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            // Close the create token modal
                            window.dispatchEvent(new CustomEvent('close-modal', {
                                detail: 'create-token'
                            }));

                            // Show the token created modal
                            window.dispatchEvent(new CustomEvent('open-modal', {
                                detail: 'token-created'
                            }));

                            // Update the token value in the created modal
                            const tokenValueElement = document.getElementById('token-value');
                            if (tokenValueElement) {
                                tokenValueElement.value = data.token;
                            }

                            // Reload the page to show the new token in the list
                            setTimeout(() => {
                                window.location.reload();
                            }, 100);
                        } else {
                            // Show validation errors
                            errorDiv.classList.remove('hidden');
                            errorDiv.scrollIntoView({
                                behavior: 'smooth',
                                block: 'nearest'
                            });

                            if (data.errors) {
                                Object.values(data.errors).flat().forEach(error => {
                                    const li = document.createElement('li');
                                    li.textContent = error;
                                    errorList.appendChild(li);
                                });
                            } else if (data.message) {
                                const li = document.createElement('li');
                                li.textContent = data.message;
                                errorList.appendChild(li);
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        // Show general error
                        errorDiv.classList.remove('hidden');
                        errorDiv.scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest'
                        });

                        const li = document.createElement('li');
                        li.textContent = '{{ __('An unexpected error occurred. Please try again.') }}';
                        errorList.appendChild(li);
                    })
                    .finally(() => {
                        createButton.disabled = false;
                        createButton.innerHTML = originalText;
                    });
            });

            // Handle escape key to close modal on mobile
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const modal = document.querySelector('[x-data]');
                    if (modal) {
                        window.dispatchEvent(new CustomEvent('close-modal', {
                            detail: 'create-token'
                        }));
                    }
                }
            });
        });
    </script>
@endpush
