<?php $__env->startSection('content'); ?>
<div class="container px-4 py-6 mx-auto">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white dark:bg-bg-dark rounded-lg shadow-md border border-border-light dark:border-border-dark">
            <div class="px-4 py-3 border-b border-border-light dark:border-border-dark">
                <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">Create New State</h3>
            </div>
            <div class="p-6">
                <!-- Note about URL slugs -->
                <div class="p-4 mb-6 text-sm text-blue-700 dark:text-blue-200 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    <p><strong>Note:</strong> The slug (URL) is based on the State Name, District Name, and Post Office Name. In the existing database, all entries are stored in lowercase.</p>
                    <p>Please remember that URLs with lowercase and uppercase letters can be treated differently on some systems. Therefore, always ensure that the above-mentioned values are kept in lowercase to maintain consistency and avoid SEO issues.</p>
                </div>

                <form action="<?php echo e(route('admin.states.store')); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    
                    <div class="mb-5">
                        <label for="name" class="block mb-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">State Name</label>
                        <input type="text" 
                            id="name" 
                            name="name" 
                            value="<?php echo e(old('name')); ?>" 
                            class="w-full px-3 py-2 border border-border-light dark:border-border-dark rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                            required>
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <p class="mt-1 text-xs text-text-secondary-light dark:text-text-secondary-dark">This name will be used to generate the URL slug. Please use lowercase.</p>
                    </div>
                    
                    <div class="mb-6">
                        <label for="featured_image" class="block mb-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Featured Image</label>
                        <div class="mt-2">
                            <input type="file" 
                                id="featured_image" 
                                name="featured_image" 
                                class="w-full text-sm text-text-secondary-light dark:text-text-secondary-dark file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-accent-light dark:file:bg-accent-dark file:text-primary-light dark:file:text-primary-dark hover:file:bg-accent-dark dark:hover:file:bg-accent-light <?php $__errorArgs = ['featured_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <?php $__errorArgs = ['featured_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <p class="mt-1 text-sm text-text-secondary-light dark:text-text-secondary-dark">Recommended size: 800x600 pixels. Max file size: 2MB</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <button type="submit" 
                            class="px-4 py-2 text-sm font-medium text-white bg-primary-light dark:bg-primary-dark rounded-md hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark transition-colors duration-200">
                            Create State
                        </button>
                        <a href="<?php echo e(route('admin.states.index')); ?>" 
                            class="px-4 py-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark bg-gray-200 dark:bg-bg-dark rounded-md hover:bg-gray-300 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-border-light dark:focus:ring-border-dark transition-colors duration-200">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/admin/states/create.blade.php ENDPATH**/ ?>