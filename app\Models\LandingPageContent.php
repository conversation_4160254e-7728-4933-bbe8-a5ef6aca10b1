<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\UploadedFile;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LandingPageContent extends Model
{
    use HasFactory;
    
    protected $fillable = ['section_id', 'key', 'value', 'type', 'options'];

    protected $casts = [
        'options' => 'array',
    ];

    protected function value(): Attribute
    {
        return Attribute::make(
            get: function ($value, $attributes) {

                if (isset($attributes['type']) && $attributes['type'] === 'repeater') {
                    $decoded = is_string($value) ? json_decode($value, true) : $value;

                    // Don't modify the photo paths during get
                    return $decoded;
                }

                return $value;
            },
            set: function ($value, $attributes) {
                if (isset($attributes['type']) && $attributes['type'] === 'repeater') {
                    if (is_array($value)) {
                        // Don't modify the incoming value, just encode it
                        return json_encode($value);
                    }
                }

                return $value;
            }
        );
    }

    public function section()
    {
        return $this->belongsTo(LandingPageSection::class, 'section_id');
    }

    public function scopeOrdered(Builder $query): Builder
    {
        return $query->select('landing_page_contents.*')
            ->leftJoin('landing_page_sections', function ($join) {
                $join->on('landing_page_contents.section_id', '=', 'landing_page_sections.id');
            })
            ->orderBy('landing_page_sections.sort_order', 'asc');
    }


    // Add this method to your LandingPageContent model
    public static function getConfigValue($key, $default = null)
    {
        $content = self::where('key', $key)
            ->whereHas('section', function ($query) {
                $query->where('slug', 'config');
            })
            ->first();

        return $content ? $content->value : $default;
    }

    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($model) {
            if ($model->type === 'image' && $model->value) {
                try {
                    Storage::disk('public')->delete($model->value);
                } catch (\Exception $e) {
                    Log::error('Failed to delete file on model deletion', [
                        'path' => $model->value,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        });

        static::updating(function ($model) {
            if (
                $model->type === 'image' &&
                $model->isDirty('value') &&
                $model->getOriginal('value')
            ) {
                try {
                    Storage::disk('public')->delete($model->getOriginal('value'));
                } catch (\Exception $e) {
                    Log::error('Failed to delete old file on update', [
                        'path' => $model->getOriginal('value'),
                        'error' => $e->getMessage()
                    ]);
                }
            }
        });
    }
}