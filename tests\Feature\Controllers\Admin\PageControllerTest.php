<?php

use App\Models\Page;
use App\Models\User;
use function Pest\Laravel\{get, post, put, delete};
use Illuminate\Support\Str;

beforeEach(function () {
    // Create and authenticate an admin user
    $admin = User::factory()->admin()->create();
    $this->actingAs($admin);
});

test('index method displays pages list', function () {
    // Delete existing pages to start with a clean slate
    Page::query()->delete();
    
    // Create pages with specific order
    $pages = collect([
        Page::factory()->create(['order' => 1]),
        Page::factory()->create(['order' => 2]),
        Page::factory()->create(['order' => 3])
    ]);

    $response = get(route('admin.pages.index'))
        ->assertStatus(200)
        ->assertViewIs('admin.pages.index');

    // Get the pages from the view
    $viewPages = $response->original->getData()['pages'];
    
    // Assert the count matches
    expect($viewPages)->toHaveCount(3);
    
    // Assert the pages are in the correct order
    expect($viewPages->pluck('id')->toArray())
        ->toBe($pages->pluck('id')->toArray());
});

test('create method displays page creation form', function () {
    get(route('admin.pages.create'))
        ->assertStatus(200)
        ->assertViewIs('admin.pages.create')
        ->assertViewHas('templates', Page::getTemplates());
});

test('store method creates a new page successfully', function () {
    $pageData = [
        'title' => 'Test Page',
        'content' => 'Test content',
        'template' => 'default',
        'meta_title' => 'Test Meta Title',
        'meta_description' => 'Test Meta Description',
        'is_active' => true,
        'show_in_menu' => true,
        'order' => 1
    ];

    post(route('admin.pages.store'), $pageData)
        ->assertRedirect(route('admin.pages.index'))
        ->assertSessionHas('success', 'Page created successfully.');

    $this->assertDatabaseHas('pages', [
        'title' => $pageData['title'],
        'content' => $pageData['content'],
        'template' => $pageData['template']
    ]);
});

test('store method handles validation errors', function () {
    $invalidData = [
        'title' => '',
        'content' => '',
        'template' => 'invalid-template',
        'meta_title' => Str::random(256), // Exceeds max length
        'meta_description' => Str::random(256), // Exceeds max length
        'order' => -1 // Invalid order
    ];

    post(route('admin.pages.store'), $invalidData)
        ->assertSessionHasErrors([
            'title',
            'content',
            'template',
            'meta_title',
            'meta_description',
            'order'
        ]);
});

test('store method handles duplicate title', function () {
    $existingPage = Page::factory()->create();

    $pageData = [
        'title' => $existingPage->title,
        'content' => 'Test content',
        'template' => 'default',
        'is_active' => true,
        'show_in_menu' => true
    ];

    post(route('admin.pages.store'), $pageData)
        ->assertSessionHasErrors('title');
});

test('edit method displays page edit form', function () {
    $page = Page::factory()->create();

    get(route('admin.pages.edit', $page))
        ->assertStatus(200)
        ->assertViewIs('admin.pages.edit')
        ->assertViewHas('page', $page)
        ->assertViewHas('templates', Page::getTemplates());
});

test('update method updates page successfully', function () {
    $page = Page::factory()->create();
    $updateData = [
        'title' => 'Updated Page Title',
        'content' => 'Updated content',
        'template' => 'default',
        'meta_title' => 'Updated Meta Title',
        'meta_description' => 'Updated Meta Description',
        'is_active' => false,
        'show_in_menu' => false,
        'order' => 2
    ];

    put(route('admin.pages.update', $page), $updateData)
        ->assertRedirect(route('admin.pages.index'))
        ->assertSessionHas('success', 'Page updated successfully.');

    $this->assertDatabaseHas('pages', [
        'id' => $page->id,
        'title' => $updateData['title'],
        'content' => $updateData['content']
    ]);
});

test('update method handles validation errors', function () {
    $page = Page::factory()->create();
    $invalidData = [
        'title' => '',
        'content' => '',
        'template' => 'invalid-template',
        'meta_title' => Str::random(256),
        'meta_description' => Str::random(256),
        'order' => -1
    ];

    put(route('admin.pages.update', $page), $invalidData)
        ->assertSessionHasErrors([
            'title',
            'content',
            'template',
            'meta_title',
            'meta_description',
            'order'
        ]);
});

test('update method handles duplicate title', function () {
    $page = Page::factory()->create();
    $existingPage = Page::factory()->create();

    $updateData = [
        'title' => $existingPage->title,
        'content' => 'Updated content',
        'template' => 'default',
        'is_active' => true,
        'show_in_menu' => true
    ];

    put(route('admin.pages.update', $page), $updateData)
        ->assertSessionHasErrors('title');
});

test('destroy method deletes page successfully', function () {
    $page = Page::factory()->create();

    delete(route('admin.pages.destroy', $page))
        ->assertRedirect(route('admin.pages.index'))
        ->assertSessionHas('success', 'Page deleted successfully.');

    $this->assertDatabaseMissing('pages', ['id' => $page->id]);
});

test('destroy method handles deletion errors', function () {
    // Create a page
    $page = Page::factory()->create();
    
    // Override the delete method in the Page model to throw an exception
    Page::deleting(function($page) {
        throw new \Exception('Deletion failed');
    });
    
    // Attempt to delete the page, which should now throw an exception
    $response = delete(route('admin.pages.destroy', $page));
    
    // Assert the response redirects to the index page with an error message
    $response->assertRedirect(route('admin.pages.index'));
    $response->assertSessionHas('error', 'Failed to delete page. Please try again.');
    
    // Verify the page still exists in the database
    $this->assertDatabaseHas('pages', ['id' => $page->id]);
});