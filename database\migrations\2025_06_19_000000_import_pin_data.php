<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class ImportPinData extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Skip data import during testing to prevent memory issues
        if (app()->environment('testing')) {
            return;
        }

        // Disable foreign key checks temporarily
        if (DB::getDriverName() !== 'sqlite') {
            DB::statement('SET FOREIGN_KEY_CHECKS=0');
        }

        // Import states data
        $statesSql = File::get(database_path('sql/pin_states.sql'));
        if (DB::getDriverName() === 'sqlite') {
            // Remove lines that start with SET FOREIGN_KEY_CHECKS
            $statesSql = preg_replace('/^SET FOREIGN_KEY_CHECKS=\d+;$/m', '', $statesSql);
        }
        DB::unprepared($statesSql);

        // Import districts data
        $districtsSql = File::get(database_path('sql/pin_districts.sql'));
        if (DB::getDriverName() === 'sqlite') {
            $districtsSql = preg_replace('/^SET FOREIGN_KEY_CHECKS=\d+;$/m', '', $districtsSql);
        }
        DB::unprepared($districtsSql);

        // Import pin codes data
        $pinCodesSql = File::get(database_path('sql/pin_codes.sql'));
        if (DB::getDriverName() === 'sqlite') {
            $pinCodesSql = preg_replace('/^SET FOREIGN_KEY_CHECKS=\d+;$/m', '', $pinCodesSql);
        }
        DB::unprepared($pinCodesSql);

        // Re-enable foreign key checks
        if (DB::getDriverName() !== 'sqlite') {
            DB::statement('SET FOREIGN_KEY_CHECKS=1');
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (app()->environment('testing')) {
            return;
        }

        // Truncate tables in reverse order
        if (DB::getDriverName() !== 'sqlite') {
            DB::statement('SET FOREIGN_KEY_CHECKS=0');
        }
        
        DB::table('pin_codes')->truncate();
        DB::table('pin_districts')->truncate();
        DB::table('pin_states')->truncate();
        
        if (DB::getDriverName() !== 'sqlite') {
            DB::statement('SET FOREIGN_KEY_CHECKS=1');
        }
    }
} 