@extends('layouts.app')

@section('title', 'Select Payment Method')

@section('content')
<div class="container-fluid px-3">
    <!-- Mobile Header -->
    <div class="mobile-header d-flex align-items-center justify-content-between py-3 border-bottom">
        <a href="{{ route('plans.public') }}" class="btn btn-link p-0">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h5 class="mb-0">Payment Method</h5>
        <div style="width: 24px;"></div> <!-- Spacer for centering -->
    </div>

    <!-- Order Summary Card -->
    <div class="card mt-3 border-0 bg-light">
        <div class="card-body p-3">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">{{ $order->plan->name ?? 'Plan' }}</h6>
                    <small class="text-muted">{{ $order->plan->duration ?? '' }} {{ $order->plan->duration_type ?? '' }}</small>
                </div>
                <div class="text-end">
                    <h5 class="mb-0 text-primary">{{ $order->currency }} {{ number_format($order->amount, 2) }}</h5>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Methods -->
    <div class="mt-4">
        <h6 class="mb-3 px-2">Choose Payment Method</h6>
        
        <form id="mobile-payment-form" action="{{ route('user.payment.process') }}" method="POST">
            @csrf
            <input type="hidden" name="order_id" value="{{ $order->id }}">
            
            <div class="payment-methods-mobile">
                @forelse($activeGateways as $gateway)
                <div class="payment-method-card mb-3" data-gateway="{{ $gateway->name }}">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-center">
                                <div class="form-check me-3">
                                    <input class="form-check-input gateway-radio" 
                                           type="radio" 
                                           name="payment_gateway" 
                                           value="{{ $gateway->id }}" 
                                           id="mobile_gateway_{{ $gateway->id }}"
                                           {{ $gateway->is_default ? 'checked' : '' }}>
                                </div>
                                
                                <div class="gateway-logo me-3">
                                    @if($gateway->logo_url)
                                        <img src="{{ $gateway->logo_url }}" 
                                             alt="{{ $gateway->display_name }}" 
                                             style="max-height: 32px; max-width: 60px; object-fit: contain;">
                                    @else
                                        <div class="gateway-icon bg-primary text-white rounded d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 32px;">
                                            @switch($gateway->name)
                                                @case('paypal')
                                                    <i class="fab fa-paypal"></i>
                                                    @break
                                                @case('razorpay')
                                                    <i class="fas fa-credit-card"></i>
                                                    @break
                                                @case('qr_bank_transfer')
                                                    <i class="fas fa-qrcode"></i>
                                                    @break
                                                @default
                                                    <i class="fas fa-credit-card"></i>
                                            @endswitch
                                        </div>
                                    @endif
                                </div>
                                
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $gateway->display_name }}</h6>
                                    @if($gateway->description)
                                        <small class="text-muted">{{ Str::limit($gateway->description, 40) }}</small>
                                    @endif
                                    
                                    <!-- Payment method icons -->
                                    <div class="payment-icons mt-2">
                                        @switch($gateway->name)
                                            @case('razorpay')
                                                <i class="fab fa-cc-visa text-primary me-1" style="font-size: 1.2em;"></i>
                                                <i class="fab fa-cc-mastercard text-warning me-1" style="font-size: 1.2em;"></i>
                                                <i class="fas fa-university text-info me-1"></i>
                                                <i class="fas fa-mobile-alt text-success"></i>
                                                @break
                                            @case('paypal')
                                                <i class="fab fa-paypal text-primary me-1" style="font-size: 1.2em;"></i>
                                                <i class="fab fa-cc-visa text-primary me-1" style="font-size: 1.2em;"></i>
                                                <i class="fab fa-cc-mastercard text-warning" style="font-size: 1.2em;"></i>
                                                @break
                                            @case('qr_bank_transfer')
                                                <i class="fas fa-qrcode text-dark me-1"></i>
                                                <i class="fas fa-university text-info"></i>
                                                @break
                                        @endswitch
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <i class="fas fa-chevron-right text-muted"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @empty
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    No payment methods available
                </div>
                @endforelse
            </div>

            <!-- Selected Gateway Info -->
            <div id="mobile-gateway-info" class="mt-3" style="display: none;">
                <div class="card border-0 bg-info bg-opacity-10">
                    <div class="card-body p-3">
                        <div id="mobile-gateway-info-content"></div>
                    </div>
                </div>
            </div>

            <!-- Security Info -->
            <div class="security-info mt-4 p-3 bg-light rounded">
                <div class="text-center">
                    <div class="d-flex justify-content-center gap-3 mb-2">
                        <i class="fas fa-shield-alt text-success fa-lg"></i>
                        <i class="fas fa-lock text-primary fa-lg"></i>
                        <i class="fas fa-user-shield text-info fa-lg"></i>
                    </div>
                    <small class="text-muted">
                        <strong>Secure Payment:</strong> Your data is protected with 256-bit SSL encryption
                    </small>
                </div>
            </div>

            <!-- Terms -->
            <div class="form-check mt-4 px-2">
                <input class="form-check-input" type="checkbox" id="mobile_terms_accepted" name="terms_accepted" required>
                <label class="form-check-label small" for="mobile_terms_accepted">
                    I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> and <a href="#" class="text-decoration-none">Privacy Policy</a>
                </label>
            </div>
        </form>
    </div>
</div>

<!-- Fixed Bottom Button -->
<div class="fixed-bottom bg-white border-top p-3">
    <button type="submit" form="mobile-payment-form" class="btn btn-primary btn-lg w-100" id="mobile-proceed-btn" disabled>
        <i class="fas fa-lock me-2"></i>
        Proceed to Payment
        <span class="spinner-border spinner-border-sm ms-2" style="display: none;"></span>
    </button>
</div>

<!-- Add bottom padding to prevent content being hidden behind fixed button -->
<div style="height: 80px;"></div>
@endsection

@push('styles')
<style>
body {
    background-color: #f8f9fa;
}

.mobile-header {
    background: white;
    position: sticky;
    top: 0;
    z-index: 100;
}

.payment-method-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-method-card:active {
    transform: scale(0.98);
}

.payment-method-card .card {
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.payment-method-card.selected .card {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.payment-icons i {
    font-size: 1em;
}

.fixed-bottom {
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
}

/* Smooth scrolling for better UX */
html {
    scroll-behavior: smooth;
}

/* Custom radio button styling for mobile */
.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

/* Loading state */
.btn:disabled {
    opacity: 0.6;
}

/* Haptic feedback simulation */
@keyframes tap-feedback {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

.payment-method-card:active {
    animation: tap-feedback 0.1s ease-in-out;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentCards = document.querySelectorAll('.payment-method-card');
    const gatewayRadios = document.querySelectorAll('.gateway-radio');
    const proceedBtn = document.getElementById('mobile-proceed-btn');
    const termsCheckbox = document.getElementById('mobile_terms_accepted');
    const paymentForm = document.getElementById('mobile-payment-form');
    const gatewayInfo = document.getElementById('mobile-gateway-info');
    const gatewayInfoContent = document.getElementById('mobile-gateway-info-content');

    // Gateway information for mobile
    const gatewayInfoData = {
        'paypal': {
            title: 'PayPal Payment',
            content: 'Secure payment with PayPal account or card',
            icon: 'fab fa-paypal'
        },
        'razorpay': {
            title: 'Razorpay Payment',
            content: 'Pay with cards, UPI, net banking, or wallets',
            icon: 'fas fa-credit-card'
        },
        'qr_bank_transfer': {
            title: 'QR Bank Transfer',
            content: 'Scan QR code and transfer from your bank app',
            icon: 'fas fa-qrcode'
        }
    };

    // Initialize
    updateProceedButton();
    
    // Set initial selection
    const defaultGateway = document.querySelector('.gateway-radio:checked');
    if (defaultGateway) {
        selectGateway(defaultGateway);
    }

    // Payment card click handlers
    paymentCards.forEach(card => {
        card.addEventListener('click', function() {
            const radio = this.querySelector('.gateway-radio');
            if (radio) {
                radio.checked = true;
                selectGateway(radio);
                
                // Haptic feedback simulation
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }
            }
        });
    });

    // Radio button change handlers
    gatewayRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                selectGateway(this);
            }
        });
    });

    // Terms checkbox handler
    termsCheckbox.addEventListener('change', updateProceedButton);

    // Form submission handler
    paymentForm.addEventListener('submit', function(e) {
        const selectedGateway = document.querySelector('.gateway-radio:checked');
        
        if (!selectedGateway) {
            e.preventDefault();
            showMobileAlert('Please select a payment method');
            return;
        }

        if (!termsCheckbox.checked) {
            e.preventDefault();
            showMobileAlert('Please accept the terms and conditions');
            return;
        }

        // Show loading state
        proceedBtn.disabled = true;
        proceedBtn.querySelector('.spinner-border').style.display = 'inline-block';
        proceedBtn.innerHTML = proceedBtn.innerHTML.replace('Proceed to Payment', 'Processing...');
    });

    function selectGateway(radio) {
        // Update card selection visual state
        paymentCards.forEach(card => card.classList.remove('selected'));
        const selectedCard = radio.closest('.payment-method-card');
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }

        // Show gateway-specific information
        const gatewayName = radio.closest('.payment-method-card').dataset.gateway;
        showGatewayInfo(gatewayName);
        
        updateProceedButton();
    }

    function showGatewayInfo(gatewayName) {
        const info = gatewayInfoData[gatewayName];
        if (info) {
            gatewayInfoContent.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="${info.icon} fa-lg text-primary me-3"></i>
                    <div>
                        <h6 class="mb-1">${info.title}</h6>
                        <small class="text-muted">${info.content}</small>
                    </div>
                </div>
            `;
            gatewayInfo.style.display = 'block';
            
            // Smooth scroll to show the info
            setTimeout(() => {
                gatewayInfo.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'nearest' 
                });
            }, 100);
        } else {
            gatewayInfo.style.display = 'none';
        }
    }

    function updateProceedButton() {
        const selectedGateway = document.querySelector('.gateway-radio:checked');
        const termsAccepted = termsCheckbox.checked;
        
        proceedBtn.disabled = !(selectedGateway && termsAccepted);
    }

    function showMobileAlert(message) {
        // Create mobile-friendly alert
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-warning alert-dismissible fade show position-fixed';
        alertDiv.style.cssText = 'top: 70px; left: 15px; right: 15px; z-index: 1050;';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // Auto-remove after 4 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 4000);
    }

    // Handle orientation change
    window.addEventListener('orientationchange', function() {
        setTimeout(() => {
            // Recalculate positions if needed
            const selectedCard = document.querySelector('.payment-method-card.selected');
            if (selectedCard) {
                selectedCard.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'center' 
                });
            }
        }, 500);
    });

    // Prevent double-tap zoom on buttons
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function(event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            event.preventDefault();
        }
        lastTouchEnd = now;
    }, false);
});
</script>
@endpush