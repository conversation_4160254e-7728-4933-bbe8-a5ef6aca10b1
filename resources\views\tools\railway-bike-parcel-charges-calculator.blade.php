@extends('layouts.app')

@include('layouts.partials.tools-json-ld')

@section('content')
    <x-breadcrumb :segments="$breadcrumbs" :pageTitle="$pageTitle" />
    <div class="container max-w-6xl mx-auto py-8 px-4 bg-bg-light dark:bg-bg-dark transition-colors duration-300">
        <div class="flex flex-wrap -mx-4">
            <div class="w-full lg:w-2/3 px-4">
                <h2 class="text-3xl font-bold mb-6 text-text-primary-light dark:text-text-primary-dark">Railway Bike Parcel
                    Charges Calculator</h2>
                <div class="mb-8">
                    <p class="mb-4 text-text-secondary-light dark:text-text-secondary-dark">Calculate railway bike parcel
                        charges in India with our easy-to-use tool. The cost depends on various factors including distance,
                        weight, packing, and insurance charges.</p>

                    <h2 class="text-xl font-semibold mb-2 text-text-primary-light dark:text-text-primary-dark">Factors
                        Affecting Charges</h2>
                    <p class="mb-4 text-text-secondary-light dark:text-text-secondary-dark">To get an accurate estimate,
                        consider these key factors:</p>

                    <ul class="list-disc pl-5 mb-4 space-y-2 text-text-secondary-light dark:text-text-secondary-dark">
                        <li><strong class="text-text-primary-light dark:text-text-primary-dark">Distance:</strong> The
                            distance between source and destination railway stations affects the base fare.</li>
                        <li><strong class="text-text-primary-light dark:text-text-primary-dark">Bike Weight:</strong>
                            Heavier bikes incur additional charges beyond the standard weight limit.</li>
                        <li><strong class="text-text-primary-light dark:text-text-primary-dark">Packing Charges:</strong>
                            Mandatory packing services add to the total cost.</li>
                        <li><strong class="text-text-primary-light dark:text-text-primary-dark">Insurance:</strong>
                            Protection for your bike based on its declared value.</li>
                    </ul>
                </div>

                <div
                    class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-border-light dark:border-border-dark mb-8 transition-colors duration-300">
                    <div
                        class="bg-gray-50 dark:bg-slate-700 p-4 rounded-t-lg border-b border-border-light dark:border-border-dark transition-colors duration-300">
                        <h2 class="text-xl font-semibold mb-2 text-text-primary-light dark:text-text-primary-dark">Calculate
                            Your Charges</h2>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark">Enter your bike details below to
                            get an instant estimate of railway parcel charges.</p>
                    </div>
                    <div class="p-6">
                        <form id="calculatorForm" class="space-y-4">
                            <div>
                                <label for="distance"
                                    class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Distance
                                    (km):</label>
                                <input type="number"
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-slate-700 text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors duration-300"
                                    id="distance" placeholder="Enter distance in kilometers" value="500" required>
                            </div>
                            <div>
                                <label for="weight"
                                    class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Bike
                                    Weight (kg):</label>
                                <input type="number"
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-slate-700 text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors duration-300"
                                    id="weight" placeholder="Enter weight of the bike" value="150" required>
                            </div>
                            <div>
                                <label for="packingCharge"
                                    class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Packing
                                    Charges (₹):</label>
                                <input type="number"
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-slate-700 text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors duration-300"
                                    id="packingCharge" placeholder="Enter packing charges" value="300" required>
                            </div>
                            <div>
                                <label for="insurance"
                                    class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark">Insurance
                                    (₹):</label>
                                <input type="number"
                                    class="mt-1 block w-full rounded-md border-border-light dark:border-border-dark bg-white dark:bg-slate-700 text-text-primary-light dark:text-text-primary-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light/20 dark:focus:ring-primary-dark/20 focus:ring-opacity-50 transition-colors duration-300"
                                    id="insurance" placeholder="Enter insurance charges" value="200" required>
                            </div>
                            <button type="button"
                                class="w-full bg-primary-light dark:bg-primary-dark hover:bg-primary-light/90 dark:hover:bg-primary-dark/90 text-white font-bold py-2 px-4 rounded-md focus:outline-none focus:shadow-outline transition-all duration-300 ease-in-out transform hover:scale-[1.02] active:scale-[0.98]"
                                onclick="calculateCharges()">
                                Calculate Charges
                            </button>
                        </form>

                        <div class="mt-6">
                            <div id="result"
                                class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">
                                Estimated Charges: ₹<span id="totalCharges">0</span>
                            </div>
                        </div>

                        <p class="mt-4 text-sm text-text-secondary-light dark:text-text-secondary-dark italic">Charges are
                            calculated based on standard railway rates and may vary. Please verify with your local railway
                            station for exact pricing.</p>
                    </div>
                </div>

                <div class="bg-primary-light/10 dark:bg-primary-dark/10 border-l-4 border-primary-light dark:border-primary-dark text-primary-light dark:text-primary-dark p-4 mb-8 rounded-r-md transition-colors duration-300"
                    role="alert">
                    <h4 class="font-bold mb-2">Calculation Example</h4>
                    <div class="text-text-primary-light dark:text-text-primary-dark space-y-1">
                        <p>• Distance: 500 km × ₹3/km = ₹1,500 (base fare)</p>
                        <p>• Weight surcharge: (150kg - 100kg) × ₹2/kg = ₹100</p>
                        <p>• Packing charges: ₹300</p>
                        <p>• Insurance: ₹200</p>
                        <p class="font-semibold">Total: ₹2,100</p>
                    </div>
                </div>

                <div
                    class="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-border-light dark:border-border-dark mb-8 transition-colors duration-300">
                    <div
                        class="bg-gray-50 dark:bg-slate-700 p-4 rounded-t-lg border-b border-border-light dark:border-border-dark transition-colors duration-300">
                        <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">Understanding
                            Railway Parcel Charges</h2>
                    </div>
                    <div class="p-6">
                        <p class="mb-4 text-text-secondary-light dark:text-text-secondary-dark">Railway bike parcel charges
                            are calculated using a structured approach:</p>
                        <ul class="list-disc pl-5 mb-4 space-y-2 text-text-secondary-light dark:text-text-secondary-dark">
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">Base Rate:</strong>
                                Typically ₹3 per kilometer for standard distances</li>
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">Weight Limit:</strong>
                                Base rate covers up to 100kg, additional charges apply beyond this</li>
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">Packing:</strong>
                                Mandatory service ranging from ₹200-₹500 depending on station</li>
                            <li><strong class="text-text-primary-light dark:text-text-primary-dark">Insurance:</strong>
                                Based on declared value, typically ₹100-₹500</li>
                        </ul>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark">Always verify current rates at
                            your local railway station as charges may vary by region and are subject to periodic updates.
                        </p>
                    </div>
                </div>
            </div>

            <div class="w-full lg:w-1/3 px-4">
                <div class="sticky top-20">
                    @include('pincodes.partials.sidebar')
                </div>
            </div>
        </div>

        <!-- Reviews Section -->
        <div class="mt-8">
            <x-tool-reviews
                :reviews="$reviews"
                :tool="$tool"
                :hasMoreReviews="$hasMoreReviews ?? false"
                :totalReviewsCount="$totalReviewsCount ?? 0"
            />
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function calculateCharges() {
            // Get input values
            const distance = document.getElementById('distance').value;
            const weight = document.getElementById('weight').value;
            const packingCharge = document.getElementById('packingCharge').value;
            const insurance = document.getElementById('insurance').value;

            // Define default rate per km and weight surcharge
            const ratePerKm = 3; // ₹ per kilometer
            const baseWeight = 100; // Base weight without extra charges
            const extraWeightCharge = 2; // ₹ per kg for extra weight

            // Calculate base fare
            let baseFare = distance * ratePerKm;

            // Calculate weight charges if the weight is above the base weight
            let weightCharges = 0;
            if (weight > baseWeight) {
                weightCharges = (weight - baseWeight) * extraWeightCharge;
            }

            // Calculate total charges
            let totalCharges = baseFare + weightCharges + parseFloat(packingCharge) + parseFloat(insurance);

            // Display the total charges
            document.getElementById('totalCharges').innerText = totalCharges.toFixed(2);
        }
    </script>
@endpush