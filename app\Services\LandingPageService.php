<?php

namespace App\Services;

use App\Models\LandingPageSection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class LandingPageService
{
    /**
     * Get all active landing page sections with their content
     *
     * @return Collection
     */
    public function getAllSections(): Collection
    {
        return Cache::remember('landing_page_sections', 60 * 60, function () {
            return LandingPageSection::with('contents')
                ->active()
                ->orderBy('sort_order')
                ->get();
        });
    }

    /**
     * Get a specific section by slug with its content
     *
     * @param string $slug
     * @return LandingPageSection|null
     */
    public function getSection(string $slug): ?LandingPageSection
    {
        return Cache::remember("landing_page_section_{$slug}", 60 * 60, function () use ($slug) {
            return LandingPageSection::with('contents')
                ->active()
                ->where('slug', $slug)
                ->first();
        });
    }

    /**
     * Format a section's contents into an easy-to-use array
     *
     * @param LandingPageSection $section
     * @return array
     */
    public function formatSectionContent(LandingPageSection $section): array
    {
        $formattedContent = [];

        foreach ($section->contents as $content) {
            $value = $content->value;

            // The 'repeater' type is already handled by the model's accessor.
            // Handle different content types
            if ($content->type === 'image' && is_array($value)) {
                // If it's an image and already an array, get the first item
                $value = !empty($value) ? $value[0] : null;
            }

            $formattedContent[$content->key] = $value;
        }

        return $formattedContent;
    }

    /**
     * Get all landing page data formatted for display
     *
     * @return array
     */
    public function getLandingPageData()
    {
        $sections = LandingPageSection::with('contents')->get();
        $pageData = [];

        foreach ($sections as $section) {
            $pageData[$section->slug] = [
                'active' => $section->is_active,
                'content' => []
            ];

            foreach ($section->contents as $content) {
                // Process value based on content type
                $value = $content->value;
                if ($content->type === 'image' && is_array($value)) {
                    $value = !empty($value) ? $value[0] : null;
                } elseif ($content->type === 'repeater' && is_string($value)) {
                    $value = json_decode($value, true) ?: [];
                }

                $pageData[$section->slug]['content'][$content->key] = $value;
            }
        }

        return $pageData;
    }

    /**
     * Clear landing page cache
     */
    public function clearCache(): void
    {
        Cache::forget('landing_page_sections');

        $sections = LandingPageSection::all();

        foreach ($sections as $section) {
            Cache::forget("landing_page_section_{$section->slug}");
        }
    }
}